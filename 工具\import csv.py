import csv
import os

def read_lua_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        return None
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return None

def parse_lua_data(lua_content):
    # 这里需要实现 Lua 文件的解析逻辑
    # 假设返回一个包含物品数据的列表
    parsed_data = [
        {
            'Type': 'example_type',
            'MergedType': 'example_merged_type',
            'UnlockPrice': 100,
            'UseEnergy': 10,
            'GeneratedItems': [{'Code': 'item1', 'Weight': 1}, {'Code': 'item2', 'Weight': 2}],
            'Cd': 5,
            'InitialNumber': 3,
            'Frequency': 2,
            'Capacity': 10,
            'SpeedUpPrice': 50
        }
    ]
    return parsed_data

def export_to_csv(data, output_file):
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Type', 'MergedType', 'UnlockPrice', 'UseEnergy', 'GeneratedItems', 'Cd', 'InitialNumber', 'Frequency', 'Capacity', 'SpeedUpPrice']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for item in data:
            generated_items_str = ', '.join([f"{i['Code']}:{i['Weight']}" for i in item.get('GeneratedItems', [])])
            writer.writerow({
                'Type': item['Type'],
                'MergedType': item['MergedType'],
                'UnlockPrice': item['UnlockPrice'],
                'UseEnergy': item['UseEnergy'],
                'GeneratedItems': generated_items_str,
                'Cd': item['Cd'],
                'InitialNumber': item['InitialNumber'],
                'Frequency': item['Frequency'],
                'Capacity': item['Capacity'],
                'SpeedUpPrice': item['SpeedUpPrice'],
            })

def main():
    lua_file_path = os.path.join('fmc_lua', 'Data', 'Config', 'ItemModelConfig.lua')
    if os.path.exists(lua_file_path):
        print("文件存在")
    else:
        print("文件不存在，请检查路径")
        return

    lua_content = read_lua_file(lua_file_path)
    if lua_content is None:
        return

    parsed_data = parse_lua_data(lua_content)
    output_csv_path = 'out1put.csv'  # 输出 CSV 文件路径
    export_to_csv(parsed_data, output_csv_path)

    print(f"数据已导出到 {output_csv_path}")

if __name__ == "__main__":
    main()