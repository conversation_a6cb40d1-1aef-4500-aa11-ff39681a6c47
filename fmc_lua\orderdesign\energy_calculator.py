"""
能量计算模块
实现订单体力消耗计算算法
"""

import math
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict
from data_models import (
    OrderData, OrderRequirement, ItemData, GeneratorData, ConfigData,
    BetMode, EfficiencyMode, PlayerGeneratorInfo
)


class EnergyCalculator:
    """体力消耗计算器"""
    
    def __init__(self, config_data: ConfigData):
        """
        初始化能量计算器
        
        Args:
            config_data: 配置数据
        """
        self.config_data = config_data
    
    def calculate_order_energy(
        self,
        order_items: List[OrderRequirement],
        bet_mode: BetMode,
        efficiency_mode: EfficiencyMode,
        player_generators: List[PlayerGeneratorInfo]
    ) -> float:
        """
        计算订单体力消耗
        
        Args:
            order_items: 订单物品需求列表
            bet_mode: 体力消耗模式 (2bet/4bet/8bet)
            efficiency_mode: 生成器效率模式
            player_generators: 玩家生成器信息
            
        Returns:
            float: 总体力消耗
        """
        try:
            # 步骤1：拆解物品至基础生成器产出
            base_items = self._decompose_to_base_items(order_items)
            
            # 步骤2：按系列汇总折合1级物品数量
            series_quantities = self._sum_base_items_by_series(base_items)
            
            # 步骤3：计算生成器点击次数
            clicks = self._calculate_generator_clicks(
                series_quantities, efficiency_mode, player_generators, bet_mode
            )
            
            # 步骤4：计算总体力
            total_energy = sum(clicks.values()) * bet_mode.value
            
            return total_energy
            
        except Exception as e:
            print(f"计算订单体力消耗时出错: {e}")
            return 0.0
    
    def _decompose_to_base_items(self, order_items: List[OrderRequirement]) -> Dict[str, float]:
        """
        将订单物品拆解至基础生成器产出
        
        Args:
            order_items: 订单物品需求列表
            
        Returns:
            Dict[str, float]: 基础物品代码 -> 数量
        """
        base_items = defaultdict(float)
        
        for requirement in order_items:
            item_type = requirement.item_type
            count = requirement.count
            
            # 递归拆解物品
            decomposed = self._decompose_single_item(item_type, count)
            
            for base_item, base_count in decomposed.items():
                base_items[base_item] += base_count
        
        return dict(base_items)
    
    def _decompose_single_item(self, item_type: str, count: float) -> Dict[str, float]:
        """
        递归拆解单个物品到基础物品
        
        Args:
            item_type: 物品类型
            count: 物品数量
            
        Returns:
            Dict[str, float]: 基础物品代码 -> 数量
        """
        # 如果是菜品（ds_开头），需要查找其配方
        if item_type.startswith('ds_'):
            return self._decompose_recipe(item_type, count)
        
        # 如果是基础物品（it_开头），查看是否需要进一步拆解
        if item_type in self.config_data.items:
            item_data = self.config_data.items[item_type]
            
            # 如果物品有生成器，说明是基础物品
            if item_data.generators:
                return {item_type: count}
            
            # 如果物品可以合成，需要拆解为更低级的物品
            # 简化处理：假设高级物品由2个低级物品合成
            if item_data.level > 1:
                lower_level_type = self._get_lower_level_item(item_type)
                if lower_level_type:
                    return self._decompose_single_item(lower_level_type, count * 2)
        
        # 默认情况，返回原物品
        return {item_type: count}
    
    def _decompose_recipe(self, recipe_type: str, count: float) -> Dict[str, float]:
        """
        拆解菜品配方
        
        Args:
            recipe_type: 菜品类型
            count: 菜品数量
            
        Returns:
            Dict[str, float]: 基础物品代码 -> 数量
        """
        if recipe_type not in self.config_data.recipes:
            # 如果没有配方信息，简化处理
            return {recipe_type: count}
        
        recipe_data = self.config_data.recipes[recipe_type]
        base_items = defaultdict(float)
        
        for required_item in recipe_data.required_items:
            item_type = required_item.get('Type', '')
            item_count = required_item.get('Count', 1)
            
            # 递归拆解所需物品
            decomposed = self._decompose_single_item(item_type, item_count * count)
            
            for base_item, base_count in decomposed.items():
                base_items[base_item] += base_count
        
        return dict(base_items)
    
    def _get_lower_level_item(self, item_type: str) -> Optional[str]:
        """
        获取更低级的物品类型
        
        Args:
            item_type: 物品类型
            
        Returns:
            Optional[str]: 更低级的物品类型，如果不存在则返回None
        """
        parts = item_type.split('_')
        if len(parts) >= 4:
            try:
                series = int(parts[1])
                category = int(parts[2])
                level = int(parts[3])
                
                if level > 1:
                    lower_type = f"it_{series}_{category}_{level - 1}"
                    if lower_type in self.config_data.items:
                        return lower_type
            except ValueError:
                pass
        
        return None
    
    def _sum_base_items_by_series(self, base_items: Dict[str, float]) -> Dict[str, Dict[str, float]]:
        """
        按系列汇总折合1级物品数量
        
        Args:
            base_items: 基础物品代码 -> 数量
            
        Returns:
            Dict[str, Dict[str, float]]: 生成器类型 -> {系列: 折合1级数量}
        """
        series_quantities = defaultdict(lambda: defaultdict(float))
        
        for item_type, count in base_items.items():
            if item_type in self.config_data.items:
                item_data = self.config_data.items[item_type]
                
                # 计算折合1级物品数量
                base_equivalent = item_data.base_equivalent * count
                
                # 按生成器分组
                for generator_type in item_data.generators:
                    if generator_type in self.config_data.generators:
                        generator_data = self.config_data.generators[generator_type]
                        series_key = str(generator_data.series)
                        series_quantities[generator_type][series_key] += base_equivalent
        
        return dict(series_quantities)
    
    def _calculate_generator_clicks(
        self,
        series_quantities: Dict[str, Dict[str, float]],
        efficiency_mode: EfficiencyMode,
        player_generators: List[PlayerGeneratorInfo],
        bet_mode: BetMode
    ) -> Dict[str, int]:
        """
        计算生成器点击次数
        
        Args:
            series_quantities: 生成器类型 -> {系列: 折合1级数量}
            efficiency_mode: 效率模式
            player_generators: 玩家生成器信息
            bet_mode: 体力消耗模式
            
        Returns:
            Dict[str, int]: 生成器类型 -> 点击次数
        """
        clicks = {}
        
        # 构建玩家生成器映射
        player_gen_map = {gen.generator_type: gen for gen in player_generators}
        
        for generator_type, series_data in series_quantities.items():
            if generator_type not in player_gen_map:
                continue
            
            player_gen = player_gen_map[generator_type]
            efficiency = self._get_generator_efficiency(generator_type, efficiency_mode, player_gen)
            
            # 计算该生成器需要的最大点击次数
            max_clicks = 0
            for series_qty in series_data.values():
                if efficiency > 0:
                    required_clicks = math.ceil(series_qty / efficiency / bet_mode.value)
                    max_clicks = max(max_clicks, required_clicks)
            
            clicks[generator_type] = max_clicks
        
        return clicks
    
    def _get_generator_efficiency(
        self,
        generator_type: str,
        efficiency_mode: EfficiencyMode,
        player_gen: PlayerGeneratorInfo
    ) -> float:
        """
        获取生成器效率
        
        Args:
            generator_type: 生成器类型
            efficiency_mode: 效率模式
            player_gen: 玩家生成器信息
            
        Returns:
            float: 生成器效率
        """
        if generator_type not in self.config_data.generators:
            return 1.0
        
        generator_data = self.config_data.generators[generator_type]
        
        if efficiency_mode == EfficiencyMode.AVERAGE:
            # 平均效率：(∑(生成器等级效率×数量)) / 总数量
            return self._calculate_average_efficiency(generator_data, player_gen)
        
        elif efficiency_mode == EfficiencyMode.DYNAMIC:
            # 动态效率：按生成器等级从高到低依次使用
            return self._calculate_dynamic_efficiency(generator_data, player_gen)
        
        elif efficiency_mode == EfficiencyMode.TOP3_AVERAGE:
            # Top3平均效率：取玩家拥有的最高3级生成器的效率平均值
            return self._calculate_top3_efficiency(generator_data, player_gen)
        
        return generator_data.efficiency
    
    def _calculate_average_efficiency(self, generator_data: GeneratorData, player_gen: PlayerGeneratorInfo) -> float:
        """计算平均效率"""
        # 简化计算：假设效率与等级成正比
        base_efficiency = generator_data.efficiency
        level_factor = player_gen.level / generator_data.level if generator_data.level > 0 else 1.0
        return base_efficiency * level_factor
    
    def _calculate_dynamic_efficiency(self, generator_data: GeneratorData, player_gen: PlayerGeneratorInfo) -> float:
        """计算动态效率"""
        # 简化计算：使用最高等级的效率
        base_efficiency = generator_data.efficiency
        level_factor = player_gen.level / generator_data.level if generator_data.level > 0 else 1.0
        return base_efficiency * level_factor
    
    def _calculate_top3_efficiency(self, generator_data: GeneratorData, player_gen: PlayerGeneratorInfo) -> float:
        """计算Top3平均效率"""
        # 简化计算：使用当前等级的效率
        base_efficiency = generator_data.efficiency
        level_factor = player_gen.level / generator_data.level if generator_data.level > 0 else 1.0
        return base_efficiency * level_factor
    
    def calculate_remaining_items(
        self,
        generator_clicks: Dict[str, int],
        required_clicks: Dict[str, int],
        efficiency_mode: EfficiencyMode,
        bet_mode: BetMode,
        player_generators: List[PlayerGeneratorInfo]
    ) -> Tuple[Dict[str, float], Dict[str, int]]:
        """
        计算剩余物品
        
        Args:
            generator_clicks: 实际生成器点击次数
            required_clicks: 需求的点击次数
            efficiency_mode: 效率模式
            bet_mode: 体力消耗模式
            player_generators: 玩家生成器信息
            
        Returns:
            Tuple[Dict[str, float], Dict[str, int]]: (折合1级数量, 实际物品数量)
        """
        base_equivalent = {}
        actual_items = {}
        
        player_gen_map = {gen.generator_type: gen for gen in player_generators}
        
        for generator_type, actual_clicks in generator_clicks.items():
            if generator_type not in player_gen_map:
                continue
            
            player_gen = player_gen_map[generator_type]
            required = required_clicks.get(generator_type, 0)
            
            if actual_clicks > required:
                excess_clicks = actual_clicks - required
                efficiency = self._get_generator_efficiency(generator_type, efficiency_mode, player_gen)
                
                # 折合1级数量
                excess_base = excess_clicks * efficiency * bet_mode.value
                base_equivalent[generator_type] = excess_base
                
                # 实际物品数量（简化计算）
                actual_items[generator_type] = int(excess_base)
        
        return base_equivalent, actual_items
