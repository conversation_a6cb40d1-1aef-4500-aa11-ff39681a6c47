UIUtil = {}

function UIUtil.ToVector2(vector3)
  return Vector2(vector3.x, vector3.y)
end

function UIUtil.ToVector3(vector2)
  return Vector3(vector2.x, vector2.y, 0)
end

function UIUtil._SetVector(vector, x, y, z)
  vector.x = x or vector.x
  vector.y = y or vector.y
  if vector.z then
    vector.z = z or vector.z
  end
end

function UIUtil._AddVector(vector, x, y, z)
  vector.x = vector.x + (x or 0)
  vector.y = vector.y + (y or 0)
  if vector.z then
    vector.z = vector.z + (z or 0)
  end
end

function UIUtil.SetSizeDelta(transform, x, y)
  local sizeDelta = transform.sizeDelta
  UIUtil._SetVector(sizeDelta, x, y)
  transform.sizeDelta = sizeDelta
end

function UIUtil.AddSizeDelta(transform, deltaX, deltaY)
  local sizeDelta = transform.sizeDelta
  UIUtil._AddVector(sizeDelta, deltaX, deltaY)
  transform.sizeDelta = sizeDelta
end

function UIUtil.SetLocalPosition(transform, x, y, z)
  local localPosition = transform.localPosition
  UIUtil._SetVector(localPosition, x, y, z)
  transform.localPosition = localPosition
end

function UIUtil.AddLocalPosition(transform, deltaX, deltaY, deltaZ)
  local localPosition = transform.localPosition
  UIUtil._AddVector(localPosition, deltaX, deltaY, deltaZ)
  transform.localPosition = localPosition
end

function UIUtil.SetPosition(transform, x, y, z)
  local position = transform.position
  UIUtil._SetVector(position, x, y, z)
  transform.position = position
end

function UIUtil.AddPosition(transform, deltaX, deltaY, deltaZ)
  local position = transform.position
  UIUtil._AddVector(position, deltaX, deltaY, deltaZ)
  transform.position = position
end

function UIUtil.SetAnchoredPosition(transform, x, y, z)
  local position = transform.anchoredPosition
  UIUtil._SetVector(position, x, y, z)
  transform.anchoredPosition = position
end

function UIUtil.AddAnchoredPosition(transform, deltaX, deltaY, deltaZ)
  local position = transform.anchoredPosition
  UIUtil._AddVector(position, deltaX, deltaY, deltaZ)
  transform.anchoredPosition = position
end

function UIUtil.SetLocalScale(transform, x, y, z)
  local localScale = transform.localScale
  UIUtil._SetVector(localScale, x, y, z)
  transform.localScale = localScale
end

function UIUtil.AddLocalScale(transform, deltaX, deltaY, deltaZ)
  local localScale = transform.localScale
  UIUtil._AddVector(localScale, deltaX, deltaY, deltaZ)
  transform.localScale = localScale
end

function UIUtil.SetRotation(transform, x, y, z)
  local rotation = transform.rotation
  UIUtil._SetVector(rotation, x, y, z)
  transform.rotation = rotation
end

function UIUtil.AddRotation(transform, deltaX, deltaY, deltaZ)
  local rotation = transform.rotation
  UIUtil._AddVector(rotation, deltaX, deltaY, deltaZ)
  transform.rotation = rotation
end

function UIUtil.SetPivot(rectTransform, pivot)
  local deltaPosition = rectTransform.pivot - pivot
  deltaPosition:Scale(rectTransform.rect.size)
  deltaPosition:Scale(UIUtil.ToVector2(rectTransform.localScale))
  deltaPosition = UIUtil.ToVector3(deltaPosition)
  deltaPosition = rectTransform.rotation * deltaPosition
  rectTransform.pivot = pivot
  rectTransform.localPosition = rectTransform.localPosition - deltaPosition
end

function UIUtil.SetColor(comp, r, g, b, a)
  local color = comp.color
  color.r = r or color.r
  color.g = g or color.g
  color.b = b or color.b
  color.a = a or color.a
  comp.color = color
end

function UIUtil.SetAlpha(comp, a)
  UIUtil.SetColor(comp, nil, nil, nil, a)
end

function UIUtil.UpdateSortingOrder(go, sortingOrder)
  local renderers = go:GetComponentsInChildren(typeof(Renderer), true)
  for i = 0, renderers.Length - 1 do
    renderers[i].sortingOrder = sortingOrder
  end
end

function UIUtil.UpdateDeltaSortingOrder(go, deltaSortingOrder)
  local renderers = go:GetComponentsInChildren(typeof(Renderer), true)
  for i = 0, renderers.Length - 1 do
    renderers[i].sortingOrder = renderers[i].sortingOrder + deltaSortingOrder
  end
end

function UIUtil.ConvertHexColor2CSColor(hexColor, withAlpha)
  local r = tonumber(hexColor:sub(1, 2), 16) / 255
  local g = tonumber(hexColor:sub(3, 4), 16) / 255
  local b = tonumber(hexColor:sub(5, 6), 16) / 255
  if withAlpha then
    local a = tonumber(hexColor:sub(7, 8), 16) / 255
    return CSColor(r, g, b, a)
  else
    return CSColor(r, g, b)
  end
end

function UIUtil.SetActive(gameObject, active)
  if gameObject.activeSelf ~= active then
    gameObject:SetActive(active)
  end
end

function UIUtil.IsEmptyComponent(component)
  if component and component.gameObject and component.gameObject.IsNull and not component.gameObject:IsNull() then
    return false
  end
  return true
end
