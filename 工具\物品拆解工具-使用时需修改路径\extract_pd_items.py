import re
import os
import traceback
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from pathlib import Path
import time
import json

def write_to_excel(items, output_file):
    if not items:
        print("没有找到任何物品，不创建Excel文件")
        return

    print(f"准备写入Excel文件: {output_file}")
    try:
        wb = Workbook()
        ws = wb.active
        
        # 定义表头
        headers = [
            'Type', 'MergedType', 'Category', 'BookOrder', 'BookReward',
            'UnlockPrice', 'UseEnergy', 'TapeItems', 'GeneratedItems',
            'Cd', 'InitialNumber', 'Frequency', 'Capacity', 'SpeedUpPrice'
        ]
        
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
            ws.cell(row=1, column=col).font = ws.cell(row=1, column=col).font.copy(bold=True)
        
        # 写入数据
        items.sort(key=lambda x: x['Type'])
        for row, item in enumerate(items, 2):
            for col, header in enumerate(headers, 1):
                if header in item:
                    # 对于复杂数据类型，将其转换为字符串形式
                    if isinstance(item[header], (list, dict)):
                        ws.cell(row=row, column=col, value=json.dumps(item[header], ensure_ascii=False))
                    else:
                        ws.cell(row=row, column=col, value=item[header])
        
        # 调整列宽以适应内容
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 30
            
        wb.save(output_file)
        print(f"Excel文件已保存到: {output_file}")
        
    except Exception as e:
        print(f"写入Excel文件时出错: {str(e)}")
        print(traceback.format_exc())

def find_item_block(lines, start_idx):
    """查找完整的物品定义块"""
    open_brackets = 0
    start_line = -1
    end_line = -1
    
    # 向上查找起始行
    i = start_idx
    while i >= 0:
        line = lines[i].strip()
        if line.startswith('{') and not line.startswith('--'):
            start_line = i
            break
        i -= 1
    
    if start_line == -1:
        return None, None
    
    # 向下查找结束行
    for i in range(start_line, len(lines)):
        line = lines[i]
        for char in line:
            if char == '{':
                open_brackets += 1
            elif char == '}':
                open_brackets -= 1
                if open_brackets == 0:
                    end_line = i
                    return start_line, end_line
    
    return None, None

def extract_items_array(text, field_name):
    """提取物品数组的辅助函数"""
    items = []
    # 找到字段的起始位置
    start_pattern = rf'{field_name}\s*=\s*{{' 
    start_match = re.search(start_pattern, text)
    if not start_match:
        return items

    # 提取整个数组内容
    content = text[start_match.end():]
    bracket_count = 1
    array_content = ""
    
    for char in content:
        if char == '{':
            bracket_count += 1
        elif char == '}':
            bracket_count -= 1
            if bracket_count == 0:
                break
        array_content += char

    # 使用正则表达式提取每个项目
    item_pattern = r'{[^{]*Code\s*=\s*"([^"]+)"[^{]*Weight\s*=\s*(\d+)[^}]*}'
    for match in re.finditer(item_pattern, array_content, re.DOTALL):
        code = match.group(1)
        weight = int(match.group(2))
        items.append({
            'Code': code,
            'Weight': weight
        })

    return items

def extract_pd_items_from_lua(file_path):
    """提取pd物品信息的主函数"""
    print(f"\n开始处理文件: {file_path}")
    if not os.path.exists(file_path):
        print(f"错误：文件不存在: {file_path}")
        return []
    
    items = []
    processed_types = set()  # 用于跟踪已处理的物品类型
    
    try:
        file_size = os.path.getsize(file_path)
        print(f"文件大小: {file_size/1024/1024:.2f} MB")
        
        start_time = time.time()
        
        # 读取文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        
        # 分割文件内容为行
        lines = content.split('\n')
        line_count = len(lines)
        print(f"文件共有 {line_count} 行")
        
        # 首先找到所有pd物品的位置
        pd_positions = []
        type_pattern = re.compile(r'Type\s*=\s*"(pd_[^"]+)"')
        
        for i, line in enumerate(lines):
            match = type_pattern.search(line)
            if match:
                pd_positions.append((i, match.group(1)))
        
        print(f"找到 {len(pd_positions)} 个可能的pd物品定义")
        
        # 处理每个找到的位置
        for pos, pd_type in pd_positions:
            try:
                # 如果这个类型已经处理过，跳过
                if pd_type in processed_types:
                    continue
                
                processed_types.add(pd_type)
                
                # 查找完整的物品块
                start_line, end_line = find_item_block(lines, pos)
                if start_line is None or end_line is None:
                    print(f"警告：无法找到物品 {pd_type} 的完整定义块")
                    continue
                
                # 提取物品块内容
                item_block = '\n'.join(lines[start_line:end_line+1])
                
                # 解析物品信息
                item = {'Type': pd_type}
                
                # 提取MergedType
                merged_type_match = re.search(r'MergedType\s*=\s*"([^"]+)"', item_block)
                if merged_type_match:
                    item['MergedType'] = merged_type_match.group(1)
                
                # 提取Category数组
                category_match = re.search(r'Category\s*=\s*{([^}]*)}', item_block)
                if category_match:
                    category_str = category_match.group(1).strip()
                    if category_str:
                        categories = []
                        for cat in category_str.split(','):
                            cat = cat.strip()
                            if cat and cat.isdigit():
                                categories.append(int(cat))
                        item['Category'] = categories
                
                # 提取数值型属性
                for attr in ['BookOrder', 'UnlockPrice', 'UseEnergy', 'Cd', 
                           'InitialNumber', 'Frequency', 'Capacity', 'SpeedUpPrice']:
                    attr_match = re.search(rf'{attr}\s*=\s*(\d+(?:\.\d+)?)', item_block)
                    if attr_match:
                        value = attr_match.group(1)
                        item[attr] = int(float(value))
                
                # 提取BookReward
                book_reward_section = re.search(r'BookReward\s*=\s*{([^}]*{[^}]*}[^}]*)}', item_block)
                if book_reward_section:
                    book_reward_text = book_reward_section.group(1)
                    book_rewards = []
                    
                    for reward_match in re.finditer(r'{([^}]+)}', book_reward_text):
                        reward_content = reward_match.group(1)
                        currency = re.search(r'Currency\s*=\s*"([^"]+)"', reward_content)
                        amount = re.search(r'Amount\s*=\s*(\d+)', reward_content)
                        
                        if currency and amount:
                            book_rewards.append({
                                'Currency': currency.group(1),
                                'Amount': int(amount.group(1))
                            })
                    
                    if book_rewards:
                        item['BookReward'] = book_rewards
                
                # 提取TapeItems
                tape_items_section = re.search(r'TapeItems\s*=\s*{.*}(?=\s*,[^}]*}|\s*})', item_block, re.DOTALL)
                if tape_items_section:
                    tape_items = extract_items_array(tape_items_section.group(0), 'TapeItems')
                    if tape_items:
                        item['TapeItems'] = tape_items
                
                # 提取GeneratedItems
                generated_items_section = re.search(r'GeneratedItems\s*=\s*{.*}(?=\s*,[^}]*}|\s*})', item_block, re.DOTALL)
                if generated_items_section:
                    generated_items = extract_items_array(generated_items_section.group(0), 'GeneratedItems')
                    if generated_items:
                        item['GeneratedItems'] = generated_items
                
                # 添加到结果列表
                items.append(item)
                if len(items) % 10 == 0:
                    print(f"已成功解析 {len(items)} 个物品...")
            
            except Exception as e:
                print(f"处理物品时出错: {str(e)}")
                print(traceback.format_exc())
                continue
        
        end_time = time.time()
        print(f"文件处理完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"成功提取 {len(items)} 个有效pd物品")
        
    except Exception as e:
        print(f"处理文件时发生错误:")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print("详细错误信息:")
        print(traceback.format_exc())
    
    return items

# 设置基础路径
base_path = 'd:/MCWork/GHabout/FMC/fmc_lua_1.18.5'

# 配置文件列表
config_file_names = [
    'ItemModelConfig.lua',

]

# 使用确切的路径格式
config_path = os.path.join(base_path, 'fmc_lua', 'Data', 'Config')
print(f"使用配置路径: {config_path}")

# 路径验证
if not os.path.exists(config_path):
    print(f"警告：配置目录不存在: {config_path}")
    print("请检查base_path设置是否正确")

# 处理所有配置文件
config_files = [os.path.join(config_path, file_name) for file_name in config_file_names]

print("开始处理配置文件...")
desktop = os.path.join(os.path.expanduser("~"), "Desktop")

for file_path in config_files:
    print(f"\n{'='*50}")
    print(f"准备处理文件: {file_path}")
    if os.path.exists(file_path):
        items = extract_pd_items_from_lua(file_path)
        if items:
            file_name = Path(file_path).stem + '_pd_items.xlsx'
            output_file = os.path.join(desktop, file_name)
            write_to_excel(items, output_file)
            print(f"文件 {file_path} 处理完成，找到 {len(items)} 个物品")
    else:
        print(f"跳过不存在的文件: {file_path}")
    print(f"{'='*50}\n")

print("\n所有文件处理完成！") 