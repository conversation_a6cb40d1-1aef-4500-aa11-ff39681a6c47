ETutorialState = {
  NotStarted = 0,
  Ongoing = 1,
  Finished = 2
}
ETutorialId = {
  ToBoard = "toboard",
  Timeline = "timeline",
  PDCD1 = "CD_pd_1_7",
  PDCD2 = "CD_pd_2_6",
  OrderGroup = "order_group",
  CG = "tutorial_cg",
  BakeOut1 = "tutorial_bakeout_1",
  CoinRaceEntryTutorial = "tutorial_coin_race_entry",
  CoinRaceFirstRoundTutorial = "tutorial_coin_race_first_main_window",
  CoinRaceSecondRoundTutorial = "tutorial_coin_race_second_main_window",
  CoinRaceOrderTutorial = "tutorial_coin_race_order",
  ExtraBoardStart = "tutorial_extraboard_start",
  ExtraBoardCobwebUnlock = "tutorial_extraboard_cobweb_unlock",
  ChangeName = "tutorial_change_name",
  AddItemOldUser = "additem_old_user"
}
ETutorialStartCondition = {
  TutorialFinished = "TutorialFinished",
  FunctionEnabled = "FunctionEnabled",
  CachedItemPushed = "CachedItemPushed",
  MainLevel = "MainLevel",
  TaskFinished = "TaskFinished",
  CashDashStart = "CashDashStart",
  BakeOutStart = "BakeOutStart",
  OrderGroupFinished = "OrderGroupFinished",
  ItemAdded = "ItemAdded",
  HasOrder = "HasOrder",
  CoinRaceStart = "CoinRaceStart",
  PkRaceStart = "PkRaceStart",
  PassStart = "PassStart",
  DigActivityStart = "DigActivityStart",
  ProgressStart = "ProgressStart",
  ExtraBoardStart = "ExtraBoardStart",
  BlindChestStart = "BlindChestStart"
}
if GameConfig.IsTestMode() then
  setmetatable(ETutorialStartCondition, {
    __index = function(_, key)
      Log.Error("ETutorialStartCondition 中缺少定义: " .. tostring(key))
      return nil
    end
  })
end
INTRO_TIMELINE_ID = "intro_timeline"
