# 器械(eq)加工物品时长规律分析

## 概述

通过分析ItemModelConfig.lua中器械的配置数据，总结器械加工不同菜品的时长规律和计算方法。

## 器械系列分析

### 🔪 eq_1系列 - 切菜器械 (ds_chopve)

#### 时长数据表
| 器械等级 | ds_chopve_1 | ds_chopve_2 | 规律分析 |
|---------|-------------|-------------|----------|
| eq_1_4  | 10秒        | 20秒        | 基础时长 |
| eq_1_5  | 9秒         | 18秒        | -1秒/-2秒 |
| eq_1_6  | 8秒         | 16秒        | -1秒/-2秒 |
| eq_1_7  | 7秒         | 14秒        | -1秒/-2秒 |
| eq_1_8  | 6秒         | 12秒        | -1秒/-2秒 |
| eq_1_9  | 5秒         | 10秒        | -1秒/-2秒 |
| eq_1_10 | 5秒         | 8秒         | 0秒/-2秒 |

#### 计算规律
- **ds_chopve_1**: 每升级减少1秒，最低5秒
- **ds_chopve_2**: 每升级减少2秒，最低8秒
- **比例关系**: ds_chopve_2 = ds_chopve_1 × 2 (除了最高级)

### 🔥 eq_2系列 - 烧烤器械 (ds_grillve)

#### 时长数据表
| 器械等级 | ds_grillve_1 | ds_grillve_2 | 规律分析 |
|---------|--------------|--------------|----------|
| eq_2_4  | 640秒        | 200秒        | 基础时长 |
| eq_2_5  | 576秒        | 180秒        | -64秒/-20秒 |
| eq_2_6  | 512秒        | 160秒        | -64秒/-20秒 |
| eq_2_7  | 448秒        | 140秒        | -64秒/-20秒 |
| eq_2_8  | 384秒        | 120秒        | -64秒/-20秒 |
| eq_2_9  | 320秒        | 100秒        | -64秒/-20秒 |
| eq_2_10 | 256秒        | 80秒         | -64秒/-20秒 |

#### 计算规律
- **ds_grillve_1**: 每升级减少64秒 (640→256)
- **ds_grillve_2**: 每升级减少20秒 (200→80)
- **比例关系**: ds_grillve_1 ≈ ds_grillve_2 × 3.2

### 🥤 eq_3系列 - 果汁器械 (ds_juice)

#### 时长数据表
| 器械等级 | ds_juice_1 | ds_juice_2 | 规律分析 |
|---------|------------|------------|----------|
| eq_3_4  | 80秒       | 160秒      | 基础时长 |
| eq_3_5  | 72秒       | 144秒      | -8秒/-16秒 |
| eq_3_6  | 64秒       | 128秒      | -8秒/-16秒 |
| eq_3_7  | 56秒       | 112秒      | -8秒/-16秒 |
| eq_3_8  | 48秒       | 96秒       | -8秒/-16秒 |
| eq_3_9  | 40秒       | 80秒       | -8秒/-16秒 |
| eq_3_10 | 32秒       | 64秒       | -8秒/-16秒 |

#### 计算规律
- **ds_juice_1**: 每升级减少8秒 (80→32)
- **ds_juice_2**: 每升级减少16秒 (160→64)
- **比例关系**: ds_juice_2 = ds_juice_1 × 2

### 🍳 eq_4系列 - 油炸器械 (ds_friedve)

#### 时长数据表
| 器械等级 | ds_friedve_1 | ds_friedve_2 | 规律分析 |
|---------|--------------|--------------|----------|
| eq_4_4  | 1280秒       | 720秒        | 基础时长 |
| eq_4_5  | 1152秒       | 648秒        | -128秒/-72秒 |
| eq_4_6  | 1024秒       | 576秒        | -128秒/-72秒 |
| eq_4_7  | 896秒        | 504秒        | -128秒/-72秒 |
| eq_4_8  | 768秒        | 432秒        | -128秒/-72秒 |
| eq_4_9  | 640秒        | 360秒        | -128秒/-72秒 |
| eq_4_10 | 512秒        | 288秒        | -128秒/-72秒 |

#### 计算规律
- **ds_friedve_1**: 每升级减少128秒 (1280→512)
- **ds_friedve_2**: 每升级减少72秒 (720→288)
- **比例关系**: ds_friedve_1 ≈ ds_friedve_2 × 1.78

### 🍲 eq_5系列 - 食物器械 (ds_fd)

#### 时长数据表
| 器械等级 | ds_fd_5 | ds_fd_6 | 规律分析 |
|---------|---------|---------|----------|
| eq_5_4  | 130秒   | 150秒   | 基础时长 |
| eq_5_5  | 117秒   | 135秒   | -13秒/-15秒 |
| eq_5_6  | 104秒   | 120秒   | -13秒/-15秒 |
| eq_5_7  | 91秒    | 105秒   | -13秒/-15秒 |
| eq_5_8  | 78秒    | 90秒    | -13秒/-15秒 |
| eq_5_9  | 65秒    | 75秒    | -13秒/-15秒 |
| eq_5_10 | 52秒    | 60秒    | -13秒/-15秒 |

#### 计算规律
- **ds_fd_5**: 每升级减少13秒 (130→52)
- **ds_fd_6**: 每升级减少15秒 (150→60)
- **比例关系**: ds_fd_6 ≈ ds_fd_5 × 1.15

### 🍸 eq_6系列 - 鸡尾酒器械 (ds_e6cockt/ds_e6soup)

#### 时长数据表
| 器械等级 | ds_e6cockt_7 | ds_e6soup_1 | 规律分析 |
|---------|--------------|-------------|----------|
| eq_6_4  | 70秒         | 80秒        | 基础时长 |
| eq_6_5  | 63秒         | 72秒        | -7秒/-8秒 |
| eq_6_6  | 56秒         | 64秒        | -7秒/-8秒 |
| eq_6_7  | 49秒         | 56秒        | -7秒/-8秒 |
| eq_6_8  | 42秒         | 48秒        | -7秒/-8秒 |
| eq_6_9  | 35秒         | 40秒        | -7秒/-8秒 |
| eq_6_10 | 28秒         | 32秒        | -7秒/-8秒 |

#### 计算规律
- **ds_e6cockt_7**: 每升级减少7秒 (70→28)
- **ds_e6soup_1**: 每升级减少8秒 (80→32)
- **比例关系**: ds_e6soup_1 ≈ ds_e6cockt_7 × 1.14

## 通用计算规律

### 📊 时长递减模式

#### **线性递减模式**
所有器械系列都采用**线性递减**模式：
```
新时长 = 基础时长 - (等级差 × 固定递减值)
```

#### **递减值规律**
| 器械系列 | 菜品1递减值 | 菜品2递减值 | 基础时长比例 |
|---------|-------------|-------------|--------------|
| eq_1    | 1秒         | 2秒         | 1:2          |
| eq_2    | 64秒        | 20秒        | 3.2:1        |
| eq_3    | 8秒         | 16秒        | 1:2          |
| eq_4    | 128秒       | 72秒        | 1.78:1       |
| eq_5    | 13秒        | 15秒        | 1:1.15       |
| eq_6    | 7秒         | 8秒         | 1:1.14       |

### 🎯 计算公式

#### **通用公式**
```lua
function CalculateCookingTime(equipmentType, recipeType, equipmentLevel)
  local baseConfig = GetBaseConfig(equipmentType, recipeType)
  local baseTime = baseConfig.baseDuration
  local decreasePerLevel = baseConfig.decreasePerLevel
  local minLevel = baseConfig.minLevel  -- 通常是4级开始有配方
  
  local levelDiff = equipmentLevel - minLevel
  local newTime = baseTime - (levelDiff * decreasePerLevel)
  
  return math.max(newTime, baseConfig.minTime)  -- 确保不低于最小时长
end
```

#### **具体配置表**
```lua
local CookingTimeConfig = {
  eq_1 = {
    ds_chopve_1 = {baseDuration = 10, decreasePerLevel = 1, minLevel = 4, minTime = 5},
    ds_chopve_2 = {baseDuration = 20, decreasePerLevel = 2, minLevel = 4, minTime = 8}
  },
  eq_2 = {
    ds_grillve_1 = {baseDuration = 640, decreasePerLevel = 64, minLevel = 4, minTime = 256},
    ds_grillve_2 = {baseDuration = 200, decreasePerLevel = 20, minLevel = 4, minTime = 80}
  },
  eq_3 = {
    ds_juice_1 = {baseDuration = 80, decreasePerLevel = 8, minLevel = 4, minTime = 32},
    ds_juice_2 = {baseDuration = 160, decreasePerLevel = 16, minLevel = 4, minTime = 64}
  },
  eq_4 = {
    ds_friedve_1 = {baseDuration = 1280, decreasePerLevel = 128, minLevel = 4, minTime = 512},
    ds_friedve_2 = {baseDuration = 720, decreasePerLevel = 72, minLevel = 4, minTime = 288}
  },
  eq_5 = {
    ds_fd_5 = {baseDuration = 130, decreasePerLevel = 13, minLevel = 4, minTime = 52},
    ds_fd_6 = {baseDuration = 150, decreasePerLevel = 15, minLevel = 4, minTime = 60}
  },
  eq_6 = {
    ds_e6cockt_7 = {baseDuration = 70, decreasePerLevel = 7, minLevel = 4, minTime = 28},
    ds_e6soup_1 = {baseDuration = 80, decreasePerLevel = 8, minLevel = 4, minTime = 32}
  }
}
```

### 📈 效率提升分析

#### **时长压缩比例**
| 器械系列 | 4级→10级压缩比 | 效率提升 |
|---------|----------------|----------|
| eq_1    | 50%→40%        | 2倍→2.5倍 |
| eq_2    | 60%→40%        | 1.67倍→2.5倍 |
| eq_3    | 60%→40%        | 1.67倍→2.5倍 |
| eq_4    | 60%→40%        | 1.67倍→2.5倍 |
| eq_5    | 60%→40%        | 1.67倍→2.5倍 |
| eq_6    | 60%→40%        | 1.67倍→2.5倍 |

#### **升级收益递减**
- **前期收益高**: 4级→7级提升明显
- **后期收益低**: 8级→10级提升有限
- **最优性价比**: 通常在7-8级达到最佳性价比

## 设计思路分析

### 🎮 游戏平衡性
1. **线性递减**: 保证升级收益的可预测性
2. **最低限制**: 防止时长过短影响游戏节奏
3. **差异化**: 不同器械有不同的时长特性

### 💰 经济平衡
1. **升级成本**: 高等级器械需要更多资源
2. **收益递减**: 后期升级收益降低，平衡投入产出
3. **多样选择**: 不同器械适合不同的游戏策略

这个时长系统通过简单的线性递减公式，实现了复杂的游戏平衡，既保证了升级的价值感，又维持了游戏的长期可玩性。
