# 器械(eq)加工时长与Score分段计算分析

## 概述

通过分析ItemModelConfig.lua中器械的配置数据，结合物品Score系统，总结器械加工时长的规律和基于Score的分段计算方法。

## Score系统概述

### 📊 物品Score规律
物品的Score值遵循**指数增长**模式：
- **1级物品**: 0.42 - 2.22 (基础分值)
- **2级物品**: 0.83 - 4.44 (约2倍)
- **3级物品**: 1.67 - 8.89 (约2倍)
- **4级物品**: 3.33 - 17.78 (约2倍)
- **最高级**: 853.33 - 1204.71 (指数增长)

### 🎯 Score计算公式
```lua
-- 基础Score公式 (以it_1_1系列为例)
function CalculateItemScore(series, level)
  local baseScore = GetSeriesBaseScore(series)  -- 如0.42
  return baseScore * (2 ^ (level - 1))
end
```

## 器械系列分析

### 🔪 eq_1系列 - 切菜器械 (ds_chopve)

#### 时长数据表
| 器械等级 | ds_chopve_1 | ds_chopve_2 | 规律分析 |
|---------|-------------|-------------|----------|
| eq_1_4  | 10秒        | 20秒        | 基础时长 |
| eq_1_5  | 9秒         | 18秒        | -1秒/-2秒 |
| eq_1_6  | 8秒         | 16秒        | -1秒/-2秒 |
| eq_1_7  | 7秒         | 14秒        | -1秒/-2秒 |
| eq_1_8  | 6秒         | 12秒        | -1秒/-2秒 |
| eq_1_9  | 5秒         | 10秒        | -1秒/-2秒 |
| eq_1_10 | 5秒         | 8秒         | 0秒/-2秒 |

#### 计算规律
- **ds_chopve_1**: 每升级减少1秒，最低5秒
- **ds_chopve_2**: 每升级减少2秒，最低8秒
- **比例关系**: ds_chopve_2 = ds_chopve_1 × 2 (除了最高级)

### 🔥 eq_2系列 - 烧烤器械 (ds_grillve)

#### 时长数据表
| 器械等级 | ds_grillve_1 | ds_grillve_2 | 规律分析 |
|---------|--------------|--------------|----------|
| eq_2_4  | 640秒        | 200秒        | 基础时长 |
| eq_2_5  | 576秒        | 180秒        | -64秒/-20秒 |
| eq_2_6  | 512秒        | 160秒        | -64秒/-20秒 |
| eq_2_7  | 448秒        | 140秒        | -64秒/-20秒 |
| eq_2_8  | 384秒        | 120秒        | -64秒/-20秒 |
| eq_2_9  | 320秒        | 100秒        | -64秒/-20秒 |
| eq_2_10 | 256秒        | 80秒         | -64秒/-20秒 |

#### 计算规律
- **ds_grillve_1**: 每升级减少64秒 (640→256)
- **ds_grillve_2**: 每升级减少20秒 (200→80)
- **比例关系**: ds_grillve_1 ≈ ds_grillve_2 × 3.2

### 🥤 eq_3系列 - 果汁器械 (ds_juice)

#### 时长数据表
| 器械等级 | ds_juice_1 | ds_juice_2 | 规律分析 |
|---------|------------|------------|----------|
| eq_3_4  | 80秒       | 160秒      | 基础时长 |
| eq_3_5  | 72秒       | 144秒      | -8秒/-16秒 |
| eq_3_6  | 64秒       | 128秒      | -8秒/-16秒 |
| eq_3_7  | 56秒       | 112秒      | -8秒/-16秒 |
| eq_3_8  | 48秒       | 96秒       | -8秒/-16秒 |
| eq_3_9  | 40秒       | 80秒       | -8秒/-16秒 |
| eq_3_10 | 32秒       | 64秒       | -8秒/-16秒 |

#### 计算规律
- **ds_juice_1**: 每升级减少8秒 (80→32)
- **ds_juice_2**: 每升级减少16秒 (160→64)
- **比例关系**: ds_juice_2 = ds_juice_1 × 2

### 🍳 eq_4系列 - 油炸器械 (ds_friedve)

#### 时长数据表
| 器械等级 | ds_friedve_1 | ds_friedve_2 | 规律分析 |
|---------|--------------|--------------|----------|
| eq_4_4  | 1280秒       | 720秒        | 基础时长 |
| eq_4_5  | 1152秒       | 648秒        | -128秒/-72秒 |
| eq_4_6  | 1024秒       | 576秒        | -128秒/-72秒 |
| eq_4_7  | 896秒        | 504秒        | -128秒/-72秒 |
| eq_4_8  | 768秒        | 432秒        | -128秒/-72秒 |
| eq_4_9  | 640秒        | 360秒        | -128秒/-72秒 |
| eq_4_10 | 512秒        | 288秒        | -128秒/-72秒 |

#### 计算规律
- **ds_friedve_1**: 每升级减少128秒 (1280→512)
- **ds_friedve_2**: 每升级减少72秒 (720→288)
- **比例关系**: ds_friedve_1 ≈ ds_friedve_2 × 1.78

### 🍲 eq_5系列 - 食物器械 (ds_fd)

#### 时长数据表
| 器械等级 | ds_fd_5 | ds_fd_6 | 规律分析 |
|---------|---------|---------|----------|
| eq_5_4  | 130秒   | 150秒   | 基础时长 |
| eq_5_5  | 117秒   | 135秒   | -13秒/-15秒 |
| eq_5_6  | 104秒   | 120秒   | -13秒/-15秒 |
| eq_5_7  | 91秒    | 105秒   | -13秒/-15秒 |
| eq_5_8  | 78秒    | 90秒    | -13秒/-15秒 |
| eq_5_9  | 65秒    | 75秒    | -13秒/-15秒 |
| eq_5_10 | 52秒    | 60秒    | -13秒/-15秒 |

#### 计算规律
- **ds_fd_5**: 每升级减少13秒 (130→52)
- **ds_fd_6**: 每升级减少15秒 (150→60)
- **比例关系**: ds_fd_6 ≈ ds_fd_5 × 1.15

### 🍸 eq_6系列 - 鸡尾酒器械 (ds_e6cockt/ds_e6soup)

#### 时长数据表
| 器械等级 | ds_e6cockt_7 | ds_e6soup_1 | 规律分析 |
|---------|--------------|-------------|----------|
| eq_6_4  | 70秒         | 80秒        | 基础时长 |
| eq_6_5  | 63秒         | 72秒        | -7秒/-8秒 |
| eq_6_6  | 56秒         | 64秒        | -7秒/-8秒 |
| eq_6_7  | 49秒         | 56秒        | -7秒/-8秒 |
| eq_6_8  | 42秒         | 48秒        | -7秒/-8秒 |
| eq_6_9  | 35秒         | 40秒        | -7秒/-8秒 |
| eq_6_10 | 28秒         | 32秒        | -7秒/-8秒 |

#### 计算规律
- **ds_e6cockt_7**: 每升级减少7秒 (70→28)
- **ds_e6soup_1**: 每升级减少8秒 (80→32)
- **比例关系**: ds_e6soup_1 ≈ ds_e6cockt_7 × 1.14

## 通用计算规律

### 📊 时长递减模式

#### **线性递减模式**
所有器械系列都采用**线性递减**模式：
```
新时长 = 基础时长 - (等级差 × 固定递减值)
```

#### **递减值规律**
| 器械系列 | 菜品1递减值 | 菜品2递减值 | 基础时长比例 |
|---------|-------------|-------------|--------------|
| eq_1    | 1秒         | 2秒         | 1:2          |
| eq_2    | 64秒        | 20秒        | 3.2:1        |
| eq_3    | 8秒         | 16秒        | 1:2          |
| eq_4    | 128秒       | 72秒        | 1.78:1       |
| eq_5    | 13秒        | 15秒        | 1:1.15       |
| eq_6    | 7秒         | 8秒         | 1:1.14       |

### 🎯 计算公式

#### **通用公式**
```lua
function CalculateCookingTime(equipmentType, recipeType, equipmentLevel)
  local baseConfig = GetBaseConfig(equipmentType, recipeType)
  local baseTime = baseConfig.baseDuration
  local decreasePerLevel = baseConfig.decreasePerLevel
  local minLevel = baseConfig.minLevel  -- 通常是4级开始有配方
  
  local levelDiff = equipmentLevel - minLevel
  local newTime = baseTime - (levelDiff * decreasePerLevel)
  
  return math.max(newTime, baseConfig.minTime)  -- 确保不低于最小时长
end
```

#### **具体配置表**
```lua
local CookingTimeConfig = {
  eq_1 = {
    ds_chopve_1 = {baseDuration = 10, decreasePerLevel = 1, minLevel = 4, minTime = 5},
    ds_chopve_2 = {baseDuration = 20, decreasePerLevel = 2, minLevel = 4, minTime = 8}
  },
  eq_2 = {
    ds_grillve_1 = {baseDuration = 640, decreasePerLevel = 64, minLevel = 4, minTime = 256},
    ds_grillve_2 = {baseDuration = 200, decreasePerLevel = 20, minLevel = 4, minTime = 80}
  },
  eq_3 = {
    ds_juice_1 = {baseDuration = 80, decreasePerLevel = 8, minLevel = 4, minTime = 32},
    ds_juice_2 = {baseDuration = 160, decreasePerLevel = 16, minLevel = 4, minTime = 64}
  },
  eq_4 = {
    ds_friedve_1 = {baseDuration = 1280, decreasePerLevel = 128, minLevel = 4, minTime = 512},
    ds_friedve_2 = {baseDuration = 720, decreasePerLevel = 72, minLevel = 4, minTime = 288}
  },
  eq_5 = {
    ds_fd_5 = {baseDuration = 130, decreasePerLevel = 13, minLevel = 4, minTime = 52},
    ds_fd_6 = {baseDuration = 150, decreasePerLevel = 15, minLevel = 4, minTime = 60}
  },
  eq_6 = {
    ds_e6cockt_7 = {baseDuration = 70, decreasePerLevel = 7, minLevel = 4, minTime = 28},
    ds_e6soup_1 = {baseDuration = 80, decreasePerLevel = 8, minLevel = 4, minTime = 32}
  }
}
```

### 📈 效率提升分析

#### **时长压缩比例**
| 器械系列 | 4级→10级压缩比 | 效率提升 |
|---------|----------------|----------|
| eq_1    | 50%→40%        | 2倍→2.5倍 |
| eq_2    | 60%→40%        | 1.67倍→2.5倍 |
| eq_3    | 60%→40%        | 1.67倍→2.5倍 |
| eq_4    | 60%→40%        | 1.67倍→2.5倍 |
| eq_5    | 60%→40%        | 1.67倍→2.5倍 |
| eq_6    | 60%→40%        | 1.67倍→2.5倍 |

#### **升级收益递减**
- **前期收益高**: 4级→7级提升明显
- **后期收益低**: 8级→10级提升有限
- **最优性价比**: 通常在7-8级达到最佳性价比

## 设计思路分析

### 🎮 游戏平衡性
1. **线性递减**: 保证升级收益的可预测性
2. **最低限制**: 防止时长过短影响游戏节奏
3. **差异化**: 不同器械有不同的时长特性

### 💰 经济平衡
1. **升级成本**: 高等级器械需要更多资源
2. **收益递减**: 后期升级收益降低，平衡投入产出
3. **多样选择**: 不同器械适合不同的游戏策略

## 🔄 时长与Score结合的分段计算

### 📈 效率-Score关系模型

#### **时长效率公式**
```lua
function CalculateEfficiency(equipmentType, equipmentLevel, recipeType)
  local cookingTime = CalculateCookingTime(equipmentType, recipeType, equipmentLevel)
  local outputScore = GetRecipeOutputScore(recipeType)

  -- 每秒Score产出效率
  return outputScore / cookingTime
end
```

#### **分段效率分析**

##### **🥬 低Score物品 (Score < 10)**
- **适用器械**: eq_1 (切菜器械)
- **时长范围**: 5-20秒
- **效率特点**: 快速产出，适合基础材料

| 物品等级 | Score | eq_1时长 | 效率(Score/秒) |
|---------|-------|----------|----------------|
| 1级     | 0.42  | 10秒     | 0.042          |
| 2级     | 0.83  | 20秒     | 0.042          |
| 高级    | 1.67  | 16秒     | 0.104          |

##### **🍖 中Score物品 (Score 10-100)**
- **适用器械**: eq_2 (烧烤), eq_3 (果汁), eq_5 (食物)
- **时长范围**: 32-640秒
- **效率特点**: 中等产出，平衡性好

| 器械类型 | Score范围 | 时长范围 | 平均效率 |
|---------|-----------|----------|----------|
| eq_2    | 26.67     | 256-640秒| 0.042-0.104 |
| eq_3    | 37.65     | 32-80秒  | 0.471-1.176 |
| eq_5    | 44.14     | 52-130秒 | 0.340-0.850 |

##### **🍳 高Score物品 (Score > 100)**
- **适用器械**: eq_4 (油炸), eq_6 (鸡尾酒)
- **时长范围**: 28-1280秒
- **效率特点**: 高价值产出，时间投入大

| 器械类型 | Score范围 | 时长范围 | 效率分析 |
|---------|-----------|----------|----------|
| eq_4    | 142.22-568.89 | 288-1280秒 | 0.444-0.494 |
| eq_6    | 71.11-142.22  | 28-80秒    | 1.778-2.540 |

### 🎯 分段计算策略

#### **基于Score的时长预测模型**
```lua
function PredictCookingTime(targetScore, equipmentType, equipmentLevel)
  local scoreRanges = {
    {min = 0, max = 10, baseTime = 10, timeMultiplier = 1.0},
    {min = 10, max = 100, baseTime = 60, timeMultiplier = 1.5},
    {min = 100, max = 1000, baseTime = 300, timeMultiplier = 2.0}
  }

  for _, range in ipairs(scoreRanges) do
    if targetScore >= range.min and targetScore < range.max then
      local scoreRatio = targetScore / range.min
      local baseTime = range.baseTime * range.timeMultiplier
      local levelReduction = GetLevelReduction(equipmentType, equipmentLevel)

      return math.max(baseTime * scoreRatio - levelReduction, GetMinTime(equipmentType))
    end
  end
end
```

#### **效率优化建议**

##### **🚀 高效率组合 (Score/秒 > 1.0)**
1. **eq_1 + 高级物品**: 切菜器械处理高级蔬菜
2. **eq_3 + 中级物品**: 果汁器械制作中级饮品
3. **eq_6 + 高级物品**: 鸡尾酒器械制作高级饮品

##### **⚖️ 平衡型组合 (Score/秒 0.1-1.0)**
1. **eq_2 + 中级物品**: 烧烤器械处理肉类
2. **eq_4 + 高级物品**: 油炸器械制作复杂菜品
3. **eq_5 + 各级物品**: 通用食物器械

##### **🐌 低效率组合 (Score/秒 < 0.1)**
- 避免使用低级器械处理高Score物品
- 避免使用高时长器械处理低Score物品

### 📊 动态时长调整算法

#### **基于Score的动态时长**
```lua
function DynamicCookingTime(baseTime, itemScore, equipmentLevel, difficultyFactor)
  -- Score影响因子 (Score越高，时长增加)
  local scoreFactor = 1 + (itemScore / 100) * 0.5

  -- 等级减少因子 (等级越高，时长减少)
  local levelFactor = 1 - (equipmentLevel - 4) * 0.1

  -- 难度调整因子
  local adjustedTime = baseTime * scoreFactor * levelFactor * difficultyFactor

  return math.max(adjustedTime, baseTime * 0.3)  -- 最低不少于30%原时长
end
```

#### **分段时长计算示例**
```lua
-- 示例：eq_2烧烤器械，7级，制作Score=26.67的物品
local baseTime = 640  -- 4级基础时长
local itemScore = 26.67
local equipmentLevel = 7
local difficultyFactor = 1.0

-- 计算过程：
-- 1. Score因子: 1 + (26.67/100) * 0.5 = 1.133
-- 2. 等级因子: 1 - (7-4) * 0.1 = 0.7
-- 3. 最终时长: 640 * 1.133 * 0.7 * 1.0 = 507秒

-- 对比原始线性公式: 640 - (7-4) * 64 = 448秒
-- 分段计算更考虑了Score的影响
```

### 🎮 游戏平衡性分析

#### **分段计算的优势**
1. **Score敏感性**: 高Score物品需要更多时间，符合游戏逻辑
2. **等级价值**: 升级器械的收益更加明显
3. **策略深度**: 玩家需要考虑Score-时长的权衡

#### **实际应用建议**
1. **早期游戏**: 专注低Score物品，快速积累
2. **中期游戏**: 平衡Score和时长，优化效率
3. **后期游戏**: 追求高Score物品，接受长时长

这个分段计算模型通过结合Score系统，实现了更加精细和平衡的时长控制，既保持了游戏的挑战性，又提供了丰富的策略选择空间。

## 📋 棋盘提示系统延迟时间配置

### ⏱️ 提示延迟时间规律

#### **基础延迟间隔**
```lua
function BaseInteractiveBoardView:_GetPromptInterval()
  local interval = GM.LevelModel:GetCurrentLevel() <= 4 and 1 or 3
  return interval
end
```

**延迟时间规则**:
- **新手期 (≤4级)**: 1秒间隔，快速提示
- **熟练期 (>4级)**: 3秒间隔，减少干扰

#### **自动运行模式延迟**
```lua
function BaseSceneBoardView:_GetPromptInterval()
  if IsAutoRun() then
    return GM.TestAutoRunModel.interval  -- 测试模式可调节间隔
  else
    return BaseInteractiveBoardView._GetPromptInterval(self)
  end
end
```

### 🎯 完整提示规则列表

#### **BoardPromptConfig.lua 完整配置**

##### **🏆 高优先级提示 (Priority 1-6)**

| 提示类型 | 优先级 | 关闭等级 | 功能描述 |
|---------|--------|----------|----------|
| **finishOrder** | 1 | 5级 | 完成订单提示 |
| **finishTask** | 2 | 5级 | 完成任务提示 |
| **testStartCook** | 3 | 0级 | 测试-开始烹饪 |
| **testTakeOutDish** | 4 | 0级 | 测试-取出菜品 |
| **testTakeOutMaterial** | 5 | 0级 | 测试-取出材料 |
| **lastOrder** | 6 | 5级 | 最后订单提示 |

##### **⚖️ 中优先级提示 (Priority 7-11)**

| 提示类型 | 优先级 | 关闭等级 | 功能描述 |
|---------|--------|----------|----------|
| **mergeItems** | 7 | 无限制 | 合成物品提示 |
| **tapCacheItems** | 8 | 5级 | 点击缓存提示 |
| **testOpenChest** | 9 | 0级 | 测试-开启宝箱 |
| **testRetrieveItem** | 10 | 0级 | 测试-回收物品 |
| **tapSpreadItem** | 11 | 5级 | 点击生成器提示 |
| **testSpreadItem** | 11 | 0级 | 测试-生成器操作 |

##### **🔧 低优先级提示 (Priority 12-14)**

| 提示类型 | 优先级 | 关闭等级 | 功能描述 |
|---------|--------|----------|----------|
| **testRecoverSpread** | 12 | 0级 | 测试-恢复生成器 |
| **testCollectAll** | 13 | 0级 | 测试-收集全部 |
| **testStoreItem** | 14 | 0级 | 测试-存储物品 |

### 🎮 提示触发机制详解

#### **延迟触发流程**
```lua
function BaseInteractiveBoardView:_StartBoardPrompt()
  self:_CancelBoardPrompt()
  local interval = self:_GetPromptInterval()  -- 获取延迟间隔
  Scheduler.Schedule(self.m_boardPromptScheduler, self, 0, 1, interval)
end
```

#### **提示显示条件检查**
```lua
function BaseInteractiveBoardView:_CanStartPrompt()
  if GM.UIManager:IsEventLockUntilNextPopup() or
     GM.TutorialModel:HasAnyStrongTutorialOngoing() or
     self.m_bDisableBoardPrompt then
    return false
  end
  -- 检查物品是否在飞行或拖拽状态
  for itemModel, _ in pairs(self.m_model:GetAllBoardItems()) do
    itemView = self:GetItemView(itemModel)
    if itemView == nil or itemView:IsFlying() or itemView:IsDragging() then
      return false
    end
  end
  return true
end
```

### 📊 提示类型分类分析

#### **🎯 核心游戏提示 (常驻)**
- **finishOrder**: 最高优先级，引导完成订单
- **finishTask**: 次高优先级，引导完成任务
- **mergeItems**: 无等级限制，核心合成引导
- **lastOrder**: 特殊订单提示，不受全局开关影响

#### **🎓 新手引导提示 (5级后关闭)**
- **tapCacheItems**: 引导使用缓存系统
- **tapSpreadItem**: 引导使用生成器系统
- **finishOrder/finishTask**: 基础操作引导

#### **🔧 测试调试提示 (开发用)**
- **test系列**: 所有CloseLevel=0的提示
- **用途**: 开发测试和调试功能
- **特点**: 不在正式游戏中显示

### ⚙️ 提示系统配置参数

#### **延迟时间配置**
```lua
-- 基础延迟配置
local PromptDelayConfig = {
  newPlayerInterval = 1,    -- 新手延迟间隔(秒)
  normalInterval = 3,       -- 正常延迟间隔(秒)
  levelThreshold = 4,       -- 等级阈值
  autoRunInterval = "dynamic"  -- 自动运行间隔(动态)
}
```

#### **显示控制配置**
```lua
-- 显示控制参数
local PromptDisplayConfig = {
  globalSwitch = "PlayerPrefs.OpenHint",  -- 全局开关
  levelBasedClose = true,                 -- 基于等级关闭
  tutorialOverride = true,                -- 教程覆盖
  animationBlock = true,                  -- 动画阻塞
  windowBlock = true                      -- 窗口阻塞
}
```

### 🎨 视觉效果延迟配置

#### **动画延迟时间**
```lua
-- UI动画延迟配置 (来自EnergyBoostWindow)
local arrDelayTime = {
  0,      -- 第1个元素立即显示
  0.3,    -- 第2个元素延迟0.3秒
  0.6,    -- 第3个元素延迟0.6秒
  0.2,    -- 第4个元素延迟0.2秒
  0.4     -- 第5个元素延迟0.4秒
}
```

#### **提示动画序列**
```lua
-- 提示显示动画 (来自Prompt.lua)
local seq = DOTween.Sequence()
seq:Append(self.m_normalText:DOFade(1, 0.2))        -- 淡入0.2秒
seq:Join(self.m_canvasGroup:DOFade(1, 0.2))         -- 同时淡入
seq:Join(self.transform:DOAnchorPosY(y + 75, 0.3))  -- 上移动画0.3秒
seq:Join(self.transform:DOScaleY(1.2, 0.3))         -- 缩放动画0.3秒
seq:Append(self.transform:DOAnchorPosY(y + 30, 0.1)) -- 回弹0.1秒
seq:Join(self.transform:DOScaleY(1, 0.1))           -- 恢复缩放
seq:Append(self.transform:DOAnchorPosY(y + 45, 0.1)) -- 最终位置
seq:AppendInterval(stayDuration or 1)               -- 停留时间
```

### 📈 提示系统性能优化

#### **延迟优化策略**
1. **等级分层**: 新手密集提示，老手稀疏提示
2. **状态检查**: 避免在不合适时机显示提示
3. **动画排队**: 防止提示动画冲突
4. **内存管理**: 及时清理提示对象

#### **用户体验优化**
1. **渐进式引导**: 从高频到低频的提示策略
2. **智能过滤**: 根据游戏状态智能选择提示
3. **视觉层次**: 不同优先级的视觉差异化
4. **可控性**: 用户可以关闭提示系统

这个提示系统通过精心设计的延迟时间和优先级规则，实现了既不干扰高级玩家，又能有效引导新手的平衡效果。
