OrderGroupWindow = setmetatable({canClickWindowMask = true}, BaseWindow)
OrderGroupWindow.__index = OrderGroupWindow
local arrBubbleWidth = {
  278,
  355,
  442,
  355
}
local arrBubbleHeight = {
  180,
  180,
  180,
  320,
  456
}
local arrRewardPos = {
  [1] = {
    Vector.Create(0, 0)
  },
  [2] = {
    Vector.Create(-70, 0),
    Vector.Create(70, 0)
  },
  [3] = {
    Vector.Create(-140, 0),
    Vector.Create(0, 0),
    Vector.Create(140, 0)
  },
  [4] = {
    Vector.Create(-70, 70),
    Vector.Create(70, 70),
    Vector.Create(-70, -70),
    Vector.Create(70, -70)
  },
  [5] = {
    Vector.Create(-140, 70),
    Vector.Create(0, 70),
    Vector.Create(140, 70),
    Vector.Create(-70, -70),
    Vector.Create(70, -70)
  },
  [6] = {
    Vector.Create(-140, 70),
    V<PERSON><PERSON>reate(0, 70),
    Vector.Create(140, 70),
    Vector.Create(-140, -70),
    Vector.Create(0, -70),
    Vector.Create(140, -70)
  },
  [7] = {
    Vector.Create(-140, 140),
    Vector.Create(0, 140),
    Vector.Create(140, 140),
    Vector.Create(-140, 0),
    Vector.Create(0, 0),
    Vector.Create(140, 0),
    Vector.Create(0, -140)
  }
}

function OrderGroupWindow:Init()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local rewards = orderModel:GetCurGroupReward()
  rewards = OrderGroupFinishWindow.TryAddProducerInventoryReward(rewards)
  local rewardCount = #rewards
  if rewardCount <= 4 then
    UIUtil.SetSizeDelta(self.m_bubbleTrans, arrBubbleWidth[rewardCount], arrBubbleHeight[rewardCount])
  elseif rewardCount == 7 then
    UIUtil.SetSizeDelta(self.m_bubbleTrans, arrBubbleWidth[3], arrBubbleHeight[5])
  end
  self.m_rewardContent:Init(rewards)
  for i = 1, rewardCount do
    local arrPos = arrRewardPos[rewardCount]
    local pos = arrPos and arrPos[i] or arrRewardPos[1][1]
    local rewardItem = self.m_rewardContent:GetRewardItem(i)
    rewardItem.transform:SetLocalPosXY(pos.x, pos.y)
  end
  GM.ItemDataModel:SetRewardsLocked(rewards)
  local finishedCount, totalCount = orderModel:GetGroupProgress()
  self.m_progressText.text = finishedCount .. "/" .. totalCount
  self.m_slider.value = finishedCount / totalCount
  if self.m_rainbowSpine then
    self.m_rainbowSpine:Init()
    self.m_rainbowSpine:PlayAnimation("appear", function()
      self.m_rainbowSpine:PlayAnimation("idle", nil, true)
    end, false, true)
  end
end

function OrderGroupWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  if self.m_rainbowSpine then
    self.m_rainbowSpine:ThrowCallback()
    self.m_rainbowSpine:PlayAnimation("disappear", nil, false, true)
  end
end
