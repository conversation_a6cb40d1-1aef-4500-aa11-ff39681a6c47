import re
import os
import traceback
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from pathlib import Path
import time
import json

def write_to_excel(items, output_file):
    if not items:
        print("没有找到任何物品，不创建Excel文件")
        return

    print(f"准备写入Excel文件: {output_file}")
    try:
        wb = Workbook()
        ws = wb.active
        
        # 扩展表头以包含更多属性
        headers = [
            'Type', 'Category', 'SkipPrice_gem', 'Reward', 'Score', 
            'BookOrder', 'BookSubOrder', 'BookReward', 'InRandom',
            'SellPrice', 'SellCurrency', 'Materials', 'Instrument'
        ]
        
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 写入数据
        items.sort(key=lambda x: x['Type'])
        for row, item in enumerate(items, 2):
            for col, header in enumerate(headers, 1):
                if header in item:
                    # 对于复杂数据类型，将其转换为字符串形式
                    if isinstance(item[header], (list, dict)):
                        ws.cell(row=row, column=col, value=json.dumps(item[header], ensure_ascii=False))
                    else:
                        ws.cell(row=row, column=col, value=item[header])
        
        # 调整列宽以适应更多内容
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 25
            
        wb.save(output_file)
        print(f"Excel文件已保存到: {output_file}")
        
    except Exception as e:
        print(f"写入Excel文件时出错: {str(e)}")
        print(traceback.format_exc())

def extract_section_items(section_text, item_pattern):
    """
    通用函数，用于从一个复杂的嵌套部分提取项目
    section_text: 包含项目的文本块
    item_pattern: 匹配单个项目的正则表达式模式
    """
    items = []
    if not section_text or not section_text.strip():
        return items
    
    # 处理可能的多行、多项结构
    # 首先尝试匹配单个项目的正则表达式
    for match in re.finditer(item_pattern, section_text, re.DOTALL):
        items.append(match.groupdict())
    
    # 如果没有找到任何匹配项，尝试查找通用结构
    if not items:
        # 查找任何 {key = value} 类型的模式
        for match in re.finditer(r'{([^{}]+)}', section_text):
            item_text = match.group(1)
            item = {}
            for key_value in re.finditer(r'(\w+)\s*=\s*(?:"([^"]+)"|(\d+))', item_text):
                key = key_value.group(1)
                value = key_value.group(2) if key_value.group(2) else int(key_value.group(3))
                item[key] = value
            if item:
                items.append(item)
    
    return items

def extract_ds_items_from_lua(file_path):
    """使用完全不同的方法提取ds物品信息"""
    print(f"\n开始处理文件: {file_path}")
    if not os.path.exists(file_path):
        print(f"错误：文件不存在: {file_path}")
        return []
    
    items = []
    try:
        file_size = os.path.getsize(file_path)
        print(f"文件大小: {file_size/1024/1024:.2f} MB")
        
        start_time = time.time()
        
        # 读取并预处理文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 首先找到所有的ds物品类型
        print("查找所有ds物品类型...")
        ds_types = re.findall(r'Type\s*=\s*"(ds_[^"]+)"', content)
        print(f"找到 {len(ds_types)} 个ds类型的物品名称")
        
        if not ds_types:
            print("未找到任何ds类型物品，尝试使用其他编码...")
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
                ds_types = re.findall(r'Type\s*=\s*"(ds_[^"]+)"', content)
                print(f"使用GBK编码找到 {len(ds_types)} 个ds类型的物品名称")
            except Exception as e:
                print(f"尝试GBK编码失败: {str(e)}")
                return []
        
        # 如果还是没找到，返回空列表
        if not ds_types:
            print("使用多种编码尝试后仍未找到任何ds类型物品")
            return []
        
        # 分割文件内容为行
        lines = content.split('\n')
        line_count = len(lines)
        print(f"文件共有 {line_count} 行")
        
        # 处理每一个ds物品
        for ds_type in ds_types:
            try:
                # 查找物品定义的起始行
                start_line = -1
                for i, line in enumerate(lines):
                    if f'Type = "{ds_type}"' in line or f'Type ="{ds_type}"' in line:
                        # 找到前面的大括号
                        j = i
                        while j >= 0:
                            if '{' in lines[j] and not lines[j].strip().startswith('--'):
                                start_line = j
                                break
                            j -= 1
                        break
                
                if start_line == -1:
                    print(f"无法找到物品 {ds_type} 的起始行")
                    continue
                
                # 查找物品定义的结束行 - 改进的方法处理嵌套的大括号
                end_line = start_line
                open_brackets = 0
                in_item_def = False
                
                for i in range(start_line, min(line_count, start_line + 200)):  # 限制查找范围
                    line = lines[i]
                    
                    # 计算这一行的大括号
                    for char in line:
                        if char == '{':
                            if not in_item_def:
                                in_item_def = True
                            open_brackets += 1
                        elif char == '}':
                            open_brackets -= 1
                            if open_brackets == 0 and in_item_def:
                                end_line = i
                                break
                    
                    if open_brackets == 0 and in_item_def and i > start_line:
                        break
                
                # 提取物品块内容
                item_block = '\n'.join(lines[start_line:end_line+1])
                print(f"成功提取物品 {ds_type} 的定义块，长度: {len(item_block)} 字符")
                
                # 解析物品信息
                item = {'Type': ds_type}
                
                # 提取Category数组
                category_match = re.search(r'Category\s*=\s*{([^}]*)}', item_block)
                if category_match:
                    category_str = category_match.group(1).strip()
                    if category_str:
                        categories = []
                        for cat in category_str.split(','):
                            cat = cat.strip()
                            if cat and cat.isdigit():
                                categories.append(int(cat))
                        item['Category'] = categories
                
                # 提取数值型属性
                for attr in ['SkipPrice_gem', 'Reward', 'Score', 'BookOrder', 'BookSubOrder', 'InRandom', 'SellPrice']:
                    attr_match = re.search(rf'{attr}\s*=\s*(\d+(?:\.\d+)?)', item_block)
                    if attr_match:
                        value = attr_match.group(1)
                        # 根据值的类型转换
                        if '.' in value:
                            item[attr] = float(value)
                        else:
                            item[attr] = int(value)
                
                # 提取BookReward - 改进的方法
                book_reward_match = re.search(r'BookReward\s*=\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}', item_block, re.DOTALL)
                if book_reward_match:
                    book_reward_text = book_reward_match.group(1)
                    book_rewards = []
                    
                    # 查找每个Currency项
                    for currency_item in re.finditer(r'{([^{}]*)}', book_reward_text):
                        currency_text = currency_item.group(1)
                        currency_dict = {}
                        
                        # 提取Currency和Amount
                        currency_name_match = re.search(r'Currency\s*=\s*"([^"]+)"', currency_text)
                        amount_match = re.search(r'Amount\s*=\s*(\d+)', currency_text)
                        
                        if currency_name_match and amount_match:
                            currency_dict["Currency"] = currency_name_match.group(1)
                            currency_dict["Amount"] = int(amount_match.group(1))
                            book_rewards.append(currency_dict)
                    
                    if book_rewards:
                        item['BookReward'] = book_rewards
                
                # 提取SellCurrency
                sell_currency_match = re.search(r'SellCurrency\s*=\s*{([^{}]*)}', item_block)
                if sell_currency_match:
                    sell_currency_text = sell_currency_match.group(1)
                    currency_match = re.search(r'Currency\s*=\s*"([^"]+)"', sell_currency_text)
                    amount_match = re.search(r'Amount\s*=\s*(\d+)', sell_currency_text)
                    
                    if currency_match and amount_match:
                        item['SellCurrency'] = {
                            'Currency': currency_match.group(1),
                            'Amount': int(amount_match.group(1))
                        }
                
                # 提取Materials - 改进的方法
                materials_match = re.search(r'Materials\s*=\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}', item_block, re.DOTALL)
                if materials_match:
                    materials_text = materials_match.group(1)
                    materials = []
                    
                    # 查找每个Material项
                    for material_item in re.finditer(r'{([^{}]*)}', materials_text):
                        material_text = material_item.group(1)
                        material_dict = {}
                        
                        # 提取Material和Amount
                        material_name_match = re.search(r'Material\s*=\s*"([^"]+)"', material_text)
                        amount_match = re.search(r'Amount\s*=\s*(\d+)', material_text)
                        
                        if material_name_match and amount_match:
                            material_dict["Material"] = material_name_match.group(1)
                            material_dict["Amount"] = int(amount_match.group(1))
                            materials.append(material_dict)
                    
                    if materials:
                        item['Materials'] = materials
                
                # 提取Instrument - 改进的方法
                instrument_match = re.search(r'Instrument\s*=\s*{([^{}]*(?:{[^{}]*}[^{}]*)*)}', item_block, re.DOTALL)
                if instrument_match:
                    instrument_text = instrument_match.group(1)
                    instruments = []
                    
                    # 查找每个Instrument项
                    for instrument_item in re.finditer(r'{([^{}]*)}', instrument_text):
                        instrument_text = instrument_item.group(1)
                        instrument_dict = {}
                        
                        # 提取Instru和Dur
                        instru_match = re.search(r'Instru\s*=\s*"([^"]+)"', instrument_text)
                        dur_match = re.search(r'Dur\s*=\s*(\d+)', instrument_text)
                        
                        if instru_match and dur_match:
                            instrument_dict["Instrument"] = instru_match.group(1)
                            instrument_dict["Duration"] = int(dur_match.group(1))
                            instruments.append(instrument_dict)
                    
                    if instruments:
                        item['Instrument'] = instruments
                
                # 添加到结果列表
                items.append(item)
                if len(items) % 10 == 0:
                    print(f"已成功解析 {len(items)}/{len(ds_types)} 个物品...")
            
            except Exception as e:
                print(f"处理物品 {ds_type} 时出错: {str(e)}")
                print(traceback.format_exc())
                continue
        
        end_time = time.time()
        print(f"文件处理完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"成功提取 {len(items)}/{len(ds_types)} 个有效ds物品")
        
    except Exception as e:
        print(f"处理文件时发生错误:")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print("详细错误信息:")
        print(traceback.format_exc())
    
    return items

# 设置基础路径，可根据实际工作空间调整
base_path = 'd:/MCWork/GHabout/FMC/fmc_lua_1.18.5'  # 请根据实际路径调整

# 配置文件列表
config_file_names = [
    'ItemModelConfig.lua',
]

# 使用确切的路径格式
config_path = os.path.join(base_path, 'fmc_lua', 'Data', 'Config')
print(f"使用配置路径: {config_path}")

# 路径验证
if not os.path.exists(config_path):
    print(f"警告：配置目录不存在: {config_path}")
    print("请检查base_path设置是否正确")

# 处理所有配置文件
config_files = [os.path.join(config_path, file_name) for file_name in config_file_names]

print("开始处理配置文件...")
desktop = os.path.join(os.path.expanduser("~"), "Desktop")

for file_path in config_files:
    print(f"\n{'='*50}")
    print(f"准备处理文件: {file_path}")
    if os.path.exists(file_path):
        # 使用新的提取方法
        items = extract_ds_items_from_lua(file_path)
        if items:
            # 使用文件名作为Excel文件名
            file_name = Path(file_path).stem + '_ds_items.xlsx'
            output_file = os.path.join(desktop, file_name)
            write_to_excel(items, output_file)
            print(f"文件 {file_path} 处理完成，找到 {len(items)} 个物品")
    else:
        print(f"跳过不存在的文件: {file_path}")
    print(f"{'='*50}\n")

print("\n所有文件处理完成！") 