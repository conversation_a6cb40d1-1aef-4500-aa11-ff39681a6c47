import json
import random
import math
import os
from collections import defaultdict
from typing import List, Dict, Tuple, Optional, Any
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QListWidget, QLabel, QLineEdit, QComboBox, QSpinBox, 
    QDoubleSpinBox, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QSplitter, QGroupBox, QCheckBox
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class Generator:
    def __init__(self, id: int, level: int, capacity: int, production: Dict[str, float]):
        self.id = id
        self.level = level
        self.capacity = capacity
        self.production = production
    
    def __repr__(self):
        return f"Generator(Lv{self.level}, Cap:{self.capacity}, Prod:{self.production})"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，用于JSON序列化"""
        return {
            "id": self.id,
            "level": self.level,
            "capacity": self.capacity,
            "production": self.production
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Generator':
        """从字典创建对象"""
        return cls(
            id=data["id"],
            level=data["level"],
            capacity=data["capacity"],
            production=data["production"]
        )

class Instrument:
    def __init__(self, id: int, level: int, processing_time: float):
        self.id = id
        self.level = level
        self.processing_time = processing_time
    
    def __repr__(self):
        return f"Instrument(Lv{self.level}, Time:{self.processing_time})"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "level": self.level,
            "processing_time": self.processing_time
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Instrument':
        return cls(
            id=data["id"],
            level=data["level"],
            processing_time=data["processing_time"]
        )

class Dish:
    def __init__(self, id: int, name: str, virtual_level: float, min_wave_interval: int, 
                 base_series: Dict[str, float], extra_items: Dict[str, float], extra_energy: float):
        self.id = id
        self.name = name
        self.virtual_level = virtual_level
        self.min_wave_interval = min_wave_interval
        self.base_series = base_series
        self.extra_items = extra_items
        self.extra_energy = extra_energy
    
    def __repr__(self):
        return f"Dish({self.name}, Lv:{self.virtual_level})"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "virtual_level": self.virtual_level,
            "min_wave_interval": self.min_wave_interval,
            "base_series": self.base_series,
            "extra_items": self.extra_items,
            "extra_energy": self.extra_energy
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Dish':
        return cls(
            id=data["id"],
            name=data["name"],
            virtual_level=data["virtual_level"],
            min_wave_interval=data["min_wave_interval"],
            base_series=data["base_series"],
            extra_items=data["extra_items"],
            extra_energy=data["extra_energy"]
        )

class Order:
    def __init__(self, order_id: int, expected_energy: float):
        self.order_id = order_id
        self.expected_energy = expected_energy
        self.dishes = []
        self.actual_energy = 0.0
    
    def add_dish(self, dish: Dish):
        self.dishes.append(dish)
    
    def __repr__(self):
        return f"Order{self.order_id} (Exp:{self.expected_energy}, Dishes:{len(self.dishes)})"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "order_id": self.order_id,
            "expected_energy": self.expected_energy,
            "dishes": [dish.to_dict() for dish in self.dishes],
            "actual_energy": self.actual_energy
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Order':
        order = cls(
            order_id=data["order_id"],
            expected_energy=data["expected_energy"]
        )
        order.dishes = [Dish.from_dict(d) for d in data["dishes"]]
        order.actual_energy = data["actual_energy"]
        return order

class WaveConfig:
    def __init__(self):
        self.feature_dishes = []
        self.energy_range = (0, 0)
        self.player_generators = {}
        self.generator_round_ranges = {}
        self.player_instruments = {}
        self.energy_weights = []
        self.bet_mode = 1
        self.efficiency_mode = "average"
        self.prev_wave_info = None
        self.instrument_params = {}
        self.remaining_items_range = {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "feature_dishes": self.feature_dishes,
            "energy_range": self.energy_range,
            "player_generators": self.player_generators,
            "generator_round_ranges": self.generator_round_ranges,
            "player_instruments": self.player_instruments,
            "energy_weights": self.energy_weights,
            "bet_mode": self.bet_mode,
            "efficiency_mode": self.efficiency_mode,
            "prev_wave_info": self.prev_wave_info,
            "instrument_params": self.instrument_params,
            "remaining_items_range": self.remaining_items_range
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WaveConfig':
        config = cls()
        config.feature_dishes = data["feature_dishes"]
        config.energy_range = tuple(data["energy_range"])
        config.player_generators = data["player_generators"]
        config.generator_round_ranges = data["generator_round_ranges"]
        config.player_instruments = data["player_instruments"]
        config.energy_weights = data["energy_weights"]
        config.bet_mode = data["bet_mode"]
        config.efficiency_mode = data["efficiency_mode"]
        config.prev_wave_info = data["prev_wave_info"]
        config.instrument_params = data["instrument_params"]
        config.remaining_items_range = data["remaining_items_range"]
        return config

class OrderEditor:
    def __init__(self):
        self.generators = {}
        self.instruments = {}
        self.dishes = {}
        self.synthesis_tree = {}
        self.cooking_recipes = {}
        self.current_config = WaveConfig()
        self.generated_solutions = []
    
    def load_base_data(self, data_path: str):
        """从JSON文件加载基础数据"""
        try:
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载生成器
            self.generators = {}
            for gen_data in data.get("generators", []):
                gen = Generator.from_dict(gen_data)
                self.generators[gen.id] = gen
            
            # 加载器械
            self.instruments = {}
            for inst_data in data.get("instruments", []):
                inst = Instrument.from_dict(inst_data)
                self.instruments[inst.id] = inst
            
            # 加载菜品
            self.dishes = {}
            for dish_data in data.get("dishes", []):
                dish = Dish.from_dict(dish_data)
                self.dishes[dish.id] = dish
            
            print(f"成功加载基础数据: {len(self.generators)}个生成器, {len(self.instruments)}个器械, {len(self.dishes)}个菜品")
            return True
        except Exception as e:
            print(f"加载基础数据失败: {str(e)}")
            return False
    
    def save_base_data(self, data_path: str):
        """保存基础数据到JSON文件"""
        try:
            data = {
                "generators": [gen.to_dict() for gen in self.generators.values()],
                "instruments": [inst.to_dict() for inst in self.instruments.values()],
                "dishes": [dish.to_dict() for dish in self.dishes.values()]
            }
            
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"基础数据已保存到: {data_path}")
            return True
        except Exception as e:
            print(f"保存基础数据失败: {str(e)}")
            return False
    
    def save_solutions(self, file_path: str):
        """保存生成的解决方案到JSON文件"""
        try:
            solutions_data = []
            for solution in self.generated_solutions:
                solution_data = {
                    "orders": [order.to_dict() for order in solution],
                    "total_energy": sum(order.actual_energy for order in solution),
                    "generator_rounds": {},  # 实际项目中需要计算
                    "remaining_items": {}    # 实际项目中需要计算
                }
                solutions_data.append(solution_data)
            
            data = {
                "config": self.current_config.to_dict(),
                "solutions": solutions_data
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"解决方案已保存到: {file_path}")
            return True
        except Exception as e:
            print(f"保存解决方案失败: {str(e)}")
            return False
    
    def load_solutions(self, file_path: str):
        """从JSON文件加载解决方案"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载配置
            self.current_config = WaveConfig.from_dict(data["config"])
            
            # 加载解决方案
            self.generated_solutions = []
            for solution_data in data["solutions"]:
                solution = [Order.from_dict(order_data) for order_data in solution_data["orders"]]
                self.generated_solutions.append(solution)
            
            print(f"成功加载{len(self.generated_solutions)}个解决方案")
            return True
        except Exception as e:
            print(f"加载解决方案失败: {str(e)}")
            return False
    
    # 其余核心逻辑方法保持不变（为简洁省略）
    # ... [之前实现的核心逻辑方法] ...


class OrderEditorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.editor = OrderEditor()
        self.current_solution_index = -1
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('订单编辑器 v1.0')
        self.setGeometry(100, 100, 1200, 800)
        
        # 主布局
        main_widget = QWidget()
        main_layout = QHBoxLayout()
        main_widget.setLayout(main_layout)
        self.setCentralWidget(main_widget)
        
        # 左侧面板（配置）
        left_panel = QTabWidget()
        main_layout.addWidget(left_panel, 1)
        
        # 基础配置标签页
        config_tab = QWidget()
        config_layout = QVBoxLayout()
        config_tab.setLayout(config_layout)
        left_panel.addTab(config_tab, "基础配置")
        
        # 数据加载部分
        data_group = QGroupBox("数据管理")
        data_layout = QHBoxLayout()
        data_group.setLayout(data_layout)
        
        self.btn_load_data = QPushButton("加载基础数据")
        self.btn_load_data.clicked.connect(self.load_base_data)
        data_layout.addWidget(self.btn_load_data)
        
        self.btn_save_data = QPushButton("保存基础数据")
        self.btn_save_data.clicked.connect(self.save_base_data)
        data_layout.addWidget(self.btn_save_data)
        
        config_layout.addWidget(data_group)
        
        # 波次配置部分
        wave_group = QGroupBox("波次配置")
        wave_layout = QVBoxLayout()
        wave_group.setLayout(wave_layout)
        
        # 特色菜品选择
        dish_layout = QHBoxLayout()
        dish_layout.addWidget(QLabel("特色菜品:"))
        self.dish_list = QListWidget()
        self.dish_list.setSelectionMode(QListWidget.MultiSelection)
        dish_layout.addWidget(self.dish_list, 1)
        wave_layout.addLayout(dish_layout)
        
        # 体力配置
        energy_layout = QHBoxLayout()
        energy_layout.addWidget(QLabel("体力消耗范围:"))
        self.energy_min = QDoubleSpinBox()
        self.energy_min.setRange(0, 1000)
        self.energy_min.setValue(100)
        energy_layout.addWidget(self.energy_min)
        
        energy_layout.addWidget(QLabel("到"))
        self.energy_max = QDoubleSpinBox()
        self.energy_max.setRange(0, 1000)
        self.energy_max.setValue(150)
        energy_layout.addWidget(self.energy_max)
        wave_layout.addLayout(energy_layout)
        
        # Bet模式
        bet_layout = QHBoxLayout()
        bet_layout.addWidget(QLabel("体力模式:"))
        self.bet_combo = QComboBox()
        self.bet_combo.addItems(["2bet", "4bet", "8bet"])
        bet_layout.addWidget(self.bet_combo)
        wave_layout.addLayout(bet_layout)
        
        # 效率计算方式
        eff_layout = QHBoxLayout()
        eff_layout.addWidget(QLabel("效率计算:"))
        self.eff_combo = QComboBox()
        self.eff_combo.addItems(["平均效率", "动态效率", "Top3平均效率"])
        eff_layout.addWidget(self.eff_combo)
        wave_layout.addLayout(eff_layout)
        
        config_layout.addWidget(wave_group)
        
        # 解决方案管理
        sol_group = QGroupBox("解决方案")
        sol_layout = QVBoxLayout()
        sol_group.setLayout(sol_layout)
        
        self.btn_generate = QPushButton("生成解决方案 (20套)")
        self.btn_generate.clicked.connect(self.generate_solutions)
        sol_layout.addWidget(self.btn_generate)
        
        self.btn_save_sol = QPushButton("保存当前解决方案")
        self.btn_save_sol.clicked.connect(self.save_solutions)
        sol_layout.addWidget(self.btn_save_sol)
        
        self.btn_load_sol = QPushButton("加载解决方案")
        self.btn_load_sol.clicked.connect(self.load_solutions)
        sol_layout.addWidget(self.btn_load_sol)
        
        config_layout.addWidget(sol_group)
        
        # 右侧面板（结果展示）
        right_panel = QTabWidget()
        main_layout.addWidget(right_panel, 2)
        
        # 解决方案标签页
        solution_tab = QWidget()
        solution_layout = QVBoxLayout()
        solution_tab.setLayout(solution_layout)
        right_panel.addTab(solution_tab, "解决方案")
        
        # 解决方案选择
        sol_select_layout = QHBoxLayout()
        sol_select_layout.addWidget(QLabel("选择方案:"))
        self.solution_combo = QComboBox()
        self.solution_combo.currentIndexChanged.connect(self.show_solution)
        sol_select_layout.addWidget(self.solution_combo)
        
        self.btn_prev = QPushButton("上一个")
        self.btn_prev.clicked.connect(self.show_prev_solution)
        sol_select_layout.addWidget(self.btn_prev)
        
        self.btn_next = QPushButton("下一个")
        self.btn_next.clicked.connect(self.show_next_solution)
        sol_select_layout.addWidget(self.btn_next)
        solution_layout.addLayout(sol_select_layout)
        
        # 订单表格
        self.order_table = QTableWidget()
        self.order_table.setColumnCount(4)
        self.order_table.setHorizontalHeaderLabels(["订单ID", "期望体力", "实际体力", "菜品组成"])
        self.order_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        solution_layout.addWidget(self.order_table)
        
        # 指标展示
        metrics_group = QGroupBox("方案指标")
        metrics_layout = QVBoxLayout()
        metrics_group.setLayout(metrics_layout)
        
        self.metrics_table = QTableWidget()
        self.metrics_table.setColumnCount(2)
        self.metrics_table.setHorizontalHeaderLabels(["指标", "值"])
        self.metrics_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.metrics_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.metrics_table.setRowCount(5)
        
        metrics = [
            ("总体力消耗", "0.0"),
            ("生成器轮数", "0.0"),
            ("器械使用率", "0%"),
            ("剩余物品", "0"),
            ("最大棋盘占用", "0")
        ]
        
        for i, (name, value) in enumerate(metrics):
            self.metrics_table.setItem(i, 0, QTableWidgetItem(name))
            self.metrics_table.setItem(i, 1, QTableWidgetItem(value))
        
        metrics_layout.addWidget(self.metrics_table)
        solution_layout.addWidget(metrics_group)
        
        # 菜品详情标签页
        dish_tab = QWidget()
        dish_layout = QVBoxLayout()
        dish_tab.setLayout(dish_layout)
        right_panel.addTab(dish_tab, "菜品详情")
        
        self.dish_table = QTableWidget()
        self.dish_table.setColumnCount(6)
        self.dish_table.setHorizontalHeaderLabels(["ID", "名称", "虚拟等级", "间隔波数", "基础系列", "额外物品"])
        self.dish_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        dish_layout.addWidget(self.dish_table)
    
    def load_sample_data(self):
        """加载示例数据"""
        # 示例生成器
        self.editor.generators = {
            1: Generator(1, 4, 8, {"A": 0.7, "B": 0.15, "C": 0.15}),
            2: Generator(2, 5, 10, {"A": 1.05, "B": 0.15, "C": 0.15}),
            3: Generator(3, 6, 12, {"A": 1.1, "B": 0.15, "C": 0.15}),
            4: Generator(4, 8, 16, {"A": 1.35, "B": 0.15, "C": 0.15}),
        }
        
        # 示例器械
        self.editor.instruments = {
            101: Instrument(101, 1, 5.0),
            102: Instrument(102, 2, 4.0),
            103: Instrument(103, 3, 3.0),
        }
        
        # 示例菜品
        self.editor.dishes = {
            1001: Dish(1001, "特色菜品A", 5.0, 2, {"A": 1.0, "B": 0.5}, {}, 0.0),
            1002: Dish(1002, "特色菜品B", 6.0, 3, {"B": 1.5, "C": 0.5}, {}, 0.0),
            2001: Dish(2001, "普通菜品1", 3.0, 1, {"A": 0.8}, {}, 0.0),
            2002: Dish(2002, "普通菜品2", 2.5, 1, {"B": 1.2}, {}, 0.0),
            2003: Dish(2003, "普通菜品3", 3.5, 2, {"C": 1.0}, {}, 0.0),
            2004: Dish(2004, "普通菜品4", 4.0, 1, {"A": 1.2, "C": 0.3}, {}, 0.0),
        }
        
        # 更新菜品列表
        self.update_dish_list()
    
    def update_dish_list(self):
        """更新菜品列表控件"""
        self.dish_list.clear()
        for dish in self.editor.dishes.values():
            item = f"{dish.id}: {dish.name} (Lv{dish.virtual_level})"
            self.dish_list.addItem(item)
    
    def load_base_data(self):
        """加载基础数据"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载基础数据", "", "JSON Files (*.json)"
        )
        if file_path:
            if self.editor.load_base_data(file_path):
                self.update_dish_list()
                QMessageBox.information(self, "成功", "基础数据加载成功！")
            else:
                QMessageBox.warning(self, "错误", "基础数据加载失败！")
    
    def save_base_data(self):
        """保存基础数据"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存基础数据", "game_data.json", "JSON Files (*.json)"
        )
        if file_path:
            if self.editor.save_base_data(file_path):
                QMessageBox.information(self, "成功", "基础数据保存成功！")
            else:
                QMessageBox.warning(self, "错误", "基础数据保存失败！")
    
    def generate_solutions(self):
        """生成解决方案"""
        # 获取特色菜品选择
        selected_dishes = []
        for item in self.dish_list.selectedItems():
            dish_id = int(item.text().split(":")[0])
            selected_dishes.append(dish_id)
        
        if len(selected_dishes) < 4 or len(selected_dishes) > 7:
            QMessageBox.warning(self, "错误", "请选择4-7个特色菜品！")
            return
        
        # 更新配置
        self.editor.current_config.feature_dishes = selected_dishes
        self.editor.current_config.energy_range = (
            self.energy_min.value(), self.energy_max.value()
        )
        self.editor.current_config.bet_mode = self.bet_combo.currentIndex() + 1
        self.editor.current_config.efficiency_mode = ["average", "dynamic", "top3"][self.eff_combo.currentIndex()]
        
        # 生成解决方案
        self.editor.generate_solutions(20)
        
        # 更新解决方案列表
        self.solution_combo.clear()
        for i in range(len(self.editor.generated_solutions)):
            self.solution_combo.addItem(f"方案 {i+1}")
        
        if self.editor.generated_solutions:
            self.current_solution_index = 0
            self.solution_combo.setCurrentIndex(0)
            self.show_solution(0)
            QMessageBox.information(self, "成功", f"已生成{len(self.editor.generated_solutions)}套解决方案！")
        else:
            QMessageBox.warning(self, "警告", "未能生成有效的解决方案！")
    
    def show_solution(self, index):
        """显示选中的解决方案"""
        if index < 0 or index >= len(self.editor.generated_solutions):
            return
        
        self.current_solution_index = index
        solution = self.editor.generated_solutions[index]
        
        # 更新订单表格
        self.order_table.setRowCount(len(solution))
        total_energy = 0
        for i, order in enumerate(solution):
            self.order_table.setItem(i, 0, QTableWidgetItem(str(order.order_id)))
            self.order_table.setItem(i, 1, QTableWidgetItem(f"{order.expected_energy:.2f}"))
            
            order_energy = sum(self.editor.calculate_energy(d, self.editor.current_config.efficiency_mode) for d in order.dishes)
            total_energy += order_energy
            self.order_table.setItem(i, 2, QTableWidgetItem(f"{order_energy:.2f}"))
            
            dish_names = ", ".join(d.name for d in order.dishes)
            self.order_table.setItem(i, 3, QTableWidgetItem(dish_names))
        
        # 更新指标
        self.metrics_table.item(0, 1).setText(f"{total_energy:.2f}")
        self.metrics_table.item(1, 1).setText("2.5")  # 模拟数据
        self.metrics_table.item(2, 1).setText("75%")  # 模拟数据
        self.metrics_table.item(3, 1).setText("15")   # 模拟数据
        self.metrics_table.item(4, 1).setText("24")   # 模拟数据
    
    def show_prev_solution(self):
        """显示上一个解决方案"""
        if self.editor.generated_solutions:
            new_index = (self.current_solution_index - 1) % len(self.editor.generated_solutions)
            self.solution_combo.setCurrentIndex(new_index)
    
    def show_next_solution(self):
        """显示下一个解决方案"""
        if self.editor.generated_solutions:
            new_index = (self.current_solution_index + 1) % len(self.editor.generated_solutions)
            self.solution_combo.setCurrentIndex(new_index)
    
    def save_solutions(self):
        """保存解决方案"""
        if not self.editor.generated_solutions:
            QMessageBox.warning(self, "警告", "没有可保存的解决方案！")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存解决方案", "solutions.json", "JSON Files (*.json)"
        )
        if file_path:
            if self.editor.save_solutions(file_path):
                QMessageBox.information(self, "成功", "解决方案保存成功！")
            else:
                QMessageBox.warning(self, "错误", "解决方案保存失败！")
    
    def load_solutions(self):
        """加载解决方案"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载解决方案", "", "JSON Files (*.json)"
        )
        if file_path:
            if self.editor.load_solutions(file_path):
                # 更新解决方案列表
                self.solution_combo.clear()
                for i in range(len(self.editor.generated_solutions)):
                    self.solution_combo.addItem(f"方案 {i+1}")
                
                if self.editor.generated_solutions:
                    self.current_solution_index = 0
                    self.solution_combo.setCurrentIndex(0)
                    self.show_solution(0)
                    QMessageBox.information(self, "成功", "解决方案加载成功！")
                else:
                    QMessageBox.warning(self, "警告", "加载的解决方案为空！")
            else:
                QMessageBox.warning(self, "错误", "解决方案加载失败！")


if __name__ == "__main__":
    app = QApplication([])
    app.setStyle("Fusion")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    window = OrderEditorApp()
    window.show()
    app.exec_()