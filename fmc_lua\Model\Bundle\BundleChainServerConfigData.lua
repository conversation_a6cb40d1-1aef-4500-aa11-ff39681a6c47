BundleChainServerConfigData = setmetatable({}, BundleServerConfigData)
BundleChainServerConfigData.__index = BundleChainServerConfigData

function BundleChainServerConfigData.Create(configData, groupData)
  local data = setmetatable(Table.ShallowCopy(configData), BundleChainServerConfigData)
  data:Init(groupData)
  return data
end

function BundleChainServerConfigData:GetStep()
  return self.step
end

function BundleChainServerConfigData:GetSkin()
  return self.skin
end

function BundleChainServerConfigData:IsCircle()
  return self.circle == 1
end

function BundleChainServerConfigData:IsFree()
  return self:GetPurchaseId() == nil
end

BundleChainServerConfigDataGroup = setmetatable({
  GiftContentKey = "bundleContentChain",
  ConfigDataClass = BundleChainServerConfigData
}, BundleServerConfigDataGroup)
BundleChainServerConfigDataGroup.__index = BundleChainServerConfigDataGroup

function BundleChainServerConfigDataGroup.Create(groupData)
  local data = setmetatable({}, BundleChainServerConfigDataGroup)
  data:Init(groupData)
  return data
end

function BundleChainServerConfigDataGroup:Init(...)
  BundleServerConfigDataGroup.Init(self, ...)
end

function BundleChainServerConfigDataGroup:_InitConfigDatas(groupData)
  self.m_mapConfigData = {}
  self.m_arrBundleIds = {}
  local arrConfigData = groupData[self.GiftContentKey]
  local configData
  for _, data in ipairs(arrConfigData) do
    configData = self.ConfigDataClass.Create(data, self)
    self.m_mapConfigData[configData:GetBundleId()] = configData
    table.insert(self.m_arrBundleIds, configData:GetBundleId())
  end
  table.sort(self.m_arrBundleIds, function(a, b)
    local stepA = self.m_mapConfigData[a]:GetStep()
    local stepB = self.m_mapConfigData[b]:GetStep()
    return stepA < stepB
  end)
  self.m_arrCircleIds = {}
  for _, bundleId in ipairs(self.m_arrBundleIds) do
    if self.m_mapConfigData[bundleId]:IsCircle() then
      table.insert(self.m_arrCircleIds, bundleId)
    end
  end
end

function BundleChainServerConfigDataGroup:GetConfigDataByIndex(index)
  if index == nil then
    return
  end
  if index <= #self.m_arrBundleIds then
    return self:GetConfigData(self.m_arrBundleIds[index])
  end
  if #self.m_arrCircleIds == 0 then
    return nil
  end
  local curIndex = (index - #self.m_arrBundleIds) % #self.m_arrCircleIds
  curIndex = curIndex == 0 and #self.m_arrCircleIds or curIndex
  return self:GetConfigData(self.m_arrCircleIds[curIndex])
end

function BundleChainServerConfigDataGroup:GetCircleBundleIds()
  return self.m_arrCircleIds
end
