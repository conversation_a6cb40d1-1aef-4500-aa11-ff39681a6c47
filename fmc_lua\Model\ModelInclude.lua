require("Model.GlobalManager")
require("Model.SceneManager")
require("Model.ConfigModel")
require("Model.GameModel")
require("Model.GameTextModel")
require("Model.OpenFunctionModel")
require("Model.SystemConfigModel")
require("Model.FlambeTimeModel")
require("Model.DataBalanceModel")
require("Model.UserData.UserDataKey")
require("Model.Audio.AudioDefinition")
require("Model.Audio.AudioModel")
require("Model.Resource.AddressableLabel")
require("Model.Resource.DownloadManager")
require("Model.Resource.ResourceLoader")
require("Model.UpdateHint.UpdateHintModel")
require("Model.Network.HttpManager")
require("Model.Network.NetworkConfig")
require("Model.Network.BufferOperation")
require("Model.Network.Api.ApiMessage")
require("Model.Network.BI.BIDefine")
require("Model.Network.BI.BIManager")
require("Model.Network.BI.NetTimeStamp")
require("Model.Network.Sso.SsoMessage")
require("Model.Network.Sso.SsoManager")
require("Model.Network.Pay.PayMessage")
require("Model.Network.Oper.OperBIManager")
require("Model.Network.Oper.OperManager")
require("Model.Network.UdpHealthCheck")
require("Model.Network.UdpManager")
require("Model.Network.UdpMessage")
require("Model.Network.Redis.RedisMessage")
require("Model.DB.DBTableConfigs")
require("Model.DB.BaseDBTable")
require("Model.DB.DatabaseModel")
require("Model.DB.DBTable")
require("Model.DB.DBTableManager")
require("Model.DB.DBIdGenerator")
require("Model.UserData.ModelHelper")
require("Model.UserData.EnergyFullNotificationHelper")
require("Model.UserData.EnergyModel")
require("Model.UserData.PropertyDataDefinition")
require("Model.UserData.PropertyDataManager")
require("Model.UserData.UserModel")
require("Model.UserData.UserProfileModel")
require("Model.UserData.LevelModel")
require("Model.UserData.MiscModel")
require("Model.UserData.RewardApi")
require("Model.UserData.PDItemSPModel")
require("Model.Sync.SyncModel")
require("Model.Shop.ShopDefinition")
require("Model.Shop.ShopItemDBManager")
require("Model.Shop.ShopDataModel")
require("Model.Shop.ShopModel")
require("Model.InAppPurchase.IAPDefinition")
require("Model.InAppPurchase.IAPProduct")
require("Model.InAppPurchase.InAppPurchaseModel")
require("Model.Bundle.BundleDefinition")
require("Model.Bundle.BundleServerConfigData")
require("Model.Bundle.BundleChainServerConfigData")
require("Model.Bundle.BundleConditionData")
require("Model.Bundle.BundleTriggerData")
require("Model.Bundle.BundleManager")
require("Model.Tutorial.TutorialDefinition")
require("Model.Tutorial.TutorialHelper")
require("Model.Tutorial.TutorialConfigData")
require("Model.Tutorial.TutorialExecuterBase")
require("Model.Tutorial.TutorialModel")
require("Model.CDN.CDNDownloader")
require("Model.CDN.CDNResourceManager")
require("Model.Activity.ActivityDefinition")
require("Model.Activity.ActivityManager")
require("Model.Activity.ActivityModelFactory")
require("Model.Activity.BaseActivityModel")
require("Model.Activity.VirtualDBTable")
require("Model.Activity.ActivityTokenHelper")
require("Model.Rate.RateModel")
require("Model.Notification.ComeBackNotificationHelper")
require("Model.Notification.NotificationModel")
require("Model.Notification.PushTokenModel")
require("Model.Notification.BaseNotificationHelper")
require("Model.AccountManager")
require("Model.Promotion.CrossPromotionModel")
require("Model.Promotion.MoreGameModel")
require("Model.Reward.RewardModel")
require("Model.AdModel")
require("Model.HeartBeatManager")
require("Model.NoticeModel")
require("Model.Survey.SurveyModel")
require("Model.UpcomingEvent.UpcomingEventModel")
require("Model.Task.TaskData")
require("Model.Task.TaskDataModel")
require("Model.Task.TaskStartCondition")
require("Model.Task.TaskStarter")
require("Model.Task.TaskManager")
require("Model.Timeline.TimelineData")
require("Model.Timeline.TimelineDataModel")
require("Model.Timeline.TimelineManager")
require("Model.Timeline.TimelineExecuter")
require("Model.Story.StoryData")
require("Model.Story.StoryDataModel")
