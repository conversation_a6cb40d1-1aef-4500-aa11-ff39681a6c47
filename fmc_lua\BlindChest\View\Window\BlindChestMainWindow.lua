BlindChestMainWindow = setmetatable({}, BlindChestBaseWindow)
BlindChestMainWindow.__index = BlindChestMainWindow

function BlindChestMainWindow:Init(model, state)
  self.m_initState = state
  BlindChestBaseWindow.Init(self, model, state)
  self.m_hudButton:Init(self.m_definition.ActivityTokenPropertyType)
  self.m_rewardCanvasGroup.alpha = 0
  self.m_rewardTip:Hide(true)
  self.m_pearlCanvas.sortingOrder = self:GetSortingOrder() + 12
  UIUtil.UpdateSortingOrder(self.m_pearlEffectGo, self:GetSortingOrder() + 11)
  UIUtil.SetActive(self.m_pearlEffectGo, false)
  UIUtil.SetActive(self.m_pearlGo, false)
  self:_InitSlots()
  self:_UpdateContent()
  self:_UpdateProgress(false)
  self:UpdatePerSecond()
  self.m_activityBundleEntryRoot:Init(self.m_model:GetType())
  self.m_model:TryTriggerLackChestToken()
  if self.m_initState == ActivityState.Ended then
    self.m_keyTip:Show("blindchest_hint_last_chance")
    GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.BlindChestLastChance, nil, nil, nil, true)
  end
  self.m_keySpine:Init()
  self.m_descText.text = GM.GameTextModel:GetText("blindchest_desc", "blindchest1_reward_icon", "blindchest1_reward_icon")
  self.m_bottomRewardText.text = GM.GameTextModel:GetText("blindchest_bigprize", "blindchest1_reward_icon", "blindchest1_reward_icon")
end

function BlindChestMainWindow:_CheckState()
  if self.m_model:HasFinishedAllRound() then
    return
  end
  BlindChestBaseWindow._CheckState(self)
end

function BlindChestMainWindow:OnDestroy()
  BlindChestBaseWindow.OnDestroy(self)
  if self.m_openSeq ~= nil then
    self.m_openSeq:Kill()
    self.m_openSeq = nil
  end
  if self.m_bEventLocked then
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
  end
  Scheduler.UnscheduleTarget(self)
end

function BlindChestMainWindow:UpdatePerSecond()
  if self.m_model == nil or self.m_model:GetNextStateTime() == nil then
    return
  end
  if self.m_model:GetState() == ActivityState.Released then
    self:Close()
    return
  end
  local delta
  if self.m_model:GetState() == ActivityState.Ended then
    UIUtil.SetActive(self.m_countdownGo, false)
  else
    delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

function BlindChestMainWindow:_InitSlots()
  self.m_slots = {}
  local prefab = self:GetSlotLayerPrefab()
  if prefab ~= nil then
    local go = GameObject.Instantiate(prefab, self.m_chestNode)
    self.m_chestLayerGo = go
    local root = go.transform
    for i = 0, root.childCount - 1 do
      local slot = root:GetChild(i).gameObject:GetLuaTable()
      slot:Init(self, i + 1, self.m_model)
      self.m_slots[i + 1] = slot
    end
  end
end

function BlindChestMainWindow:GetSlotLayerPrefab()
  local turnConfig = self.m_model:GetTurnConfig()
  local turnNumber = turnConfig.SlotNumber
  local prefix = "m_slotLayer"
  for i = 6, 12 do
    if i >= turnNumber and self[prefix .. i] ~= nil then
      return self[prefix .. i]
    end
  end
end

function BlindChestMainWindow:_UpdateContent()
  local turnConfig = self.m_model:GetTurnConfig()
  self.m_rewardContent:Init(turnConfig.TopRewards, 4)
  local validRewards = RewardApi.FilterRewards(turnConfig.TopRewards)
  if 4 < #validRewards then
    self.m_rewardScrollRect.enabled = true
    self.m_rewardScrollRect.horizontalNormalizedPosition = 0
  else
    self.m_rewardScrollRect.enabled = false
    UIUtil.SetAnchoredPosition(self.m_rewardScrollRect.content, 0)
  end
end

function BlindChestMainWindow:_UpdateProgress(withAnimation)
  local turn = self.m_model:GetTurn()
  local turnNum = self.m_model:GetCurRoundTurnNum()
  self.m_progressSliderText.text = turn - 1 .. "/" .. turnNum
  if withAnimation then
    self.m_progressSlider:DOValue((turn - 1) / turnNum, 0.3)
  else
    self.m_progressSlider.value = (turn - 1) / turnNum
  end
end

function BlindChestMainWindow:OnKeyBtnClick()
  self:HideRewardTip()
  self.m_keyTip:Show()
end

function BlindChestMainWindow:OnCloseBtnClick()
  self:HideRewardTip()
  if self.m_model:GetState() == ActivityState.Ended then
    if self.m_model:GetKeyCount() > 0 then
      GM.UIManager:OpenView(self.m_definition.EndConfirmWindowPrefabName, self.m_model, self.m_model:GetState(), function()
        self:Close()
        GM.UIManager:OpenViewWhenIdle(self.m_definition.EndWindowPrefabName, self.m_model, self.m_model:GetState())
      end)
    else
      self:Close()
      GM.UIManager:OpenViewWhenIdle(self.m_definition.EndWindowPrefabName, self.m_model, self.m_model:GetState())
    end
  else
    self:Close()
  end
end

function BlindChestMainWindow:OnSlotClicked(slot)
  if self.m_openSeq ~= nil then
    self.m_openSeq:Kill()
    self.m_openSeq = nil
  end
  self:HideRewardTip()
  local count = self.m_model:GetKeyCount()
  if count == 0 then
    if not self.m_model:OnLackChestToken() then
      GM.UIManager:ShowPromptWithKey("blindchest_notenoughkey", nil, nil, true, false, nil, SpecialPromptPrefabName.Window2)
    end
    return
  end
  self.m_hudButton:UpdateValue(count - 1)
  local result = self.m_model:Open(slot:GetIndex())
  GM.UIManager:SetEventLock(true)
  self.m_bEventLocked = true
  slot:PlayOpenAnimation(result)
  local keyTrans = self.m_keyGo.transform
  keyTrans:SetParent(slot:GetTargetTrans())
  keyTrans.anchoredPosition = Vector2(0, 0)
  local seq = DOTween.Sequence()
  self.m_keySpine.skeletonAnimation:Initialize()
  self.m_keySpine:PlayAnimation("open", nil, false)
  local audioTime = 0.5
  seq:InsertCallback(audioTime, function()
    GM.AudioModel:PlayEffect(AudioFileConfigName.sfxBlindChestOpen)
  end)
  local openTime = 1
  if result == BlindChestModel.TopRewardIndex then
    local pearlTrans = self.m_pearlGo.transform
    seq:InsertCallback(openTime, function()
      self.m_pearlGo:SetActive(true)
      pearlTrans.position = slot.transform.position
      pearlTrans.localScale = Vector3.zero
    end)
    local waitTime = 0.4
    local duration = 0.5
    seq:Insert(openTime, pearlTrans:DOScale(Vector3(0.6, 0.6, 1), waitTime - 0.2))
    seq:Insert(openTime + waitTime, pearlTrans:DOAnchorPos(Vector2(0, 110), duration))
    seq:Insert(openTime + waitTime, pearlTrans:DOScale(1.1, duration))
    seq:Insert(openTime + waitTime + duration, pearlTrans:DOScale(1, 0.1))
  end
  seq:InsertCallback(openTime, function()
    keyTrans:SetParent(self.transform)
    GM.UIManager:SetEventLock(false)
    self.m_bEventLocked = false
    if result == BlindChestModel.TopRewardIndex then
      DelayExecuteFuncInView(function()
        local turnData = self.m_model:GetTurnConfig(nil, self.m_model:GetTurn() - 1)
        GM.UIManager:OpenView(UIPrefabConfigName.BlindChestTopRewardWindow, turnData.TopRewards, "blindchest_bigprize_title", true, function()
          UIUtil.SetActive(self.m_pearlEffectGo, true)
        end, function()
          self:PlayPearlGetAnimation()
        end)
      end, 0.5, self, true)
    else
      local reward
      if result ~= BlindChestModel.EmptyRewardIndex then
        reward = RewardApi.FilterRewards({
          self.m_model:GetTurnConfig().OtherRewards[result]
        })[1]
        self.m_rewardItem:Init(reward, false)
        self.m_rewardItem:SetAmountText("+" .. reward[PROPERTY_COUNT])
        self.m_rewardCanvasGroup.alpha = 1
        self.m_rewardItem.transform.position = slot.transform.position
        self.m_rewardItem.transform.localScale = Vector3.zero
        self.m_rewardItem.transform:DOScale(Vector3(1, 1, 1), 0.2)
        self.m_rewardCanvasGroup:DOFade(0, 0.2):SetDelay(0.5):OnComplete(function()
          RewardApi.AcquireRewardsInView({reward}, {
            arrWorldPos = {
              self.m_rewardItem.transform.position
            }
          })
        end)
      else
        GM.UIManager:ShowPromptWithKey("blindchest_empty", PositionUtil.UICameraWorld2Screen(slot.transform.position), 0.4, false, false, nil, SpecialPromptPrefabName.Prompt)
      end
    end
  end)
  seq:InsertCallback(openTime + 0.8, function()
    self.m_openSeq = nil
    if result ~= BlindChestModel.TopRewardIndex then
      self.m_model:TryTriggerLackChestToken()
    end
  end)
  self.m_openSeq = seq
end

function BlindChestMainWindow:OnBgClicked()
  self:HideRewardTip()
end

function BlindChestMainWindow:_ShowNextRound()
  for _, slot in ipairs(self.m_slots) do
    slot:PlayHideAnim()
  end
  DelayExecuteFuncInView(function()
    GameObject.Destroy(self.m_chestLayerGo)
    self:_InitSlots()
    for _, slot in ipairs(self.m_slots) do
      slot:PlayEnterAnim()
    end
    self:_UpdateContent()
    self.m_model:TryTriggerLackChestToken()
  end, 0.6, self, true)
end

function BlindChestMainWindow:OpenHelpWindow()
  GM.UIManager:OpenView(self.m_definition.HelpWindowPrefabName, self.m_model, self.m_model:GetState())
end

function BlindChestMainWindow:PlayPearlGetAnimation()
  UIUtil.SetActive(self.m_pearlEffectGo, false)
  GM.UIManager:SetEventLock(true)
  self.m_bEventLocked = true
  local pearlTrans = self.m_pearlGo.transform
  local seq = DOTween.Sequence()
  seq:Append(pearlTrans:DOMove(self.m_propertyIconNode.position, 0.6))
  seq:Join(pearlTrans:DOScale(self.m_propertyIconNode.localScale.x, 0.6))
  seq:AppendCallback(function()
    self.m_pearlGo:SetActive(false)
    self:_UpdateProgress(true)
    DelayExecuteFuncInView(function()
      self.m_bEventLocked = false
      GM.UIManager:SetEventLock(false)
      local turn = self.m_model:GetTurn()
      if turn <= self.m_model:GetCurRoundTurnNum() then
        self:_ShowNextRound()
      else
        GM.UIManager:OpenView(UIPrefabConfigName.BlindChestFinalRewardWindow, self.m_finalRewardIconNode.position, 0.4, self.m_definition.FinalRewardIcon, self.m_model:GetFinalRewards(self.m_model:GetRound()), nil, true, nil, function()
          DelayExecuteFuncInView(function()
            self:Close()
            GM.UIManager:OpenViewWhenIdle(self.m_definition.SuccessWindowPrefabName, self.m_model, self.m_model:GetState(), true)
          end, 1, self)
        end)
      end
    end, 0.3, self)
  end)
end

function BlindChestMainWindow:ShowFinalRewardTip()
  self.m_rewardTip:Show(self.m_model:GetFinalRewards(self.m_model:GetRound()), self.m_finalRewardIconNode, 4, -63, true, true)
end

function BlindChestMainWindow:PopupFinalRewardTip()
  self:ShowFinalRewardTip()
  DelayExecuteFuncInView(function()
    self:HideRewardTip()
  end, 2, self)
end

function BlindChestMainWindow:HideRewardTip()
  self.m_rewardTip:Hide()
end

function BlindChestMainWindow:GetRewardGroup()
  return self.m_rewardMaskArea
end

function BlindChestMainWindow:GetSlotTouchArea()
  return self.m_slots[1].transform
end

function BlindChestMainWindow:GetKeyButtonTrans()
  return self.m_keyButtonTrans
end

function BlindChestMainWindow:GetHudButton()
  return self.m_hudButton
end

BlindChestMainHudButton = setmetatable({}, HudPropertyButton)
BlindChestMainHudButton.__index = BlindChestMainHudButton

function BlindChestMainHudButton:UpdateValueText()
  if self.m_valueText then
    self.m_valueText.text = "x" .. math.floor(self.m_value + 0.5)
  end
end

function BlindChestMainHudButton:UpdateValue(value)
  if self.m_valueTween then
    self.m_valueTween:Kill()
    self.m_valueTween = nil
  end
  self.m_value = value
  self.m_iTextAnimationTo = self.m_value
  self:UpdateValueText()
end
