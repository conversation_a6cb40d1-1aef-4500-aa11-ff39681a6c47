require("UI.UIFramework.UIUtil")
require("UI.UIFramework.UIDefinition")
require("UI.Animation.PropertyAnimationManager")
require("UI.Animation.PopupAvatarAnimation")
require("UI.Animation.FlyElement")
require("UI.UIFramework.BaseView")
require("UI.UIFramework.BaseWindow")
require("UI.UIFramework.MaskLayer")
require("UI.UIFramework.ModeViewController")
require("UI.UIFramework.PopupChain")
require("UI.UIFramework.TutorialPopupHelper")
require("UI.UIFramework.Prompt")
require("UI.UIFramework.PromptManager")
require("UI.UIFramework.UIManager")
require("UI.UIFramework.CGPlayer")
require("UI.Button.SimpleButton")
require("UI.Button.HudBaseButton")
require("UI.Button.HudPropertyButton")
require("UI.Button.HudGeneralButton")
require("UI.Button.EnergyHudButton")
require("UI.Button.GemHudButton")
require("UI.Button.SkipPropHudButton")
require("UI.Button.SkipPropInventoryButton")
require("UI.Button.CoinHudButton")
require("UI.Button.ItemTipButton")
require("UI.Button.UIBoardItemTipButton")
require("UI.Button.PropertyButton")
require("UI.Button.ShopButton")
require("UI.Button.InventoryButton")
require("UI.Button.BundleButton")
require("UI.Button.ChainBundleButton")
require("UI.Button.OnePlusNBundleButton")
require("UI.Button.SettingButton")
require("UI.Button.CrossPromotionButton")
require("UI.Button.UpcomingEventButton")
require("UI.Button.DiscoveriesButton")
require("UI.Button.RoomButton")
require("UI.Button.TaskButton")
require("UI.Button.MainBoardButton")
require("UI.Button.OrderDayButton")
require("UI.SubView.ServiceFloat")
require("UI.SubView.TabView.TabButton")
require("UI.SubView.TabView.TabView")
require("UI.SubView.RewardContent")
require("UI.SubView.DiscountView")
require("UI.SubView.WindowRoleBubble")
require("UI.SubView.SectionView")
require("UI.SubView.LevelRewardArea")
require("UI.SubView.Exclamation")
require("UI.SubView.LevelProgress")
require("UI.SubView.Switch")
require("UI.SubView.DecoratedTitle")
require("UI.SubView.RewardTip")
require("UI.Layer.BaseLoadingLayer")
require("UI.Layer.LoadingLayer")
require("UI.Layer.TutorialLayer")
require("UI.Layer.TimelineLayer")
require("UI.SceneView.BaseSceneView")
require("UI.SceneView.SceneViewHud")
require("UI.SceneView.SceneViewHudSideBar")
require("UI.SceneView.SceneViewHudSideBarLeft")
require("UI.SceneView.SceneViewHudSideBarBottomLeft")
require("UI.SceneView.SceneViewHudSideBarBottom")
require("UI.SceneView.SceneViewHudSideBarRight")
require("UI.SceneView.ChapterTransition")
require("UI.SceneView.SyncStateView")
require("UI.Window.GeneralMsgWindow")
require("UI.Window.NetworkErrorWindow")
require("UI.Window.RewardWindow")
require("UI.Window.BoxRewardWindow")
require("UI.Window.UpcomingEventWindow")
require("UI.Window.GoPlayWindow")
require("UI.Window.NameWindow")
require("UI.Window.TwoButtonWindow")
require("UI.Window.InputConfirmWindow")
require("UI.Window.MailDataWindow")
require("UI.Window.Task.TaskWindow")
require("UI.Window.Task.TaskPopupHelper")
require("UI.Window.Task.TaskGroupFinishWindow")
require("UI.Window.Task.TaskClearWindow")
require("UI.Window.Level.LevelUpPopupHelper")
require("UI.Window.ItemDetail.ItemDetailWindow")
require("UI.Window.ItemDetail.ItemDishDetailWindow")
require("UI.Window.ItemDetail.ItemDetailCell")
require("UI.Window.ItemDetail.ItemDescDetailWindow")
require("UI.Window.ItemDetail.AddItemChangeInfoWindow")
require("UI.Window.Shop.BaseShopCell")
require("UI.Window.Shop.BaseShopContainer")
require("UI.Window.Shop.BaseShopListContainer")
require("UI.Window.Shop.BaseShopSectionContainer")
require("UI.Window.Shop.ShopContainerCreator")
require("UI.Window.Shop.BuyEnergyWindow")
require("UI.Window.Shop.BuyEnergyDiscountWindow")
require("UI.Window.Shop.ShopCellBundle")
require("UI.Window.Shop.ShopCellItem")
require("UI.Window.Shop.ShopCellProperty")
require("UI.Window.Shop.ShopContainerDailyDeals")
require("UI.Window.Shop.ShopContainerDiamonds")
require("UI.Window.Shop.ShopContainerHotSales")
require("UI.Window.Shop.ShopContainerPassActivity")
require("UI.Window.Shop.ShopWindow")
require("UI.Window.Bundle.BundlePopupHelper")
require("UI.Window.Bundle.BundleTestInfo")
require("UI.Window.Bundle.BundleBaseWindow")
require("UI.Window.Bundle.ActivityBundleEntryRoot")
require("UI.Window.Bundle.BundleNormalWindow")
require("UI.Window.Bundle.StarterBundleWindow")
require("UI.Window.Bundle.EnergyBundleWindow")
require("UI.Window.Bundle.CDBundleWindow")
require("UI.Window.Bundle.OrderGroupBundleWindow")
require("UI.Window.Bundle.MultiTierBundleWindow")
require("UI.Window.Bundle.ShopContainerBundleMultiTier")
require("UI.Window.Bundle.BundleChainWindow")
require("UI.Window.Bundle.BundleOnePlusNWindow")
require("UI.Window.IAP.PurchaseFailWindow")
require("UI.Window.Story.StoryComponent")
require("UI.Window.Story.StoryWindow")
require("UI.Window.Setting.AvatarSelectCell")
require("UI.Window.Setting.AvatarSelectWindow")
require("UI.Window.Setting.PrivacySettingWindow")
require("UI.Window.Setting.SettingToggle")
require("UI.Window.Setting.SettingWindow")
require("UI.Window.Setting.LanguageSettingWindow")
require("UI.Window.Setting.UserAvatar")
require("UI.Window.Inventory.InventoryCell")
require("UI.Window.Inventory.InventoryNewSlotCell")
require("UI.Window.Inventory.InventoryRow")
require("UI.Window.Inventory.InventoryWindow")
require("UI.Window.Inventory.InventoryProducerCell")
require("UI.Window.Inventory.InventoryFlyItem")
require("UI.Window.Update.UpdateHintWindow")
require("UI.Window.Board.ItemChooseCell")
require("UI.Window.Board.ItemChooseWindow")
require("UI.Window.Board.DeleteItemConfirmWindow")
require("UI.Window.Board.RemoveBubbleConfirmWindow")
require("UI.Window.Board.GeneralGemConfirmWindow")
require("UI.Window.Board.UseInventoryItemConfirmWindow")
require("UI.Window.Board.LevelDownConfirmWindow")
require("UI.Window.Board.OrderFinishRewardWindow")
require("UI.Window.Board.ItemRecycleWindow")
require("UI.Window.Board.ItemRecycleAnimationWindow")
require("UI.Window.Board.ItemRecyclePopupHelper")
require("UI.Window.Board.ItemTypeDeleteWindow")
require("UI.Window.Board.ItemTypeDeletePopupHelper")
require("UI.Window.Board.SkipTimeAutoEffectPopupHelper")
require("UI.Window.Rate.RateWindow")
require("UI.Window.Rate.RatePopupHelper")
require("UI.Window.Notification.NotificationTutorialWindow")
require("UI.Window.Notification.NotificationOpenWindow")
require("UI.Window.Account.AccountNoticeWindow")
require("UI.Window.Account.AccountStatusWindow")
require("UI.Window.Account.DataConflictRegion")
require("UI.Window.Account.DataConflictWindow")
require("UI.Window.Account.SignInWindow")
require("UI.Window.Account.SignOutWindow")
require("UI.Window.Resource.DownloadConfirmWindow")
require("UI.Window.Resource.ReloadConfirmWindow")
require("UI.Window.Promotion.CrossPromotionTaskCell")
require("UI.Window.Promotion.CrossPromotionWindow")
require("UI.Window.Promotion.MoreGameCell")
require("UI.Window.Promotion.MoreGameWindow")
require("UI.Window.Notice.NoticeButton")
require("UI.Window.Notice.NoticeCell")
require("UI.Window.Notice.NoticeContentWindow")
require("UI.Window.Notice.NoticePopupHelper")
require("UI.Window.Notice.NoticeRewardCell")
require("UI.Window.Notice.NoticeWindow")
require("UI.Window.Survey.SurveyChoice")
require("UI.Window.Survey.BaseSurveyQuestion")
require("UI.Window.Survey.SurveyChoiceQuestion")
require("UI.Window.Survey.SurveyFillBlankQuestion")
require("UI.Window.Survey.SurveySingleChoiceFillBlankImgQuestion")
require("UI.Window.Survey.SurveyTipWindow")
require("UI.Window.Survey.SurveyWindow")
require("UI.Window.Survey.SurveyTipWindowPopupHelper")
require("UI.Window.Tutorial.TutorialBoardContainer")
require("UI.Window.Tutorial.TutorialBoardWindow")
require("UI.Window.Chapter.ChapterSelectWindow")
require("UI.Window.Chapter.ChapterFinishWindow")
require("UI.Window.Chapter.ChapterUnlockWindow")
require("UI.Window.Discoveries.DiscoveriesChain")
require("UI.Window.Discoveries.DiscoveriesCell")
require("UI.Window.Discoveries.DiscoveriesWindow")
require("UI.Window.OrderGroup.OrderDayCalender")
require("UI.Window.OrderGroup.OrderGroupWindow")
require("UI.Window.OrderGroup.OrderGroupFinishWindow")
require("UI.Window.OrderGroup.OrderGroupRefreshWindow")
require("UI.Window.OrderGroup.OrderGroupPopupHelper")
require("UI.Window.OrderGroup.OrderDayPopupHelper")
require("UI.Window.OrderGroup.OrderDayClearWindow")
require("UI.Window.OrderGroup.FlambeTimeBoard")
require("UI.Window.OrderGroup.FlambeTimeWindow")
require("UI.Window.OrderGroup.FlambeTimePopupHelper")
require("UI.Window.OrderGroup.ScrollBackToOrderWindow")
require("UI.Window.OrderGroup.ScrollBackToOrderPopupHelper")
require("UI.Window.Cook.RevertCookConfirmWindow")
require("UI.Window.DataBalance.DataBalanceWindow")
require("UI.Window.DataBalance.DataBalancePopupHelper")
