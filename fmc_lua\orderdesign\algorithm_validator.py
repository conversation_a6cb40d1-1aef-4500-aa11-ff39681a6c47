"""
算法验证模块
基于需求背景2验证订单生成算法的正确性
"""

import math
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict
from data_models import (
    OrderData, OrderRequirement, ConfigData, BetMode, EfficiencyMode,
    PlayerGeneratorInfo, OrderGenerationResult, ProductionData
)


class AlgorithmValidator:
    """算法验证器"""
    
    def __init__(self, config_data: ConfigData):
        """
        初始化算法验证器
        
        Args:
            config_data: 配置数据
        """
        self.config_data = config_data
    
    def validate_production_calculation(
        self, 
        generator_code: str, 
        expected_production: List[ProductionData]
    ) -> bool:
        """
        验证生成器产出计算
        
        Args:
            generator_code: 生成器代码
            expected_production: 期望的产出数据
            
        Returns:
            bool: 验证是否通过
        """
        if generator_code not in self.config_data.generators:
            return False
        
        generator = self.config_data.generators[generator_code]
        
        # 验证产出数据是否匹配
        if len(generator.production) != len(expected_production):
            return False
        
        for actual, expected in zip(generator.production, expected_production):
            if (actual.item_code != expected.item_code or 
                actual.weight != expected.weight):
                return False
        
        return True
    
    def validate_energy_calculation(
        self,
        order_requirements: List[OrderRequirement],
        bet_mode: BetMode,
        expected_energy: float,
        tolerance: float = 0.1
    ) -> bool:
        """
        验证体力消耗计算
        
        Args:
            order_requirements: 订单需求
            bet_mode: 体力消耗模式
            expected_energy: 期望体力消耗
            tolerance: 容差范围
            
        Returns:
            bool: 验证是否通过
        """
        # 简化的体力计算验证
        calculated_energy = 0.0
        
        for req in order_requirements:
            # 基础体力消耗计算
            base_energy = req.count * bet_mode.value
            calculated_energy += base_energy
        
        # 检查是否在容差范围内
        return abs(calculated_energy - expected_energy) <= tolerance
    
    def validate_constraint_satisfaction(
        self,
        result: OrderGenerationResult,
        constraints: Dict[str, Any]
    ) -> Tuple[bool, List[str]]:
        """
        验证约束条件满足情况
        
        Args:
            result: 订单生成结果
            constraints: 约束条件
            
        Returns:
            Tuple[bool, List[str]]: (是否满足所有约束, 违反的约束列表)
        """
        violations = []
        
        # 验证订单数量约束
        if len(result.orders) != 7:
            violations.append(f"订单数量不正确: 期望7个，实际{len(result.orders)}个")
        
        # 验证体力消耗约束
        if 'energy_range' in constraints:
            min_energy, max_energy = constraints['energy_range']
            if not (min_energy <= result.total_energy <= max_energy):
                violations.append(f"体力消耗超出范围: {result.total_energy} 不在 [{min_energy}, {max_energy}] 内")
        
        # 验证特色菜品约束
        if 'special_dishes' in constraints:
            special_dishes = constraints['special_dishes']
            found_special_dishes = []
            
            for order in result.orders:
                for req in order.requirements:
                    if req.item_type in special_dishes:
                        found_special_dishes.append(req.item_type)
            
            missing_dishes = set(special_dishes) - set(found_special_dishes)
            if missing_dishes:
                violations.append(f"缺少特色菜品: {list(missing_dishes)}")
        
        return len(violations) == 0, violations
    
    def validate_algorithm_consistency(
        self,
        results: List[OrderGenerationResult],
        tolerance: float = 0.05
    ) -> bool:
        """
        验证算法一致性（多次运行结果的稳定性）
        
        Args:
            results: 多次运行的结果
            tolerance: 容差比例
            
        Returns:
            bool: 算法是否一致
        """
        if len(results) < 2:
            return True
        
        # 计算体力消耗的变异系数
        energies = [result.total_energy for result in results]
        mean_energy = sum(energies) / len(energies)
        
        if mean_energy == 0:
            return True
        
        variance = sum((e - mean_energy) ** 2 for e in energies) / len(energies)
        std_dev = math.sqrt(variance)
        coefficient_of_variation = std_dev / mean_energy
        
        return coefficient_of_variation <= tolerance
    
    def validate_data_integrity(self) -> Tuple[bool, List[str]]:
        """
        验证配置数据完整性
        
        Returns:
            Tuple[bool, List[str]]: (数据是否完整, 问题列表)
        """
        issues = []
        
        # 验证物品数据
        for item_code, item in self.config_data.items.items():
            if not item.generators:
                issues.append(f"物品 {item_code} 没有关联的生成器")
            
            if item.level <= 0:
                issues.append(f"物品 {item_code} 等级无效: {item.level}")
        
        # 验证生成器数据
        for gen_code, generator in self.config_data.generators.items():
            if generator.frequency <= 0:
                issues.append(f"生成器 {gen_code} 频率无效: {generator.frequency}")
            
            if generator.capacity <= 0:
                issues.append(f"生成器 {gen_code} 容量无效: {generator.capacity}")
            
            if not generator.generated_items and not generator.production:
                issues.append(f"生成器 {gen_code} 没有产出物品")
        
        # 验证订单数据
        for chapter, orders in self.config_data.orders.items():
            if not orders:
                issues.append(f"章节 {chapter} 没有订单")
            
            for order in orders:
                if not order.requirements:
                    issues.append(f"订单 {order.order_id} 没有需求")
        
        return len(issues) == 0, issues
    
    def run_comprehensive_validation(
        self,
        test_results: List[OrderGenerationResult],
        test_constraints: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        运行综合验证
        
        Args:
            test_results: 测试结果列表
            test_constraints: 测试约束条件
            
        Returns:
            Dict[str, Any]: 验证报告
        """
        report = {
            'data_integrity': {},
            'constraint_satisfaction': {},
            'algorithm_consistency': {},
            'overall_score': 0.0,
            'recommendations': []
        }
        
        # 数据完整性验证
        data_ok, data_issues = self.validate_data_integrity()
        report['data_integrity'] = {
            'passed': data_ok,
            'issues': data_issues,
            'score': 1.0 if data_ok else 0.5
        }
        
        # 约束满足验证
        constraint_results = []
        for result in test_results:
            satisfied, violations = self.validate_constraint_satisfaction(result, test_constraints)
            constraint_results.append({
                'satisfied': satisfied,
                'violations': violations
            })
        
        constraint_pass_rate = sum(1 for r in constraint_results if r['satisfied']) / len(constraint_results)
        report['constraint_satisfaction'] = {
            'pass_rate': constraint_pass_rate,
            'results': constraint_results,
            'score': constraint_pass_rate
        }
        
        # 算法一致性验证
        consistency_ok = self.validate_algorithm_consistency(test_results)
        report['algorithm_consistency'] = {
            'passed': consistency_ok,
            'score': 1.0 if consistency_ok else 0.7
        }
        
        # 计算总分
        scores = [
            report['data_integrity']['score'],
            report['constraint_satisfaction']['score'],
            report['algorithm_consistency']['score']
        ]
        report['overall_score'] = sum(scores) / len(scores)
        
        # 生成建议
        if not data_ok:
            report['recommendations'].append("修复配置数据完整性问题")
        
        if constraint_pass_rate < 0.8:
            report['recommendations'].append("优化约束满足算法")
        
        if not consistency_ok:
            report['recommendations'].append("提高算法稳定性")
        
        return report
    
    def generate_test_cases(self) -> List[Dict[str, Any]]:
        """
        生成测试用例
        
        Returns:
            List[Dict[str, Any]]: 测试用例列表
        """
        test_cases = []
        
        # 基础测试用例
        test_cases.append({
            'name': '基础功能测试',
            'special_dishes': ['ds_chopve_1', 'ds_chopve_2', 'ds_flb_1', 'ds_chopve_3'],
            'energy_range': (100.0, 200.0),
            'bet_mode': BetMode.BET_2,
            'efficiency_mode': EfficiencyMode.AVERAGE
        })
        
        # 边界测试用例
        test_cases.append({
            'name': '最小体力测试',
            'special_dishes': ['ds_chopve_1'],
            'energy_range': (50.0, 80.0),
            'bet_mode': BetMode.BET_2,
            'efficiency_mode': EfficiencyMode.DYNAMIC
        })
        
        test_cases.append({
            'name': '最大体力测试',
            'special_dishes': ['ds_chopve_1', 'ds_chopve_2', 'ds_flb_1', 'ds_chopve_3', 'ds_chopve_4', 'ds_chopve_5', 'ds_chopve_6'],
            'energy_range': (300.0, 500.0),
            'bet_mode': BetMode.BET_8,
            'efficiency_mode': EfficiencyMode.TOP3_AVERAGE
        })
        
        # 异常测试用例
        test_cases.append({
            'name': '空菜品测试',
            'special_dishes': [],
            'energy_range': (100.0, 200.0),
            'bet_mode': BetMode.BET_4,
            'efficiency_mode': EfficiencyMode.AVERAGE
        })
        
        return test_cases
