PropertyDataManager = {}
PropertyDataManager.__index = PropertyDataManager

function PropertyDataManager:Init()
  Log.Info("PropertyDataManager初始化")
end

function PropertyDataManager:IsPropertyType(type)
  if self.m_mapPropertyType == nil then
    self.m_mapPropertyType = {}
    for _, v in pairs(EPropertyType) do
      self.m_mapPropertyType[v] = true
    end
  end
  return self.m_mapPropertyType[type]
end

function PropertyDataManager:Acquire(arrProperties, source, scene, sourceGameMode)
  Log.Assert(source == EPropertySource.Buy or source == EPropertySource.Give, "should only acquire buy or give property")
  local type, count, originalNum, cryptStr
  for i = 1, #arrProperties do
    type = arrProperties[i][PROPERTY_TYPE]
    originalNum = arrProperties[i][PROPERTY_COUNT]
    cryptStr = arrProperties[i][PROPERTY_CRYPT]
    if not cryptStr then
      count = originalNum
      Log.Error((scene or "nil") .. " 这里的奖励领取未加密，请联系程序加密！")
    else
      count = tonumber(Crypt.CryptCurrency(cryptStr))
      if count ~= originalNum then
        local strScene = scene or "nil"
        local strCrypt = cryptStr or "nil"
        local strCryptNum = count or "nil"
        local strErr = strScene .. " type:" .. type .. " originalNum:" .. originalNum .. " crypt:" .. strCrypt .. " cryptNum:" .. strCryptNum
        GM.BIManager:LogErrorInfo(EBIType.CurrencyError, strErr)
        if count == nil or count <= 0 or count > math.floor(count) then
          count = originalNum
        end
      end
    end
    local activityType, handler = self:_GetActivityHandler(type, "acquire")
    if activityType ~= nil then
      local model = GM.ActivityManager:GetModel(activityType)
      model[handler](model, count)
    elseif EnergyModel.PropertyType2EnergyType(type) ~= nil then
      GM.EnergyModel:AddEnergy(EnergyModel.PropertyType2EnergyType(type), count, true)
    elseif EnergyModel.PropertyType2InfiniteEnergyType(type) ~= nil then
      GM.EnergyModel:AddEnergyInfiniteTime(EnergyModel.PropertyType2InfiniteEnergyType(type), count)
    elseif type == EPropertyType.BakeOutToken then
      GM.ActivityManager:GetModel(ActivityType.BakeOut):AcquireToken(count)
    elseif type == EPropertyType.SpreeExp then
      local activeModel = GM.ActivityManager:GetStartedSpreeActivity()
      if activeModel ~= nil and 0 < count then
        activeModel:AddLevelExp(count)
      end
    elseif PassActivityModel.IsVIPTicket(type) then
      local bpModel
      for actType, actDefinition in pairs(PassActivityDefinition) do
        bpModel = GM.ActivityManager:GetModel(actType)
        if bpModel ~= nil and bpModel:TryAcquireVIPTicket(type) then
          break
        end
      end
    else
      local key = self:GetPropertyKey(type, source)
      GM.UserModel:ChangeNumber(key, count)
      if GameConfig.IsTestMode() and scene == EBIType.ItemCollect then
        if type == EPropertyType.Gem then
          GM.BIManager:ChangeNumber(EPropertyTestKey.GemAcquireFromBoardCollect, count)
        elseif type == EPropertyType.Gold then
          GM.BIManager:ChangeNumber(EPropertyTestKey.GoldAcquireFromBoardCollect, count)
        end
      end
      GM.DBTableManager:SaveAllWhenLateUpdate()
    end
    GM.BIManager:LogAcquire(type, count, scene, source == EPropertySource.Give, sourceGameMode)
  end
  local message = {arrProperties = arrProperties, scene = scene}
  EventDispatcher.DispatchEvent(EEventType.PropertyAcquired, message)
end

function PropertyDataManager:AcquireWithCollectAnimation(arrProperties, source, uiWorldPos, customData, scene)
  self:Acquire(arrProperties, source, scene)
  self:PlayCollectAnimation(arrProperties, uiWorldPos, customData)
end

function PropertyDataManager:AcquireWithSimpleAnimation(arrProperties, source, scene)
  self:Acquire(arrProperties, source, scene)
  self:PlayPropertyIncreaseAnimation(arrProperties)
end

function PropertyDataManager:PlayCollectAnimation(arrProperties, uiWorldPos, customData)
  EventDispatcher.DispatchEvent(EEventType.PlayCollectAnimation, {
    arrProperties = arrProperties,
    uiWorldPos = uiWorldPos,
    customData = customData
  })
end

function PropertyDataManager:PlayPropertyIncreaseAnimation(arrProperties, customData)
  for i, property in ipairs(arrProperties) do
    EventDispatcher.DispatchEvent(EEventType.PlayPropertyIncreaseAnimation, {
      property = property,
      customData = (customData or Table.Empty)[i] or Table.Empty
    })
  end
end

function PropertyDataManager:DispatchConsumeEvent(propertyType, num, scene, ext, consumer)
  local message = {
    property = {
      [PROPERTY_TYPE] = propertyType,
      [PROPERTY_COUNT] = num
    },
    ext = ext
  }
  EventDispatcher.DispatchEvent(EEventType.PropertyConsumed, message)
  GM.BIManager:LogUseItem(propertyType, num, scene, consumer)
  self:PlayPropertyDecreaseAnimation(message)
end

function PropertyDataManager:PlayPropertyDecreaseAnimation(message)
  EventDispatcher.DispatchEvent(EEventType.PlayPropertyDecreaseAnimation, message)
end

function PropertyDataManager:PlayConsumeAnimation(propertyType, num, uiWorldPos, customData)
  local property = {
    [PROPERTY_TYPE] = propertyType,
    [PROPERTY_COUNT] = num
  }
  EventDispatcher.DispatchEvent(EEventType.PlayConsumeAnimation, {
    property = property,
    uiWorldPos = uiWorldPos,
    customData = customData
  })
end

function PropertyDataManager:Consume(propertyType, num, scene, noAnim, consumer)
  num = num or 1
  if num < 0 then
    Log.Error("PropertyDataManager:Consume num < 0")
    return false
  end
  if num == 0 then
    return true
  end
  local activityType, handler = self:_GetActivityHandler(propertyType, "consume")
  if activityType ~= nil then
    local model = GM.ActivityManager:GetModel(activityType)
    local result = model[handler](model, num)
    return result
  end
  if EnergyModel.PropertyType2EnergyType(propertyType) ~= nil then
    local result, realCostNum = GM.EnergyModel:SubtractEnergy(EnergyModel.PropertyType2EnergyType(propertyType), num, scene)
    if result then
      self:DispatchConsumeEvent(propertyType, realCostNum, scene, {origin = num, noAnim = noAnim}, consumer)
    end
    return result
  end
  if num <= self:GetPropertyNum(propertyType) then
    local result = true
    local buyKey = self:GetPropertyKey(propertyType, EPropertySource.Buy)
    local buyNum = self:GetPropertyNum(propertyType, EPropertySource.Buy)
    local consumedGiveNum = 0
    local consumedBuyNum = 0
    if num <= buyNum then
      consumedBuyNum = num
      result = GM.UserModel:ChangeNumber(buyKey, -consumedBuyNum)
    else
      consumedGiveNum = num - buyNum
      consumedBuyNum = buyNum
      local giveKey = self:GetPropertyKey(propertyType, EPropertySource.Give)
      result = GM.UserModel:ChangeNumber(buyKey, -consumedBuyNum) and GM.UserModel:ChangeNumber(giveKey, -consumedGiveNum)
    end
    if result then
      self:DispatchConsumeEvent(propertyType, num, scene, {noAnim = noAnim}, consumer)
      if scene ~= EBIType.Test and propertyType == EPropertyType.Gem then
        GM.BIManager:ChangeNumber(EBISyncKey.ConsumedFreeGem, consumedGiveNum)
        GM.BIManager:ChangeNumber(EBISyncKey.ConsumedPaidGem, consumedBuyNum)
      elseif GameConfig.IsTestMode() and scene ~= EBIType.Test and propertyType == EPropertyType.Gold then
        GM.BIManager:ChangeNumber(EPropertyTestKey.ConsumedGold, num)
      end
    end
    return result
  end
  return false
end

function PropertyDataManager:GetPropertyKey(key, source)
  if key == nil then
    return false
  end
  return key .. EPropertyKeySuffix[source]
end

function PropertyDataManager:GetPropertyNum(propertyType, propertySource)
  local activityType, handler = self:_GetActivityHandler(propertyType, "get_number")
  if activityType ~= nil then
    local model = GM.ActivityManager:GetModel(activityType)
    return model[handler](model)
  end
  if EnergyModel.PropertyType2EnergyType(propertyType) ~= nil then
    if propertySource == EPropertySource.Buy then
      return 0
    else
      return GM.EnergyModel:GetEnergy(EnergyModel.PropertyType2EnergyType(propertyType))
    end
  end
  local propertyNum = {}
  propertyNum[EPropertySource.Give] = GM.UserModel:GetInNumber(self:GetPropertyKey(propertyType, EPropertySource.Give))
  propertyNum[EPropertySource.Buy] = GM.UserModel:GetInNumber(self:GetPropertyKey(propertyType, EPropertySource.Buy))
  if propertySource then
    return propertyNum[propertySource]
  else
    return propertyNum[EPropertySource.Give] + propertyNum[EPropertySource.Buy]
  end
end

function PropertyDataManager:PropertyToSyncDataTable(ePropertyType)
  local tb = {}
  for _, ePropertySource in pairs(EPropertySource) do
    tb[ePropertySource] = self:GetPropertyNum(ePropertyType, ePropertySource)
  end
  return tb
end

function PropertyDataManager:PropertyToSyncData(ePropertyType)
  return json.encode(self:PropertyToSyncDataTable(ePropertyType))
end

function PropertyDataManager:PropertyFromSyncData(ePropertyType, propertyJson)
  propertyJson = propertyJson or ""
  local tb = json.decode(propertyJson) or {}
  local mapContent = {}
  for ePropertySource, count in pairs(tb) do
    mapContent[self:GetPropertyKey(ePropertyType, ePropertySource)] = {value = count}
  end
  GM.UserModel:BatchSet(mapContent)
  return tb
end

function PropertyDataManager:GetPropertyNumFromSyncData(propertyJson)
  local total = 0
  local tb = json.decode(propertyJson) or {}
  for _, count in pairs(tb) do
    total = total + count
  end
  return total
end

function PropertyDataManager:MergeProperties(arrFrom, arrTo)
  local mapMerged = {}
  for i = 1, #arrTo do
    local ePropertyType = arrTo[i][PROPERTY_TYPE]
    local iAmount = arrTo[i][PROPERTY_COUNT]
    mapMerged[ePropertyType] = iAmount
  end
  for i = 1, #arrFrom do
    local ePropertyType = arrFrom[i][PROPERTY_TYPE]
    local iAmount = arrFrom[i][PROPERTY_COUNT]
    if mapMerged[ePropertyType] then
      mapMerged[ePropertyType] = mapMerged[ePropertyType] + iAmount
    else
      mapMerged[ePropertyType] = iAmount
    end
  end
  local arrMerged = {}
  for type, count in pairs(mapMerged) do
    arrMerged[#arrMerged + 1] = {
      [PROPERTY_TYPE] = type,
      [PROPERTY_COUNT] = count
    }
  end
  return arrMerged
end

function PropertyDataManager:GetPropertySpriteKey(ePropertyType)
  local strSprite = EPropertySprite[ePropertyType]
  if strSprite then
    return strSprite
  end
  return ""
end

function PropertyDataManager:GetRewardsText(arrProperties)
  local key = ""
  for i = 1, #arrProperties do
    local ePropertyType = arrProperties[i][PROPERTY_TYPE]
    local iAmount = arrProperties[i][PROPERTY_COUNT]
    key = key .. StringUtil.GetElementInTextStr(self:GetPropertySpriteKey(ePropertyType)) .. tostring(iAmount) .. "  "
  end
  return key
end

function PropertyDataManager:CreateNewUser()
  local mapContent = {}
  for _, v in pairs(EPropertyType) do
    mapContent[self:GetPropertyKey(v, EPropertySource.Give)] = {value = 0}
  end
  GM.UserModel:BatchSet(mapContent)
  local arrInitialProperties = {
    {
      [PROPERTY_TYPE] = EPropertyType.Gem,
      [PROPERTY_COUNT] = 100,
      [PROPERTY_CRYPT] = Crypt.CryptCurrency(100)
    },
    {
      [PROPERTY_TYPE] = EPropertyType.SkipProp,
      [PROPERTY_COUNT] = 50,
      [PROPERTY_CRYPT] = Crypt.CryptCurrency(50)
    }
  }
  self:Acquire(arrInitialProperties, EPropertySource.Give, EBIType.CreateNewUser)
end

function PropertyDataManager:_GetActivityHandler(property, use)
  if property == nil then
    Log.Error("[PropertyDataManager:_GetActivityHandler] property is nil")
    return nil, nil
  end
  local activityType, handler = nil, "ActivityToken"
  for _, oneActDef in ipairs(ActivityDefinitions) do
    if activityType ~= nil then
      break
    end
    for actType, actDef in pairs(oneActDef) do
      if property == actDef.ActivityTokenPropertyType then
        activityType = actType
        break
      end
    end
  end
  local arrSingleProperty = {}
  if activityType == nil and arrSingleProperty[property] ~= nil then
    activityType = arrSingleProperty[property].actType
    if arrSingleProperty[property].handler ~= nil then
      handler = arrSingleProperty[property].handler
    end
  end
  if use == "acquire" then
    handler = "Acquire" .. handler
  elseif use == "consume" then
    handler = "Consume" .. handler
  elseif use == "get_number" then
    handler = "Get" .. handler .. "Number"
  else
    handler = nil
  end
  return activityType, handler
end
