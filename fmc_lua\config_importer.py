"""
配置导入模块
支持从MC、FL等项目导入菜品配置
"""

import os
import re
import json
from typing import List, Dict, Any
from dish_database import DishDatabase, Dish, ProcessingStep

class ConfigImporter:
    """配置导入器"""
    
    def __init__(self, db: DishDatabase):
        self.db = db
    
    def import_from_item_model_config(self, config_path: str, source: str = "FL"):
        """从ItemModelConfig.lua导入菜品"""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析菜品配置
        dishes = self._parse_item_model_config(content, source)
        
        # 导入到数据库
        imported_count = 0
        for dish in dishes:
            try:
                self.db.save_dish(dish, "importer")
                imported_count += 1
            except Exception as e:
                print(f"导入菜品 {dish.dish_id} 失败: {e}")
        
        return imported_count
    
    def _parse_item_model_config(self, content: str, source: str) -> List[Dish]:
        """解析ItemModelConfig.lua内容"""
        dishes = []
        
        # 移除注释
        content = re.sub(r'--.*?\n', '\n', content)
        
        # 查找所有菜品配置块（以ds_开头的为菜品）
        pattern = r'\{\s*Type\s*=\s*"(ds_[^"]+)"([^}]*)\}'
        matches = re.finditer(pattern, content, re.DOTALL)
        
        for match in matches:
            dish_code = match.group(1)
            dish_content = match.group(2)
            
            # 解析菜品信息
            dish = self._parse_dish_info(dish_code, dish_content, source)
            if dish:
                dishes.append(dish)
        
        return dishes
    
    def _parse_dish_info(self, dish_code: str, content: str, source: str) -> Dish:
        """解析单个菜品信息"""
        dish = Dish(
            dish_id=dish_code,
            source=source,
            source_id=dish_code
        )
        
        # 解析菜品名称（从代码推断）
        dish.name = self._generate_dish_name(dish_code)
        
        # 解析GeneratedItems作为食材需求
        generated_items = self._parse_generated_items(content)
        if generated_items:
            # 创建一个加工步骤
            step = ProcessingStep(
                step_number=1,
                ingredients=[item['Code'] for item in generated_items],
                equipment="",
                processing_time=0,
                result_name=dish.name
            )
            dish.processing_steps = [step]
        
        # 根据菜品代码推断餐厅主题
        theme_info = self._infer_restaurant_theme(dish_code)
        if theme_info:
            dish.restaurant_theme_id = theme_info[0]
            dish.restaurant_theme_name = theme_info[1]
        
        # 根据菜品代码推断国家
        country_info = self._infer_country(dish_code)
        if country_info:
            dish.country_id = country_info[0]
            dish.country_name = country_info[1]
        
        return dish
    
    def _parse_generated_items(self, content: str) -> List[Dict]:
        """解析GeneratedItems"""
        items = []
        
        # 查找GeneratedItems块
        pattern = r'GeneratedItems\s*=\s*\{([^}]*)\}'
        match = re.search(pattern, content)
        
        if match:
            items_content = match.group(1)
            
            # 解析每个物品
            item_pattern = r'\{\s*Code\s*=\s*"([^"]+)"\s*,\s*Weight\s*=\s*(\d+)\s*\}'
            item_matches = re.finditer(item_pattern, items_content)
            
            for item_match in item_matches:
                items.append({
                    'Code': item_match.group(1),
                    'Weight': int(item_match.group(2))
                })
        
        return items
    
    def _generate_dish_name(self, dish_code: str) -> str:
        """根据代码生成菜品名称"""
        # 简单的名称映射
        name_mapping = {
            'ds_grillmt': '烤肉',
            'ds_fd': '炸物',
            'ds_juice': '果汁',
            'ds_mixdrk': '混合饮料',
            'ds_dst': '甜点',
            'ds_friedve': '炸蔬菜',
            'ds_friedsf': '炸海鲜',
            'ds_grillsf': '烤海鲜',
            'ds_grillve': '烤蔬菜',
            'ds_chopfru': '切水果',
            'ds_chopfs': '切鱼',
            'ds_chopfr': '切肉',
            'ds_friedmt': '炸肉'
        }
        
        # 提取基础类型
        for key, name in name_mapping.items():
            if dish_code.startswith(key):
                # 提取数字后缀
                number_match = re.search(r'(\d+)$', dish_code)
                if number_match:
                    number = number_match.group(1)
                    return f"{name}{number}"
                return name
        
        return dish_code
    
    def _infer_restaurant_theme(self, dish_code: str) -> tuple:
        """根据菜品代码推断餐厅主题"""
        theme_mapping = {
            'grillmt': ('BBQ', '烧烤店'),
            'grillsf': ('SEAFOOD', '海鲜店'),
            'grillve': ('MARKET', '市场'),
            'friedmt': ('BBQ', '烧烤店'),
            'friedsf': ('SEAFOOD', '海鲜店'),
            'friedve': ('MARKET', '市场'),
            'juice': ('MARKET', '市场'),
            'mixdrk': ('MARKET', '市场'),
            'dst': ('BAKERY', '面包店'),
            'chopfru': ('MARKET', '市场'),
            'chopfs': ('SEAFOOD', '海鲜店'),
            'fd': ('MARKET', '市场')
        }
        
        for key, theme in theme_mapping.items():
            if key in dish_code:
                return theme
        
        return None
    
    def _infer_country(self, dish_code: str) -> tuple:
        """根据菜品代码推断国家"""
        # 简单的国家推断逻辑
        if 'sushi' in dish_code.lower():
            return ('JP', '日本')
        elif 'pasta' in dish_code.lower():
            return ('IT', '意大利')
        elif 'tacos' in dish_code.lower():
            return ('MX', '墨西哥')
        elif 'dimsum' in dish_code.lower():
            return ('CN', '中国')
        else:
            return ('US', '美国')  # 默认
    
    def import_from_json(self, json_path: str, source: str = "其他"):
        """从JSON文件导入菜品"""
        if not os.path.exists(json_path):
            raise FileNotFoundError(f"JSON文件不存在: {json_path}")
        
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        imported_count = 0
        
        # 假设JSON格式为菜品列表
        if isinstance(data, list):
            for dish_data in data:
                try:
                    dish = self._parse_json_dish(dish_data, source)
                    if dish:
                        self.db.save_dish(dish, "importer")
                        imported_count += 1
                except Exception as e:
                    print(f"导入菜品失败: {e}")
        
        return imported_count
    
    def _parse_json_dish(self, dish_data: dict, source: str) -> Dish:
        """解析JSON菜品数据"""
        dish = Dish(
            dish_id=dish_data.get('id', ''),
            name=dish_data.get('name', ''),
            source=source,
            source_id=dish_data.get('source_id', ''),
            restaurant_theme_id=dish_data.get('theme_id', ''),
            restaurant_theme_name=dish_data.get('theme_name', ''),
            country_id=dish_data.get('country_id', ''),
            country_name=dish_data.get('country_name', '')
        )
        
        # 解析加工步骤
        steps_data = dish_data.get('steps', [])
        for step_data in steps_data:
            step = ProcessingStep(
                step_number=step_data.get('step_number', 1),
                ingredients=step_data.get('ingredients', []),
                equipment=step_data.get('equipment', ''),
                processing_time=step_data.get('time', 0),
                result_name=step_data.get('result', '')
            )
            dish.processing_steps.append(step)
        
        return dish
    
    def export_to_json(self, output_path: str, dishes: List[Dish] = None):
        """导出菜品到JSON文件"""
        if dishes is None:
            # 导出所有菜品
            dishes = self.db.search_dishes()
        
        # 转换为JSON格式
        dishes_data = []
        for dish in dishes:
            dish_data = {
                'id': dish.dish_id,
                'name': dish.name,
                'source': dish.source,
                'source_id': dish.source_id,
                'theme_id': dish.restaurant_theme_id,
                'theme_name': dish.restaurant_theme_name,
                'country_id': dish.country_id,
                'country_name': dish.country_name,
                'created_time': dish.created_time,
                'updated_time': dish.updated_time,
                'steps': []
            }
            
            # 添加加工步骤
            for step in dish.processing_steps:
                step_data = {
                    'step_number': step.step_number,
                    'ingredients': step.ingredients,
                    'equipment': step.equipment,
                    'time': step.processing_time,
                    'result': step.result_name
                }
                dish_data['steps'].append(step_data)
            
            dishes_data.append(dish_data)
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(dishes_data, f, ensure_ascii=False, indent=2)
        
        return len(dishes_data)
    
    def export_to_excel(self, output_path: str, dishes: List[Dish] = None):
        """导出菜品到Excel文件"""
        try:
            import pandas as pd
        except ImportError:
            raise ImportError("需要安装pandas库: pip install pandas openpyxl")
        
        if dishes is None:
            dishes = self.db.search_dishes()
        
        # 准备数据
        rows = []
        for dish in dishes:
            # 基本信息行
            base_row = {
                '菜品ID': dish.dish_id,
                '菜品名称': dish.name,
                '来源': dish.source,
                '原项目ID': dish.source_id,
                '餐厅主题ID': dish.restaurant_theme_id,
                '餐厅主题名称': dish.restaurant_theme_name,
                '国家ID': dish.country_id,
                '国家名称': dish.country_name,
                '创建时间': dish.created_time,
                '更新时间': dish.updated_time
            }
            
            if not dish.processing_steps:
                # 没有加工步骤的菜品
                rows.append(base_row)
            else:
                # 有加工步骤的菜品，每个步骤一行
                for step in dish.processing_steps:
                    row = base_row.copy()
                    row.update({
                        '加工步骤': step.step_number,
                        '食材': ', '.join(step.ingredients),
                        '器械': step.equipment,
                        '加工时间(分钟)': step.processing_time,
                        '产出名称': step.result_name
                    })
                    rows.append(row)
        
        # 创建DataFrame并导出
        df = pd.DataFrame(rows)
        df.to_excel(output_path, index=False, engine='openpyxl')
        
        return len(rows)
