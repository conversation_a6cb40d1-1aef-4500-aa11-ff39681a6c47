"""
测试智能订单生成器
"""

import os
import sys

def test_order_generator():
    """测试订单生成器功能"""
    try:
        # 导入必要的模块
        from config_loader import ConfigLoader
        from order_generator import OrderGenerator
        from data_models import OrderGenerationConfig, BetMode, EfficiencyMode, PlayerGeneratorInfo
        
        print("=== 测试智能订单生成器 ===")
        
        # 1. 加载配置
        print("1. 加载配置文件...")
        config_path = os.path.join(os.path.dirname(__file__), "Data", "Config")
        config_loader = ConfigLoader(config_path)
        config_data = config_loader.load_all_configs()
        
        print(f"   加载了 {len(config_data.items)} 个物品")
        print(f"   加载了 {len(config_data.generators)} 个生成器")
        print(f"   加载了 {sum(len(orders) for orders in config_data.orders.values())} 个订单")
        
        # 2. 创建订单生成器
        print("2. 创建订单生成器...")
        order_generator = OrderGenerator(config_data)
        
        # 3. 配置生成参数
        print("3. 配置生成参数...")
        generation_config = OrderGenerationConfig()
        
        # 设置特色菜品
        generation_config.special_dishes = ["ds_chopve_1", "ds_chopve_2", "ds_flb_1", "ds_chopve_3"]
        
        # 设置体力范围
        generation_config.total_energy_range = (100.0, 200.0)
        generation_config.energy_fluctuation_ratio = 0.1
        
        # 设置体力消耗模式
        generation_config.bet_mode = BetMode.BET_2
        
        # 设置效率模式
        generation_config.efficiency_mode = EfficiencyMode.AVERAGE
        
        # 设置玩家生成器信息
        generation_config.player_generators = [
            PlayerGeneratorInfo(generator_type="pd_1_4", level=4, count=2),
            PlayerGeneratorInfo(generator_type="pd_1_5", level=5, count=1),
            PlayerGeneratorInfo(generator_type="pd_2_4", level=4, count=2),
        ]
        
        # 设置订单体力权重
        generation_config.order_energy_weights = [10, 10, 15, 15, 15, 20, 30]
        
        print("   配置完成")
        
        # 4. 生成订单方案
        print("4. 生成订单方案...")
        schemes = order_generator.generate_order_schemes(generation_config, 3)
        
        print(f"   成功生成 {len(schemes)} 套方案")
        
        # 5. 显示结果
        print("5. 生成结果:")
        for i, scheme in enumerate(schemes):
            print(f"\n=== 方案 {i+1} ===")
            print(f"总体力消耗: {scheme.total_energy:.2f}")
            print(f"方案有效性: {'有效' if scheme.is_valid else '无效'}")
            
            print("订单列表:")
            for j, order in enumerate(scheme.orders):
                print(f"  订单{j+1}: {order.order_id}")
                for req in order.requirements:
                    print(f"    需求: {req.item_type} x{req.count}")
                if hasattr(order, 'energy_cost'):
                    print(f"    体力消耗: {order.energy_cost:.2f}")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loader():
    """测试配置加载器"""
    try:
        print("=== 测试配置加载器 ===")
        
        from config_loader import ConfigLoader
        
        config_path = os.path.join(os.path.dirname(__file__), "Data", "Config")
        config_loader = ConfigLoader(config_path)
        config_data = config_loader.load_all_configs()
        
        print(f"物品数量: {len(config_data.items)}")
        print(f"生成器数量: {len(config_data.generators)}")
        print(f"章节数量: {len(config_data.orders)}")
        
        # 显示一些示例数据
        if config_data.items:
            item_sample = list(config_data.items.values())[0]
            print(f"示例物品: {item_sample.code}, 等级: {item_sample.level}, 系列: {item_sample.series}")
        
        if config_data.generators:
            gen_sample = list(config_data.generators.values())[0]
            print(f"示例生成器: {gen_sample.code}, 等级: {gen_sample.level}, 效率: {gen_sample.efficiency}")
        
        print("=== 配置加载测试完成 ===")
        return True
        
    except Exception as e:
        print(f"配置加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_energy_calculator():
    """测试能量计算器"""
    try:
        print("=== 测试能量计算器 ===")
        
        from config_loader import ConfigLoader
        from energy_calculator import EnergyCalculator
        from data_models import OrderRequirement, BetMode, EfficiencyMode, PlayerGeneratorInfo
        
        # 加载配置
        config_path = os.path.join(os.path.dirname(__file__), "Data", "Config")
        config_loader = ConfigLoader(config_path)
        config_data = config_loader.load_all_configs()
        
        # 创建能量计算器
        energy_calculator = EnergyCalculator(config_data)
        
        # 创建测试订单需求
        order_items = [
            OrderRequirement(item_type="it_1_1_2", count=1),
            OrderRequirement(item_type="it_1_1_3", count=1),
        ]
        
        # 创建玩家生成器信息
        player_generators = [
            PlayerGeneratorInfo(generator_type="pd_1_4", level=4, count=2),
            PlayerGeneratorInfo(generator_type="pd_1_5", level=5, count=1),
        ]
        
        # 计算能量消耗
        energy = energy_calculator.calculate_order_energy(
            order_items, BetMode.BET_2, EfficiencyMode.AVERAGE, player_generators
        )
        
        print(f"计算得到的能量消耗: {energy}")
        
        print("=== 能量计算测试完成 ===")
        return True
        
    except Exception as e:
        print(f"能量计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试订单生成器模块...")
    
    # 测试配置加载器
    if not test_config_loader():
        sys.exit(1)
    
    print()
    
    # 测试能量计算器
    if not test_energy_calculator():
        sys.exit(1)
    
    print()
    
    # 测试订单生成器
    if not test_order_generator():
        sys.exit(1)
    
    print("\n所有测试通过！")
