# 浮岛物语订单设计器 - 增强版

## 概述

基于需求背景2进行全面优化的智能订单设计器，支持精确的算法实现和完整的数据结构解析。

## 核心优化

### 1. 数据结构增强
基于需求背景2中的完整字段定义：

#### 生成器数据（LevelInitiativeProduce）
- **TapeItems**: 传送带物品配置
- **GeneratedItems**: 产出物品配置
- **Capacity**: 产出次数上限
- **Frequency**: 产出频率
- **Cd**: 冷却时间
- **UseEnergy**: 消耗体力
- **UnlockPrice**: 解锁价格
- **SpeedUpPrice**: 加速价格
- **InitialNumber**: 初始数量
- **MergedType**: 合成后类型
- **BookReward**: 图鉴奖励

#### 物品数据（LevelGoodsBase）
- **Category**: 物品分类
- **Score**: 物品分数
- **Generators**: 关联生成器
- **UseEnergy**: 消耗体力
- **DropsTotal**: 总掉落数
- **DropOnSpot**: 现场掉落
- **LevelDown**: 等级下降
- **Reward**: 奖励值

#### 器械数据（LevelInstrument）
- 完整的器械属性配置
- 效率系数和时间计算

#### 菜品数据（LevelGoodsCooking）
- 完整的菜品配方配置
- 加工时间和能量消耗

### 2. 算法精确实现
- **精确产出计算**: 基于production字段的准确算法
- **约束条件验证**: 完整的约束检查机制
- **算法一致性**: 多次运行结果的稳定性保证
- **性能优化**: 高效的数据处理和计算

### 3. 功能模块

#### 核心功能
- **精确算法生成**: 基于需求背景2的精确算法实现
- **批量生成对比**: 生成多套方案并进行对比分析
- **算法验证**: 完整的算法正确性验证机制
- **配置分析**: 深入的配置文件分析和统计
- **性能分析**: 算法性能监控和优化建议
- **数据导出**: 支持多种格式的结果导出

#### 界面增强
- **多标签页设计**: 结果展示和配置分析分离
- **实时状态显示**: 详细的操作状态和进度提示
- **友好的错误处理**: 完善的异常处理和用户提示
- **配置持久化**: 用户配置的保存和恢复

## 文件结构

```
fmc_lua/orderdesign/
├── main.py                           # 增强版主程序入口
├── data_models.py                     # 增强的数据模型定义
├── config_loader.py                   # 增强的配置文件加载器
├── order_generator.py                 # 智能订单生成器（含精确算法）
├── energy_calculator.py               # 体力消耗计算器
├── algorithm_validator.py             # 算法验证模块
├── README.md                         # 说明文档
└── ../Data/Config/                   # 配置文件目录
    ├── ItemModelConfig.lua           # 物品和生成器配置
    ├── OrderFixedConfig_*.lua        # 各章节订单配置
    └── ...                          # 其他配置文件
```

## 使用方法

### 启动程序
```bash
cd fmc_lua/orderdesign
python main.py
```

### 主要功能使用

#### 1. 精确算法生成
1. 启动程序后，系统自动加载配置文件
2. 在左侧配置面板设置参数：
   - **特色菜品**: 输入4-7个菜品代码（逗号分隔）
   - **体力范围**: 设置最小值和最大值
   - **体力模式**: 选择2bet/4bet/8bet
   - **效率模式**: 选择平均效率/动态效率/top3平均效率
3. 点击"精确生成"按钮
4. 在右侧"生成结果"标签页查看详细结果

#### 2. 批量生成对比
1. 配置相同的参数
2. 点击"批量生成"按钮
3. 系统生成10套方案并显示对比结果
4. 可以分析不同方案的体力消耗差异

#### 3. 配置分析
1. 程序启动时自动进行配置分析
2. 在"配置分析"标签页查看：
   - 物品和生成器统计
   - 系列分布情况
   - 订单数量统计
3. 通过菜单"分析" -> "配置分析"可重新分析

#### 4. 算法验证
1. 通过菜单"生成" -> "算法验证"
2. 系统运行综合验证测试
3. 检查数据完整性、约束满足、算法一致性
4. 生成详细的验证报告和优化建议

## 配置说明

### 输入配置
程序会自动从Config目录读取以下配置：
- **物品配置**: ItemModelConfig.lua
- **生成器配置**: 从ItemModelConfig.lua中的pd_开头项目解析
- **订单配置**: OrderFixedConfig_*.lua（按章节）

### 生成参数
- **特色菜品**: 4-7个指定菜品代码
- **体力消耗范围**: 总体力消耗的最小值和最大值
- **订单体力权重**: 7个订单的体力分配权重（默认：[10,10,15,15,15,20,30]）
- **体力消耗模式**: 2bet/4bet/8bet
- **生成器效率模式**: 平均效率/动态效率/top3平均效率

## 输出结果

每套生成的方案包含：
- **订单内容**: 7个订单的物品组成（含特色菜品标记）
- **体力消耗**: 总体力值、7个订单的分步消耗
- **生成器指标**: 各类生成器点击次数、轮数、使用物品最高等级
- **器械指标**: 使用次数、总时长
- **剩余物品**: 折合1级数量（分系列）、实际物品数量
- **最优完成顺序**: 按体力消耗升序的最优提交顺序

## 快捷键

- **Ctrl+N**: 新建订单
- **Ctrl+S**: 保存订单
- **Ctrl+D**: 删除订单
- **Ctrl+O**: 加载配置
- **Ctrl+Q**: 退出程序
- **F1**: 显示帮助

## 注意事项

1. 确保Config目录下有正确的配置文件
2. 特色菜品数量必须在4-7个之间
3. 生成的方案会自动校验约束条件
4. 如果生成失败，请检查配置参数是否合理

## 扩展功能

程序采用模块化设计，可以方便地扩展以下功能：
- 添加新的效率计算模式
- 支持更多约束条件
- 自定义订单模板
- 导出生成结果到Excel
- 历史方案管理

## 技术架构

- **界面框架**: Tkinter
- **数据模型**: dataclass + typing
- **配置解析**: 正则表达式解析Lua配置文件
- **算法实现**: 按需求文档的6阶段算法
- **模块化设计**: 便于维护和扩展
