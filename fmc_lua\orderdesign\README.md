# 浮岛物语订单编辑器

## 概述

这是一个基于需求文档完善的智能订单编辑器，支持根据策划输入的参数自动生成符合标准的随机订单方案。

## 功能特性

### 1. 基础订单编辑功能
- 创建、编辑、删除订单
- 管理订单需求和奖励
- 支持多章节管理
- 实时体力消耗计算

### 2. 智能订单生成功能（新增）
- **特色菜品配置**: 支持4-7个指定菜品
- **体力消耗控制**: 可设置总体力消耗范围和波动比例
- **多种效率模式**: 平均效率/动态效率/top3平均效率
- **多种体力模式**: 2bet/4bet/8bet
- **约束校验**: 自动校验生成器、器械、物品等约束条件
- **批量生成**: 一次生成多套方案供选择

### 3. 算法实现
按照需求文档实现的6个阶段算法：
- **阶段A**: 计算特色菜品消耗
- **阶段B**: 分配最高等级物品
- **阶段C**: 订单顺序匹配
- **阶段D**: 剩余物品补全
- **阶段E**: 校验与修正
- **阶段F**: 最优完成顺序计算

## 文件结构

```
fmc_lua/
├── main.py                           # 主程序入口
├── orderCreaterzuluwindcontinuezoo.py # 主界面和原有功能
├── data_models.py                     # 数据模型定义
├── config_loader.py                   # 配置文件加载器
├── order_generator.py                 # 智能订单生成器
├── energy_calculator.py               # 体力消耗计算器
├── Data/Config/                       # 配置文件目录
│   ├── ItemModelConfig.lua           # 物品和生成器配置
│   ├── OrderFixedConfig_*.lua        # 各章节订单配置
│   └── ...                          # 其他配置文件
└── Board/                            # 棋盘相关模块
```

## 使用方法

### 启动程序
```bash
python main.py
```

### 基础订单编辑
1. 选择章节
2. 点击"新建订单"或双击现有订单
3. 编辑订单需求和奖励
4. 保存订单

### 智能订单生成
1. 点击菜单"生成" -> "智能订单生成器"
2. 配置生成参数：
   - 特色菜品（用逗号分隔，如：ds_chopve_1,ds_chopve_2,ds_flb_1,ds_chopve_3）
   - 总体力消耗范围（最小值-最大值）
   - 体力消耗模式（2bet/4bet/8bet）
   - 生成器效率模式
3. 点击"生成方案"
4. 查看生成结果

## 配置说明

### 输入配置
程序会自动从Config目录读取以下配置：
- **物品配置**: ItemModelConfig.lua
- **生成器配置**: 从ItemModelConfig.lua中的pd_开头项目解析
- **订单配置**: OrderFixedConfig_*.lua（按章节）

### 生成参数
- **特色菜品**: 4-7个指定菜品代码
- **体力消耗范围**: 总体力消耗的最小值和最大值
- **订单体力权重**: 7个订单的体力分配权重（默认：[10,10,15,15,15,20,30]）
- **体力消耗模式**: 2bet/4bet/8bet
- **生成器效率模式**: 平均效率/动态效率/top3平均效率

## 输出结果

每套生成的方案包含：
- **订单内容**: 7个订单的物品组成（含特色菜品标记）
- **体力消耗**: 总体力值、7个订单的分步消耗
- **生成器指标**: 各类生成器点击次数、轮数、使用物品最高等级
- **器械指标**: 使用次数、总时长
- **剩余物品**: 折合1级数量（分系列）、实际物品数量
- **最优完成顺序**: 按体力消耗升序的最优提交顺序

## 快捷键

- **Ctrl+N**: 新建订单
- **Ctrl+S**: 保存订单
- **Ctrl+D**: 删除订单
- **Ctrl+O**: 加载配置
- **Ctrl+Q**: 退出程序
- **F1**: 显示帮助

## 注意事项

1. 确保Config目录下有正确的配置文件
2. 特色菜品数量必须在4-7个之间
3. 生成的方案会自动校验约束条件
4. 如果生成失败，请检查配置参数是否合理

## 扩展功能

程序采用模块化设计，可以方便地扩展以下功能：
- 添加新的效率计算模式
- 支持更多约束条件
- 自定义订单模板
- 导出生成结果到Excel
- 历史方案管理

## 技术架构

- **界面框架**: Tkinter
- **数据模型**: dataclass + typing
- **配置解析**: 正则表达式解析Lua配置文件
- **算法实现**: 按需求文档的6阶段算法
- **模块化设计**: 便于维护和扩展
