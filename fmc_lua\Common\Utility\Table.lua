Table = {}
Table.Empty = setmetatable({}, {
  __index = {},
  __newindex = function(t, k, v)
    Log.Error("cannot set in Table.Empty")
  end
})

function Table.ShallowCopy(source)
  if source == nil then
    return nil
  end
  local newTable = {}
  for k, v in pairs(source) do
    newTable[k] = v
  end
  return newTable
end

function Table.ShallowCopyArray(source)
  if source == nil then
    return nil
  end
  local newTable = {}
  local element
  for _, v in ipairs(source) do
    element = {}
    for k, v in pairs(source[_]) do
      element[k] = v
    end
    newTable[_] = element
  end
  return newTable
end

function Table.DeepCopy(object, ignoreMeta)
  local lookup_table = {}
  
  local function _copy(object)
    if type(object) ~= "table" then
      return object
    elseif lookup_table[object] then
      return lookup_table[object]
    end
    local new_table = {}
    lookup_table[object] = new_table
    for key, value in pairs(object) do
      new_table[_copy(key)] = _copy(value)
    end
    return ignoreMeta and new_table or setmetatable(new_table, getmetatable(object))
  end
  
  return _copy(object)
end

function Table.GetMapSize(map)
  local size = 0
  if map then
    for _, _ in pairs(map) do
      size = size + 1
    end
  end
  return size
end

function Table.ListAppend(to, from)
  table.move(from, 1, #from, #to + 1, to)
end

function Table.ListRemove(source, element)
  local count = #source
  for i = 1, count do
    if source[i] == element then
      table.remove(source, i)
      return true
    end
  end
  return false
end

function Table.ListContain(source, target)
  local count = #source
  for i = 1, count do
    if source[i] == target then
      return i
    end
  end
  return false
end

function Table.ListMerge(mergeTo, toMerge)
  local mapMerged = {}
  for i = 1, #mergeTo do
    mapMerged[mergeTo[i]] = true
  end
  for i = 1, #toMerge do
    mapMerged[toMerge[i]] = true
  end
  return Table.GetKeys(mapMerged)
end

function Table.ListRandomSelectN(source, selectCount)
  if selectCount >= #source then
    return source
  end
  local results = {}
  for i = 1, selectCount do
    local index = math.random(#source)
    table.insert(results, source[index])
    table.remove(source, index)
  end
  return results
end

function Table.ListRandomSelectOne(source)
  if #source == 0 then
    return nil
  end
  local randomIndex = math.random(#source)
  return source[randomIndex]
end

function Table.ListWeightSelectOne(source, weightKey)
  weightKey = weightKey or "Weight"
  local sumWeight = 0
  for _, item in ipairs(source) do
    sumWeight = sumWeight + item[weightKey]
  end
  local randomIndex = math.random(math.floor(sumWeight))
  for index, item in ipairs(source) do
    if randomIndex <= item[weightKey] then
      return item, index
    else
      randomIndex = randomIndex - item[weightKey]
    end
  end
  Log.Assert(false, "Table.ListWeightSelectOne")
  return nil
end

function Table.ListWeightSelectN(source, weightKey, selectCount)
  Log.Assert(selectCount ~= nil and 0 < selectCount, "Table.ListWeightSelectN selectCount error !!!")
  if selectCount == 1 then
    local item, index = Table.ListWeightSelectOne(source, weightKey)
    return {item}
  end
  weightKey = weightKey or "Weight"
  local sumWeight = 0
  for _, item in ipairs(source) do
    sumWeight = sumWeight + item[weightKey]
  end
  local arrRandomIndex = {}
  for i = 1, selectCount do
    arrRandomIndex[i] = math.random(sumWeight)
  end
  local arrResult = {}
  for _, item in ipairs(source) do
    for i, weight in ipairs(arrRandomIndex) do
      if 0 < weight then
        if weight <= item[weightKey] then
          arrResult[i] = item
          if Table.GetMapSize(arrResult) == selectCount then
            return arrResult
          end
        end
        arrRandomIndex[i] = weight - item[weightKey]
      end
    end
  end
  Log.Assert(false, "Table.ListWeightSelectN")
end

function Table.ListRemoveDuplicate(list)
  local map = {}
  local new = {}
  local value
  local count = #list
  for i = 1, count do
    value = list[i]
    if not map[value] then
      map[value] = true
      new[#new + 1] = value
    end
  end
  return new
end

function Table.Contain(source, target)
  for _, value in pairs(source) do
    if value == target then
      return true
    end
  end
  return false
end

function Table.GetIndex(list, target)
  local count = #list
  for i = 1, count do
    if list[i] == target then
      return i
    end
  end
  return 0
end

function Table.GetIndexWithFilter(list, filter)
  local count = #list
  for i = 1, count do
    if filter(list[i]) then
      return i
    end
  end
  return 0
end

function Table.GetKey(map, target)
  for key, value in pairs(map) do
    if value == target then
      return key
    end
  end
  return nil
end

function Table.Select(source, filter)
  local result = {}
  for _, value in pairs(source) do
    if filter(value) then
      table.insert(result, value)
    end
  end
  return result
end

function Table.ListSelect(source, filter)
  local result = {}
  local count = #source
  for i = 1, count do
    local value = source[i]
    if filter(value) then
      table.insert(result, value)
    end
  end
  return result
end

function Table.Foreach(source, func)
  for _, value in pairs(source) do
    func(value)
  end
end

function Table.ListForeach(source, func)
  local count = #source
  for i = 1, count do
    func(i, source[i])
  end
end

function Table.GetKeys(source)
  local keys = {}
  local count = 0
  for key, _ in pairs(source) do
    count = count + 1
    keys[count] = key
  end
  return keys
end

function Table.GetValueList(source)
  local values = {}
  local count = 0
  for _, value in pairs(source) do
    count = count + 1
    values[count] = value
  end
  return values
end

function Table.IsEmpty(source)
  return IsNil(source) or type(source) ~= "table" or next(source) == nil
end

function Table.Reverse(source)
  local list = {}
  for i = 1, #source do
    list[#source - i + 1] = source[i]
  end
  return list
end

function Table.ListRep(ele, repCount, deepCopy)
  local list = {}
  for i = 1, repCount do
    list[#list + 1] = deepCopy and Table.DeepCopy(ele) or ele
  end
  return list
end

function Table.ListAlwaysRandomSelectN(source, selectCount)
  local result = {}
  if Table.IsEmpty(source) then
    return result
  end
  local tb = Table.DeepCopy(source)
  for i = 1, math.min(#tb, selectCount) do
    local index = math.random(#tb)
    result[#result + 1] = tb[index]
    table.remove(tb, index)
  end
  return result
end

function Table.ListRandomReorder(array)
  for i = #array, 1, -1 do
    local random = math.random(#array)
    local tmp = array[random]
    array[random] = array[i]
    array[i] = tmp
  end
end

function Table.IsSubArray(aArray, bArray)
  for _, b in ipairs(bArray) do
    if not Table.ListContain(aArray, b) then
      return false
    end
  end
  return true
end

function Table.Shuffle(source)
  return Table.ListRandomSelectN(source, #source)
end

function Table.CheckListEqual(aList, bList)
  local mapNum = {}
  for _, value in ipairs(aList or {}) do
    if mapNum[value] == nil then
      mapNum[value] = 0
    end
    mapNum[value] = mapNum[value] + 1
  end
  for _, value in ipairs(bList or {}) do
    if mapNum[value] == nil then
      return false
    end
    mapNum[value] = mapNum[value] - 1
  end
  for _, num in pairs(mapNum) do
    if num ~= 0 then
      return false
    end
  end
  return true
end
