ProgressActivityRewardProgressWindow = setmetatable({}, ProgressActivityBaseWindow)
ProgressActivityRewardProgressWindow.__index = ProgressActivityRewardProgressWindow

function ProgressActivityRewardProgressWindow:Init(eActvitiyType)
  ProgressActivityBaseWindow.Init(self, eActvitiyType)
  self.m_LevelRewardAreaLuaTable:Init(self.m_model)
  self.m_SliderLua:Init(self.m_model)
  self.m_SliderLua:SetCanClicked(false)
  self:UpdateContent()
  self:UpdatePerSecond()
  local sortingOrder = self:GetSortingOrder()
  self.m_spriteMask.frontSortingOrder = sortingOrder + 2
  self.m_spriteMask.backSortingOrder = sortingOrder - 1
  self.m_spriteMask.transform:SetLocalScaleX(self.m_scrollView.transform.rect.size.x / 100)
  self.m_spriteMask.transform:SetLocalScaleY(self.m_scrollView.transform.rect.size.y / 100)
end

function ProgressActivityRewardProgressWindow:UpdateContent()
  self.m_SliderLua:UpdateContent()
  self.m_LevelRewardAreaLuaTable:UpdateContent()
end

function ProgressActivityRewardProgressWindow:UpdatePerSecond()
  if self.m_model ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

ProgressActivityLevelRewardArea = setmetatable({RewardCellHeight = 210}, LevelRewardArea)
ProgressActivityLevelRewardArea.__index = ProgressActivityLevelRewardArea

function ProgressActivityLevelRewardArea:Init(model)
  self.m_model = model
  LevelRewardArea.Init(self, #self.m_model:GetLevelConfigs())
  self.m_sliderBgNode:SetLocalScaleY(self.m_sliderNode.rect.size.y / 4)
end

function ProgressActivityLevelRewardArea:UpdateContent()
  self:_UpdateLevelContent()
end

function ProgressActivityLevelRewardArea:GetProgressCell(level)
  local cellObject = Object.Instantiate(self.m_progressCell, self.m_sliderNode)
  local cell = cellObject:GetLuaTable()
  cell:Init(self.m_model, level)
  return cell
end

function ProgressActivityLevelRewardArea:GetRewardCell(level)
  local cellObject = Object.Instantiate(self.m_rewardCell, self.m_rewardsNode)
  local cell = cellObject:GetLuaTable()
  cell:Init(self.m_model, level)
  return cell
end

function ProgressActivityLevelRewardArea:GetCurrentLevel()
  return self.m_model:GetLevel() - 1
end

function ProgressActivityLevelRewardArea:_UpdatePositionContent()
  local currentLevel = self:GetCurrentLevel()
  local level = self.m_model:GetLevel()
  local prefent = self.m_model:GetActivityTokenNumber() / self.m_model:GetLevelRequire(level)
  local height = self.RewardCellHeight * (currentLevel - 0.5 + prefent) + self.m_topPadding
  UIUtil.SetSizeDelta(self.m_sliderFillNode, nil, height)
  self:JumpToLevel(currentLevel)
end

ProgressActivityLevelProgressCell = setmetatable({}, LevelProgressCell)
ProgressActivityLevelProgressCell.__index = ProgressActivityLevelProgressCell

function ProgressActivityLevelProgressCell:Init(activityModel, level)
  LevelProgressCell.Init(self, level)
  self.m_levelText.text = level
end

function ProgressActivityLevelProgressCell:UpdateLevelContent(currentLevel)
  if currentLevel < self.m_level then
    self.m_bgImg.sprite = self.m_bgImg_back1
  else
    self.m_bgImg.sprite = self.m_bgImg_back2
  end
  self.m_effectGo:SetActive(currentLevel + 1 == self.m_level)
end

ProgressActivityLevelRewardSingleCell = setmetatable({}, LevelRewardCell)
ProgressActivityLevelRewardSingleCell.__index = ProgressActivityLevelRewardSingleCell

function ProgressActivityLevelRewardSingleCell:Init(model, level)
  self.m_model = model
  LevelRewardCell.Init(self, level)
end

function ProgressActivityLevelRewardSingleCell:UpdateLevelContent(level)
  UIUtil.SetActive(self.m_SignleObject, false)
  UIUtil.SetActive(self.m_OKGo, false)
  UIUtil.SetActive(self.m_lock_singleGo, false)
  local levelRewradConfig = self.m_model:GetLevelConfigs()
  local EleConfig = levelRewradConfig[self.m_level]
  local normalReward = EleConfig.Rewards
  if level >= self.m_level then
    UIUtil.SetActive(self.m_SignleObject, true)
    UIUtil.SetActive(self.m_rewardSingleLuaTable.gameObject, false)
    UIUtil.SetActive(self.m_OKGo, true)
    self.m_rewardSingleLuaTable:Init(normalReward)
  elseif self.m_level >= level + 1 then
    UIUtil.SetActive(self.m_SignleObject, true)
    self.m_rewardSingleLuaTable:Init(normalReward)
  end
  UIUtil.SetActive(self.m_lock_singleGo, self.m_level > level + 1)
  self:UpdatePerSecond()
end

function ProgressActivityLevelRewardSingleCell:UpdatePerSecond()
end
