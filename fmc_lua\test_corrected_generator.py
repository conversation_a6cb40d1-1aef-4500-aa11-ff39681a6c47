"""
测试修正后的店铺订单生成器
验证每天7个订单，每个订单1-2个物品的逻辑
"""

import os
import sys

def test_corrected_order_generation():
    """测试修正后的订单生成"""
    print("=== 测试修正后的订单生成 ===")
    
    try:
        from shop_order_simple import SimpleShopOrderGenerator, ShopOrderPlan, DayOrder, SingleOrder
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = SimpleShopOrderGenerator(root)
        generator._load_config_data()
        
        # 设置测试参数
        generator.selected_shop.set("BBQ")
        generator.selected_chapter.set(4)  # 使用章节4来对比
        generator.generation_days.set(3)  # 生成3天的订单
        generator.energy_mode.set("平衡")
        generator.max_energy_per_day.set(80)
        generator.min_energy_per_day.set(20)
        
        # 生成订单
        generator._generate_orders()
        
        if generator.current_plan:
            plan = generator.current_plan
            print(f"✓ 订单生成成功")
            print(f"  店铺: {plan.shop_name}")
            print(f"  章节: {plan.chapter_id}")
            print(f"  总天数: {plan.total_days}")
            print(f"  总能量: {plan.total_energy:.1f}")
            print(f"  平均每日能量: {plan.total_energy/plan.total_days:.1f}")
            
            print("\n每日订单详情:")
            for day_order in plan.daily_orders:
                print(f"\n--- 第{day_order.day}天 ---")
                print(f"  订单数量: {len(day_order.orders)}个")
                print(f"  总能量: {day_order.total_energy:.1f}")
                print(f"  难度: {day_order.difficulty}")
                print(f"  预估时间: {day_order.estimated_time}分钟")
                
                print("  组奖励:")
                for reward in day_order.group_rewards:
                    print(f"    • {reward['Currency']} x{reward['Amount']}")
                
                print("  订单详情:")
                for order_idx, single_order in enumerate(day_order.orders):
                    print(f"    订单{order_idx+1} ({single_order.order_id}):")
                    print(f"      需求数量: {len(single_order.requirements)}个")
                    
                    for req_idx, req in enumerate(single_order.requirements):
                        item_code = req['Type']
                        count = req['Count']
                        item = generator.items_data.get(item_code)
                        if item:
                            energy = item.energy_cost * count
                            print(f"        需求{req_idx+1}: {item.name or item_code} x{count} (能量: {energy:.1f})")
                        else:
                            print(f"        需求{req_idx+1}: {item_code} x{count}")
                    
                    if single_order.rewards:
                        print("      订单奖励:")
                        for reward in single_order.rewards:
                            print(f"        • {reward['Currency']} x{reward['Amount']}")
                    else:
                        print("      订单奖励: 无")
            
            # 验证结构正确性
            print(f"\n=== 结构验证 ===")
            total_orders = sum(len(day_order.orders) for day_order in plan.daily_orders)
            total_requirements = sum(
                len(single_order.requirements) 
                for day_order in plan.daily_orders 
                for single_order in day_order.orders
            )
            
            print(f"总订单数: {total_orders}")
            print(f"总需求数: {total_requirements}")
            print(f"平均每天订单数: {total_orders/plan.total_days:.1f}")
            print(f"平均每订单需求数: {total_requirements/total_orders:.1f}")
            
            # 检查是否符合预期（每天7个订单）
            all_days_correct = True
            for day_order in plan.daily_orders:
                if len(day_order.orders) != 7:
                    print(f"⚠️  第{day_order.day}天订单数不正确: {len(day_order.orders)}个（应为7个）")
                    all_days_correct = False
                
                for single_order in day_order.orders:
                    req_count = len(single_order.requirements)
                    if req_count < 1 or req_count > 2:
                        print(f"⚠️  订单{single_order.order_id}需求数不正确: {req_count}个（应为1-2个）")
                        all_days_correct = False
            
            if all_days_correct:
                print("✓ 所有天数都有7个订单，每个订单1-2个需求")
            else:
                print("✗ 订单结构不符合预期")
        else:
            print("✗ 订单生成失败: 没有生成计划")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 订单生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_structure_comparison():
    """测试订单结构对比"""
    print("\n=== 测试订单结构对比 ===")
    
    try:
        # 读取实际的OrderFixedConfig_4.lua来对比结构
        config_path = os.path.join(os.path.dirname(__file__), "Data", "Config", "OrderFixedConfig_4.lua")
        
        if not os.path.exists(config_path):
            print(f"配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单统计实际配置中的订单结构
        import re
        
        # 统计GroupId分布
        group_pattern = r'GroupId\s*=\s*(\d+)'
        groups = re.findall(group_pattern, content)
        
        # 统计需求数量
        req1_pattern = r'Requirement_1\s*=\s*\{'
        req2_pattern = r'Requirement_2\s*=\s*\{'
        
        req1_count = len(re.findall(req1_pattern, content))
        req2_count = len(re.findall(req2_pattern, content))
        
        print(f"实际配置文件分析:")
        print(f"  总订单数: {req1_count}")
        print(f"  有Requirement_1的订单: {req1_count}")
        print(f"  有Requirement_2的订单: {req2_count}")
        print(f"  平均每订单需求数: {(req1_count + req2_count) / req1_count:.1f}")
        
        # 统计每组订单数
        from collections import Counter
        group_counts = Counter(groups)
        print(f"  组数: {len(group_counts)}")
        print(f"  每组订单数分布:")
        for group_id, count in sorted(group_counts.items(), key=lambda x: int(x[0])):
            print(f"    组{group_id}: {count}个订单")
        
        # 检查是否每组都是7个订单
        expected_orders_per_group = 7
        all_groups_correct = all(count == expected_orders_per_group for count in group_counts.values())
        
        if all_groups_correct:
            print(f"✓ 所有组都有{expected_orders_per_group}个订单")
        else:
            print(f"⚠️  不是所有组都有{expected_orders_per_group}个订单")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_energy_calculation_accuracy():
    """测试能量计算准确性"""
    print("\n=== 测试能量计算准确性 ===")
    
    try:
        from shop_order_simple import SimpleShopOrderGenerator
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = SimpleShopOrderGenerator(root)
        generator._load_config_data()
        
        # 测试几个已知物品的能量消耗
        test_items = ['it_2_3_1_2', 'ds_grillmt_7', 'ds_fd_21', 'clean']
        
        print("测试物品能量消耗:")
        for item_code in test_items:
            if item_code in generator.items_data:
                item = generator.items_data[item_code]
                print(f"  {item_code}: {item.energy_cost}能量, 类型:{item.type}, 等级:{item.level}")
            else:
                print(f"  {item_code}: 未找到")
        
        # 生成一个简单的订单并验证能量计算
        generator.generation_days.set(1)
        generator.max_energy_per_day.set(50)
        generator.min_energy_per_day.set(30)
        generator._generate_orders()
        
        if generator.current_plan and generator.current_plan.daily_orders:
            day_order = generator.current_plan.daily_orders[0]
            
            print(f"\n生成的第1天订单能量验证:")
            print(f"  总能量: {day_order.total_energy:.1f}")
            
            calculated_energy = 0
            for single_order in day_order.orders:
                order_energy = 0
                for req in single_order.requirements:
                    item_code = req['Type']
                    count = req['Count']
                    if item_code in generator.items_data:
                        item_energy = generator.items_data[item_code].energy_cost * count
                        order_energy += item_energy
                        calculated_energy += item_energy
                
                print(f"    订单{single_order.order_id}: {order_energy:.1f}能量")
            
            print(f"  手动计算总能量: {calculated_energy:.1f}")
            print(f"  能量计算{'正确' if abs(day_order.total_energy - calculated_energy) < 0.1 else '错误'}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 能量计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修正后的店铺订单生成器...")
    
    tests = [
        test_corrected_order_generation,
        test_order_structure_comparison,
        test_energy_calculation_accuracy
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！修正后的订单生成器工作正常。")
        print("\n修正要点:")
        print("• 每天生成7个订单（符合实际配置）")
        print("• 每个订单包含1-2个物品需求")
        print("• 组奖励在完成所有订单后获得")
        print("• 能量计算基于物品的UseEnergy属性")
        print("\n可以运行以下命令启动程序:")
        print("python shop_order_simple.py")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
