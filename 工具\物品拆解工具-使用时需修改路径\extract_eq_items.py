import os
import re
import json
import time
import traceback
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from pathlib import Path

def write_to_excel(items, output_file):
    if not items:
        print("没有找到任何物品，不创建Excel文件")
        return

    print(f"准备写入Excel文件: {output_file}")
    try:
        wb = Workbook()
        ws = wb.active
        
        # 定义表头
        headers = [
            'Type', 'MergedType', 'Category', 'BookOrder', 'BookReward',
            'UnlockPrice', 'ProtectLevel', 'Recipes'
        ]
        
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
            ws.cell(row=1, column=col).font = ws.cell(row=1, column=col).font.copy(bold=True)
        
        # 写入数据
        items.sort(key=lambda x: x['Type'])
        for row, item in enumerate(items, 2):
            for col, header in enumerate(headers, 1):
                if header in item:
                    # 对于复杂数据类型，将其转换为字符串形式
                    if isinstance(item[header], (list, dict)):
                        ws.cell(row=row, column=col, value=json.dumps(item[header], ensure_ascii=False))
                    else:
                        ws.cell(row=row, column=col, value=item[header])
        
        # 调整列宽以适应内容
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 30
            
        wb.save(output_file)
        print(f"Excel文件已保存到: {output_file}")
        
    except Exception as e:
        print(f"写入Excel文件时出错: {str(e)}")
        print(traceback.format_exc())

def extract_eq_items_from_lua(file_path):
    """提取eq物品信息的主函数"""
    print(f"\n开始处理文件: {file_path}")
    if not os.path.exists(file_path):
        print(f"错误：文件不存在: {file_path}")
        return []
    
    items = []
    processed_types = set()  # 用于跟踪已处理的物品类型
    
    try:
        file_size = os.path.getsize(file_path)
        print(f"文件大小: {file_size/1024/1024:.2f} MB")
        
        start_time = time.time()
        
        # 读取文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        
        # 分割文件内容为行
        lines = content.split('\n')
        line_count = len(lines)
        print(f"文件共有 {line_count} 行")
        
        # 首先找到所有eq物品的位置
        eq_positions = []
        type_pattern = re.compile(r'Type\s*=\s*"(eq_[^"]+)"')
        
        for i, line in enumerate(lines):
            match = type_pattern.search(line)
            if match:
                eq_positions.append((i, match.group(1)))
        
        print(f"找到 {len(eq_positions)} 个可能的eq物品定义")
        
        # 处理每个找到的位置
        for pos, eq_type in eq_positions:
            try:
                # 如果这个类型已经处理过，跳过
                if eq_type in processed_types:
                    continue
                
                processed_types.add(eq_type)
                
                # 查找完整的物品块
                start_line, end_line = find_item_block(lines, pos)
                if start_line is None or end_line is None:
                    print(f"警告：无法找到物品 {eq_type} 的完整定义块")
                    continue
                
                # 提取物品块内容
                item_block = '\n'.join(lines[start_line:end_line+1])
                
                # 解析物品信息
                item = {'Type': eq_type}
                
                # 提取MergedType
                merged_type_match = re.search(r'MergedType\s*=\s*"([^"]+)"', item_block)
                if merged_type_match:
                    item['MergedType'] = merged_type_match.group(1)
                
                # 提取Category数组
                category_match = re.search(r'Category\s*=\s*{([^}]*)}', item_block)
                if category_match:
                    category_str = category_match.group(1).strip()
                    if category_str:
                        categories = []
                        for cat in category_str.split(','):
                            cat = cat.strip()
                            if cat and cat.isdigit():
                                categories.append(int(cat))
                        item['Category'] = categories
                
                # 提取数值型属性
                for attr in ['BookOrder', 'UnlockPrice', 'ProtectLevel']:
                    attr_match = re.search(rf'{attr}\s*=\s*(\d+(?:\.\d+)?)', item_block)
                    if attr_match:
                        value = attr_match.group(1)
                        item[attr] = float(value) if '.' in value else int(value)
                
                # 提取BookReward
                book_reward_section = re.search(r'BookReward\s*=\s*{([^}]*{[^}]*}[^}]*)}', item_block)
                if book_reward_section:
                    book_reward_text = book_reward_section.group(1)
                    book_rewards = []
                    
                    for reward_match in re.finditer(r'{([^}]+)}', book_reward_text):
                        reward_content = reward_match.group(1)
                        currency = re.search(r'Currency\s*=\s*"([^"]+)"', reward_content)
                        amount = re.search(r'Amount\s*=\s*(\d+)', reward_content)
                        
                        if currency and amount:
                            book_rewards.append({
                                'Currency': currency.group(1),
                                'Amount': int(amount.group(1))
                            })
                    
                    if book_rewards:
                        item['BookReward'] = book_rewards
                
                # 提取Recipes
                recipes_section = re.search(r'Recipes\s*=\s*{([^}]*{[^}]*}[^}]*)*}', item_block)
                if recipes_section:
                    recipes_text = recipes_section.group(1)
                    recipes = []
                    
                    for recipe_match in re.finditer(r'{([^}]+)}', recipes_text):
                        recipe_content = recipe_match.group(1)
                        recipe = re.search(r'Recipe\s*=\s*"([^"]+)"', recipe_content)
                        dur = re.search(r'Dur\s*=\s*(\d+)', recipe_content)
                        
                        if recipe and dur:
                            recipes.append({
                                'Recipe': recipe.group(1),
                                'Dur': int(dur.group(1))
                            })
                    
                    if recipes:
                        item['Recipes'] = recipes
                
                # 添加到结果列表
                items.append(item)
                if len(items) % 20 == 0:
                    print(f"已成功解析 {len(items)} 个物品...")
            
            except Exception as e:
                print(f"处理物品时出错: {str(e)}")
                print(traceback.format_exc())
                continue
        
        end_time = time.time()
        print(f"文件处理完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"成功提取 {len(items)} 个有效eq物品")
        
    except Exception as e:
        print(f"处理文件时发生错误:")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print("详细错误信息:")
        print(traceback.format_exc())
    
    return items

def find_item_block(lines, start_idx):
    """查找完整的物品定义块"""
    open_brackets = 0
    start_line = -1
    end_line = -1
    
    # 向上查找起始行
    i = start_idx
    while i >= 0:
        line = lines[i].strip()
        if line.startswith('{') and not line.startswith('--'):
            start_line = i
            break
        i -= 1
    
    if start_line == -1:
        return None, None
    
    # 向下查找结束行
    for i in range(start_line, len(lines)):
        line = lines[i]
        for char in line:
            if char == '{':
                open_brackets += 1
            elif char == '}':
                open_brackets -= 1
                if open_brackets == 0:
                    end_line = i
                    return start_line, end_line
    
    return None, None

def main():
    """主函数"""
    # 获取桌面路径
    desktop = os.path.expanduser("~/Desktop")
    

    # 处理ItemModelConfig.lua
    config_path = "d:/MCWork/GHabout/FMC/fmc_lua_1.18.5/fmc_lua/Data/Config/ItemModelConfig.lua"
    output_path = os.path.join(desktop, "ItemModelConfig_eq_items.xlsx")
    
    items = extract_eq_items_from_lua(config_path)
    write_to_excel(items, output_path)



if __name__ == "__main__":
    main() 