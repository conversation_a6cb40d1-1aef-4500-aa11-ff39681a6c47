EnergyBoostModel = {}
EnergyBoostModel.__index = EnergyBoostModel
local TRIGGER_DURATION_IN_MINS = 30
local TRIGGER_DURATION_IN_SECONDS = TRIGGER_DURATION_IN_MINS * 60

function EnergyBoostModel:Init()
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self.OnLoginFinished)
  EventDispatcher.AddListener(EEventType.OnAddEnergy, self, self.OnAddEnergy)
end

function EnergyBoostModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function EnergyBoostModel:OnLoginFinished()
  local on = GM.ConfigModel:IsEnergyBoostConfigOn()
  if on ~= self.m_bBoostOn then
    self.m_bBoostOn = on
    EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
  end
  local bTriggerOn, triggerCount = GM.ConfigModel:IsEnergyBoostTriggerConfigOn()
  bTriggerOn = bTriggerOn and not self.m_bBoostOn
  if bTriggerOn ~= self.m_bBoostTriggerOn then
    self.m_bBoostTriggerOn = bTriggerOn
  end
  self.m_triggerCount = triggerCount or math.maxinteger
  self:UpdatePerSecond()
end

function EnergyBoostModel:GetTimeLimitedConfigDurationInMinutes()
  return TRIGGER_DURATION_IN_MINS
end

function EnergyBoostModel:GetTimeLimitedTriggerCount()
  return self.m_triggerCount
end

function EnergyBoostModel:OnAddEnergy(msg)
  if not self.m_bBoostTriggerOn or msg.type ~= EnergyType.Main then
    return
  end
  if self.m_triggerCount > 0 and msg.cur >= self.m_triggerCount then
    self:_TriggerTimeLimitedEnergyBoost()
  end
end

function EnergyBoostModel:LateInit()
  self.m_bLateInited = true
end

function EnergyBoostModel:UpdatePerSecond()
  if not self.m_bLateInited then
    return
  end
  local isTimeLimitedOn = self:_IsTimeLimitedEnergyBoostOn()
  if isTimeLimitedOn ~= self.m_bTimeLimitedOn then
    self.m_bTimeLimitedOn = isTimeLimitedOn
    EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
  end
end

function EnergyBoostModel:_TriggerTimeLimitedEnergyBoost()
  GM.MiscModel:SetEnergyBoostTriggerEndTime(GM.GameModel:GetServerTime() + TRIGGER_DURATION_IN_SECONDS)
  if self.m_bTimeLimitedOn then
  else
    self:UpdatePerSecond()
  end
  GM.BIManager:LogAction(EBIType.TimeLimitedEnergyBoostTriggered, TRIGGER_DURATION_IN_SECONDS)
end

function EnergyBoostModel:_IsTimeLimitedEnergyBoostOn()
  return GM.GameModel:GetServerTime() < GM.MiscModel:GetEnergyBoostTriggerEndTimeInNumber()
end

function EnergyBoostModel:IsEnergyBoostModeOn()
  return self:IsEnergyBoostConfigOn() and self:IsEnergyBoostUserOn()
end

function EnergyBoostModel:IsEnergyBoostConfigOn()
  return self.m_bBoostOn or self.m_bTimeLimitedOn
end

function EnergyBoostModel:IsSwitchModeOn()
  return self.m_bBoostOn
end

function EnergyBoostModel:IsTimeLimitedOn()
  return self.m_bTimeLimitedOn
end

function EnergyBoostModel:GetTimeLimitedLeftTime()
  local leftTime = GM.MiscModel:GetEnergyBoostTriggerEndTimeInNumber() - GM.GameModel:GetServerTime()
  return leftTime < 0 and 0 or leftTime
end

function EnergyBoostModel:IsEnergyBoostUserOn()
  return GM.MiscModel:GetEnergyBoostUserOnInNumber() == 1
end

function EnergyBoostModel:SetEnergyBoostUserOn(on, byUser)
  GM.MiscModel:SetEnergyBoostUserOn(on and 1 or 0)
  if byUser ~= false then
    GM.BIManager:LogAction(EBIType.EnergyBoostSwitchUserOn, {
      on = on and 1 or 0
    })
  end
  EventDispatcher.DispatchEvent(EEventType.EnergyBoostModeChanged)
end

function EnergyBoostModel:CanEnergyBoost(type)
  if not self:IsEnergyBoostModeOn() then
    return false
  end
  if not GM.ItemDataModel:IsPd(type) then
    return false
  end
  local modelConfig = GM.ItemDataModel:GetModelConfig(type)
  return modelConfig and modelConfig.UseEnergy == 1
end

function EnergyBoostModel:GetEnergyBoostCostNum()
  return 2
end
