"""
测试菜品库系统
验证数据库操作和界面功能
"""

import os
import sys
import tempfile
from datetime import datetime

def test_database_operations():
    """测试数据库操作"""
    print("=== 测试数据库操作 ===")
    
    try:
        from dish_database import DishDatabase, Dish, ProcessingStep
        
        # 使用临时数据库
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        db = DishDatabase(temp_db.name)
        
        # 测试基础数据加载
        ingredients = db.get_ingredients()
        equipment = db.get_equipment()
        countries = db.get_countries()
        themes = db.get_restaurant_themes()
        
        print(f"✓ 基础数据加载成功")
        print(f"  食材数量: {len(ingredients)}")
        print(f"  器械数量: {len(equipment)}")
        print(f"  国家数量: {len(countries)}")
        print(f"  餐厅主题数量: {len(themes)}")
        
        # 测试菜品创建和保存
        test_dish = Dish(
            dish_id="test_001",
            name="测试菜品",
            source="测试",
            source_id="test_001",
            restaurant_theme_id="BBQ",
            restaurant_theme_name="烧烤店",
            country_id="CN",
            country_name="中国"
        )
        
        # 添加加工步骤
        step1 = ProcessingStep(
            step_number=1,
            ingredients=["牛肉", "洋葱"],
            equipment="烤箱",
            processing_time=30,
            result_name="烤牛肉"
        )
        
        step2 = ProcessingStep(
            step_number=2,
            ingredients=["烤牛肉", "面包"],
            equipment="",
            processing_time=5,
            result_name="牛肉汉堡"
        )
        
        test_dish.processing_steps = [step1, step2]
        
        # 保存菜品
        db.save_dish(test_dish, "test_user")
        
        # 测试搜索
        search_results = db.search_dishes(name_keyword="测试")
        assert len(search_results) == 1
        assert search_results[0].dish_id == "test_001"
        
        print(f"✓ 菜品保存和搜索成功")
        
        # 测试按食材搜索
        ingredient_results = db.search_dishes(ingredients=["牛肉"])
        assert len(ingredient_results) == 1
        
        print(f"✓ 按食材搜索成功")
        
        # 测试按器械搜索
        equipment_results = db.search_dishes(equipment=["烤箱"])
        assert len(equipment_results) == 1
        
        print(f"✓ 按器械搜索成功")
        
        # 测试编辑记录
        records = db.get_edit_records("test_001")
        assert len(records) == 1
        assert records[0].operator_id == "test_user"
        
        print(f"✓ 编辑记录功能正常")
        
        # 清理临时文件
        os.unlink(temp_db.name)
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_import():
    """测试配置导入功能"""
    print("\n=== 测试配置导入功能 ===")
    
    try:
        from dish_database import DishDatabase
        from config_importer import ConfigImporter
        
        # 使用临时数据库
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        db = DishDatabase(temp_db.name)
        importer = ConfigImporter(db)
        
        # 创建测试配置内容
        test_config = '''
        {
            Type = "ds_grillmt_1",
            UseEnergy = 2,
            Frequency = 10,
            Capacity = 1,
            GeneratedItems = {
                {Code = "it_1_1_1", Weight = 1},
                {Code = "it_2_2_2", Weight = 2}
            }
        },
        {
            Type = "ds_friedsf_3",
            UseEnergy = 3,
            Frequency = 15,
            Capacity = 2,
            GeneratedItems = {
                {Code = "it_3_3_3", Weight = 1}
            }
        }
        '''
        
        # 创建临时配置文件
        temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.lua', delete=False, encoding='utf-8')
        temp_config.write(test_config)
        temp_config.close()
        
        # 测试导入
        count = importer.import_from_item_model_config(temp_config.name, "TEST")
        
        print(f"✓ 配置导入成功，导入 {count} 个菜品")
        
        # 验证导入结果
        dishes = db.search_dishes(source="TEST")
        assert len(dishes) == count
        
        # 检查菜品详情
        grillmt_dish = None
        for dish in dishes:
            if dish.dish_id == "ds_grillmt_1":
                grillmt_dish = dish
                break
        
        assert grillmt_dish is not None
        assert grillmt_dish.name == "烤肉1"
        assert grillmt_dish.restaurant_theme_id == "BBQ"
        assert len(grillmt_dish.processing_steps) == 1
        assert "it_1_1_1" in grillmt_dish.processing_steps[0].ingredients
        
        print(f"✓ 导入的菜品数据验证正确")
        
        # 清理临时文件
        os.unlink(temp_db.name)
        os.unlink(temp_config.name)
        
        return True
        
    except Exception as e:
        print(f"✗ 配置导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_export_import():
    """测试JSON导出导入功能"""
    print("\n=== 测试JSON导出导入功能 ===")
    
    try:
        from dish_database import DishDatabase, Dish, ProcessingStep
        from config_importer import ConfigImporter
        
        # 使用临时数据库
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        db = DishDatabase(temp_db.name)
        importer = ConfigImporter(db)
        
        # 创建测试菜品
        test_dish = Dish(
            dish_id="json_test_001",
            name="JSON测试菜品",
            source="JSON",
            source_id="json_001",
            restaurant_theme_id="SUSHI",
            restaurant_theme_name="寿司店",
            country_id="JP",
            country_name="日本"
        )
        
        step = ProcessingStep(
            step_number=1,
            ingredients=["鱼肉", "大米"],
            equipment="切菜板",
            processing_time=15,
            result_name="寿司"
        )
        test_dish.processing_steps = [step]
        
        db.save_dish(test_dish, "json_test")
        
        # 测试JSON导出
        temp_json = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        temp_json.close()
        
        dishes = db.search_dishes(source="JSON")
        count = importer.export_to_json(temp_json.name, dishes)
        
        print(f"✓ JSON导出成功，导出 {count} 个菜品")
        
        # 创建新数据库测试导入
        temp_db2 = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db2.close()
        
        db2 = DishDatabase(temp_db2.name)
        importer2 = ConfigImporter(db2)
        
        # 测试JSON导入
        import_count = importer2.import_from_json(temp_json.name, "JSON_IMPORT")
        
        print(f"✓ JSON导入成功，导入 {import_count} 个菜品")
        
        # 验证导入结果
        imported_dishes = db2.search_dishes(source="JSON_IMPORT")
        assert len(imported_dishes) == 1
        
        imported_dish = imported_dishes[0]
        assert imported_dish.name == "JSON测试菜品"
        assert imported_dish.country_name == "日本"
        assert len(imported_dish.processing_steps) == 1
        
        print(f"✓ JSON导入的菜品数据验证正确")
        
        # 清理临时文件
        os.unlink(temp_db.name)
        os.unlink(temp_db2.name)
        os.unlink(temp_json.name)
        
        return True
        
    except Exception as e:
        print(f"✗ JSON导出导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_functionality():
    """测试搜索功能"""
    print("\n=== 测试搜索功能 ===")
    
    try:
        from dish_database import DishDatabase, Dish, ProcessingStep
        
        # 使用临时数据库
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db.close()
        
        db = DishDatabase(temp_db.name)
        
        # 创建多个测试菜品
        dishes_data = [
            {
                'id': 'search_001',
                'name': '中式炒饭',
                'ingredients': ['大米', '鸡蛋', '胡萝卜'],
                'equipment': '平底锅',
                'theme': ('DIMSUM', '茶餐厅'),
                'country': ('CN', '中国')
            },
            {
                'id': 'search_002', 
                'name': '意式面条',
                'ingredients': ['面粉', '番茄', '牛肉'],
                'equipment': '汤锅',
                'theme': ('PASTA', '意面店'),
                'country': ('IT', '意大利')
            },
            {
                'id': 'search_003',
                'name': '日式寿司',
                'ingredients': ['大米', '鱼肉'],
                'equipment': '切菜板',
                'theme': ('SUSHI', '寿司店'),
                'country': ('JP', '日本')
            }
        ]
        
        # 保存测试菜品
        for dish_data in dishes_data:
            dish = Dish(
                dish_id=dish_data['id'],
                name=dish_data['name'],
                source="SEARCH_TEST",
                restaurant_theme_id=dish_data['theme'][0],
                restaurant_theme_name=dish_data['theme'][1],
                country_id=dish_data['country'][0],
                country_name=dish_data['country'][1]
            )
            
            step = ProcessingStep(
                step_number=1,
                ingredients=dish_data['ingredients'],
                equipment=dish_data['equipment'],
                processing_time=20,
                result_name=dish_data['name']
            )
            dish.processing_steps = [step]
            
            db.save_dish(dish, "search_test")
        
        # 测试各种搜索条件
        
        # 1. 按名称关键词搜索
        name_results = db.search_dishes(name_keyword="炒饭")
        assert len(name_results) == 1
        assert name_results[0].name == "中式炒饭"
        print(f"✓ 按名称关键词搜索成功")
        
        # 2. 按食材搜索
        rice_results = db.search_dishes(ingredients=["大米"])
        assert len(rice_results) == 2  # 炒饭和寿司都有大米
        print(f"✓ 按食材搜索成功")
        
        # 3. 按器械搜索
        pan_results = db.search_dishes(equipment=["平底锅"])
        assert len(pan_results) == 1
        assert pan_results[0].name == "中式炒饭"
        print(f"✓ 按器械搜索成功")
        
        # 4. 按国家搜索
        china_results = db.search_dishes(country="CN")
        assert len(china_results) == 1
        assert china_results[0].country_name == "中国"
        print(f"✓ 按国家搜索成功")
        
        # 5. 按餐厅主题搜索
        sushi_results = db.search_dishes(theme="SUSHI")
        assert len(sushi_results) == 1
        assert sushi_results[0].restaurant_theme_name == "寿司店"
        print(f"✓ 按餐厅主题搜索成功")
        
        # 6. 组合搜索
        combo_results = db.search_dishes(ingredients=["大米"], country="JP")
        assert len(combo_results) == 1
        assert combo_results[0].name == "日式寿司"
        print(f"✓ 组合搜索成功")
        
        # 清理临时文件
        os.unlink(temp_db.name)
        
        return True
        
    except Exception as e:
        print(f"✗ 搜索功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试菜品库系统...")
    
    tests = [
        test_database_operations,
        test_config_import,
        test_json_export_import,
        test_search_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！菜品库系统工作正常。")
        print("\n系统功能:")
        print("• 菜品录入和编辑")
        print("• 多条件搜索和筛选")
        print("• MC/FL配置导入")
        print("• JSON/Excel导出")
        print("• 编辑记录追溯")
        print("\n可以运行以下命令启动程序:")
        print("python dish_manager.py")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
