"""
数据模型模块
定义订单编辑器中使用的各种数据结构
基于需求背景2进行优化
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union, Tuple
from enum import Enum


class EfficiencyMode(Enum):
    """生成器效率模式"""
    AVERAGE = "average"  # 平均效率
    DYNAMIC = "dynamic"  # 动态效率
    TOP3_AVERAGE = "top3_average"  # top3平均效率


class BetMode(Enum):
    """体力消耗模式"""
    BET_2 = 2
    BET_4 = 4
    BET_8 = 8


@dataclass
class ProductionData:
    """生成器产出数据（对应需求背景2中的production字段）"""
    item_code: str  # 物品代码，如 "100020"
    weight: int  # 产出权重
    total_clicks: int = 20  # 总点击次数（默认20）


@dataclass
class ItemData:
    """物品数据（对应需求背景2中的LevelGoodsBase）"""
    code: str  # 物品代码，如 "it_1_1_1"
    name: str = ""  # 物品名称
    category: List[int] = field(default_factory=list)  # 物品分类（对应Category字段）
    score: float = 0.0  # 物品分数（对应Score字段）
    generators: List[str] = field(default_factory=list)  # 可生产的生成器（对应Generators字段）
    merged_type: Optional[str] = None  # 合成后的物品类型（对应MergedType字段）
    level: int = 1  # 物品等级
    series: int = 1  # 物品系列
    base_equivalent: float = 1.0  # 折合1级物品数量

    # 新增字段：基于需求背景2
    use_energy: int = 1  # 消耗体力（对应UseEnergy字段）
    cd: int = 3  # 冷却时间（对应Cd字段）
    frequency: int = 1  # 频率（对应Frequency字段）
    capacity: int = 1  # 容量（对应Capacity字段）
    drops_total: int = 1  # 总掉落数（对应DropsTotal字段）
    drop_on_spot: int = 1  # 现场掉落（对应DropOnSpot字段）
    level_down: int = 1  # 等级下降（对应LevelDown字段）
    reward: int = 0  # 奖励（对应Reward字段）
    speed_up_price: int = 1  # 加速价格（对应SpeedUpPrice字段）
    initial_number: int = 0  # 初始数量（对应InitialNumber字段）


@dataclass
class GeneratorData:
    """生成器数据（对应需求背景2中的LevelInitiativeProduce）"""
    code: str  # 生成器代码，如 "pd_1_4"
    name: str = ""  # 生成器名称
    level: int = 1  # 生成器等级
    efficiency: float = 1.0  # 产出效率
    capacity: int = 24  # 产出次数上限（对应Capacity字段）
    cd: int = 7200  # 冷却时间（秒，对应Cd字段）
    frequency: int = 8  # 产出频率（对应Frequency字段）
    generated_items: List[Dict[str, Any]] = field(default_factory=list)  # 产出物品（对应GeneratedItems）
    use_energy: int = 1  # 消耗体力（对应UseEnergy字段）
    series: int = 1  # 生成器系列
    tape_items: List[Dict[str, Any]] = field(default_factory=list)  # 传送带物品（对应TapeItems字段）
    unlock_price: int = 0  # 解锁价格（对应UnlockPrice字段）
    speed_up_price: int = 0  # 加速价格（对应SpeedUpPrice字段）
    initial_number: int = 0  # 初始数量（对应InitialNumber字段）
    merged_type: str = ""  # 合成后类型（对应MergedType字段）
    book_reward: List[Dict[str, Any]] = field(default_factory=list)  # 图鉴奖励（对应BookReward字段）

    # 新增字段：基于需求背景2的production数据结构
    production: List[ProductionData] = field(default_factory=list)  # 产出数据列表


@dataclass
class InstrumentData:
    """器械数据（对应需求背景2中的LevelInstrument）"""
    code: str  # 器械代码
    name: str = ""  # 器械名称
    level: int = 1  # 器械等级
    efficiency_factor: float = 1.0  # 效率系数
    base_time: int = 60  # 基础加工时间（秒）

    # 新增字段：基于需求背景2
    unlock_price: int = 0  # 解锁价格
    use_energy: int = 1  # 消耗体力
    capacity: int = 1  # 容量
    frequency: int = 1  # 频率
    cd: int = 60  # 冷却时间
    speed_up_price: int = 1  # 加速价格
    initial_number: int = 0  # 初始数量
    merged_type: str = ""  # 合成后类型
    book_reward: List[Dict[str, Any]] = field(default_factory=list)  # 图鉴奖励


@dataclass
class RecipeData:
    """菜品配方数据（对应需求背景2中的LevelGoodsCooking）"""
    code: str  # 菜品代码，如 "ds_chopve_1"
    name: str = ""  # 菜品名称
    required_items: List[Dict[str, Any]] = field(default_factory=list)  # 所需物品
    required_instruments: List[str] = field(default_factory=list)  # 所需器械
    cooking_time: int = 60  # 加工时间（秒）
    base_equivalent: float = 1.0  # 折合1级物品数量
    virtual_level: int = 1  # 虚拟等级
    min_interval_waves: int = 0  # 最短间隔波数
    base_series: float = 1.0  # 基础系列（小数）
    remaining_items: List[str] = field(default_factory=list)  # 剩余物品（衍生物）
    extra_energy: int = 0  # 额外体力

    # 新增字段：基于需求背景2
    unlock_price: int = 0  # 解锁价格
    use_energy: int = 1  # 消耗体力
    capacity: int = 1  # 容量
    frequency: int = 1  # 频率
    cd: int = 60  # 冷却时间
    speed_up_price: int = 1  # 加速价格
    initial_number: int = 0  # 初始数量
    merged_type: str = ""  # 合成后类型
    book_reward: List[Dict[str, Any]] = field(default_factory=list)  # 图鉴奖励
    category: List[int] = field(default_factory=list)  # 分类
    score: float = 0.0  # 分数
    generators: List[str] = field(default_factory=list)  # 生成器列表


@dataclass
class OrderRequirement:
    """订单需求"""
    item_type: str  # 物品类型
    count: int  # 数量
    params: str = ""  # 额外参数


@dataclass
class OrderReward:
    """订单奖励"""
    currency: str  # 货币类型
    amount: int  # 数量
    params: str = ""  # 额外参数


@dataclass
class OrderData:
    """订单数据"""
    order_id: str  # 订单ID
    chapter_id: int = 1  # 章节ID
    group_id: int = 1  # 组ID
    requirements: List[OrderRequirement] = field(default_factory=list)  # 需求列表
    rewards: List[OrderReward] = field(default_factory=list)  # 奖励列表
    pre_ids: List[str] = field(default_factory=list)  # 前置订单ID
    energy_cost: float = 0.0  # 体力消耗
    is_special: bool = False  # 是否为特色菜品


@dataclass
class PlayerGeneratorInfo:
    """玩家生成器信息"""
    generator_type: str  # 生成器类型
    level: int  # 等级
    count: int  # 数量


@dataclass
class PlayerInstrumentInfo:
    """玩家器械信息"""
    instrument_type: str  # 器械类型
    level: int  # 等级
    count: int  # 数量


@dataclass
class GeneratorUsageRange:
    """生成器使用范围"""
    min_rounds: int  # 最小轮数
    max_rounds: int  # 最大轮数


@dataclass
class InstrumentUsageLimit:
    """器械使用限制"""
    max_usage_count: int = 0  # 最大使用次数
    max_total_time: int = 0  # 最大总时长（秒）
    max_single_time: int = 0  # 单次最大时长（秒）


@dataclass
class RemainingItemsRange:
    """剩余物品范围"""
    min_base_equivalent: float = 0.0  # 最小折合1级物品数量
    max_base_equivalent: float = 0.0  # 最大折合1级物品数量
    max_actual_count: int = 0  # 实际物品数量上限


@dataclass
class OrderGenerationConfig:
    """订单生成配置"""
    # 前置波次信息
    previous_wave_orders: List[OrderData] = field(default_factory=list)
    
    # 特色菜品（4-7个）
    special_dishes: List[str] = field(default_factory=list)
    
    # 波订单体力消耗范围
    total_energy_range: Tuple[float, float] = (0.0, 0.0)  # (最小值, 最大值)
    energy_fluctuation_ratio: float = 0.1  # 波动比例
    
    # 玩家生成器信息
    player_generators: List[PlayerGeneratorInfo] = field(default_factory=list)
    
    # 生成器轮数范围
    generator_usage_ranges: Dict[str, GeneratorUsageRange] = field(default_factory=dict)
    
    # 玩家器械信息
    player_instruments: List[PlayerInstrumentInfo] = field(default_factory=list)
    
    # 器械使用限制
    instrument_usage_limit: InstrumentUsageLimit = field(default_factory=InstrumentUsageLimit)
    
    # 剩余物品范围
    remaining_items_range: RemainingItemsRange = field(default_factory=RemainingItemsRange)
    
    # 订单体力权重（7个订单）
    order_energy_weights: List[int] = field(default_factory=lambda: [10, 10, 15, 15, 15, 20, 30])
    
    # 体力消耗模式
    bet_mode: BetMode = BetMode.BET_2
    
    # 生成器效率模式
    efficiency_mode: EfficiencyMode = EfficiencyMode.AVERAGE
    
    # 生成器点击次数范围
    generator_click_ranges: Dict[str, Tuple[int, int]] = field(default_factory=dict)
    
    # 器械使用指标
    instrument_usage_ranges: Dict[str, Tuple[int, int]] = field(default_factory=dict)  # 使用次数范围
    instrument_time_ranges: Dict[str, Tuple[int, int]] = field(default_factory=dict)  # 总时长范围
    
    # 物品最高等级要求
    max_item_levels: Dict[str, int] = field(default_factory=dict)  # 每类生成器必须使用的物品最高等级


@dataclass
class OrderGenerationResult:
    """订单生成结果"""
    orders: List[OrderData] = field(default_factory=list)  # 生成的7个订单
    total_energy: float = 0.0  # 总体力消耗
    generator_clicks: Dict[str, int] = field(default_factory=dict)  # 各生成器点击次数
    generator_rounds: Dict[str, int] = field(default_factory=dict)  # 各生成器轮数
    generator_max_levels: Dict[str, int] = field(default_factory=dict)  # 各生成器使用的最高等级物品
    instrument_usage: Dict[str, int] = field(default_factory=dict)  # 器械使用次数
    instrument_total_time: Dict[str, int] = field(default_factory=dict)  # 器械总时长
    remaining_items_base: Dict[str, float] = field(default_factory=dict)  # 剩余物品折合1级数量（分系列）
    remaining_items_actual: Dict[str, int] = field(default_factory=dict)  # 实际剩余物品数量
    board_occupation: Dict[str, int] = field(default_factory=dict)  # 棋盘占用数
    instrument_usage_gap: Dict[str, int] = field(default_factory=dict)  # 器械使用差距分析
    optimal_completion_order: List[int] = field(default_factory=list)  # 最优完成顺序
    step_energy_costs: List[float] = field(default_factory=list)  # 分步体力消耗
    is_valid: bool = False  # 方案是否有效


@dataclass
class ConfigData:
    """配置数据集合"""
    items: Dict[str, ItemData] = field(default_factory=dict)  # 物品配置
    generators: Dict[str, GeneratorData] = field(default_factory=dict)  # 生成器配置
    instruments: Dict[str, InstrumentData] = field(default_factory=dict)  # 器械配置
    recipes: Dict[str, RecipeData] = field(default_factory=dict)  # 菜品配方配置
    orders: Dict[int, List[OrderData]] = field(default_factory=dict)  # 现有订单配置（按章节）
