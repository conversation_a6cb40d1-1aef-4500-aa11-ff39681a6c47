BoardTaskBubble = {}
BoardTaskBubble.__index = BoardTaskBubble
local TimePerFrame = 0.016666666666666666

function BoardTaskBubble:Awake()
  EventDispatcher.AddListener(EEventType.PropertyAcquired, self, self.CheckCanFinishTask)
  EventDispatcher.AddListener(EEventType.PropertyConsumed, self, self.CheckCanFinishTask)
  EventDispatcher.AddListener(EEventType.MainTaskFinished, self, self.CheckCanFinishTask)
  EventDispatcher.AddListener(EEventType.NewTasksUnlocked, self, self.CheckCanFinishTask)
  EventDispatcher.AddListener(EEventType.OrderAnimationFinished, self, self.CheckCanFinishTask)
  EventDispatcher.AddListener(EEventType.EnterMainScene, self, self.CheckCanFinishTask)
  EventDispatcher.AddListener(EEventType.UpdateOrderEmpty, self, self.CheckCanFinishTask)
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnChangeGameMode)
  EventDispatcher.AddListener(EEventType.CacheBubbleStateChanged, self, self.OnStateChanged)
  self.m_orderArea = self.m_orderAreaGo:GetLuaTable()
  self.gameObject:SetActive(true)
  self.m_bVisible = true
  self.m_originScale = 1
  self:OnStateChanged(true)
  EventDispatcher.DispatchEvent(EEventType.CacheRootNodeStateChanged)
  self:_CheckShow(true)
end

function BoardTaskBubble:OnStateChanged(bInit)
  if not GM.ConfigModel:UseNewCacheLayout() then
    return
  end
  local bExistCache = MainBoardView.GetInstance():GetModel():GetCachedItemCount() > 0
  local targetScale = bExistCache and 0.76 or 1
  if self.m_originScale ~= targetScale and self.m_showTween then
    self.m_showTween:Complete(true)
    self.m_showTween = nil
  end
  self.m_originScale = targetScale
  if self.m_stateSeq ~= nil then
    self.m_stateSeq:Kill()
    self.m_stateSeq = nil
  end
  if self.m_bVisible and not bInit then
    local seq = DOTween.Sequence()
    local animTime = 0.2
    seq:Append(self.transform:DOScale(Vector3(self.m_originScale, self.m_originScale, 1), animTime):SetEase(Ease.OutCubic))
    seq:AppendCallback(function()
      LayoutRebuilder.ForceRebuildLayoutImmediate(self.transform.parent)
      self.m_stateSeq = nil
    end)
    self.m_stateSeq = seq
  elseif bInit then
    UIUtil.SetLocalScale(self.transform, self.m_originScale, self.m_originScale)
  end
end

function BoardTaskBubble:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  if self.m_showTween then
    self.m_showTween:Kill()
    self.m_showTween = nil
  end
end

function BoardTaskBubble:OnChangeGameMode()
  if GM.SceneManager:GetGameMode() == EGameMode.Board then
    self:CheckCanFinishTask()
  end
end

function BoardTaskBubble:OnClick()
  GM.BIManager:LogProject(EBIProjectType.ClickTaskBubble, self.m_taskId)
  GM.SceneManager:ChangeGameMode(EGameMode.Main, function()
    if GM.UIManager:IsViewExisting(UIPrefabConfigName.TaskWindow) then
      return
    end
    GM.UIManager:OpenView(UIPrefabConfigName.TaskWindow, ETaskWindowRefer.BoardBubble)
  end)
end

function BoardTaskBubble:CheckCanFinishTask()
  if self.m_orderArea:IsPlayingOrderAnimation() then
    return
  end
  self:_CheckShow()
end

function BoardTaskBubble:GetContentRectTrans()
  return self.m_contentRectTrans
end

function BoardTaskBubble:_CheckShow(ignoreAnimation)
  local canFinish, firstSatisfiedTaskId, satisfiedCount = GM.TaskManager:CanFinishOngoingTask()
  self.m_taskId = firstSatisfiedTaskId
  if self:_CanShow() then
    if not self.m_bVisible then
      self.m_bVisible = true
      EventDispatcher.DispatchEvent(EEventType.TaskBubbleStateChanged)
      EventDispatcher.DispatchEvent(EEventType.CacheRootNodeStateChanged)
      if not ignoreAnimation then
        self:PlayShowTween()
      else
        if self.m_showTween ~= nil then
          self.m_showTween:Pause()
        end
        if self.m_stateSeq ~= nil then
          self.m_stateSeq:Kill()
          self.m_stateSeq = nil
        end
        self.gameObject.transform.localScale = Vector3(self.m_originScale, self.m_originScale, 1)
      end
      EventDispatcher.DispatchEvent(EEventType.RefreshPrompt)
      if self.m_orderArea ~= nil then
        self.m_orderArea:ForceRebuildLayout()
      end
    end
  elseif self.m_bVisible then
    self.m_bVisible = false
    EventDispatcher.DispatchEvent(EEventType.TaskBubbleStateChanged)
    if self.m_showTween ~= nil then
      self.m_showTween:Pause()
    end
    if self.m_stateSeq ~= nil then
      self.m_stateSeq:Kill()
      self.m_stateSeq = nil
    end
    self.transform:SetLocalScale(0)
    EventDispatcher.DispatchEvent(EEventType.RefreshPrompt)
    if self.m_orderArea ~= nil then
      self.m_orderArea:ForceRebuildLayout()
    end
    EventDispatcher.DispatchEvent(EEventType.CacheRootNodeStateChanged)
  end
end

function BoardTaskBubble:_CanShow()
  return BoardTaskBubble.CanShow()
end

function BoardTaskBubble.CanShow()
  local canFinish, firstSatisfiedTaskId, satisfiedCount = GM.TaskManager:CanFinishOngoingTask()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local hasOrder = orderModel:HasOrders()
  return canFinish and hasOrder
end

function BoardTaskBubble:SetHandEffectActive(active)
  self.m_handEffectGo:SetActive(active)
end

function BoardTaskBubble:PlayShowTween()
  local transform = self.gameObject.transform
  if self.m_showTween == nil then
    local showTween = DOTween.Sequence()
    showTween:Append(transform:DOScale(Vector3(self.m_originScale + 0.1, 1, 1), 10 * TimePerFrame))
    showTween:Append(transform:DOScale(self.m_originScale, 5 * TimePerFrame))
    showTween:OnComplete(function()
      self.m_bPlayingAnimation = nil
    end)
    showTween:SetAutoKill(false)
    self.m_showTween = showTween
  end
  self.m_showTween:Pause()
  transform.localScale = V3Zero
  self.m_bPlayingAnimation = true
  self.m_showTween:Restart()
end

function BoardTaskBubble:CanPlayAnimaion()
  return not self.m_bVisible and self:_CanShow()
end

function BoardTaskBubble:IsPlayingAnimation()
  return self.m_bPlayingAnimation
end

function BoardTaskBubble:GetAnimationLeftWidth()
  return 177 * (self.m_originScale - self.gameObject.transform.localScale.x)
end

function BoardTaskBubble:Update()
  if self.m_bPlayingAnimation then
    if not self.m_parentTrans then
      self.m_parentTrans = self.transform.parent
    end
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_parentTrans)
  end
end

function BoardTaskBubble:IsShowing()
  return self.m_bVisible
end

NoOrderTaskBubble = setmetatable({}, BoardTaskBubble)
NoOrderTaskBubble.__index = NoOrderTaskBubble

function NoOrderTaskBubble:OnStateChanged()
end

function NoOrderTaskBubble:_CanShow()
  local canFinish, firstSatisfiedTaskId, satisfiedCount = GM.TaskManager:CanFinishOngoingTask()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local hasOrder = orderModel:HasOrders()
  return canFinish and not hasOrder
end

function NoOrderTaskBubble:_CheckShow()
  BoardTaskBubble._CheckShow(self, true)
end
