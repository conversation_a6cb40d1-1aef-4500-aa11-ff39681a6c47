PkRaceBaseWindow = setmetatable({disableEffectWhenCloseView = true}, BaseWindow)
PkRaceBaseWindow.__index = PkRaceBaseWindow

function PkRaceBaseWindow:Init(activityType, bAutoOpen)
  self.m_activityType = activityType
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_model:SetWindowOpened()
  if self.m_spine then
    self.m_spine:Init()
    self.m_spine:SetAnimation("appear", false)
    self.m_spine:AddAnimation("idle", true)
  end
  AddHandlerAndRecordMap(self.m_model.event, ExtraBoardActivityEventType.StateChanged, {
    obj = self,
    method = self.TryClose
  })
  self:LogWindowAction(EBIType.UIActionType.Open, {
    bAutoOpen and EBIReferType.AutoPopup or EBIReferType.UserClick
  })
end

function PkRaceBaseWindow:TryClose()
  self:Close()
end

function PkRaceBaseWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

PkRaceHelpWindow = setmetatable({
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
PkRaceHelpWindow.__index = PkRaceHelpWindow
