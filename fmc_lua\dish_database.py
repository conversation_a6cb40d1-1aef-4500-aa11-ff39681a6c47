"""
菜品库管理系统
支持菜品录入、查询、编辑和导入功能
"""

import os
import sqlite3
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter import *
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import re

@dataclass
class ProcessingStep:
    """加工步骤"""
    step_number: int
    ingredients: List[str] = field(default_factory=list)
    equipment: str = ""
    processing_time: int = 0  # 分钟
    result_name: str = ""

@dataclass
class Dish:
    """菜品数据结构"""
    dish_id: str = ""
    name: str = ""
    processing_steps: List[ProcessingStep] = field(default_factory=list)
    source: str = ""  # MC/FL/其他
    source_id: str = ""  # 在原项目中的ID
    restaurant_theme_id: str = ""
    restaurant_theme_name: str = ""
    country_id: str = ""
    country_name: str = ""
    created_time: str = ""
    updated_time: str = ""

@dataclass
class EditRecord:
    """编辑记录"""
    record_id: str
    dish_id: str
    operator_id: str
    edit_content: str
    edit_time: str

class DishDatabase:
    """菜品数据库管理"""
    
    def __init__(self, db_path="dish_database.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建固定配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ingredients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                category TEXT,
                created_time TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                category TEXT,
                created_time TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS countries (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                created_time TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS restaurant_themes (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                created_time TEXT
            )
        ''')
        
        # 创建菜品表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dishes (
                dish_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                processing_steps TEXT,  -- JSON格式存储加工步骤
                source TEXT,
                source_id TEXT,
                restaurant_theme_id TEXT,
                restaurant_theme_name TEXT,
                country_id TEXT,
                country_name TEXT,
                created_time TEXT,
                updated_time TEXT,
                FOREIGN KEY (restaurant_theme_id) REFERENCES restaurant_themes (id),
                FOREIGN KEY (country_id) REFERENCES countries (id)
            )
        ''')
        
        # 创建编辑记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS edit_records (
                record_id TEXT PRIMARY KEY,
                dish_id TEXT,
                operator_id TEXT,
                edit_content TEXT,
                edit_time TEXT,
                FOREIGN KEY (dish_id) REFERENCES dishes (dish_id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # 初始化基础数据
        self._init_basic_data()
    
    def _init_basic_data(self):
        """初始化基础数据"""
        # 初始化食材
        basic_ingredients = [
            ("番茄", "蔬菜"), ("洋葱", "蔬菜"), ("土豆", "蔬菜"), ("胡萝卜", "蔬菜"),
            ("牛肉", "肉类"), ("猪肉", "肉类"), ("鸡肉", "肉类"), ("鱼肉", "肉类"),
            ("大米", "谷物"), ("面粉", "谷物"), ("鸡蛋", "蛋类"), ("牛奶", "乳制品"),
            ("盐", "调料"), ("糖", "调料"), ("胡椒", "调料"), ("橄榄油", "调料")
        ]
        
        # 初始化器械
        basic_equipment = [
            ("烤箱", "烘烤"), ("平底锅", "煎炒"), ("汤锅", "煮炖"), ("蒸锅", "蒸制"),
            ("烤架", "烧烤"), ("搅拌机", "搅拌"), ("切菜板", "切配"), ("刀具", "切配"),
            ("微波炉", "加热"), ("油炸锅", "油炸")
        ]
        
        # 初始化国家
        basic_countries = [
            ("CN", "中国"), ("US", "美国"), ("IT", "意大利"), ("FR", "法国"),
            ("JP", "日本"), ("KR", "韩国"), ("IN", "印度"), ("MX", "墨西哥"),
            ("TH", "泰国"), ("GR", "希腊")
        ]
        
        # 初始化餐厅主题
        basic_themes = [
            ("BBQ", "烧烤店"), ("BAKERY", "面包店"), ("DIMSUM", "茶餐厅"), 
            ("MARKET", "市场"), ("SEAFOOD", "海鲜店"), ("SUSHI", "寿司店"),
            ("PASTA", "意面店"), ("TACOS", "墨西哥餐厅")
        ]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        current_time = datetime.now().isoformat()
        
        # 插入基础数据（忽略重复）
        for name, category in basic_ingredients:
            cursor.execute(
                "INSERT OR IGNORE INTO ingredients (name, category, created_time) VALUES (?, ?, ?)",
                (name, category, current_time)
            )
        
        for name, category in basic_equipment:
            cursor.execute(
                "INSERT OR IGNORE INTO equipment (name, category, created_time) VALUES (?, ?, ?)",
                (name, category, current_time)
            )
        
        for id, name in basic_countries:
            cursor.execute(
                "INSERT OR IGNORE INTO countries (id, name, created_time) VALUES (?, ?, ?)",
                (id, name, current_time)
            )
        
        for id, name in basic_themes:
            cursor.execute(
                "INSERT OR IGNORE INTO restaurant_themes (id, name, created_time) VALUES (?, ?, ?)",
                (id, name, current_time)
            )
        
        conn.commit()
        conn.close()
    
    def get_ingredients(self):
        """获取所有食材"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM ingredients ORDER BY name")
        result = [row[0] for row in cursor.fetchall()]
        conn.close()
        return result
    
    def get_equipment(self):
        """获取所有器械"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM equipment ORDER BY name")
        result = [row[0] for row in cursor.fetchall()]
        conn.close()
        return result
    
    def get_countries(self):
        """获取所有国家"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM countries ORDER BY name")
        result = {row[0]: row[1] for row in cursor.fetchall()}
        conn.close()
        return result
    
    def get_restaurant_themes(self):
        """获取所有餐厅主题"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, name FROM restaurant_themes ORDER BY name")
        result = {row[0]: row[1] for row in cursor.fetchall()}
        conn.close()
        return result
    
    def save_dish(self, dish: Dish, operator_id: str = "system"):
        """保存菜品"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        current_time = datetime.now().isoformat()
        
        # 序列化加工步骤
        steps_json = json.dumps([
            {
                'step_number': step.step_number,
                'ingredients': step.ingredients,
                'equipment': step.equipment,
                'processing_time': step.processing_time,
                'result_name': step.result_name
            }
            for step in dish.processing_steps
        ], ensure_ascii=False)
        
        # 检查是否已存在
        cursor.execute("SELECT dish_id FROM dishes WHERE dish_id = ?", (dish.dish_id,))
        exists = cursor.fetchone()
        
        if exists:
            # 更新
            cursor.execute('''
                UPDATE dishes SET 
                name = ?, processing_steps = ?, source = ?, source_id = ?,
                restaurant_theme_id = ?, restaurant_theme_name = ?,
                country_id = ?, country_name = ?, updated_time = ?
                WHERE dish_id = ?
            ''', (
                dish.name, steps_json, dish.source, dish.source_id,
                dish.restaurant_theme_id, dish.restaurant_theme_name,
                dish.country_id, dish.country_name, current_time, dish.dish_id
            ))
            
            edit_content = f"更新菜品: {dish.name}"
        else:
            # 插入
            cursor.execute('''
                INSERT INTO dishes (
                    dish_id, name, processing_steps, source, source_id,
                    restaurant_theme_id, restaurant_theme_name,
                    country_id, country_name, created_time, updated_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                dish.dish_id, dish.name, steps_json, dish.source, dish.source_id,
                dish.restaurant_theme_id, dish.restaurant_theme_name,
                dish.country_id, dish.country_name, current_time, current_time
            ))
            
            edit_content = f"创建菜品: {dish.name}"
        
        # 记录编辑历史
        record_id = f"edit_{current_time}_{dish.dish_id}"
        cursor.execute('''
            INSERT INTO edit_records (record_id, dish_id, operator_id, edit_content, edit_time)
            VALUES (?, ?, ?, ?, ?)
        ''', (record_id, dish.dish_id, operator_id, edit_content, current_time))
        
        conn.commit()
        conn.close()
    
    def search_dishes(self, ingredients=None, equipment=None, country=None, 
                     theme=None, source=None, name_keyword=None):
        """搜索菜品"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM dishes WHERE 1=1"
        params = []
        
        if name_keyword:
            query += " AND name LIKE ?"
            params.append(f"%{name_keyword}%")
        
        if country:
            query += " AND country_id = ?"
            params.append(country)
        
        if theme:
            query += " AND restaurant_theme_id = ?"
            params.append(theme)
        
        if source:
            query += " AND source = ?"
            params.append(source)
        
        cursor.execute(query, params)
        dishes = []
        
        for row in cursor.fetchall():
            dish = Dish(
                dish_id=row[0],
                name=row[1],
                source=row[3],
                source_id=row[4],
                restaurant_theme_id=row[5],
                restaurant_theme_name=row[6],
                country_id=row[7],
                country_name=row[8],
                created_time=row[9],
                updated_time=row[10]
            )
            
            # 反序列化加工步骤
            if row[2]:
                steps_data = json.loads(row[2])
                dish.processing_steps = [
                    ProcessingStep(
                        step_number=step['step_number'],
                        ingredients=step['ingredients'],
                        equipment=step['equipment'],
                        processing_time=step['processing_time'],
                        result_name=step['result_name']
                    )
                    for step in steps_data
                ]
            
            # 过滤食材和器械
            if ingredients or equipment:
                match = True
                
                if ingredients:
                    dish_ingredients = set()
                    for step in dish.processing_steps:
                        dish_ingredients.update(step.ingredients)
                    
                    if not any(ing in dish_ingredients for ing in ingredients):
                        match = False
                
                if equipment and match:
                    dish_equipment = set()
                    for step in dish.processing_steps:
                        if step.equipment:
                            dish_equipment.add(step.equipment)
                    
                    if not any(eq in dish_equipment for eq in equipment):
                        match = False
                
                if match:
                    dishes.append(dish)
            else:
                dishes.append(dish)
        
        conn.close()
        return dishes
    
    def get_dish_by_id(self, dish_id: str):
        """根据ID获取菜品"""
        dishes = self.search_dishes()
        for dish in dishes:
            if dish.dish_id == dish_id:
                return dish
        return None
    
    def get_edit_records(self, dish_id: str):
        """获取编辑记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "SELECT * FROM edit_records WHERE dish_id = ? ORDER BY edit_time DESC",
            (dish_id,)
        )
        
        records = []
        for row in cursor.fetchall():
            records.append(EditRecord(
                record_id=row[0],
                dish_id=row[1],
                operator_id=row[2],
                edit_content=row[3],
                edit_time=row[4]
            ))
        
        conn.close()
        return records
