# 浮岛物语 - 店铺订单生成器

## 概述

基于当前项目配置的店铺订单生成器，支持逐日生成新店铺订单，提供限制条件选择、物品筛选和可视化展示功能。

## 核心功能

### 1. 逐日订单生成
- **按天生成**: 支持1-30天的订单计划生成
- **智能分配**: 根据能量模式和难度自动分配每日任务
- **约束控制**: 支持多种限制条件设置
- **实时预览**: 生成过程中实时显示进度和结果

### 2. 物品筛选系统
- **基于ItemModelConfig.lua**: 直接读取游戏配置文件
- **多维度筛选**: 支持按类型、等级、系列、能量消耗等筛选
- **实时搜索**: 支持关键词实时筛选物品
- **详细信息**: 显示物品的完整属性和产出信息

### 3. 限制条件设置
- **能量范围**: 设置每日最小/最大能量消耗
- **物品类型**: 选择允许的物品类型（生成器/物品/器械/菜品）
- **等级限制**: 设置物品最高等级限制
- **能量模式**: 节能/平衡/高效/极限四种模式
- **难度控制**: 简单/普通/困难/专家四个难度级别

### 4. 可视化展示
- **订单计划树**: 层次化显示每日订单和详细需求
- **统计信息**: 实时显示物品统计和计划分析
- **详细信息**: 双击查看订单或物品的详细信息
- **历史对比**: 支持多套方案的对比分析

### 5. 体力消耗分析
- **精确计算**: 基于物品属性精确计算体力消耗
- **分步显示**: 显示每个物品的具体能量消耗
- **总量控制**: 确保每日总能量在设定范围内
- **预估时间**: 根据物品频率计算预估完成时间

## 文件结构

```
fmc_lua/
├── shop_order_generator.py      # 完整版（需要matplotlib）
├── shop_order_simple.py         # 简化版（无图表依赖）
├── README_shop_order_generator.md # 说明文档
└── Data/Config/                 # 配置文件目录
    ├── ItemModelConfig.lua      # 物品和生成器配置
    ├── OrderFixedConfig_*.lua   # 现有订单配置
    ├── OrderGroupConfig_*.lua   # 订单组配置
    └── Mainline/               # 店铺配置
        ├── BBQ/
        ├── Bakery/
        └── ...
```

## 使用方法

### 启动程序
```bash
# 简化版（推荐）
python shop_order_simple.py

# 完整版（需要matplotlib）
python shop_order_generator.py
```

### 基本操作流程

#### 1. 设置基础参数
- **选择店铺**: 从16个可用店铺中选择目标店铺
- **设置章节**: 选择目标章节（1-16）
- **生成天数**: 设置要生成的天数（1-30天）
- **能量模式**: 选择能量消耗模式
- **难度模式**: 选择订单难度

#### 2. 配置限制条件
- **能量范围**: 设置每日最小和最大能量消耗
- **物品类型**: 勾选允许使用的物品类型
- **等级限制**: 设置物品最高等级

#### 3. 生成订单
- 点击"生成订单"按钮
- 系统自动分析配置文件
- 根据设置生成符合条件的订单计划
- 在订单计划标签页查看结果

#### 4. 查看和分析
- **订单计划**: 查看每日订单的详细安排
- **物品列表**: 浏览所有可用物品及其属性
- **详细信息**: 双击订单或物品查看详情
- **统计信息**: 查看生成计划的统计分析

### 高级功能

#### 物品筛选
1. 切换到"物品列表"标签页
2. 在筛选框中输入关键词
3. 支持按代码、名称、类型筛选
4. 点击物品查看详细属性

#### 订单详情
1. 在订单计划中双击任意订单
2. 自动切换到详细信息标签页
3. 显示完整的需求和奖励信息
4. 包含能量消耗和时间预估

#### 计划保存和加载
1. 通过"文件"菜单保存当前计划
2. 支持JSON格式的计划文件
3. 可以加载之前保存的计划
4. 便于方案对比和历史回顾

## 配置文件支持

### ItemModelConfig.lua解析
- **完整属性解析**: UseEnergy、Frequency、Capacity等
- **产出物品**: GeneratedItems和TapeItems
- **分类信息**: Category和Score
- **价格信息**: UnlockPrice和SpeedUpPrice

### 店铺配置支持
- **任务配置**: TaskConfig_*.lua文件
- **多店铺支持**: BBQ、Bakery、DimSum等16个店铺
- **章节关联**: 支持不同章节的配置

### 订单配置集成
- **现有订单**: OrderFixedConfig_*.lua
- **订单组**: OrderGroupConfig_*.lua
- **历史数据**: 支持查看现有订单信息

## 算法特性

### 智能分配算法
1. **能量平衡**: 确保每日能量在设定范围内
2. **难度递进**: 根据天数适当调整难度
3. **物品多样性**: 避免重复使用相同物品
4. **约束满足**: 严格遵守所有限制条件

### 奖励计算
1. **基础奖励**: 根据能量消耗计算经验和能量奖励
2. **随机奖励**: 30%概率获得额外奖励物品
3. **平衡机制**: 确保奖励与付出成正比

### 时间预估
1. **基于频率**: 根据物品频率计算生产时间
2. **容量考虑**: 考虑生成器容量限制
3. **操作时间**: 包含基础操作和等待时间

## 界面说明

### 左侧控制面板
- **基础设置**: 店铺、章节、天数等基本参数
- **限制条件**: 能量、类型、等级等约束设置
- **操作按钮**: 生成、清空、筛选等功能按钮
- **统计信息**: 实时显示配置和计划统计

### 右侧显示面板
- **订单计划**: 树形结构显示完整计划
- **物品列表**: 表格显示所有可用物品
- **详细信息**: 文本显示选中项的详细信息

### 状态栏
- 显示当前操作状态
- 显示加载进度和结果统计
- 显示错误信息和提示

## 扩展功能

### 批量生成（完整版）
- 生成多套方案进行对比
- 不同参数组合的效果分析
- 最优方案推荐

### 图表分析（完整版）
- 能量消耗趋势图
- 难度分布直方图
- 物品使用统计图

### 数据导出
- JSON格式的计划文件
- 支持Excel导出（需要openpyxl）
- 便于外部分析和处理

## 注意事项

1. **配置文件**: 确保Data/Config目录下有正确的配置文件
2. **编码格式**: 配置文件需要UTF-8编码
3. **内存使用**: 大量物品数据可能占用较多内存
4. **生成时间**: 复杂约束条件可能增加生成时间

## 故障排除

### 常见问题
1. **配置加载失败**: 检查配置文件路径和格式
2. **生成结果为空**: 检查限制条件是否过于严格
3. **界面显示异常**: 检查屏幕分辨率和字体设置

### 性能优化
1. **减少生成天数**: 降低计算复杂度
2. **简化约束条件**: 减少筛选条件
3. **使用简化版**: 避免图表渲染开销

这个店铺订单生成器为浮岛物语提供了强大的订单规划工具，支持灵活的配置和直观的可视化展示，大大提升了订单设计的效率和质量。
