"""
菜品编辑窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
from tkinter import *
from datetime import datetime
from dish_database import DishDatabase, Dish, ProcessingStep

class DishEditWindow:
    """菜品编辑窗口"""
    
    def __init__(self, parent, db: DishDatabase, dish_id=None, operator_id="admin"):
        self.parent = parent
        self.db = db
        self.dish_id = dish_id
        self.operator_id = operator_id
        self.dish = None
        
        # 如果是编辑模式，加载现有菜品
        if dish_id:
            self.dish = db.get_dish_by_id(dish_id)
            if not self.dish:
                messagebox.showerror("错误", "菜品不存在")
                return
        
        # 创建窗口
        self.window = Toplevel(parent)
        self.window.title("编辑菜品" if dish_id else "新建菜品")
        self.window.geometry("800x700")
        self.window.resizable(True, True)
        
        # 加工步骤列表
        self.processing_steps = []
        
        # 创建界面
        self._create_ui()
        
        # 如果是编辑模式，填充数据
        if self.dish:
            self._load_dish_data()
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 基本信息框架
        basic_frame = ttk.LabelFrame(main_frame, text="基本信息", padding="10")
        basic_frame.pack(fill=X, pady=5)
        
        # 菜品ID
        ttk.Label(basic_frame, text="菜品ID:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        self.dish_id_var = StringVar()
        ttk.Entry(basic_frame, textvariable=self.dish_id_var, width=30).grid(
            row=0, column=1, sticky=W, padx=5, pady=2)
        
        # 菜品名称
        ttk.Label(basic_frame, text="菜品名称:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        self.dish_name_var = StringVar()
        ttk.Entry(basic_frame, textvariable=self.dish_name_var, width=30).grid(
            row=1, column=1, sticky=W, padx=5, pady=2)
        
        # 来源
        ttk.Label(basic_frame, text="来源:").grid(row=2, column=0, sticky=W, padx=5, pady=2)
        self.source_var = StringVar()
        source_combo = ttk.Combobox(basic_frame, textvariable=self.source_var,
                                   values=["MC", "FL", "其他"], state="readonly", width=27)
        source_combo.grid(row=2, column=1, sticky=W, padx=5, pady=2)
        
        # 原项目ID
        ttk.Label(basic_frame, text="原项目ID:").grid(row=3, column=0, sticky=W, padx=5, pady=2)
        self.source_id_var = StringVar()
        ttk.Entry(basic_frame, textvariable=self.source_id_var, width=30).grid(
            row=3, column=1, sticky=W, padx=5, pady=2)
        
        # 餐厅主题
        ttk.Label(basic_frame, text="餐厅主题:").grid(row=4, column=0, sticky=W, padx=5, pady=2)
        self.theme_var = StringVar()
        self.theme_combo = ttk.Combobox(basic_frame, textvariable=self.theme_var,
                                       state="readonly", width=27)
        self.theme_combo.grid(row=4, column=1, sticky=W, padx=5, pady=2)
        
        # 国家
        ttk.Label(basic_frame, text="国家:").grid(row=5, column=0, sticky=W, padx=5, pady=2)
        self.country_var = StringVar()
        self.country_combo = ttk.Combobox(basic_frame, textvariable=self.country_var,
                                         state="readonly", width=27)
        self.country_combo.grid(row=5, column=1, sticky=W, padx=5, pady=2)
        
        # 加载下拉列表数据
        self._load_combo_data()
        
        # 加工步骤框架
        steps_frame = ttk.LabelFrame(main_frame, text="加工步骤", padding="10")
        steps_frame.pack(fill=BOTH, expand=True, pady=5)
        
        # 步骤工具栏
        steps_toolbar = ttk.Frame(steps_frame)
        steps_toolbar.pack(fill=X, pady=5)
        
        ttk.Button(steps_toolbar, text="添加步骤", command=self._add_step).pack(side=LEFT, padx=5)
        ttk.Button(steps_toolbar, text="删除步骤", command=self._remove_step).pack(side=LEFT, padx=5)
        ttk.Button(steps_toolbar, text="上移", command=self._move_step_up).pack(side=LEFT, padx=5)
        ttk.Button(steps_toolbar, text="下移", command=self._move_step_down).pack(side=LEFT, padx=5)
        
        # 步骤列表
        columns = ("step", "ingredients", "equipment", "time", "result")
        self.steps_tree = ttk.Treeview(steps_frame, columns=columns, show="headings", height=10)
        
        self.steps_tree.heading("step", text="步骤")
        self.steps_tree.heading("ingredients", text="食材")
        self.steps_tree.heading("equipment", text="器械")
        self.steps_tree.heading("time", text="时间(分钟)")
        self.steps_tree.heading("result", text="产出名称")
        
        self.steps_tree.column("step", width=50)
        self.steps_tree.column("ingredients", width=200)
        self.steps_tree.column("equipment", width=100)
        self.steps_tree.column("time", width=80)
        self.steps_tree.column("result", width=150)
        
        # 添加滚动条
        steps_scrollbar = ttk.Scrollbar(steps_frame, orient=VERTICAL, command=self.steps_tree.yview)
        self.steps_tree.configure(yscrollcommand=steps_scrollbar.set)
        
        self.steps_tree.pack(side=LEFT, fill=BOTH, expand=True)
        steps_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 绑定双击事件
        self.steps_tree.bind("<Double-1>", self._edit_step)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=X, pady=10)
        
        ttk.Button(button_frame, text="保存", command=self._save_dish).pack(side=RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.window.destroy).pack(side=RIGHT, padx=5)
    
    def _load_combo_data(self):
        """加载下拉列表数据"""
        # 加载餐厅主题
        themes = self.db.get_restaurant_themes()
        theme_values = [f"{k} - {v}" for k, v in themes.items()]
        self.theme_combo['values'] = theme_values
        
        # 加载国家
        countries = self.db.get_countries()
        country_values = [f"{k} - {v}" for k, v in countries.items()]
        self.country_combo['values'] = country_values
    
    def _load_dish_data(self):
        """加载菜品数据"""
        if not self.dish:
            return
        
        self.dish_id_var.set(self.dish.dish_id)
        self.dish_name_var.set(self.dish.name)
        self.source_var.set(self.dish.source)
        self.source_id_var.set(self.dish.source_id)
        
        # 设置餐厅主题
        if self.dish.restaurant_theme_id and self.dish.restaurant_theme_name:
            theme_value = f"{self.dish.restaurant_theme_id} - {self.dish.restaurant_theme_name}"
            self.theme_var.set(theme_value)
        
        # 设置国家
        if self.dish.country_id and self.dish.country_name:
            country_value = f"{self.dish.country_id} - {self.dish.country_name}"
            self.country_var.set(country_value)
        
        # 加载加工步骤
        self.processing_steps = self.dish.processing_steps.copy()
        self._update_steps_display()
    
    def _update_steps_display(self):
        """更新步骤显示"""
        # 清空现有显示
        for item in self.steps_tree.get_children():
            self.steps_tree.delete(item)
        
        # 添加步骤
        for step in self.processing_steps:
            ingredients_text = ", ".join(step.ingredients) if step.ingredients else ""
            
            self.steps_tree.insert("", "end", values=(
                step.step_number,
                ingredients_text,
                step.equipment,
                step.processing_time,
                step.result_name
            ))
    
    def _add_step(self):
        """添加步骤"""
        step_number = len(self.processing_steps) + 1
        step_window = StepEditWindow(self.window, self.db, step_number)
        step_window.window.transient(self.window)
        step_window.window.grab_set()
        
        # 等待窗口关闭
        self.window.wait_window(step_window.window)
        
        # 如果有返回结果，添加到步骤列表
        if hasattr(step_window, 'result_step') and step_window.result_step:
            self.processing_steps.append(step_window.result_step)
            self._update_steps_display()
    
    def _remove_step(self):
        """删除步骤"""
        selection = self.steps_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的步骤")
            return
        
        # 获取选中的步骤索引
        item = self.steps_tree.item(selection[0])
        step_number = int(item['values'][0])
        
        # 删除步骤
        self.processing_steps = [step for step in self.processing_steps if step.step_number != step_number]
        
        # 重新编号
        for i, step in enumerate(self.processing_steps):
            step.step_number = i + 1
        
        self._update_steps_display()
    
    def _move_step_up(self):
        """上移步骤"""
        selection = self.steps_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要移动的步骤")
            return
        
        item = self.steps_tree.item(selection[0])
        step_number = int(item['values'][0])
        
        if step_number > 1:
            # 交换步骤
            for i, step in enumerate(self.processing_steps):
                if step.step_number == step_number:
                    self.processing_steps[i], self.processing_steps[i-1] = self.processing_steps[i-1], self.processing_steps[i]
                    break
            
            # 重新编号
            for i, step in enumerate(self.processing_steps):
                step.step_number = i + 1
            
            self._update_steps_display()
    
    def _move_step_down(self):
        """下移步骤"""
        selection = self.steps_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要移动的步骤")
            return
        
        item = self.steps_tree.item(selection[0])
        step_number = int(item['values'][0])
        
        if step_number < len(self.processing_steps):
            # 交换步骤
            for i, step in enumerate(self.processing_steps):
                if step.step_number == step_number:
                    self.processing_steps[i], self.processing_steps[i+1] = self.processing_steps[i+1], self.processing_steps[i]
                    break
            
            # 重新编号
            for i, step in enumerate(self.processing_steps):
                step.step_number = i + 1
            
            self._update_steps_display()
    
    def _edit_step(self, event):
        """编辑步骤"""
        selection = self.steps_tree.selection()
        if not selection:
            return
        
        item = self.steps_tree.item(selection[0])
        step_number = int(item['values'][0])
        
        # 找到对应的步骤
        step_to_edit = None
        for step in self.processing_steps:
            if step.step_number == step_number:
                step_to_edit = step
                break
        
        if step_to_edit:
            step_window = StepEditWindow(self.window, self.db, step_number, step_to_edit)
            step_window.window.transient(self.window)
            step_window.window.grab_set()
            
            # 等待窗口关闭
            self.window.wait_window(step_window.window)
            
            # 如果有返回结果，更新步骤
            if hasattr(step_window, 'result_step') and step_window.result_step:
                for i, step in enumerate(self.processing_steps):
                    if step.step_number == step_number:
                        self.processing_steps[i] = step_window.result_step
                        break
                
                self._update_steps_display()
    
    def _save_dish(self):
        """保存菜品"""
        try:
            # 验证必填字段
            if not self.dish_id_var.get().strip():
                messagebox.showerror("错误", "请输入菜品ID")
                return
            
            if not self.dish_name_var.get().strip():
                messagebox.showerror("错误", "请输入菜品名称")
                return
            
            # 解析餐厅主题和国家
            theme_id = theme_name = ""
            if self.theme_var.get():
                parts = self.theme_var.get().split(" - ")
                theme_id = parts[0]
                theme_name = parts[1] if len(parts) > 1 else ""
            
            country_id = country_name = ""
            if self.country_var.get():
                parts = self.country_var.get().split(" - ")
                country_id = parts[0]
                country_name = parts[1] if len(parts) > 1 else ""
            
            # 创建菜品对象
            dish = Dish(
                dish_id=self.dish_id_var.get().strip(),
                name=self.dish_name_var.get().strip(),
                processing_steps=self.processing_steps,
                source=self.source_var.get(),
                source_id=self.source_id_var.get().strip(),
                restaurant_theme_id=theme_id,
                restaurant_theme_name=theme_name,
                country_id=country_id,
                country_name=country_name
            )
            
            # 保存到数据库
            self.db.save_dish(dish, self.operator_id)
            
            messagebox.showinfo("成功", "菜品保存成功")
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {e}")


class StepEditWindow:
    """步骤编辑窗口"""
    
    def __init__(self, parent, db: DishDatabase, step_number, step=None):
        self.parent = parent
        self.db = db
        self.step_number = step_number
        self.step = step
        self.result_step = None
        
        # 创建窗口
        self.window = Toplevel(parent)
        self.window.title(f"编辑第{step_number}步" if step else f"添加第{step_number}步")
        self.window.geometry("500x400")
        self.window.resizable(False, False)
        
        # 创建界面
        self._create_ui()
        
        # 如果是编辑模式，填充数据
        if step:
            self._load_step_data()
    
    def _create_ui(self):
        """创建用户界面"""
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 步骤号
        ttk.Label(main_frame, text=f"第{self.step_number}轮加工", 
                 font=("Arial", 12, "bold")).pack(pady=5)
        
        # 食材选择
        ingredients_frame = ttk.LabelFrame(main_frame, text="食材", padding="10")
        ingredients_frame.pack(fill=X, pady=5)
        
        # 食材输入
        input_frame = ttk.Frame(ingredients_frame)
        input_frame.pack(fill=X)
        
        self.ingredient_var = StringVar()
        ingredient_combo = ttk.Combobox(input_frame, textvariable=self.ingredient_var, width=20)
        ingredient_combo.pack(side=LEFT, padx=2)
        
        ttk.Button(input_frame, text="添加", command=self._add_ingredient).pack(side=LEFT, padx=2)
        
        # 已选食材列表
        self.ingredients_listbox = Listbox(ingredients_frame, height=5)
        self.ingredients_listbox.pack(fill=X, pady=5)
        self.ingredients_listbox.bind('<Double-Button-1>', self._remove_ingredient)
        
        # 加载食材列表
        ingredients = self.db.get_ingredients()
        ingredient_combo['values'] = ingredients
        
        # 器械选择
        equipment_frame = ttk.LabelFrame(main_frame, text="器械", padding="10")
        equipment_frame.pack(fill=X, pady=5)
        
        self.equipment_var = StringVar()
        equipment_combo = ttk.Combobox(equipment_frame, textvariable=self.equipment_var,
                                      state="readonly", width=30)
        equipment_combo.pack(fill=X)
        
        # 加载器械列表
        equipment = [""] + self.db.get_equipment()
        equipment_combo['values'] = equipment
        
        # 时间输入
        time_frame = ttk.LabelFrame(main_frame, text="加工时间", padding="10")
        time_frame.pack(fill=X, pady=5)
        
        self.time_var = IntVar()
        time_spin = ttk.Spinbox(time_frame, from_=0, to=999, textvariable=self.time_var, width=30)
        time_spin.pack(fill=X)
        ttk.Label(time_frame, text="分钟").pack()
        
        # 产出名称
        result_frame = ttk.LabelFrame(main_frame, text="产出名称", padding="10")
        result_frame.pack(fill=X, pady=5)
        
        self.result_var = StringVar()
        ttk.Entry(result_frame, textvariable=self.result_var, width=30).pack(fill=X)
        
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=X, pady=10)
        
        ttk.Button(button_frame, text="确定", command=self._save_step).pack(side=RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.window.destroy).pack(side=RIGHT, padx=5)
        
        # 选中的食材列表
        self.selected_ingredients = []
    
    def _load_step_data(self):
        """加载步骤数据"""
        if not self.step:
            return
        
        # 加载食材
        self.selected_ingredients = self.step.ingredients.copy()
        for ingredient in self.selected_ingredients:
            self.ingredients_listbox.insert(END, ingredient)
        
        # 加载器械
        self.equipment_var.set(self.step.equipment)
        
        # 加载时间
        self.time_var.set(self.step.processing_time)
        
        # 加载产出名称
        self.result_var.set(self.step.result_name)
    
    def _add_ingredient(self):
        """添加食材"""
        ingredient = self.ingredient_var.get().strip()
        if ingredient and ingredient not in self.selected_ingredients:
            self.selected_ingredients.append(ingredient)
            self.ingredients_listbox.insert(END, ingredient)
            self.ingredient_var.set("")
    
    def _remove_ingredient(self, event):
        """移除食材"""
        selection = self.ingredients_listbox.curselection()
        if selection:
            index = selection[0]
            ingredient = self.ingredients_listbox.get(index)
            self.ingredients_listbox.delete(index)
            self.selected_ingredients.remove(ingredient)
    
    def _save_step(self):
        """保存步骤"""
        # 创建步骤对象
        self.result_step = ProcessingStep(
            step_number=self.step_number,
            ingredients=self.selected_ingredients,
            equipment=self.equipment_var.get(),
            processing_time=self.time_var.get(),
            result_name=self.result_var.get().strip()
        )
        
        self.window.destroy()
