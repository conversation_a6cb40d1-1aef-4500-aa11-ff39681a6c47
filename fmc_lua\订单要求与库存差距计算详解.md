# 订单要求与库存差距计算详解

## 概述

本文档详细介绍游戏中如何计算订单要求与当前库存之间的差距，这是Link模式选择最优订单的核心算法基础。

## 🎯 核心计算流程

### 主要入口方法
```lua
-- BaseSceneBoardModel.lua 第272-279行
function BaseSceneBoardModel:GetUnfilledOrderRequirements(excludeStore, selectOrder)
  local directResult, indirectResult, filledResult, codeCountMap = 
    self:GetUnfilledOrderRequirementsSeparately(excludeStore, selectOrder)
  
  -- 合并直接需求和间接需求
  directResult = Table.ShallowCopy(directResult)
  for code, count in pairs(indirectResult) do
    directResult[code] = (directResult[code] or 0) + count
  end
  
  return directResult, codeCountMap  -- 返回未满足需求和当前库存统计
end
```

## 📊 第一步：库存统计 (GetCodeCountMap)

### 🏠 棋盘物品统计
```lua
-- BaseBoardModel.lua 第141-154行
function BaseBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = {}
  includeBoard = includeBoard ~= false
  
  if includeBoard then
    -- 统计棋盘上的所有物品
    for item, _ in pairs(self:GetAllBoardItems()) do
      local code = item:GetCode()
      if codeCountMap[code] == nil then
        codeCountMap[code] = 0
      end
      codeCountMap[code] = codeCountMap[code] + 1
    end
  end
  
  return codeCountMap
end
```

### 📦 库存物品统计
```lua
-- MainBoardModel.lua 第88-105行
function MainBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = BaseSceneBoardModel.GetCodeCountMap(self, includeBoard, includeCache, includeStore)
  
  includeStore = includeStore ~= false
  if includeStore then
    local item
    -- 统计库存中的所有物品
    for i = 1, self.m_itemStoreModel:GetItemCount() do
      item = self.m_itemStoreModel:GetItem(i)
      if item ~= nil then
        local code = item:GetCode()
        if codeCountMap[code] == nil then
          codeCountMap[code] = 0
        end
        codeCountMap[code] = codeCountMap[code] + 1
      end
    end
  end
  
  return codeCountMap
end
```

### 🎒 缓存物品统计
```lua
-- BaseInteractiveBoardModel.lua 第22-38行
function BaseInteractiveBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = BaseBoardModel.GetCodeCountMap(self, includeBoard, includeCache, includeStore)
  
  includeCache = includeCache ~= false
  if includeCache then
    local code
    -- 统计缓存中的所有物品
    for i = 1, self:GetCachedItemCount() do
      code = self:GetCachedItem(i)
      if code ~= nil then
        if codeCountMap[code] == nil then
          codeCountMap[code] = 0
        end
        codeCountMap[code] = codeCountMap[code] + 1
      end
    end
  end
  
  return codeCountMap
end
```

### 📊 库存统计结果示例
```lua
-- codeCountMap 结构示例
{
  ["it_1_1"] = 8,      -- 1级蔬菜 8个
  ["it_1_2"] = 3,      -- 2级蔬菜 3个
  ["it_1_3"] = 1,      -- 3级蔬菜 1个
  ["it_2_1"] = 5,      -- 1级水果 5个
  ["it_2_2"] = 2,      -- 2级水果 2个
  ["eq_1_5"] = 1,      -- 5级切菜器械 1个
  ["ds_chopve_1"] = 2, -- 切菜成品 2个
  ["pd_1_3"] = 1       -- 3级蔬菜生成器 1个
}
```

## 🎯 第二步：订单需求统计

### 📋 直接需求统计
```lua
-- BaseSceneBoardModel.lua 第200-213行
function BaseSceneBoardModel:GetUnfilledDirectOrderRequirements(excludeStore, selectOrder)
  local unfilledDirectResult = {}
  local filledResult = {}
  
  -- 统计所有订单的直接需求
  for _, order in pairs(selectOrder and {selectOrder} or self:GetOrders()) do
    local requirements = order:GetRequirements()
    for _, require in ipairs(requirements) do
      unfilledDirectResult[require] = (unfilledDirectResult[require] or 0) + 1
    end
  end
  
  -- 获取当前库存统计
  local codeCountMap = self:GetCodeCountMap(true, false, not excludeStore)
  self:_AddCookCmpItems(codeCountMap, not excludeStore)  -- 加入烹饪中的物品
  
  -- 用库存填充需求，计算差距
  self:_FillWithCodeCountMap(unfilledDirectResult, codeCountMap, filledResult)
  
  return unfilledDirectResult, codeCountMap, filledResult
end
```

### 🍳 烹饪物品处理
```lua
-- BaseSceneBoardModel.lua 第449-510行
function BaseSceneBoardModel:_GetCodeCountMapInclueInCook(includeBoard, includeStore)
  local codeCountMap = self:GetCodeCountMap(includeBoard, false, includeStore)
  local itemCookCmps = self:GetAllItemCookCmp(includeBoard, includeStore)
  
  for _, cookCmp in ipairs(itemCookCmps) do
    if cookCmp:GetState() == EItemCookState.Cooked or cookCmp:GetState() == EItemCookState.Cooking then
      -- 已完成或正在烹饪的菜品，计入成品
      local recipe = cookCmp:GetRecipe()
      codeCountMap[recipe] = (codeCountMap[recipe] or 0) + 1
    elseif cookCmp:GetState() == EItemCookState.Prepare or cookCmp:GetState() == EItemCookState.CanCook then
      -- 准备中的菜品，计入材料
      local arrItemCodes = cookCmp:GetCurMaterialsArray()
      for _, code in ipairs(arrItemCodes) do
        codeCountMap[code] = (codeCountMap[code] or 0) + 1
      end
    end
  end
  
  return codeCountMap
end
```

## ⚖️ 第三步：差距计算 (_FillWithCodeCountMap)

### 🧮 核心差距计算算法
```lua
-- BaseSceneBoardModel.lua 第512-534行
function BaseSceneBoardModel:_FillWithCodeCountMap(mapUnfilled, codeCountMap, filledResult)
  local deleteKeys = {}
  
  for code, count in pairs(mapUnfilled) do
    local existCount = codeCountMap[code]  -- 当前库存数量
    
    if existCount and 0 < existCount then
      if count <= existCount then
        -- 库存足够，完全满足需求
        deleteKeys[#deleteKeys + 1] = code
        codeCountMap[code] = existCount - count  -- 扣除使用的库存
        filledResult[code] = (filledResult[code] or 0) + count  -- 记录满足的数量
        
        if codeCountMap[code] <= 0 then
          codeCountMap[code] = nil  -- 库存用完，移除记录
        end
      else
        -- 库存不足，部分满足需求
        mapUnfilled[code] = count - existCount  -- 更新未满足数量
        filledResult[code] = (filledResult[code] or 0) + existCount  -- 记录满足的数量
        codeCountMap[code] = nil  -- 库存用完
      end
    end
    -- 如果没有库存，mapUnfilled[code]保持不变
  end
  
  -- 移除完全满足的需求
  for _, key in ipairs(deleteKeys) do
    mapUnfilled[key] = nil
  end
end
```

### 📊 差距计算示例

#### **场景设置**
```lua
-- 订单需求
local orderRequirements = {
  ["it_1_3"] = 2,      -- 需要2个3级蔬菜
  ["it_2_2"] = 3,      -- 需要3个2级水果
  ["ds_chopve_1"] = 1  -- 需要1个切菜成品
}

-- 当前库存
local currentInventory = {
  ["it_1_1"] = 8,      -- 有8个1级蔬菜
  ["it_1_2"] = 3,      -- 有3个2级蔬菜
  ["it_1_3"] = 1,      -- 有1个3级蔬菜
  ["it_2_1"] = 5,      -- 有5个1级水果
  ["it_2_2"] = 1,      -- 有1个2级水果
  ["ds_chopve_1"] = 2  -- 有2个切菜成品
}
```

#### **计算过程**
```lua
-- 第1步：it_1_3需求2个，库存1个
-- 结果：mapUnfilled["it_1_3"] = 2 - 1 = 1 (还缺1个)
--       filledResult["it_1_3"] = 1 (满足1个)
--       codeCountMap["it_1_3"] = nil (库存用完)

-- 第2步：it_2_2需求3个，库存1个
-- 结果：mapUnfilled["it_2_2"] = 3 - 1 = 2 (还缺2个)
--       filledResult["it_2_2"] = 1 (满足1个)
--       codeCountMap["it_2_2"] = nil (库存用完)

-- 第3步：ds_chopve_1需求1个，库存2个
-- 结果：mapUnfilled["ds_chopve_1"] = nil (完全满足)
--       filledResult["ds_chopve_1"] = 1 (满足1个)
--       codeCountMap["ds_chopve_1"] = 2 - 1 = 1 (剩余1个)
```

#### **最终结果**
```lua
-- 未满足需求 (unfilledRequirements)
{
  ["it_1_3"] = 1,      -- 还缺1个3级蔬菜
  ["it_2_2"] = 2       -- 还缺2个2级水果
}

-- 已满足需求 (filledResult)
{
  ["it_1_3"] = 1,      -- 满足了1个3级蔬菜
  ["it_2_2"] = 1,      -- 满足了1个2级水果
  ["ds_chopve_1"] = 1  -- 满足了1个切菜成品
}

-- 剩余库存 (codeCountMap)
{
  ["it_1_1"] = 8,      -- 1级蔬菜未使用
  ["it_1_2"] = 3,      -- 2级蔬菜未使用
  ["it_2_1"] = 5,      -- 1级水果未使用
  ["ds_chopve_1"] = 1  -- 剩余1个切菜成品
}
```

## 🔄 第四步：间接需求处理

### 🍽️ 菜品材料需求分解
```lua
-- BaseSceneBoardModel.lua 第215-271行 (简化版)
function BaseSceneBoardModel:GetUnfilledOrderRequirementsSeparately(excludeStore, selectOrder)
  local directResult, codeCountMap, filledResult = self:GetUnfilledDirectOrderRequirements(excludeStore, selectOrder)
  local indirectResult = {}
  
  -- 处理菜品的材料需求
  for directCode, count in pairs(directResult) do
    if GM.ItemDataModel:IsDishes(directCode) then
      -- 获取菜品所需材料
      local arrNonDishMaterials, arrDishMaterials = GM.ItemDataModel:GetAllMaterials(directCode)
      
      -- 计算材料需求
      for _, material in ipairs(arrNonDishMaterials) do
        indirectResult[material] = (indirectResult[material] or 0) + count
      end
      
      -- 递归处理菜品材料中的其他菜品
      for _, dishMaterial in ipairs(arrDishMaterials) do
        indirectResult[dishMaterial] = (indirectResult[dishMaterial] or 0) + count
      end
    end
  end
  
  -- 用剩余库存填充间接需求
  self:_FillWithCodeCountMap(indirectResult, codeCountMap, filledResult)
  
  return directResult, indirectResult, filledResult, codeCountMap
end
```

### 📊 间接需求示例
```lua
-- 假设订单需要 "dish_salad" × 1 (沙拉)
-- 沙拉配方：it_1_2 × 2 + it_2_1 × 1 (2个2级蔬菜 + 1个1级水果)

-- 直接需求
directResult = {
  ["dish_salad"] = 1  -- 需要1个沙拉
}

-- 间接需求 (材料需求)
indirectResult = {
  ["it_1_2"] = 2,     -- 需要2个2级蔬菜
  ["it_2_1"] = 1      -- 需要1个1级水果
}

-- 最终合并需求
finalRequirements = {
  ["dish_salad"] = 1, -- 直接需求：1个沙拉
  ["it_1_2"] = 2,     -- 间接需求：2个2级蔬菜
  ["it_2_1"] = 1      -- 间接需求：1个1级水果
}
```

## 🎮 实际应用场景

### 📈 Link模式订单选择
```lua
-- 计算每个订单的未满足需求
for id, order in pairs(orders) do
  local unfilledRequirements, codeCountMap = GM.MainBoardModel:GetUnfilledOrderRequirements(false, order)
  
  -- 基于未满足需求计算体力消耗
  local energyDiff = CalculateEnergyDiff(unfilledRequirements, codeCountMap)
  
  -- 选择体力消耗最小的订单
  if minEnergyDiff == nil or minEnergyDiff > energyDiff then
    minEnergyDiff = energyDiff
    nextTargetOrder = order
  end
end
```

### 🎯 提示系统应用
```lua
-- 检查是否有足够材料完成订单
local unfilledRequirements = GetUnfilledOrderRequirements(false, currentOrder)
if next(unfilledRequirements) == nil then
  -- 所有需求都已满足，可以提交订单
  ShowSubmitOrderPrompt()
else
  -- 还有未满足的需求，显示缺少的物品
  ShowMissingItemsPrompt(unfilledRequirements)
end
```

## 📝 总结

### 🔑 关键特点

1. **全面统计**: 包含棋盘、库存、缓存、烹饪中的所有物品
2. **精确计算**: 逐项对比需求与库存，计算精确差距
3. **递归处理**: 自动分解菜品需求到基础材料
4. **实时更新**: 考虑烹饪状态的动态变化

### 🎯 算法优势

1. **高效性**: 通过缓存机制避免重复计算
2. **准确性**: 考虑所有可能的物品来源
3. **灵活性**: 支持单订单或多订单计算
4. **扩展性**: 易于添加新的物品来源或需求类型

这个差距计算系统是整个订单管理和Link模式的核心基础，通过精确的数学计算确保了游戏逻辑的准确性和公平性。
