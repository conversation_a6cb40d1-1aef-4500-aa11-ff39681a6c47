# 订单要求与库存差距计算详解

## 概述

本文档详细介绍游戏中如何计算订单要求与当前库存之间的差距，这是Link模式选择最优订单的核心算法基础。

## 🎯 核心计算流程

### 主要入口方法
```lua
-- BaseSceneBoardModel.lua 第272-279行
function BaseSceneBoardModel:GetUnfilledOrderRequirements(excludeStore, selectOrder)
  local directResult, indirectResult, filledResult, codeCountMap = 
    self:GetUnfilledOrderRequirementsSeparately(excludeStore, selectOrder)
  
  -- 合并直接需求和间接需求
  directResult = Table.ShallowCopy(directResult)
  for code, count in pairs(indirectResult) do
    directResult[code] = (directResult[code] or 0) + count
  end
  
  return directResult, codeCountMap  -- 返回未满足需求和当前库存统计
end
```

## 📊 第一步：库存统计 (GetCodeCountMap)

### 🏠 棋盘物品统计
```lua
-- BaseBoardModel.lua 第141-154行
function BaseBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = {}
  includeBoard = includeBoard ~= false
  
  if includeBoard then
    -- 统计棋盘上的所有物品
    for item, _ in pairs(self:GetAllBoardItems()) do
      local code = item:GetCode()
      if codeCountMap[code] == nil then
        codeCountMap[code] = 0
      end
      codeCountMap[code] = codeCountMap[code] + 1
    end
  end
  
  return codeCountMap
end
```

### 📦 库存物品统计
```lua
-- MainBoardModel.lua 第88-105行
function MainBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = BaseSceneBoardModel.GetCodeCountMap(self, includeBoard, includeCache, includeStore)
  
  includeStore = includeStore ~= false
  if includeStore then
    local item
    -- 统计库存中的所有物品
    for i = 1, self.m_itemStoreModel:GetItemCount() do
      item = self.m_itemStoreModel:GetItem(i)
      if item ~= nil then
        local code = item:GetCode()
        if codeCountMap[code] == nil then
          codeCountMap[code] = 0
        end
        codeCountMap[code] = codeCountMap[code] + 1
      end
    end
  end
  
  return codeCountMap
end
```

### 🎒 缓存物品统计
```lua
-- BaseInteractiveBoardModel.lua 第22-38行
function BaseInteractiveBoardModel:GetCodeCountMap(includeBoard, includeCache, includeStore)
  local codeCountMap = BaseBoardModel.GetCodeCountMap(self, includeBoard, includeCache, includeStore)
  
  includeCache = includeCache ~= false
  if includeCache then
    local code
    -- 统计缓存中的所有物品
    for i = 1, self:GetCachedItemCount() do
      code = self:GetCachedItem(i)
      if code ~= nil then
        if codeCountMap[code] == nil then
          codeCountMap[code] = 0
        end
        codeCountMap[code] = codeCountMap[code] + 1
      end
    end
  end
  
  return codeCountMap
end
```

### 📊 库存统计结果示例
```lua
-- codeCountMap 结构示例
{
  ["it_1_1"] = 8,      -- 1级蔬菜 8个
  ["it_1_2"] = 3,      -- 2级蔬菜 3个
  ["it_1_3"] = 1,      -- 3级蔬菜 1个
  ["it_2_1"] = 5,      -- 1级水果 5个
  ["it_2_2"] = 2,      -- 2级水果 2个
  ["eq_1_5"] = 1,      -- 5级切菜器械 1个
  ["ds_chopve_1"] = 2, -- 切菜成品 2个
  ["pd_1_3"] = 1       -- 3级蔬菜生成器 1个
}
```

## 🎯 第二步：订单需求统计

### 📋 直接需求统计
```lua
-- BaseSceneBoardModel.lua 第200-213行
function BaseSceneBoardModel:GetUnfilledDirectOrderRequirements(excludeStore, selectOrder)
  local unfilledDirectResult = {}
  local filledResult = {}
  
  -- 统计所有订单的直接需求
  for _, order in pairs(selectOrder and {selectOrder} or self:GetOrders()) do
    local requirements = order:GetRequirements()
    for _, require in ipairs(requirements) do
      unfilledDirectResult[require] = (unfilledDirectResult[require] or 0) + 1
    end
  end
  
  -- 获取当前库存统计
  local codeCountMap = self:GetCodeCountMap(true, false, not excludeStore)
  self:_AddCookCmpItems(codeCountMap, not excludeStore)  -- 加入烹饪中的物品
  
  -- 用库存填充需求，计算差距
  self:_FillWithCodeCountMap(unfilledDirectResult, codeCountMap, filledResult)
  
  return unfilledDirectResult, codeCountMap, filledResult
end
```

### 🍳 烹饪物品处理
```lua
-- BaseSceneBoardModel.lua 第449-510行
function BaseSceneBoardModel:_GetCodeCountMapInclueInCook(includeBoard, includeStore)
  local codeCountMap = self:GetCodeCountMap(includeBoard, false, includeStore)
  local itemCookCmps = self:GetAllItemCookCmp(includeBoard, includeStore)
  
  for _, cookCmp in ipairs(itemCookCmps) do
    if cookCmp:GetState() == EItemCookState.Cooked or cookCmp:GetState() == EItemCookState.Cooking then
      -- 已完成或正在烹饪的菜品，计入成品
      local recipe = cookCmp:GetRecipe()
      codeCountMap[recipe] = (codeCountMap[recipe] or 0) + 1
    elseif cookCmp:GetState() == EItemCookState.Prepare or cookCmp:GetState() == EItemCookState.CanCook then
      -- 准备中的菜品，计入材料
      local arrItemCodes = cookCmp:GetCurMaterialsArray()
      for _, code in ipairs(arrItemCodes) do
        codeCountMap[code] = (codeCountMap[code] or 0) + 1
      end
    end
  end
  
  return codeCountMap
end
```

## ⚖️ 第三步：差距计算 (_FillWithCodeCountMap)

### 🧮 核心差距计算算法
```lua
-- BaseSceneBoardModel.lua 第512-534行
function BaseSceneBoardModel:_FillWithCodeCountMap(mapUnfilled, codeCountMap, filledResult)
  local deleteKeys = {}
  
  for code, count in pairs(mapUnfilled) do
    local existCount = codeCountMap[code]  -- 当前库存数量
    
    if existCount and 0 < existCount then
      if count <= existCount then
        -- 库存足够，完全满足需求
        deleteKeys[#deleteKeys + 1] = code
        codeCountMap[code] = existCount - count  -- 扣除使用的库存
        filledResult[code] = (filledResult[code] or 0) + count  -- 记录满足的数量
        
        if codeCountMap[code] <= 0 then
          codeCountMap[code] = nil  -- 库存用完，移除记录
        end
      else
        -- 库存不足，部分满足需求
        mapUnfilled[code] = count - existCount  -- 更新未满足数量
        filledResult[code] = (filledResult[code] or 0) + existCount  -- 记录满足的数量
        codeCountMap[code] = nil  -- 库存用完
      end
    end
    -- 如果没有库存，mapUnfilled[code]保持不变
  end
  
  -- 移除完全满足的需求
  for _, key in ipairs(deleteKeys) do
    mapUnfilled[key] = nil
  end
end
```

### 📊 差距计算示例

#### **场景设置**
```lua
-- 订单需求
local orderRequirements = {
  ["it_1_3"] = 2,      -- 需要2个3级蔬菜
  ["it_2_2"] = 3,      -- 需要3个2级水果
  ["ds_chopve_1"] = 1  -- 需要1个切菜成品
}

-- 当前库存
local currentInventory = {
  ["it_1_1"] = 8,      -- 有8个1级蔬菜
  ["it_1_2"] = 3,      -- 有3个2级蔬菜
  ["it_1_3"] = 1,      -- 有1个3级蔬菜
  ["it_2_1"] = 5,      -- 有5个1级水果
  ["it_2_2"] = 1,      -- 有1个2级水果
  ["ds_chopve_1"] = 2  -- 有2个切菜成品
}
```

#### **计算过程**
```lua
-- 第1步：it_1_3需求2个，库存1个
-- 结果：mapUnfilled["it_1_3"] = 2 - 1 = 1 (还缺1个)
--       filledResult["it_1_3"] = 1 (满足1个)
--       codeCountMap["it_1_3"] = nil (库存用完)

-- 第2步：it_2_2需求3个，库存1个
-- 结果：mapUnfilled["it_2_2"] = 3 - 1 = 2 (还缺2个)
--       filledResult["it_2_2"] = 1 (满足1个)
--       codeCountMap["it_2_2"] = nil (库存用完)

-- 第3步：ds_chopve_1需求1个，库存2个
-- 结果：mapUnfilled["ds_chopve_1"] = nil (完全满足)
--       filledResult["ds_chopve_1"] = 1 (满足1个)
--       codeCountMap["ds_chopve_1"] = 2 - 1 = 1 (剩余1个)
```

#### **最终结果**
```lua
-- 未满足需求 (unfilledRequirements)
{
  ["it_1_3"] = 1,      -- 还缺1个3级蔬菜
  ["it_2_2"] = 2       -- 还缺2个2级水果
}

-- 已满足需求 (filledResult)
{
  ["it_1_3"] = 1,      -- 满足了1个3级蔬菜
  ["it_2_2"] = 1,      -- 满足了1个2级水果
  ["ds_chopve_1"] = 1  -- 满足了1个切菜成品
}

-- 剩余库存 (codeCountMap)
{
  ["it_1_1"] = 8,      -- 1级蔬菜未使用
  ["it_1_2"] = 3,      -- 2级蔬菜未使用
  ["it_2_1"] = 5,      -- 1级水果未使用
  ["ds_chopve_1"] = 1  -- 剩余1个切菜成品
}
```

## 🔄 第四步：间接需求处理

### 🍽️ 菜品材料需求分解
```lua
-- BaseSceneBoardModel.lua 第215-271行 (简化版)
function BaseSceneBoardModel:GetUnfilledOrderRequirementsSeparately(excludeStore, selectOrder)
  local directResult, codeCountMap, filledResult = self:GetUnfilledDirectOrderRequirements(excludeStore, selectOrder)
  local indirectResult = {}
  
  -- 处理菜品的材料需求
  for directCode, count in pairs(directResult) do
    if GM.ItemDataModel:IsDishes(directCode) then
      -- 获取菜品所需材料
      local arrNonDishMaterials, arrDishMaterials = GM.ItemDataModel:GetAllMaterials(directCode)
      
      -- 计算材料需求
      for _, material in ipairs(arrNonDishMaterials) do
        indirectResult[material] = (indirectResult[material] or 0) + count
      end
      
      -- 递归处理菜品材料中的其他菜品
      for _, dishMaterial in ipairs(arrDishMaterials) do
        indirectResult[dishMaterial] = (indirectResult[dishMaterial] or 0) + count
      end
    end
  end
  
  -- 用剩余库存填充间接需求
  self:_FillWithCodeCountMap(indirectResult, codeCountMap, filledResult)
  
  return directResult, indirectResult, filledResult, codeCountMap
end
```

### 📊 间接需求示例
```lua
-- 假设订单需要 "dish_salad" × 1 (沙拉)
-- 沙拉配方：it_1_2 × 2 + it_2_1 × 1 (2个2级蔬菜 + 1个1级水果)

-- 直接需求
directResult = {
  ["dish_salad"] = 1  -- 需要1个沙拉
}

-- 间接需求 (材料需求)
indirectResult = {
  ["it_1_2"] = 2,     -- 需要2个2级蔬菜
  ["it_2_1"] = 1      -- 需要1个1级水果
}

-- 最终合并需求
finalRequirements = {
  ["dish_salad"] = 1, -- 直接需求：1个沙拉
  ["it_1_2"] = 2,     -- 间接需求：2个2级蔬菜
  ["it_2_1"] = 1      -- 间接需求：1个1级水果
}
```

## 🎮 实际应用场景

### 📈 Link模式订单选择
```lua
-- 计算每个订单的未满足需求
for id, order in pairs(orders) do
  local unfilledRequirements, codeCountMap = GM.MainBoardModel:GetUnfilledOrderRequirements(false, order)
  
  -- 基于未满足需求计算体力消耗
  local energyDiff = CalculateEnergyDiff(unfilledRequirements, codeCountMap)
  
  -- 选择体力消耗最小的订单
  if minEnergyDiff == nil or minEnergyDiff > energyDiff then
    minEnergyDiff = energyDiff
    nextTargetOrder = order
  end
end
```

### 🎯 提示系统应用
```lua
-- 检查是否有足够材料完成订单
local unfilledRequirements = GetUnfilledOrderRequirements(false, currentOrder)
if next(unfilledRequirements) == nil then
  -- 所有需求都已满足，可以提交订单
  ShowSubmitOrderPrompt()
else
  -- 还有未满足的需求，显示缺少的物品
  ShowMissingItemsPrompt(unfilledRequirements)
end
```

## 📝 总结

### 🔑 关键特点

1. **全面统计**: 包含棋盘、库存、缓存、烹饪中的所有物品
2. **精确计算**: 逐项对比需求与库存，计算精确差距
3. **递归处理**: 自动分解菜品需求到基础材料
4. **实时更新**: 考虑烹饪状态的动态变化

### 🎯 算法优势

1. **高效性**: 通过缓存机制避免重复计算
2. **准确性**: 考虑所有可能的物品来源
3. **灵活性**: 支持单订单或多订单计算
4. **扩展性**: 易于添加新的物品来源或需求类型

这个差距计算系统是整个订单管理和Link模式的核心基础，通过精确的数学计算确保了游戏逻辑的准确性和公平性。

## 📚 各步骤函数详细列表

### 🎯 主入口函数

#### **GetUnfilledOrderRequirements**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第272-279行
- **功能**: 获取订单未满足需求的主入口
- **调用**: `GetUnfilledOrderRequirementsSeparately`
- **返回**: `unfilledRequirements, codeCountMap`

#### **GetOrderFillEnergyDiff**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第281-296行
- **功能**: 计算订单填充的体力差额
- **调用**: `GetUnfilledOrderRequirements`, `_GetEnergyDiff`
- **返回**: `energyDiff, log`

### 📊 第一步：库存统计函数

#### **GetCodeCountMap (基础版)**
- **文件**: `fmc_lua/Board/Model/Board/BaseBoardModel.lua`
- **行数**: 第141-154行
- **功能**: 统计棋盘物品数量
- **参数**: `includeBoard, includeCache, includeStore`
- **返回**: `codeCountMap`

#### **GetCodeCountMap (交互版)**
- **文件**: `fmc_lua/Board/Model/Board/BaseInteractiveBoardModel.lua`
- **行数**: 第22-38行
- **功能**: 继承基础版，增加缓存物品统计
- **调用**: `BaseBoardModel.GetCodeCountMap`
- **新增**: 缓存物品统计逻辑

#### **GetCodeCountMap (主棋盘版)**
- **文件**: `fmc_lua/Board/Model/Board/MainBoardModel.lua`
- **行数**: 第88-105行
- **功能**: 继承交互版，增加库存物品统计
- **调用**: `BaseSceneBoardModel.GetCodeCountMap`
- **新增**: 库存物品统计逻辑

#### **GetAllBoardItems**
- **文件**: `fmc_lua/Board/Model/Board/BaseBoardModel.lua`
- **功能**: 获取棋盘上所有物品
- **调用者**: `GetCodeCountMap`

#### **GetCachedItemCount / GetCachedItem**
- **文件**: `fmc_lua/Board/Model/Board/BaseInteractiveBoardModel.lua`
- **功能**: 获取缓存物品数量和具体物品
- **调用者**: `GetCodeCountMap (交互版)`

#### **GetItemCount / GetItem (库存)**
- **文件**: `fmc_lua/Board/Model/ItemStoreModel.lua`
- **功能**: 获取库存物品数量和具体物品
- **调用者**: `GetCodeCountMap (主棋盘版)`

### 🍳 烹饪物品处理函数

#### **_GetCodeCountMapInclueInCook**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第450-510行
- **功能**: 获取包含烹饪物品的代码数量映射
- **调用**: `GetCodeCountMap`, `GetAllItemCookCmp`
- **返回**: `codeCountMap, itemCookCmpMaterials`

#### **_AddCookCmpItems**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第488-510行
- **功能**: 将烹饪组件物品添加到代码数量映射
- **调用**: `GetAllItemCookCmp`
- **处理**: 不同烹饪状态的物品

#### **GetAllItemCookCmp**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **功能**: 获取所有烹饪组件
- **参数**: `includeBoard, includeStore`
- **调用者**: `_AddCookCmpItems`, `_GetCodeCountMapInclueInCook`

### 📋 第二步：订单需求统计函数

#### **GetUnfilledDirectOrderRequirements**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第200-213行
- **功能**: 获取未满足的直接订单需求
- **调用**: `GetOrders`, `GetRequirements`, `GetCodeCountMap`, `_AddCookCmpItems`, `_FillWithCodeCountMap`
- **返回**: `unfilledDirectResult, codeCountMap, filledResult`

#### **GetOrders**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **功能**: 获取当前所有订单
- **调用者**: `GetUnfilledDirectOrderRequirements`

#### **GetRequirements**
- **文件**: `fmc_lua/Board/Model/Order/BaseOrder.lua`
- **行数**: 第67-69行
- **功能**: 获取订单的需求列表
- **返回**: `m_requirements`
- **调用者**: 订单需求统计

#### **GetUniqueRequirements**
- **文件**: `fmc_lua/Board/Model/Order/BaseOrder.lua`
- **行数**: 第71-82行
- **功能**: 获取订单的唯一需求及数量
- **返回**: `countMap, arrOrder`
- **用途**: 去重统计订单需求

### ⚖️ 第三步：差距计算函数

#### **_FillWithCodeCountMap**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第512-534行
- **功能**: 核心差距计算算法
- **参数**: `mapUnfilled, codeCountMap, filledResult`
- **逻辑**: 用库存填充需求，计算差距

#### **_FilterOut**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第470-486行
- **功能**: 从代码数量映射中过滤出指定数量
- **参数**: `itemCode, needCount, codeCountMap`
- **返回**: 剩余需求数量

### 🔄 第四步：间接需求处理函数

#### **GetUnfilledOrderRequirementsSeparately**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第215-271行
- **功能**: 分别获取直接和间接未满足需求
- **调用**: `GetUnfilledDirectOrderRequirements`, `IsDishes`, `GetAllMaterials`
- **返回**: `directResult, indirectResult, filledResult, codeCountMap`

#### **IsDishes**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 判断物品是否为菜品
- **调用者**: 间接需求处理逻辑

#### **GetAllMaterials**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取菜品的所有材料
- **返回**: `arrNonDishMaterials, arrDishMaterials`
- **调用者**: 菜品需求分解

#### **GetMaterials**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取菜品的直接材料
- **调用者**: 菜品配方解析

### 🎮 应用层函数

#### **GetOrderRequirements**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第167-199行
- **功能**: 获取订单需求（包含间接需求选项）
- **参数**: `includeIndirect, selectOrder`
- **返回**: `directDishes, directNonDishes, indirectDishes, indirectNonDishes`

#### **GetOrderCodeRequireCount**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第154-165行
- **功能**: 获取订单代码需求数量
- **调用**: `GetOrders`, `GetRequirementFillStates`
- **用途**: 统计未填充的需求数量

#### **GetRequirementFillStates**
- **文件**: `fmc_lua/Board/Model/Order/BaseOrder.lua`
- **行数**: 第84-86行
- **功能**: 获取需求填充状态
- **返回**: `m_requirementFillStates`
- **用途**: 判断需求是否已满足

### 🔍 辅助工具函数

#### **IsOrderRelaventItem**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第601-625行
- **功能**: 判断物品是否与订单相关
- **参数**: `order, itemCode`
- **返回**: `boolean`

#### **IsUnfilledOrderRequirementsChainOrPdChainItem**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第548-599行
- **功能**: 判断物品是否为未满足订单需求链或生产链物品
- **参数**: `itemCode, direct, excludeStore, selectOrder`
- **返回**: `boolean`

#### **GetUnfilledDishMaterials**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第536-546行
- **功能**: 获取菜品的未满足材料需求
- **参数**: `dishType, excludeStore`
- **返回**: `mapUnfilled`

### 📊 缓存相关函数

#### **m_frameCache 机制**
- **文件**: 各个计算函数中
- **功能**: 帧级缓存，避免重复计算
- **使用**: `GetUnfilledOrderRequirementsConsiderView`, `IsUnfilledOrderRequirementsChainOrPdChainItem`

#### **GetUnfilledOrderRequirementsConsiderView**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第318-448行
- **功能**: 考虑视图的未满足订单需求（带缓存）
- **返回**: 复杂的需求分析结果

### 🎯 Link模式专用函数

#### **_GetNextTargetOrder**
- **文件**: `fmc_lua/Model/FlambeTimeModel.lua`
- **行数**: 第381-391行
- **功能**: 获取下一个目标订单（体力消耗最小）
- **调用**: `GetOrderFillEnergyDiff`
- **返回**: `nextTargetOrder`

#### **_GetEnergyDiff**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第298-316行
- **功能**: 递归计算体力差额
- **调用**: `GetRemainRequireToLevel1CountMinusBelowLevelItems`, `GetItemCurProduceEnergy`
- **返回**: `energyDiff, log`

### 📈 数据模型函数

#### **GetChainId**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取物品链ID
- **调用者**: 体力计算、物品分类

#### **GetChainLevel**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取物品链等级
- **调用者**: 物品等级比较

#### **GetItemGenerators**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取物品的生成器
- **调用者**: 生产链分析

#### **GetTransformFrom**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取物品的转换来源
- **调用者**: 递归体力计算

#### **GetItemCurProduceEnergy**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取物品当前生产体力消耗
- **调用者**: 基础体力计算

#### **GetModelConfig**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取物品模型配置
- **调用者**: 物品属性查询

### 🛠️ 工具函数

#### **GetRemainRequireToLevel1CountMinusBelowLevelItems**
- **文件**: `fmc_lua/Board/Model/ItemUtility.lua`
- **功能**: 计算折合1级物品的剩余需求数量
- **调用者**: `_GetEnergyDiff`

#### **GetItemType**
- **文件**: `fmc_lua/Board/Model/ItemUtility.lua`
- **功能**: 根据链和等级获取物品类型
- **调用者**: 物品类型转换

#### **GetToLevel1Count**
- **文件**: `fmc_lua/Board/Model/ItemUtility.lua`
- **行数**: 第71-73行
- **功能**: 计算转换为1级物品的数量
- **公式**: `2^(level-1)`

### 🔧 订单状态管理函数

#### **UpdateState**
- **文件**: `fmc_lua/Board/Model/Order/BaseOrderModel.lua`
- **功能**: 更新订单状态
- **调用**: `_RealUpdateOrderState`
- **参数**: `codeCountMap, itemCookCmp`

#### **_RealUpdateOrderState**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第69-78行
- **功能**: 实际更新订单状态
- **调用**: `GetCodeCountMap`, `GetAllItemCookCmp`

#### **GetOrderCodeStateMap**
- **文件**: `fmc_lua/Board/Model/Board/BaseSceneBoardModel.lua`
- **行数**: 第135-152行
- **功能**: 获取订单代码状态映射
- **返回**: `codeStateMap`

### 🎲 特殊场景函数

#### **FilterItemsByType**
- **文件**: `fmc_lua/Board/Model/Board/BaseBoardModel.lua`
- **功能**: 按类型过滤物品
- **调用者**: 物品查找和匹配

#### **TryFillOrderRequirement**
- **文件**: `fmc_lua/Board/Model/Order/OrderStateHelper.lua`
- **行数**: 第3-17行
- **功能**: 尝试填充订单需求
- **参数**: `boardModel, requirement, needCount`
- **返回**: `success, existCount`

#### **IsDisposableInstrument**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 判断是否为一次性器械
- **调用者**: 订单填充逻辑

### 📊 统计和分析函数

#### **GetCountByCode**
- **文件**: `fmc_lua/Board/Model/Board/ItemCacheModel.lua`
- **行数**: 第185-188行
- **功能**: 按代码获取数量
- **调用者**: 缓存物品统计

#### **GetItemScore**
- **文件**: `fmc_lua/Model/ItemDataModel.lua`
- **功能**: 获取物品分数
- **调用者**: 物品价值评估

#### **Map2String / String2Map**
- **文件**: `fmc_lua/Board/Model/ItemUtility.lua`
- **行数**: 第43-60行
- **功能**: 映射与字符串互转
- **用途**: 数据序列化和调试

### 🎮 游戏模式相关函数

#### **GetModeByCode**
- **文件**: `fmc_lua/Board/Model/ItemUtility.lua`
- **行数**: 第36-41行
- **功能**: 根据代码获取游戏模式
- **返回**: `EGameMode`

#### **IsExtraBoardActivityItem**
- **文件**: `fmc_lua/ExtraBoardActivity/Model/ExtraBoardActivityModel.lua`
- **功能**: 判断是否为额外棋盘活动物品
- **调用者**: 游戏模式判断

## 📋 函数调用关系图

### 🔄 主要调用链
```
GetOrderFillEnergyDiff
  ↓
GetUnfilledOrderRequirements
  ↓
GetUnfilledOrderRequirementsSeparately
  ↓
GetUnfilledDirectOrderRequirements
  ↓
GetCodeCountMap → _AddCookCmpItems → _FillWithCodeCountMap
```

### 🎯 Link模式调用链
```
_GetNextTargetOrder
  ↓
GetOrderFillEnergyDiff
  ↓
_GetEnergyDiff (递归)
  ↓
GetRemainRequireToLevel1CountMinusBelowLevelItems
  ↓
GetItemCurProduceEnergy
```

这个完整的函数体系构成了订单差距计算的技术架构，每个函数都有明确的职责分工和调用关系，确保了系统的模块化、可维护性和计算准确性。
