BaseShopListContainer = setmetatable({}, BaseShopContainer)
BaseShopListContainer.__index = BaseShopListContainer

function BaseShopListContainer:Init(shopType, canCostForRefresh, cellNumberInRow)
  self.m_shopType = shopType
  self.m_canCostForRefresh = canCostForRefresh
  self.m_cellNumberInRow = cellNumberInRow
  self:_InitContainer()
  self:_UpdateContent()
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self._OnShopRefreshed)
  EventDispatcher.AddListener(EEventType.OnAdEnd, self, self._OnAdEnd)
  if not self.m_animated then
    self.m_animated = true
    self.transform:SetLocalScaleXY(0)
    self.transform:DOScale(1, 0.4):SetEase(Ease.OutBack, 1)
  end
end

function BaseShopListContainer:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BaseShopListContainer:UpdatePerSecond()
  if self.m_refreshTime ~= nil then
    self.m_refreshText.text = TimeUtil.ParseTimeDescription(math.max(0, self.m_refreshTime - GM.GameModel:GetServerTime()), nil, nil, false)
  end
end

function BaseShopListContainer:_OnShopRefreshed(message)
  if message ~= nil and message.shopType == self.m_shopType then
    self:_UpdateContent()
  end
end

function BaseShopListContainer:_OnAdEnd(message)
  if message.bSuccess and message.eAdType == EAdType.ShopRefresh and self.m_adRefresh then
    self:_DoRefresh(true)
    self.m_adRefresh = false
  end
  self.m_adRefreshButtonGo:SetActive(self.m_canCostForRefresh and GM.AdModel:CanShowAd(EAdType.ShopRefresh))
end

function BaseShopListContainer:GetCellCount()
  return self.m_cellCount
end

function BaseShopListContainer:GetCell(index)
  return self.m_cells[index]
end

function BaseShopListContainer:_GetRefreshTime()
  return nil
end

function BaseShopListContainer:_DoRefresh(isAd)
  return false
end

function BaseShopListContainer:_UpdateContent()
  self:_TryUpdateRefreshCostText()
  local cellData = self:_GetCellData()
  local cellDataEmpty = #cellData == 0
  self.gameObject:SetActive(not cellDataEmpty)
  if cellDataEmpty then
    return
  end
  for i, data in ipairs(cellData) do
    if self.m_cells[i] ~= nil then
      self.m_cells[i].gameObject:SetActive(true)
      self.m_cells[i]:UpdateContent(data)
    else
      local cellGo = Object.Instantiate(self.m_cellPrefab, self.m_cellsLayoutGroup.transform)
      self.m_cells[i] = cellGo:GetLuaTable()
      self.m_cells[i]:Init(data, function()
        self:_OnCellClicked(self.m_cells[i])
      end)
      if self.m_sortingOrder ~= nil then
        self.m_cells[i]:UpdateSortingOrder(self.m_sortingOrder)
      end
    end
  end
  for i = #cellData + 1, #self.m_cells do
    if self.m_cells[i] ~= nil then
      self.m_cells[i].gameObject:SetActive(false)
    end
  end
  self.m_refreshTime = self:_GetRefreshTime()
  if self.m_refreshTime ~= nil then
    self.m_refreshContentGo:SetActive(true)
    self:UpdatePerSecond()
  else
    self.m_refreshContentGo:SetActive(false)
  end
  if self.m_cellCount ~= #cellData then
    self.m_cellCount = #cellData
    self:_UpdateContentForCellCount()
  end
end

function BaseShopListContainer:GetShopType()
  return self.m_shopType
end

function BaseShopListContainer:UpdateSortingOrder(sortingOrder)
  if self.m_sortingOrder ~= sortingOrder and sortingOrder ~= nil then
    for _, cell in ipairs(self.m_cells) do
      if cell.UpdateSortingOrder then
        cell:UpdateSortingOrder(sortingOrder)
      end
    end
  end
  self.m_sortingOrder = sortingOrder
end

function BaseShopListContainer:OnClose()
  for _, cell in ipairs(self.m_cells) do
    cell:OnClose()
  end
  self.m_focusRootTrans.gameObject:SetActive(false)
end

function BaseShopListContainer:GetContentHeight()
  return self.gameObject.activeSelf and self.m_height or 0
end

function BaseShopListContainer:_InitContainer()
  self.m_cells = {}
  self.m_cellCount = 0
  self.m_refreshButtonGo:SetActive(self.m_canCostForRefresh)
  self.m_adRefreshButtonGo:SetActive(self.m_canCostForRefresh and GM.AdModel:CanShowAd(EAdType.ShopRefresh))
  self:_TryUpdateRefreshCostText()
  self.m_originHeight = self.gameObject.transform.sizeDelta.y
  self.m_refreshContentHeight = self.m_refreshContentGo.transform.sizeDelta.y
  self.m_cellWidth = self.m_cellsLayoutGroup.cellSize.x
  self.m_cellHeight = self.m_cellsLayoutGroup.cellSize.y
  self.m_spacingWidth = self.m_cellsLayoutGroup.spacing.x
  self.m_spacingHeight = self.m_cellsLayoutGroup.spacing.y
end

function BaseShopListContainer:_TryUpdateRefreshCostText()
  if self.m_canCostForRefresh then
    self.m_refreshButtonText.text = GM.ShopModel:GetRefreshGemCost()
  end
end

function BaseShopListContainer:_UpdateContentForCellCount()
  local rows = (self.m_cellCount - 1) // self.m_cellNumberInRow + 1
  self.m_height = self.m_originHeight + (self.m_cellHeight + self.m_spacingHeight) * math.max(0, rows - 1) + self.m_cellHeight - (self.m_refreshTime and 0 or self.m_refreshContentHeight)
  self:_UpdateHeight()
end

function BaseShopListContainer:_UpdateHeight()
  self.gameObject:SetActive(false)
  self.gameObject.transform.sizeDelta = Vector2(self.gameObject.transform.sizeDelta.x, self.m_height)
  self.gameObject:SetActive(true)
end

function BaseShopListContainer:OnAdButtonClicked()
  self.m_adRefresh = true
  GM.AdModel:ShowAd(EAdType.ShopRefresh, self.m_shopType)
end

function BaseShopListContainer:OnRefreshButtonClicked()
  local currentGemCount = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
  local gemCost = GM.ShopModel:GetRefreshGemCost()
  if currentGemCount >= gemCost then
    if self:_DoRefresh(false) then
      self:_UpdateContent()
    end
    self:_TryUpdateRefreshCostText()
  else
    GM.ShopModel:OnLackOfGem(gemCost - currentGemCount)
  end
end

function BaseShopListContainer:ShowFocusEffect()
  GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.effect_shop_tishi), self.m_focusRootTrans, Vector3(0, 30, 0))
end
