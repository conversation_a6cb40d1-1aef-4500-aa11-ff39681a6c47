import re
import os
import traceback
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from pathlib import Path
import time

def write_to_excel(all_orders, output_file):
    if not all_orders:
        print("没有找到任何订单，不创建Excel文件")
        return

    print(f"准备写入Excel文件: {output_file}")
    try:
        wb = Workbook()
        # 删除默认创建的sheet
        wb.remove(wb.active)
        
        # 写入每个文件的数据到不同的sheet
        for file_name, orders in all_orders.items():
            # 使用文件名作为sheet名（去掉路径和扩展名）
            sheet_name = Path(file_name).stem
            ws = wb.create_sheet(sheet_name)
            
            # 写入表头
            headers = ['Id', 'GroupId', 'ChapterId', 
                      'Requirement_1_Type', 'Requirement_1_Count',
                      'Requirement_2_Type', 'Requirement_2_Count',
                      'Flambe', 'Rewards_Currency', 'Rewards_Amount',
                      'Reward_1', 'Reward_2']
            
            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)
            
            # 写入数据
            for row, order in enumerate(orders, 2):
                ws.cell(row=row, column=1, value=order.get('Id', ''))
                ws.cell(row=row, column=2, value=order.get('GroupId', ''))
                ws.cell(row=row, column=3, value=order.get('ChapterId', ''))
                ws.cell(row=row, column=4, value=order.get('Requirement_1_Type', ''))
                ws.cell(row=row, column=5, value=order.get('Requirement_1_Count', ''))
                ws.cell(row=row, column=6, value=order.get('Requirement_2_Type', ''))
                ws.cell(row=row, column=7, value=order.get('Requirement_2_Count', ''))
                ws.cell(row=row, column=8, value=order.get('Flambe', ''))
                ws.cell(row=row, column=9, value=order.get('Rewards_Currency', ''))
                ws.cell(row=row, column=10, value=order.get('Rewards_Amount', ''))
                ws.cell(row=row, column=11, value=order.get('Reward_1', ''))
                ws.cell(row=row, column=12, value=order.get('Reward_2', ''))
            
            # 调整列宽
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[get_column_letter(col)].width = 20
        
        wb.save(output_file)
        print(f"Excel文件已保存到: {output_file}")
        
    except Exception as e:
        print(f"写入Excel文件时出错: {str(e)}")
        print(traceback.format_exc())

def extract_order_info(file_path):
    print(f"\n开始处理文件: {file_path}")
    if not os.path.exists(file_path):
        print(f"错误：文件不存在: {file_path}")
        return []
    
    orders = []
    try:
        file_size = os.path.getsize(file_path)
        print(f"文件大小: {file_size/1024/1024:.2f} MB")
        
        start_time = time.time()
        with open(file_path, 'r', encoding='utf-8') as f:
            try:
                print(f"开始逐行读取文件...")
                current_order = {}
                line_count = 0
                in_rewards = False
                rewards_lines = []
                
                for line in f:
                    line_count += 1
                    if line_count % 10000 == 0:
                        print(f"已处理 {line_count} 行...")
                    
                    # 检查是否是新订单的开始
                    if 'Id = "' in line:
                        if current_order:
                            orders.append(current_order)
                        current_order = {}
                        id_match = re.search(r'Id\s*=\s*"([^"]+)"', line)
                        if id_match:
                            current_order['Id'] = id_match.group(1)
                    
                    # 提取GroupId
                    if 'GroupId' in line:
                        group_match = re.search(r'GroupId\s*=\s*(\d+)', line)
                        if group_match:
                            current_order['GroupId'] = int(group_match.group(1))
                    
                    # 提取ChapterId
                    if 'ChapterId' in line:
                        chapter_match = re.search(r'ChapterId\s*=\s*(\d+)', line)
                        if chapter_match:
                            current_order['ChapterId'] = int(chapter_match.group(1))
                    
                    # 提取Requirement_1
                    if 'Requirement_1' in line:
                        type_match = re.search(r'Type\s*=\s*"([^"]+)"', line)
                        count_match = re.search(r'Count\s*=\s*(\d+)', line)
                        if type_match:
                            current_order['Requirement_1_Type'] = type_match.group(1)
                        if count_match:
                            current_order['Requirement_1_Count'] = int(count_match.group(1))
                    
                    # 提取Requirement_2
                    if 'Requirement_2' in line:
                        type_match = re.search(r'Type\s*=\s*"([^"]+)"', line)
                        count_match = re.search(r'Count\s*=\s*(\d+)', line)
                        if type_match:
                            current_order['Requirement_2_Type'] = type_match.group(1)
                        if count_match:
                            current_order['Requirement_2_Count'] = int(count_match.group(1))
                    
                    # 提取Flambe（如果有）
                    if 'Flambe' in line:
                        flambe_match = re.search(r'Flambe\s*=\s*(\d+)', line)
                        if flambe_match:
                            current_order['Flambe'] = int(flambe_match.group(1))
                    
                    # 处理Rewards部分
                    if 'Rewards = {' in line:
                        in_rewards = True
                        rewards_lines = []
                        continue
                    
                    if in_rewards:
                        if '}' in line and len(rewards_lines) > 0:  # 确保不是空的Rewards
                            in_rewards = False
                            rewards_text = ' '.join(rewards_lines)
                            # 提取第一个奖励的Currency和Amount
                            currency_match = re.search(r'Currency\s*=\s*"([^"]+)"', rewards_text)
                            amount_match = re.search(r'Amount\s*=\s*(\d+)', rewards_text)
                            if currency_match:
                                current_order['Rewards_Currency'] = currency_match.group(1)
                            if amount_match:
                                current_order['Rewards_Amount'] = int(amount_match.group(1))
                        else:
                            rewards_lines.append(line.strip())
                    
                    # 提取Reward_1和Reward_2（如果有）
                    if 'Reward_1' in line:
                        reward_match = re.search(r'Reward_1\s*=\s*"([^"]+)"', line)
                        if reward_match:
                            current_order['Reward_1'] = reward_match.group(1)
                    
                    if 'Reward_2' in line:
                        reward_match = re.search(r'Reward_2\s*=\s*"([^"]+)"', line)
                        if reward_match:
                            current_order['Reward_2'] = reward_match.group(1)
                
                # 处理最后一个订单
                if current_order:
                    orders.append(current_order)
                
            except UnicodeDecodeError:
                print(f"UTF-8编码失败，尝试使用GBK编码...")
                f.seek(0)
                content = f.read().encode('utf-8').decode('gbk')
            
            end_time = time.time()
            print(f"文件处理完成，耗时: {end_time - start_time:.2f} 秒")
            print(f"找到 {len(orders)} 个订单")
            
    except Exception as e:
        print(f"处理文件时发生错误:")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print("详细错误信息:")
        print(traceback.format_exc())
    
    return orders

# 处理所有配置文件
# 设置基础路径，可根据实际工作空间调整
# base_path = '../'  # 这个相对路径不正确

# 方案1：使用绝对路径（推荐）
base_path = 'd:/MCWork/GHabout/FMC/fmc_lua_1.18.5'  # 请根据实际路径调整

# 方案2：如果fmc_lua目录与工具目录在同一级别，则使用：
# base_path = '..'  # 如果是在工具目录下运行脚本

# 方案3：如果fmc_lua目录就在工具目录内，则使用：
# base_path = '.'  # 当前目录

# 配置文件列表
config_file_names = [
    'OrderFixedConfig_1.lua',
    'OrderFixedConfig_2.lua',
    'OrderFixedConfig_3.lua',
    'OrderFixedConfig_4.lua',
    'OrderFixedConfig_5.lua',
    'OrderFixedConfig_6.lua',
    'OrderFixedConfig_7.lua',
    'OrderFixedConfig_8.lua',
    'OrderFixedConfig_9.lua',
    'OrderFixedConfig_10.lua',
    'OrderFixedConfig_11.lua',
    'OrderFixedConfig_12.lua',
    'OrderFixedConfig_13.lua',
    'OrderFixedConfig_14.lua',
    'OrderFixedConfig_15.lua',
    'OrderFixedConfig_16.lua',
]

config_path = os.path.join(base_path, 'fmc_lua', 'Data', 'Config')
# 增加路径检查，帮助诊断
if not os.path.exists(config_path):
    print(f"警告：配置目录不存在: {config_path}")
    print("请检查base_path设置是否正确")

config_files = [os.path.join(config_path, file_name) for file_name in config_file_names]

print("开始处理配置文件...")
desktop = os.path.join(os.path.expanduser("~"), "Desktop")
output_file = os.path.join(desktop, "OrderFixedConfig_All.xlsx")

# 存储所有文件的数据
all_orders = {}

for file_path in config_files:
    print(f"\n{'='*50}")
    print(f"准备处理文件: {file_path}")
    if os.path.exists(file_path):
        orders = extract_order_info(file_path)
        if orders:
            all_orders[file_path] = orders
            print(f"文件 {file_path} 处理完成，找到 {len(orders)} 个订单")
    else:
        print(f"跳过不存在的文件: {file_path}")
    print(f"{'='*50}\n")

# 将所有数据写入同一个Excel文件
if all_orders:
    write_to_excel(all_orders, output_file)
    print(f"\n所有数据已保存到: {output_file}")
else:
    print("\n没有找到任何数据")

print("\n所有文件处理完成！") 