"""
增强版订单生成器
基于实际OrderFixedConfig_4.lua结构，生成类似配置效果
支持设置母棋子和器械情况
"""

import os
import re
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter import *
from collections import defaultdict
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import random
import math
from datetime import datetime

@dataclass
class PlayerState:
    """玩家状态（母棋子和器械情况）"""
    generators: Dict[str, int] = field(default_factory=dict)  # 生成器及数量
    equipment: Dict[str, int] = field(default_factory=dict)   # 器械及数量
    chapter_progress: int = 1  # 章节进度
    day_progress: int = 1      # 天数进度
    unlocked_items: List[str] = field(default_factory=list)  # 已解锁物品

@dataclass
class OrderRequirement:
    """订单需求"""
    type: str
    count: int = 1

@dataclass
class OrderReward:
    """订单奖励"""
    currency: str
    amount: int = 1

@dataclass
class GeneratedOrder:
    """生成的订单"""
    id: str
    group_id: int
    chapter_id: int
    requirement_1: OrderRequirement
    requirement_2: Optional[OrderRequirement] = None
    rewards: List[OrderReward] = field(default_factory=list)

@dataclass
class GeneratedOrderGroup:
    """生成的订单组"""
    group_id: int
    chapter_id: int
    day: int
    orders: List[GeneratedOrder] = field(default_factory=list)
    group_rewards: List[OrderReward] = field(default_factory=list)
    flambe: Optional[str] = None  # "link" 或 None

class EnhancedOrderGenerator:
    """增强版订单生成器"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("增强版订单生成器 - 基于实际配置结构")
        self.root.geometry("1600x1000")
        
        # 数据存储
        self.config_path = os.path.join(os.path.dirname(__file__), "Data", "Config")
        self.items_data = {}  # 所有物品数据
        self.player_state = PlayerState()  # 玩家状态
        self.generated_groups = []  # 生成的订单组
        
        # 界面变量
        self.selected_chapter = IntVar(value=4)
        self.start_day = IntVar(value=34)
        self.generation_days = IntVar(value=7)
        self.start_group_id = IntVar(value=1)
        
        # 生成参数
        self.difficulty_level = StringVar(value="普通")
        self.reward_mode = StringVar(value="平衡")
        self.flambe_probability = DoubleVar(value=0.4)  # 40%概率有flambe
        self.single_order_reward_prob = DoubleVar(value=0.3)  # 30%概率单个订单有奖励
        
        # 创建界面
        self._create_ui()
        
        # 加载配置数据
        self._load_config_data()
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主菜单
        menubar = Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="导出OrderFixedConfig", command=self._export_order_fixed_config)
        file_menu.add_command(label="导出OrderGroupConfig", command=self._export_order_group_config)
        file_menu.add_separator()
        file_menu.add_command(label="保存玩家状态", command=self._save_player_state)
        file_menu.add_command(label="加载玩家状态", command=self._load_player_state)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        menubar.add_cascade(label="文件", menu=file_menu)
        
        # 生成菜单
        generate_menu = Menu(menubar, tearoff=0)
        generate_menu.add_command(label="生成订单", command=self._generate_orders)
        generate_menu.add_command(label="清空结果", command=self._clear_results)
        generate_menu.add_command(label="预览配置", command=self._preview_config)
        menubar.add_cascade(label="生成", menu=generate_menu)
        
        # 创建主框架
        main_paned = ttk.PanedWindow(self.root, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_paned, width=450)
        main_paned.add(control_frame, weight=1)
        
        # 右侧显示面板
        display_frame = ttk.Frame(main_paned)
        main_paned.add(display_frame, weight=2)
        
        # 创建控制面板
        self._create_control_panel(control_frame)
        
        # 创建显示面板
        self._create_display_panel(display_frame)
        
        # 状态栏
        self.status_var = StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=SUNKEN)
        status_bar.pack(side=BOTTOM, fill=X)
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 基础设置
        basic_frame = ttk.LabelFrame(parent, text="基础设置", padding="10")
        basic_frame.pack(fill=X, pady=5)
        
        # 章节设置
        ttk.Label(basic_frame, text="目标章节:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        chapter_spin = ttk.Spinbox(basic_frame, from_=1, to=16, textvariable=self.selected_chapter, width=15)
        chapter_spin.grid(row=0, column=1, sticky=W, padx=5, pady=2)
        
        # 起始天数
        ttk.Label(basic_frame, text="起始天数:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        start_day_spin = ttk.Spinbox(basic_frame, from_=1, to=365, textvariable=self.start_day, width=15)
        start_day_spin.grid(row=1, column=1, sticky=W, padx=5, pady=2)
        
        # 生成天数
        ttk.Label(basic_frame, text="生成天数:").grid(row=2, column=0, sticky=W, padx=5, pady=2)
        days_spin = ttk.Spinbox(basic_frame, from_=1, to=30, textvariable=self.generation_days, width=15)
        days_spin.grid(row=2, column=1, sticky=W, padx=5, pady=2)
        
        # 起始组ID
        ttk.Label(basic_frame, text="起始组ID:").grid(row=3, column=0, sticky=W, padx=5, pady=2)
        group_id_spin = ttk.Spinbox(basic_frame, from_=1, to=100, textvariable=self.start_group_id, width=15)
        group_id_spin.grid(row=3, column=1, sticky=W, padx=5, pady=2)
        
        # 生成参数
        params_frame = ttk.LabelFrame(parent, text="生成参数", padding="10")
        params_frame.pack(fill=X, pady=5)
        
        # 难度等级
        ttk.Label(params_frame, text="难度等级:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        difficulty_combo = ttk.Combobox(params_frame, textvariable=self.difficulty_level,
                                       values=["简单", "普通", "困难", "专家"], state="readonly", width=15)
        difficulty_combo.grid(row=0, column=1, sticky=W, padx=5, pady=2)
        
        # 奖励模式
        ttk.Label(params_frame, text="奖励模式:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        reward_combo = ttk.Combobox(params_frame, textvariable=self.reward_mode,
                                   values=["节约", "平衡", "丰富", "豪华"], state="readonly", width=15)
        reward_combo.grid(row=1, column=1, sticky=W, padx=5, pady=2)
        
        # Flambe概率
        ttk.Label(params_frame, text="Flambe概率:").grid(row=2, column=0, sticky=W, padx=5, pady=2)
        flambe_scale = ttk.Scale(params_frame, from_=0.0, to=1.0, variable=self.flambe_probability, 
                                orient=HORIZONTAL, length=150)
        flambe_scale.grid(row=2, column=1, sticky=W, padx=5, pady=2)
        
        # 单订单奖励概率
        ttk.Label(params_frame, text="单订单奖励概率:").grid(row=3, column=0, sticky=W, padx=5, pady=2)
        single_reward_scale = ttk.Scale(params_frame, from_=0.0, to=1.0, 
                                       variable=self.single_order_reward_prob, 
                                       orient=HORIZONTAL, length=150)
        single_reward_scale.grid(row=3, column=1, sticky=W, padx=5, pady=2)
        
        # 玩家状态设置
        player_frame = ttk.LabelFrame(parent, text="玩家状态设置", padding="10")
        player_frame.pack(fill=BOTH, expand=True, pady=5)
        
        # 创建Notebook用于玩家状态标签页
        player_notebook = ttk.Notebook(player_frame)
        player_notebook.pack(fill=BOTH, expand=True)
        
        # 生成器标签页
        self._create_generators_tab(player_notebook)
        
        # 器械标签页
        self._create_equipment_tab(player_notebook)
        
        # 操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=X, pady=10)
        
        ttk.Button(button_frame, text="生成订单", command=self._generate_orders,
                  style="Accent.TButton").pack(fill=X, pady=2)
        ttk.Button(button_frame, text="清空结果", command=self._clear_results).pack(fill=X, pady=2)
        ttk.Button(button_frame, text="导出配置", command=self._export_configs).pack(fill=X, pady=2)
    
    def _create_generators_tab(self, parent):
        """创建生成器标签页"""
        generators_frame = ttk.Frame(parent)
        parent.add(generators_frame, text="生成器")
        
        # 生成器列表
        gen_columns = ("code", "name", "level", "series", "count")
        self.generators_tree = ttk.Treeview(generators_frame, columns=gen_columns, show="headings", height=8)
        
        self.generators_tree.heading("code", text="代码")
        self.generators_tree.heading("name", text="名称")
        self.generators_tree.heading("level", text="等级")
        self.generators_tree.heading("series", text="系列")
        self.generators_tree.heading("count", text="数量")
        
        self.generators_tree.column("code", width=80)
        self.generators_tree.column("name", width=100)
        self.generators_tree.column("level", width=50)
        self.generators_tree.column("series", width=50)
        self.generators_tree.column("count", width=50)
        
        gen_scrollbar = ttk.Scrollbar(generators_frame, orient=VERTICAL, command=self.generators_tree.yview)
        self.generators_tree.configure(yscrollcommand=gen_scrollbar.set)
        
        self.generators_tree.pack(side=LEFT, fill=BOTH, expand=True)
        gen_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 添加生成器按钮
        gen_button_frame = ttk.Frame(generators_frame)
        gen_button_frame.pack(fill=X, pady=5)
        
        ttk.Button(gen_button_frame, text="添加生成器", command=self._add_generator).pack(side=LEFT, padx=5)
        ttk.Button(gen_button_frame, text="删除选中", command=self._remove_generator).pack(side=LEFT, padx=5)
        ttk.Button(gen_button_frame, text="自动配置", command=self._auto_config_generators).pack(side=LEFT, padx=5)
    
    def _create_equipment_tab(self, parent):
        """创建器械标签页"""
        equipment_frame = ttk.Frame(parent)
        parent.add(equipment_frame, text="器械")
        
        # 器械列表
        eq_columns = ("code", "name", "level", "series", "count")
        self.equipment_tree = ttk.Treeview(equipment_frame, columns=eq_columns, show="headings", height=8)
        
        self.equipment_tree.heading("code", text="代码")
        self.equipment_tree.heading("name", text="名称")
        self.equipment_tree.heading("level", text="等级")
        self.equipment_tree.heading("series", text="系列")
        self.equipment_tree.heading("count", text="数量")
        
        self.equipment_tree.column("code", width=80)
        self.equipment_tree.column("name", width=100)
        self.equipment_tree.column("level", width=50)
        self.equipment_tree.column("series", width=50)
        self.equipment_tree.column("count", width=50)
        
        eq_scrollbar = ttk.Scrollbar(equipment_frame, orient=VERTICAL, command=self.equipment_tree.yview)
        self.equipment_tree.configure(yscrollcommand=eq_scrollbar.set)
        
        self.equipment_tree.pack(side=LEFT, fill=BOTH, expand=True)
        eq_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 添加器械按钮
        eq_button_frame = ttk.Frame(equipment_frame)
        eq_button_frame.pack(fill=X, pady=5)
        
        ttk.Button(eq_button_frame, text="添加器械", command=self._add_equipment).pack(side=LEFT, padx=5)
        ttk.Button(eq_button_frame, text="删除选中", command=self._remove_equipment).pack(side=LEFT, padx=5)
        ttk.Button(eq_button_frame, text="自动配置", command=self._auto_config_equipment).pack(side=LEFT, padx=5)
    
    def _create_display_panel(self, parent):
        """创建显示面板"""
        # 创建Notebook用于多标签页
        self.display_notebook = ttk.Notebook(parent)
        self.display_notebook.pack(fill=BOTH, expand=True)
        
        # 订单预览标签页
        self._create_orders_preview_tab()
        
        # 配置预览标签页
        self._create_config_preview_tab()
        
        # 统计分析标签页
        self._create_statistics_tab()
    
    def _create_orders_preview_tab(self):
        """创建订单预览标签页"""
        orders_frame = ttk.Frame(self.display_notebook)
        self.display_notebook.add(orders_frame, text="订单预览")
        
        # 订单列表
        columns = ("id", "group_id", "day", "req1", "req2", "rewards")
        self.orders_tree = ttk.Treeview(orders_frame, columns=columns, show="tree headings", height=25)
        
        self.orders_tree.heading("#0", text="订单组")
        self.orders_tree.heading("id", text="订单ID")
        self.orders_tree.heading("group_id", text="组ID")
        self.orders_tree.heading("day", text="天数")
        self.orders_tree.heading("req1", text="需求1")
        self.orders_tree.heading("req2", text="需求2")
        self.orders_tree.heading("rewards", text="奖励")
        
        self.orders_tree.column("#0", width=120)
        self.orders_tree.column("id", width=80)
        self.orders_tree.column("group_id", width=60)
        self.orders_tree.column("day", width=60)
        self.orders_tree.column("req1", width=150)
        self.orders_tree.column("req2", width=150)
        self.orders_tree.column("rewards", width=200)
        
        orders_scrollbar = ttk.Scrollbar(orders_frame, orient=VERTICAL, command=self.orders_tree.yview)
        self.orders_tree.configure(yscrollcommand=orders_scrollbar.set)
        
        self.orders_tree.pack(side=LEFT, fill=BOTH, expand=True)
        orders_scrollbar.pack(side=RIGHT, fill=Y)
    
    def _create_config_preview_tab(self):
        """创建配置预览标签页"""
        config_frame = ttk.Frame(self.display_notebook)
        self.display_notebook.add(config_frame, text="配置预览")
        
        self.config_text = Text(config_frame, wrap=NONE, font=("Consolas", 10))
        config_scrollbar_v = ttk.Scrollbar(config_frame, orient=VERTICAL, command=self.config_text.yview)
        config_scrollbar_h = ttk.Scrollbar(config_frame, orient=HORIZONTAL, command=self.config_text.xview)
        self.config_text.configure(yscrollcommand=config_scrollbar_v.set, xscrollcommand=config_scrollbar_h.set)
        
        self.config_text.pack(side=LEFT, fill=BOTH, expand=True)
        config_scrollbar_v.pack(side=RIGHT, fill=Y)
        config_scrollbar_h.pack(side=BOTTOM, fill=X)
    
    def _create_statistics_tab(self):
        """创建统计分析标签页"""
        stats_frame = ttk.Frame(self.display_notebook)
        self.display_notebook.add(stats_frame, text="统计分析")
        
        self.stats_text = Text(stats_frame, wrap=WORD, font=("Consolas", 10))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=LEFT, fill=BOTH, expand=True)
        stats_scrollbar.pack(side=RIGHT, fill=Y)

    def _load_config_data(self):
        """加载配置数据"""
        try:
            self.status_var.set("正在加载配置数据...")

            # 加载ItemModelConfig.lua
            self._load_item_model_config()

            # 初始化默认玩家状态
            self._init_default_player_state()

            # 更新显示
            self._update_generators_display()
            self._update_equipment_display()

            self.status_var.set(f"配置加载完成 - 物品:{len(self.items_data)}")

        except Exception as e:
            self.status_var.set(f"配置加载失败: {e}")
            messagebox.showerror("错误", f"配置加载失败: {e}")

    def _load_item_model_config(self):
        """加载物品模型配置"""
        config_file = os.path.join(self.config_path, "ItemModelConfig.lua")

        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")

        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析Lua配置
        self._parse_item_model_content(content)

    def _parse_item_model_content(self, content):
        """解析物品模型内容"""
        # 移除注释
        content = re.sub(r'--.*?\n', '\n', content)

        # 查找所有配置块
        pattern = r'\{\s*Type\s*=\s*"([^"]+)"([^}]*)\}'
        matches = re.finditer(pattern, content, re.DOTALL)

        for match in matches:
            item_type = match.group(1)
            item_content = match.group(2)

            item = {
                'code': item_type,
                'name': item_type,  # 简化处理
                'type': self._get_item_type(item_type),
                'level': self._extract_level(item_type),
                'series': self._extract_series(item_type),
                'energy_cost': self._extract_energy_cost(item_content),
                'frequency': self._extract_frequency(item_content),
                'capacity': self._extract_capacity(item_content)
            }

            self.items_data[item_type] = item

    def _get_item_type(self, item_code):
        """获取物品类型"""
        if item_code.startswith('pd_'):
            return "生成器"
        elif item_code.startswith('eq_'):
            return "器械"
        elif item_code.startswith('it_'):
            return "物品"
        elif item_code.startswith('ds_'):
            return "菜品"
        else:
            return "其他"

    def _extract_level(self, item_code):
        """提取物品等级"""
        parts = item_code.split('_')
        if len(parts) >= 3:
            try:
                return int(parts[2])
            except ValueError:
                pass
        return 1

    def _extract_series(self, item_code):
        """提取物品系列"""
        parts = item_code.split('_')
        if len(parts) >= 2:
            try:
                return int(parts[1])
            except ValueError:
                pass
        return 1

    def _extract_energy_cost(self, content):
        """提取能量消耗"""
        match = re.search(r'UseEnergy\s*=\s*(\d+)', content)
        return int(match.group(1)) if match else 1

    def _extract_frequency(self, content):
        """提取频率"""
        match = re.search(r'Frequency\s*=\s*(\d+)', content)
        return int(match.group(1)) if match else 10

    def _extract_capacity(self, content):
        """提取容量"""
        match = re.search(r'Capacity\s*=\s*(\d+)', content)
        return int(match.group(1)) if match else 1

    def _init_default_player_state(self):
        """初始化默认玩家状态"""
        # 根据章节设置默认的生成器和器械
        chapter = self.selected_chapter.get()

        # 默认生成器配置
        default_generators = {
            'pd_1_3': 2, 'pd_2_3': 2, 'pd_3_3': 1, 'pd_4_3': 1, 'pd_5_3': 1, 'pd_6_3': 1
        }

        # 默认器械配置
        default_equipment = {
            'eq_1_3': 2, 'eq_2_2': 3, 'eq_2_3': 1, 'eq_3_2': 2, 'eq_3_3': 1,
            'eq_4_3': 1, 'eq_5_3': 2
        }

        # 根据章节调整数量
        multiplier = min(chapter / 4.0, 2.0)  # 章节越高，设备越多

        for gen_code, count in default_generators.items():
            if gen_code in self.items_data:
                self.player_state.generators[gen_code] = max(1, int(count * multiplier))

        for eq_code, count in default_equipment.items():
            if eq_code in self.items_data:
                self.player_state.equipment[eq_code] = max(1, int(count * multiplier))

    def _update_generators_display(self):
        """更新生成器显示"""
        # 清空现有数据
        for item in self.generators_tree.get_children():
            self.generators_tree.delete(item)

        # 添加生成器数据
        for gen_code, count in self.player_state.generators.items():
            if gen_code in self.items_data:
                item = self.items_data[gen_code]
                self.generators_tree.insert("", "end", values=(
                    gen_code,
                    item['name'],
                    item['level'],
                    item['series'],
                    count
                ))

    def _update_equipment_display(self):
        """更新器械显示"""
        # 清空现有数据
        for item in self.equipment_tree.get_children():
            self.equipment_tree.delete(item)

        # 添加器械数据
        for eq_code, count in self.player_state.equipment.items():
            if eq_code in self.items_data:
                item = self.items_data[eq_code]
                self.equipment_tree.insert("", "end", values=(
                    eq_code,
                    item['name'],
                    item['level'],
                    item['series'],
                    count
                ))

    def _add_generator(self):
        """添加生成器"""
        # 创建选择窗口
        self._open_item_selector("生成器", "pd_", self.player_state.generators)

    def _add_equipment(self):
        """添加器械"""
        # 创建选择窗口
        self._open_item_selector("器械", "eq_", self.player_state.equipment)

    def _open_item_selector(self, item_type, prefix, target_dict):
        """打开物品选择窗口"""
        selector_window = Toplevel(self.root)
        selector_window.title(f"选择{item_type}")
        selector_window.geometry("600x400")
        selector_window.transient(self.root)
        selector_window.grab_set()

        # 物品列表
        columns = ("code", "name", "level", "series")
        item_tree = ttk.Treeview(selector_window, columns=columns, show="headings", height=15)

        item_tree.heading("code", text="代码")
        item_tree.heading("name", text="名称")
        item_tree.heading("level", text="等级")
        item_tree.heading("series", text="系列")

        # 添加符合条件的物品
        for code, item in self.items_data.items():
            if code.startswith(prefix):
                item_tree.insert("", "end", values=(
                    code, item['name'], item['level'], item['series']
                ))

        item_tree.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # 数量输入
        count_frame = ttk.Frame(selector_window)
        count_frame.pack(fill=X, padx=10, pady=5)

        ttk.Label(count_frame, text="数量:").pack(side=LEFT)
        count_var = IntVar(value=1)
        count_spin = ttk.Spinbox(count_frame, from_=1, to=10, textvariable=count_var, width=10)
        count_spin.pack(side=LEFT, padx=5)

        # 按钮
        button_frame = ttk.Frame(selector_window)
        button_frame.pack(fill=X, padx=10, pady=10)

        def add_selected():
            selection = item_tree.selection()
            if selection:
                item_values = item_tree.item(selection[0])['values']
                item_code = item_values[0]
                count = count_var.get()

                target_dict[item_code] = target_dict.get(item_code, 0) + count

                # 更新显示
                if prefix == "pd_":
                    self._update_generators_display()
                else:
                    self._update_equipment_display()

                selector_window.destroy()

        ttk.Button(button_frame, text="添加", command=add_selected).pack(side=RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=selector_window.destroy).pack(side=RIGHT, padx=5)

    def _remove_generator(self):
        """删除选中的生成器"""
        selection = self.generators_tree.selection()
        if selection:
            item_values = self.generators_tree.item(selection[0])['values']
            gen_code = item_values[0]
            if gen_code in self.player_state.generators:
                del self.player_state.generators[gen_code]
                self._update_generators_display()

    def _remove_equipment(self):
        """删除选中的器械"""
        selection = self.equipment_tree.selection()
        if selection:
            item_values = self.equipment_tree.item(selection[0])['values']
            eq_code = item_values[0]
            if eq_code in self.player_state.equipment:
                del self.player_state.equipment[eq_code]
                self._update_equipment_display()

    def _auto_config_generators(self):
        """自动配置生成器"""
        chapter = self.selected_chapter.get()
        self._init_default_player_state()
        self._update_generators_display()
        messagebox.showinfo("成功", f"已自动配置章节{chapter}的推荐生成器")

    def _auto_config_equipment(self):
        """自动配置器械"""
        chapter = self.selected_chapter.get()
        self._init_default_player_state()
        self._update_equipment_display()
        messagebox.showinfo("成功", f"已自动配置章节{chapter}的推荐器械")

    def _generate_orders(self):
        """生成订单"""
        try:
            self.status_var.set("正在生成订单...")

            # 清空之前的结果
            self.generated_groups = []

            # 获取生成参数
            chapter_id = self.selected_chapter.get()
            start_day = self.start_day.get()
            days_count = self.generation_days.get()
            start_group_id = self.start_group_id.get()

            # 生成每天的订单组
            for day_offset in range(days_count):
                current_day = start_day + day_offset
                current_group_id = start_group_id + day_offset

                # 生成订单组
                order_group = self._generate_order_group(
                    group_id=current_group_id,
                    chapter_id=chapter_id,
                    day=current_day
                )

                self.generated_groups.append(order_group)

            # 更新显示
            self._update_orders_display()
            self._update_statistics()
            self._update_config_preview()

            self.status_var.set(f"订单生成完成 - {days_count}天，{len(self.generated_groups) * 7}个订单")

        except Exception as e:
            self.status_var.set(f"订单生成失败: {e}")
            messagebox.showerror("错误", f"订单生成失败: {e}")

    def _generate_order_group(self, group_id, chapter_id, day):
        """生成单个订单组（7个订单）"""
        order_group = GeneratedOrderGroup(
            group_id=group_id,
            chapter_id=chapter_id,
            day=day
        )

        # 决定是否有flambe
        if random.random() < self.flambe_probability.get():
            order_group.flambe = "link"

        # 生成7个订单
        for order_index in range(7):
            order_id = f"{chapter_id}{group_id:02d}{(order_index + 1) * 10}"

            order = self._generate_single_order(
                order_id=order_id,
                group_id=group_id,
                chapter_id=chapter_id
            )

            order_group.orders.append(order)

        # 生成组奖励
        order_group.group_rewards = self._generate_group_rewards(chapter_id, group_id)

        return order_group

    def _generate_single_order(self, order_id, group_id, chapter_id):
        """生成单个订单"""
        order = GeneratedOrder(
            id=order_id,
            group_id=group_id,
            chapter_id=chapter_id,
            requirement_1=self._generate_requirement(),
            requirement_2=self._generate_requirement() if random.random() < 0.8 else None
        )

        # 30%概率有单个订单奖励
        if random.random() < self.single_order_reward_prob.get():
            order.rewards = [OrderReward(currency="additem_1", amount=1)]

        return order

    def _generate_requirement(self):
        """生成订单需求"""
        # 根据玩家状态和难度选择合适的物品
        available_items = self._get_available_items_for_requirements()

        if not available_items:
            # 如果没有可用物品，使用基础物品
            return OrderRequirement(type="clean", count=1)

        # 随机选择物品
        item_code = random.choice(available_items)
        count = 1  # 大部分订单都是1个

        return OrderRequirement(type=item_code, count=count)

    def _get_available_items_for_requirements(self):
        """获取可用于需求的物品列表"""
        available_items = []

        # 基础物品（总是可用）
        basic_items = ["clean"]
        available_items.extend(basic_items)

        # 根据玩家拥有的生成器，添加可生产的物品
        for gen_code in self.player_state.generators:
            if gen_code in self.items_data:
                # 根据生成器类型添加对应的产出物品
                generated_items = self._get_generator_products(gen_code)
                available_items.extend(generated_items)

        # 添加一些菜品（基于章节）
        chapter_dishes = self._get_chapter_dishes(self.selected_chapter.get())
        available_items.extend(chapter_dishes)

        return list(set(available_items))  # 去重

    def _get_generator_products(self, gen_code):
        """获取生成器的产出物品"""
        # 简化处理，根据生成器代码推断产出
        products = []

        # 解析生成器系列和等级
        parts = gen_code.split('_')
        if len(parts) >= 3:
            series = parts[1]
            level = parts[2]

            # 根据系列生成对应的物品
            for item_level in range(1, int(level) + 1):
                for sub_level in range(1, 10):  # 假设每个等级有多个子等级
                    item_code = f"it_{series}_{item_level}_{sub_level}"
                    if item_code in self.items_data:
                        products.append(item_code)

        return products

    def _get_chapter_dishes(self, chapter_id):
        """获取章节相关的菜品"""
        dishes = []

        # 根据章节添加相应的菜品
        dish_types = ["ds_grillmt", "ds_fd", "ds_juice", "ds_mixdrk", "ds_dst",
                     "ds_friedve", "ds_friedsf", "ds_grillsf", "ds_grillve",
                     "ds_chopfru", "ds_chopfs", "ds_chopfr", "ds_friedmt"]

        for dish_type in dish_types:
            for level in range(1, min(chapter_id * 3, 25)):  # 章节越高，菜品等级越高
                dish_code = f"{dish_type}_{level}"
                if dish_code in self.items_data:
                    dishes.append(dish_code)

        return dishes

    def _generate_group_rewards(self, chapter_id, group_id):
        """生成组奖励"""
        rewards = []
        reward_mode = self.reward_mode.get()

        # 基础奖励数量
        base_reward_count = {
            "节约": 2,
            "平衡": 3,
            "丰富": 4,
            "豪华": 5
        }.get(reward_mode, 3)

        # 生成器奖励
        generator_types = [f"pd_{i}_3" for i in range(1, 7)]
        selected_gen = random.choice(generator_types)
        rewards.append(OrderReward(currency=selected_gen, amount=1))

        # 器械奖励
        equipment_types = [f"eq_{i}_3" for i in range(1, 6)] + [f"eq_{i}_2" for i in range(2, 4)]
        selected_eq = random.choice(equipment_types)
        rewards.append(OrderReward(currency=selected_eq, amount=random.randint(1, 2)))

        # 额外奖励
        if len(rewards) < base_reward_count:
            extra_rewards = ["greenbox_1", "eq_2_2"]
            for _ in range(base_reward_count - len(rewards)):
                extra_reward = random.choice(extra_rewards)
                rewards.append(OrderReward(currency=extra_reward, amount=1))

        return rewards

    def _update_orders_display(self):
        """更新订单显示"""
        # 清空现有数据
        for item in self.orders_tree.get_children():
            self.orders_tree.delete(item)

        # 添加订单组
        for order_group in self.generated_groups:
            # 格式化组奖励
            group_rewards_text = ", ".join([f"{r.currency}x{r.amount}" for r in order_group.group_rewards])

            # 添加组节点
            flambe_text = " (Flambe)" if order_group.flambe else ""
            group_node = self.orders_tree.insert("", "end",
                                                text=f"第{order_group.day}天 - 组{order_group.group_id}{flambe_text}",
                                                values=("", order_group.group_id, order_group.day,
                                                       "", "", group_rewards_text))

            # 添加订单
            for order in order_group.orders:
                req1_text = f"{order.requirement_1.type} x{order.requirement_1.count}"
                req2_text = f"{order.requirement_2.type} x{order.requirement_2.count}" if order.requirement_2 else ""
                rewards_text = ", ".join([f"{r.currency}x{r.amount}" for r in order.rewards])

                self.orders_tree.insert(group_node, "end", text=f"  订单 {order.id}",
                                      values=(order.id, order.group_id, "", req1_text, req2_text, rewards_text))

        # 展开所有组
        for item in self.orders_tree.get_children():
            self.orders_tree.item(item, open=True)

    def _update_statistics(self):
        """更新统计信息"""
        self.stats_text.delete(1.0, END)

        if not self.generated_groups:
            self.stats_text.insert(END, "暂无生成的订单")
            return

        # 基础统计
        total_groups = len(self.generated_groups)
        total_orders = sum(len(group.orders) for group in self.generated_groups)
        flambe_count = sum(1 for group in self.generated_groups if group.flambe)

        self.stats_text.insert(END, "=== 订单生成统计 ===\n")
        self.stats_text.insert(END, f"总天数: {total_groups}\n")
        self.stats_text.insert(END, f"总订单数: {total_orders}\n")
        self.stats_text.insert(END, f"Flambe天数: {flambe_count}\n")
        self.stats_text.insert(END, f"Flambe比例: {flambe_count/total_groups*100:.1f}%\n\n")

        # 需求物品统计
        requirement_stats = defaultdict(int)
        for group in self.generated_groups:
            for order in group.orders:
                requirement_stats[order.requirement_1.type] += order.requirement_1.count
                if order.requirement_2:
                    requirement_stats[order.requirement_2.type] += order.requirement_2.count

        self.stats_text.insert(END, "=== 需求物品统计 ===\n")
        sorted_requirements = sorted(requirement_stats.items(), key=lambda x: x[1], reverse=True)
        for item_type, count in sorted_requirements[:10]:  # 显示前10个
            item_name = self.items_data.get(item_type, {}).get('name', item_type)
            self.stats_text.insert(END, f"{item_name} ({item_type}): {count}个\n")

        # 奖励统计
        reward_stats = defaultdict(int)
        for group in self.generated_groups:
            for reward in group.group_rewards:
                reward_stats[reward.currency] += reward.amount
            for order in group.orders:
                for reward in order.rewards:
                    reward_stats[reward.currency] += reward.amount

        self.stats_text.insert(END, "\n=== 奖励统计 ===\n")
        sorted_rewards = sorted(reward_stats.items(), key=lambda x: x[1], reverse=True)
        for currency, amount in sorted_rewards:
            self.stats_text.insert(END, f"{currency}: {amount}个\n")

    def _update_config_preview(self):
        """更新配置预览"""
        self.config_text.delete(1.0, END)

        if not self.generated_groups:
            self.config_text.insert(END, "-- 暂无生成的配置")
            return

        # 生成OrderFixedConfig格式
        self.config_text.insert(END, "-- OrderFixedConfig 格式预览\n")
        self.config_text.insert(END, "return {\n")

        for group in self.generated_groups:
            for order in group.orders:
                self.config_text.insert(END, f"  {{\n")
                self.config_text.insert(END, f"    Id = \"{order.id}\",\n")
                self.config_text.insert(END, f"    GroupId = {order.group_id},\n")
                self.config_text.insert(END, f"    ChapterId = {order.chapter_id},\n")
                self.config_text.insert(END, f"    Requirement_1 = {{Type = \"{order.requirement_1.type}\", Count = {order.requirement_1.count}}},\n")

                if order.requirement_2:
                    self.config_text.insert(END, f"    Requirement_2 = {{Type = \"{order.requirement_2.type}\", Count = {order.requirement_2.count}}},\n")

                if order.rewards:
                    self.config_text.insert(END, f"    Rewards = {{\n")
                    for reward in order.rewards:
                        self.config_text.insert(END, f"      {{Currency = \"{reward.currency}\", Amount = {reward.amount}}},\n")
                    self.config_text.insert(END, f"    }},\n")

                self.config_text.insert(END, f"  }},\n")

        self.config_text.insert(END, "}\n\n")

        # 生成OrderGroupConfig格式
        self.config_text.insert(END, "-- OrderGroupConfig 格式预览\n")
        self.config_text.insert(END, "return {\n")

        for group in self.generated_groups:
            self.config_text.insert(END, f"  {{\n")
            self.config_text.insert(END, f"    GroupId = {group.group_id},\n")
            self.config_text.insert(END, f"    ChapterId = {group.chapter_id},\n")
            self.config_text.insert(END, f"    Day = {group.day},\n")

            if group.flambe:
                self.config_text.insert(END, f"    Flambe = \"{group.flambe}\",\n")

            self.config_text.insert(END, f"    Rewards = {{\n")
            for reward in group.group_rewards:
                self.config_text.insert(END, f"      {{Currency = \"{reward.currency}\", Amount = {reward.amount}}},\n")
            self.config_text.insert(END, f"    }}\n")
            self.config_text.insert(END, f"  }},\n")

        self.config_text.insert(END, "}\n")

    def _clear_results(self):
        """清空结果"""
        self.generated_groups = []

        # 清空显示
        for item in self.orders_tree.get_children():
            self.orders_tree.delete(item)

        self.config_text.delete(1.0, END)
        self.stats_text.delete(1.0, END)

        self.status_var.set("结果已清空")

    def _preview_config(self):
        """预览配置"""
        if not self.generated_groups:
            messagebox.showwarning("警告", "请先生成订单")
            return

        # 切换到配置预览标签页
        self.display_notebook.select(1)

    def _export_configs(self):
        """导出配置文件"""
        if not self.generated_groups:
            messagebox.showwarning("警告", "没有可导出的配置")
            return

        # 选择导出目录
        export_dir = filedialog.askdirectory(title="选择导出目录")
        if not export_dir:
            return

        try:
            # 导出OrderFixedConfig
            self._export_order_fixed_config(export_dir)

            # 导出OrderGroupConfig
            self._export_order_group_config(export_dir)

            messagebox.showinfo("成功", f"配置文件已导出到: {export_dir}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

    def _export_order_fixed_config(self, output_dir=None):
        """导出OrderFixedConfig"""
        if output_dir is None:
            filename = filedialog.asksaveasfilename(
                title="导出OrderFixedConfig",
                defaultextension=".lua",
                filetypes=[("Lua文件", "*.lua"), ("所有文件", "*.*")]
            )
        else:
            chapter_id = self.selected_chapter.get()
            filename = os.path.join(output_dir, f"OrderFixedConfig_{chapter_id}.lua")

        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("return {\n")

                for group in self.generated_groups:
                    for order in group.orders:
                        f.write(f"  {{\n")
                        f.write(f"    Id = \"{order.id}\",\n")
                        f.write(f"    GroupId = {order.group_id},\n")
                        f.write(f"    ChapterId = {order.chapter_id},\n")
                        f.write(f"    Requirement_1 = {{Type = \"{order.requirement_1.type}\", Count = {order.requirement_1.count}}},\n")

                        if order.requirement_2:
                            f.write(f"    Requirement_2 = {{Type = \"{order.requirement_2.type}\", Count = {order.requirement_2.count}}},\n")

                        if order.rewards:
                            f.write(f"    Rewards = {{\n")
                            for reward in order.rewards:
                                f.write(f"      {{Currency = \"{reward.currency}\", Amount = {reward.amount}}},\n")
                            f.write(f"    }},\n")

                        f.write(f"  }},\n")

                f.write("}\n")

    def _export_order_group_config(self, output_dir=None):
        """导出OrderGroupConfig"""
        if output_dir is None:
            filename = filedialog.asksaveasfilename(
                title="导出OrderGroupConfig",
                defaultextension=".lua",
                filetypes=[("Lua文件", "*.lua"), ("所有文件", "*.*")]
            )
        else:
            chapter_id = self.selected_chapter.get()
            filename = os.path.join(output_dir, f"OrderGroupConfig_{chapter_id}.lua")

        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("return {\n")

                for group in self.generated_groups:
                    f.write(f"  {{\n")
                    f.write(f"    GroupId = {group.group_id},\n")
                    f.write(f"    ChapterId = {group.chapter_id},\n")
                    f.write(f"    Day = {group.day},\n")

                    if group.flambe:
                        f.write(f"    Flambe = \"{group.flambe}\",\n")

                    f.write(f"    Rewards = {{\n")
                    for reward in group.group_rewards:
                        f.write(f"      {{Currency = \"{reward.currency}\", Amount = {reward.amount}}},\n")
                    f.write(f"    }}\n")
                    f.write(f"  }},\n")

                f.write("}\n")

    def _save_player_state(self):
        """保存玩家状态"""
        filename = filedialog.asksaveasfilename(
            title="保存玩家状态",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                state_data = {
                    'generators': self.player_state.generators,
                    'equipment': self.player_state.equipment,
                    'chapter_progress': self.player_state.chapter_progress,
                    'day_progress': self.player_state.day_progress,
                    'unlocked_items': self.player_state.unlocked_items,
                    'saved_time': datetime.now().isoformat()
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(state_data, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("成功", "玩家状态保存成功")

            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

    def _load_player_state(self):
        """加载玩家状态"""
        filename = filedialog.askopenfilename(
            title="加载玩家状态",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    state_data = json.load(f)

                # 恢复玩家状态
                self.player_state.generators = state_data.get('generators', {})
                self.player_state.equipment = state_data.get('equipment', {})
                self.player_state.chapter_progress = state_data.get('chapter_progress', 1)
                self.player_state.day_progress = state_data.get('day_progress', 1)
                self.player_state.unlocked_items = state_data.get('unlocked_items', [])

                # 更新显示
                self._update_generators_display()
                self._update_equipment_display()

                messagebox.showinfo("成功", "玩家状态加载成功")

            except Exception as e:
                messagebox.showerror("错误", f"加载失败: {e}")


def main():
    """主程序入口"""
    root = tk.Tk()
    app = EnhancedOrderGenerator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
