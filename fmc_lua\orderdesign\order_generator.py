"""
订单生成器模块
实现智能订单生成算法
"""

import random
import math
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict
from data_models import (
    OrderData, OrderRequirement, OrderReward, OrderGenerationConfig,
    OrderGenerationResult, ConfigData, BetMode, EfficiencyMode,
    PlayerGeneratorInfo, PlayerInstrumentInfo
)
from energy_calculator import EnergyCalculator


class OrderGenerator:
    """智能订单生成器"""
    
    def __init__(self, config_data: ConfigData):
        """
        初始化订单生成器
        
        Args:
            config_data: 配置数据
        """
        self.config_data = config_data
        self.energy_calculator = EnergyCalculator(config_data)
    
    def generate_order_schemes(
        self,
        generation_config: OrderGenerationConfig,
        num_schemes: int = 20
    ) -> List[OrderGenerationResult]:
        """
        生成多套订单方案
        
        Args:
            generation_config: 订单生成配置
            num_schemes: 生成方案数量
            
        Returns:
            List[OrderGenerationResult]: 生成的订单方案列表
        """
        schemes = []
        
        for i in range(num_schemes):
            try:
                scheme = self._generate_single_scheme(generation_config)
                if scheme and scheme.is_valid:
                    schemes.append(scheme)
            except Exception as e:
                print(f"生成第{i+1}套方案时出错: {e}")
                continue
        
        # 按总体力消耗排序
        schemes.sort(key=lambda x: x.total_energy)
        
        return schemes
    
    def _generate_single_scheme(self, config: OrderGenerationConfig) -> Optional[OrderGenerationResult]:
        """
        生成单套订单方案
        
        Args:
            config: 订单生成配置
            
        Returns:
            Optional[OrderGenerationResult]: 生成的订单方案，失败时返回None
        """
        try:
            # 阶段A：计算特色菜品消耗
            special_orders, special_consumption = self._phase_a_calculate_special_dishes(config)
            
            # 阶段B：分配最高等级物品
            max_level_orders, max_level_consumption = self._phase_b_allocate_max_level_items(
                config, special_consumption
            )
            
            # 阶段C：订单顺序匹配
            ordered_orders = self._phase_c_match_order_sequence(
                config, special_orders + max_level_orders
            )
            
            # 阶段D：剩余物品补全
            complete_orders = self._phase_d_complete_remaining_items(
                config, ordered_orders, special_consumption, max_level_consumption
            )
            
            # 阶段E：校验与修正
            result = self._phase_e_validate_and_correct(config, complete_orders)
            
            # 阶段F：最优完成顺序计算
            if result and result.is_valid:
                self._phase_f_calculate_optimal_completion_order(result)
            
            return result
            
        except Exception as e:
            print(f"生成订单方案时出错: {e}")
            return None
    
    def _phase_a_calculate_special_dishes(
        self, config: OrderGenerationConfig
    ) -> Tuple[List[OrderData], Dict[str, Any]]:
        """
        阶段A：计算特色菜品消耗
        
        Args:
            config: 订单生成配置
            
        Returns:
            Tuple[List[OrderData], Dict[str, Any]]: (特色菜品订单列表, 消耗统计)
        """
        special_orders = []
        consumption = {
            'generator_max_levels': defaultdict(int),
            'instrument_usage': defaultdict(int),
            'instrument_time': defaultdict(int),
            'generator_clicks': defaultdict(int),
            'energy_cost': 0.0,
            'remaining_items_base': defaultdict(float)
        }
        
        # 验证特色菜品数量
        if len(config.special_dishes) < 4 or len(config.special_dishes) > 7:
            raise ValueError(f"特色菜品数量必须在4-7个之间，当前: {len(config.special_dishes)}")
        
        for i, dish_type in enumerate(config.special_dishes):
            order = OrderData(
                order_id=f"special_{i+1}",
                requirements=[OrderRequirement(item_type=dish_type, count=1)],
                is_special=True
            )
            
            # 计算该菜品的资源消耗
            dish_consumption = self._calculate_dish_consumption(dish_type, config)
            
            # 累加到总消耗中
            self._accumulate_consumption(consumption, dish_consumption)
            
            special_orders.append(order)
        
        return special_orders, consumption
    
    def _phase_b_allocate_max_level_items(
        self, config: OrderGenerationConfig, special_consumption: Dict[str, Any]
    ) -> Tuple[List[OrderData], Dict[str, Any]]:
        """
        阶段B：分配最高等级物品
        
        Args:
            config: 订单生成配置
            special_consumption: 特色菜品消耗统计
            
        Returns:
            Tuple[List[OrderData], Dict[str, Any]]: (最高等级物品订单列表, 消耗统计)
        """
        max_level_orders = []
        consumption = {
            'generator_max_levels': defaultdict(int),
            'instrument_usage': defaultdict(int),
            'instrument_time': defaultdict(int),
            'generator_clicks': defaultdict(int),
            'energy_cost': 0.0,
            'remaining_items_base': defaultdict(float)
        }
        
        # 根据生成器必须使用的物品最高等级要求，随机选择菜品
        for generator_type, max_level in config.max_item_levels.items():
            # 查找包含该等级物品的菜品
            candidate_dishes = self._find_dishes_with_max_level_items(generator_type, max_level)
            
            if candidate_dishes:
                # 随机选择一个菜品
                selected_dish = random.choice(candidate_dishes)
                
                # 校验器械指标
                if self._validate_instrument_constraints(selected_dish, config, consumption):
                    order = OrderData(
                        order_id=f"max_level_{generator_type}",
                        requirements=[OrderRequirement(item_type=selected_dish, count=1)]
                    )
                    
                    # 计算消耗
                    dish_consumption = self._calculate_dish_consumption(selected_dish, config)
                    self._accumulate_consumption(consumption, dish_consumption)
                    
                    max_level_orders.append(order)
        
        return max_level_orders, consumption
    
    def _phase_c_match_order_sequence(
        self, config: OrderGenerationConfig, existing_orders: List[OrderData]
    ) -> List[OrderData]:
        """
        阶段C：订单顺序匹配
        
        Args:
            config: 订单生成配置
            existing_orders: 已有订单列表（特色菜品+最高等级物品）
            
        Returns:
            List[OrderData]: 排序后的7个订单
        """
        # 计算每个订单的体力消耗
        for order in existing_orders:
            order.energy_cost = self.energy_calculator.calculate_order_energy(
                order.requirements, config.bet_mode, config.efficiency_mode, config.player_generators
            )
        
        # 按体力消耗排序
        existing_orders.sort(key=lambda x: x.energy_cost)
        
        # 计算订单体力需求
        total_weight = sum(config.order_energy_weights)
        total_energy = sum(config.total_energy_range) / 2  # 使用中间值
        
        ordered_orders = []
        
        for i in range(7):
            order_energy_target = total_energy * config.order_energy_weights[i] / total_weight
            
            # 查找最匹配的订单
            best_order = None
            best_match_score = float('inf')
            
            for order in existing_orders:
                if order in ordered_orders:
                    continue
                
                # 检查体力需求是否在60%-80%范围内
                energy_ratio = order.energy_cost / order_energy_target
                if 0.6 <= energy_ratio <= 0.8:
                    match_score = abs(energy_ratio - 0.7)  # 目标70%
                    if match_score < best_match_score:
                        best_match_score = match_score
                        best_order = order
            
            if best_order:
                best_order.order_id = f"order_{i+1}"
                ordered_orders.append(best_order)
            else:
                # 如果没有合适的订单，创建一个空订单
                empty_order = OrderData(order_id=f"order_{i+1}")
                ordered_orders.append(empty_order)
        
        # 确保有7个订单
        while len(ordered_orders) < 7:
            empty_order = OrderData(order_id=f"order_{len(ordered_orders)+1}")
            ordered_orders.append(empty_order)
        
        return ordered_orders[:7]
    
    def _phase_d_complete_remaining_items(
        self,
        config: OrderGenerationConfig,
        orders: List[OrderData],
        special_consumption: Dict[str, Any],
        max_level_consumption: Dict[str, Any]
    ) -> List[OrderData]:
        """
        阶段D：剩余物品补全
        
        Args:
            config: 订单生成配置
            orders: 当前订单列表
            special_consumption: 特色菜品消耗
            max_level_consumption: 最高等级物品消耗
            
        Returns:
            List[OrderData]: 补全后的订单列表
        """
        # 计算剩余目标值
        total_consumption = self._merge_consumption(special_consumption, max_level_consumption)
        
        for i, order in enumerate(orders):
            if len(order.requirements) == 0:
                # 空订单，需要补全1-2个物品
                num_items = random.randint(1, 2)
                
                for j in range(num_items):
                    # 随机选择物品
                    item_type = self._select_random_item(config, total_consumption)
                    if item_type:
                        requirement = OrderRequirement(item_type=item_type, count=1)
                        order.requirements.append(requirement)
            
            elif len(order.requirements) == 1:
                # 只有一个物品，可能需要补充一个
                if random.random() < 0.5:  # 50%概率补充
                    item_type = self._select_random_item(config, total_consumption)
                    if item_type:
                        requirement = OrderRequirement(item_type=item_type, count=1)
                        order.requirements.append(requirement)
        
        return orders
    
    def _phase_e_validate_and_correct(
        self, config: OrderGenerationConfig, orders: List[OrderData]
    ) -> OrderGenerationResult:
        """
        阶段E：校验与修正
        
        Args:
            config: 订单生成配置
            orders: 订单列表
            
        Returns:
            OrderGenerationResult: 校验结果
        """
        result = OrderGenerationResult(orders=orders)
        
        # 计算整体指标
        total_energy = 0.0
        generator_clicks = defaultdict(int)
        
        for order in orders:
            order_energy = self.energy_calculator.calculate_order_energy(
                order.requirements, config.bet_mode, config.efficiency_mode, config.player_generators
            )
            total_energy += order_energy
            order.energy_cost = order_energy
        
        result.total_energy = total_energy
        
        # 校验是否在配置范围内
        energy_min, energy_max = config.total_energy_range
        energy_in_range = energy_min <= total_energy <= energy_max
        
        # 简化校验，实际应该包含所有约束条件
        result.is_valid = energy_in_range
        
        return result
    
    def _phase_f_calculate_optimal_completion_order(self, result: OrderGenerationResult):
        """
        阶段F：最优完成顺序计算
        
        Args:
            result: 订单生成结果
        """
        # 按体力消耗升序排序
        order_indices = list(range(len(result.orders)))
        order_indices.sort(key=lambda i: result.orders[i].energy_cost)
        
        result.optimal_completion_order = order_indices
        
        # 计算分步体力消耗
        result.step_energy_costs = [result.orders[i].energy_cost for i in order_indices]
    
    def _calculate_dish_consumption(self, dish_type: str, config: OrderGenerationConfig) -> Dict[str, Any]:
        """计算菜品消耗"""
        # 简化实现
        return {
            'energy_cost': 10.0,  # 示例值
            'generator_clicks': {'pd_1_4': 1},
            'instrument_usage': {},
            'instrument_time': {}
        }
    
    def _accumulate_consumption(self, total: Dict[str, Any], addition: Dict[str, Any]):
        """累加消耗统计"""
        total['energy_cost'] += addition.get('energy_cost', 0.0)
        
        for key, value in addition.get('generator_clicks', {}).items():
            total['generator_clicks'][key] += value
    
    def _find_dishes_with_max_level_items(self, generator_type: str, max_level: int) -> List[str]:
        """查找包含指定等级物品的菜品"""
        # 简化实现，返回一些示例菜品
        return ['ds_chopve_1', 'ds_chopve_2', 'ds_flb_1']
    
    def _validate_instrument_constraints(
        self, dish_type: str, config: OrderGenerationConfig, consumption: Dict[str, Any]
    ) -> bool:
        """校验器械约束"""
        # 简化实现，总是返回True
        return True
    
    def _merge_consumption(self, consumption1: Dict[str, Any], consumption2: Dict[str, Any]) -> Dict[str, Any]:
        """合并消耗统计"""
        merged = consumption1.copy()
        merged['energy_cost'] += consumption2.get('energy_cost', 0.0)
        
        for key, value in consumption2.get('generator_clicks', {}).items():
            merged['generator_clicks'][key] += value
        
        return merged
    
    def _select_random_item(self, config: OrderGenerationConfig, consumption: Dict[str, Any]) -> Optional[str]:
        """随机选择物品"""
        # 简化实现，从可用物品中随机选择
        available_items = list(self.config_data.items.keys())
        if available_items:
            return random.choice(available_items)
        return None
