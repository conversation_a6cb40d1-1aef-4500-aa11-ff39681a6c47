GlobalManager = {}
GlobalManager.__index = GlobalManager
GlobalManager.LocalFile = "simplePrefs"

function GlobalManager.Create()
  local gm = setmetatable({}, GlobalManager)
  gm._singletons = {}
  gm._arrSingletons = {}
  gm.SimplePrefs = SimpleStringDict.Create(GlobalManager.LocalFile)
  return gm
end

function GlobalManager:Init()
  self.m_bInitStartOK = false
  self.m_bInitFinished = false
  self.m_bEnteredThirdPartyApp = false
  self.m_mapSingletonsShouldUpdate = {}
  local singletonsToCreate = {
    "DatabaseModel",
    "DBTableManager",
    "ConfigModel",
    "CDNResourceManager",
    "HttpManager",
    "SsoManager",
    "BIManager",
    "LoopAnimationManager",
    "ResourceLoader",
    "GameTextModel",
    "OpenFunctionModel",
    "SystemConfigModel",
    "GameModel",
    "SyncModel",
    "DataResource",
    "AudioModel",
    "SceneManager",
    "PropertyDataManager",
    "UserModel",
    "UserProfileModel",
    "MiscModel",
    "PDItemSPModel",
    "EnergyModel",
    "SDKHelper",
    "InAppPurchaseModel",
    "InGameProfiler",
    "LevelModel",
    "ItemDataModel",
    "FlambeTimeModel",
    "MainBoardModel",
    "ShopDataModel",
    "ShopModel",
    "TutorialModel",
    "OperManager",
    "OperBIManager",
    "BundleManager",
    "UpdateHintModel",
    "ActivityManager",
    "RateModel",
    "NotificationModel",
    "PushTokenModel",
    "AccountManager",
    "DownloadManager",
    "CrossPromotionModel",
    "MoreGameModel",
    "RewardModel",
    "AdModel",
    "HeartBeatManager",
    "NoticeModel",
    "SurveyModel",
    "UpcomingEventModel",
    "EnergyBoostModel",
    "ReturnUserModel",
    "ChapterManager",
    "ChapterDataModel",
    "TaskDataModel",
    "TaskManager",
    "TimelineDataModel",
    "TimelineManager",
    "StoryDataModel",
    "UdpManager",
    "DataBalanceModel",
    "ItemRecycleModel",
    "ItemTypeDeleteModel"
  }
  if GameConfig.IsTestMode() then
    table.insert(singletonsToCreate, "TestModel")
    table.insert(singletonsToCreate, "TestAutoRunModel")
  end
  for i = 1, #singletonsToCreate do
    self:CreateSingleton(singletonsToCreate[i])
  end
  for i = 1, #singletonsToCreate do
    local singleton = self._singletons[singletonsToCreate[i]]
    if singleton.Init then
      singleton:Init()
    end
  end
  self.m_bInitStartOK = true
  self.BIManager:LogSession("session_start")
  self.m_beforeInitTimeStamp = MicrofunProfiler.Instance:AddLaunchTimeStamp("before init")
  self.ResourceLoader:LoadPrefab(self.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.UIRoot), nil, V3Zero, function(o)
    MicrofunProfiler.Instance:EndLaunchTimeStamp(self.m_beforeInitTimeStamp)
  end)
end

function GlobalManager:CreateSingleton(name)
  local type = _ENV[name]
  local instance = setmetatable({}, type)
  self:AddSingleton(name, instance, true)
  Log.Info("singleton " .. name .. " created.")
end

function GlobalManager:AddSingleton(name, value, shouldUpdate)
  self._singletons[name] = value
  self._arrSingletons[#self._arrSingletons + 1] = value
  self[name] = value
  if shouldUpdate then
    self.m_mapSingletonsShouldUpdate[value] = true
  end
end

function GlobalManager:RemoveSingleton(name)
  local value = self._singletons[name]
  if not value then
    Log.Error("singleton " .. name .. " not found.")
    return
  end
  Table.ListRemove(self._arrSingletons, value)
  self._singletons[name] = nil
  self[name] = nil
  self.m_mapSingletonsShouldUpdate[value] = nil
  Log.Info("singleton " .. name .. " removed.")
end

function GlobalManager:IsNewUser()
  return self.UserModel:IsNewUser() or self.TutorialModel:IsNewUser()
end

function GlobalManager:Update(dt)
  if not self.m_bInitStartOK then
    return
  end
  if not self.m_bInitFinished then
    if GM.UIManager ~= nil then
      self.m_bInitFinished = true
      self.SceneManager:StartLoading()
      local updatePerSecond = function()
        if self.destroying then
          return
        end
        for _, v in ipairs(self._arrSingletons) do
          if v.UpdatePerSecond then
            v:UpdatePerSecond()
          end
        end
      end
      Scheduler.Schedule(updatePerSecond, self, 1)
    end
    return
  end
  for _, v in ipairs(self._arrSingletons) do
    if self.m_mapSingletonsShouldUpdate[v] and v.Update then
      v:Update(dt)
    end
  end
end

function GlobalManager:LateUpdate()
  if not self.m_bInitFinished then
    return
  end
  for _, v in ipairs(self._arrSingletons) do
    if self.m_mapSingletonsShouldUpdate[v] and v ~= self.DBTableManager and v.LateUpdate then
      v:LateUpdate()
    end
  end
  self.DBTableManager:LateUpdate()
  if PlayerPrefs.isDirty then
    PlayerPrefs.Save()
  end
  if self.SimplePrefs:IsDirty() then
    self.SimplePrefs:Serialize()
  end
end

function GlobalManager:Destroy()
  self.destroying = true
  if self.UIManager then
    self.UIManager:OnSceneChange()
  end
  for _, v in ipairs(self._arrSingletons) do
    if v.Destroy and v ~= self.DBTableManager then
      v:Destroy()
    end
  end
  self.DBTableManager:Destroy()
  PlayerPrefs.Save()
  self.SimplePrefs:Serialize()
  Scheduler.UnscheduleTarget(self)
end

function GlobalManager:_InitFinished()
  if not self.m_bInitStartOK then
    return false
  end
  for _, v in ipairs(self._arrSingletons) do
    if v.initFinished ~= nil and v.initFinished ~= true then
      return false
    end
  end
  return true
end

function GlobalManager:LoadServerConfig(tbLoginResp)
  for _, v in ipairs(self._arrSingletons) do
    if v.LoadServerConfig then
      v:LoadServerConfig(tbLoginResp)
    end
  end
end

function GlobalManager:LoadFileConfig()
  self.ConfigModel:LoadConfigs()
  self.ChapterDataModel:LoadConfigs()
  for _, v in ipairs(self._arrSingletons) do
    if v.LoadFileConfig then
      v:LoadFileConfig()
    end
  end
end

function GlobalManager:OnLoadFileConfigFinished()
  for _, v in ipairs(self._arrSingletons) do
    if v.OnLoadFileConfigFinished then
      v:OnLoadFileConfigFinished()
    end
  end
end

function GlobalManager:OnSyncDataFinished()
  self.TaskManager:OnSyncDataFinished()
  self.PDItemSPModel:OnSyncDataFinished()
  for _, v in ipairs(self._arrSingletons) do
    if v.OnSyncDataFinished and v ~= self.TaskManager and v ~= self.PDItemSPModel then
      v:OnSyncDataFinished()
    end
  end
  if self:IsNewUser() then
    self.UserModel:CreateNewUser()
    self.EnergyModel:ResetEnergy(EnergyType.Main)
  end
end

function GlobalManager:LateInit()
  self.OpenFunctionModel:LateInit()
  self.ActivityManager:LateInit()
  for _, v in ipairs(self._arrSingletons) do
    if v.LateInit and v ~= self.ActivityManager and v ~= self.OpenFunctionModel then
      v:LateInit()
    end
  end
end

function GlobalManager:OnCheckResourcesFinished()
  for _, v in ipairs(self._arrSingletons) do
    if v.OnCheckResourcesFinished then
      v:OnCheckResourcesFinished()
    end
  end
end

function GlobalManager:ApplicationFocusChanged(bHasFocus)
  if not self.m_bInitStartOK then
    return
  end
  if bHasFocus then
    self.NotificationModel:UnregisterAll()
  else
    self.NotificationModel:RegisterAll()
  end
end

function GlobalManager:ApplicationDidEnterBackground()
  if not self.m_bInitStartOK then
    return
  end
  self.SceneManager:ApplicationDidEnterBackground()
  self.BIManager:LogSession("session_end")
  EventDispatcher.DispatchEvent(EEventType.ApplicationDidEnterBackground)
  self:LateUpdate()
  GM.DBTableManager:TrySaveAll()
  self.m_needCheckCatalogOnApplicationWillEnterForeground = true
end

function GlobalManager:ApplicationWillEnterForeground()
  if not self.m_bInitStartOK then
    return
  end
  self.GameModel:UpdatePerSecond()
  self.SceneManager:ApplicationWillEnterForeground()
  self.BIManager:LogSession("session_start")
  if self.m_bEnteredThirdPartyApp then
    self:OnBackFromThirdPartyApp()
    return
  end
  self.SyncModel:ApplicationWillEnterForeground()
  self.UpdateHintModel:SetHasPopupWindow(false)
  local eGameMode = self.SceneManager:GetGameMode()
  if eGameMode ~= EGameMode.Loading then
    self.DownloadManager:TryMuteDownload()
    local callback = function(result, response)
      if not result then
        return
      end
      if self.CDNResourceManager:IsNeedRestartGame() then
        self:RestartGame(nil, EBIProjectType.RestartGameAction.CDN)
      else
        self.CDNResourceManager:TryDownloadLatestGameText(false)
      end
      if self.m_needCheckCatalogOnApplicationWillEnterForeground then
        self.GameModel:CheckCatalogAndRestart()
      end
    end
    self.GameModel:Login(callback)
    self.NotificationModel:LogStateBI()
  end
  EventDispatcher.DispatchEvent(EEventType.ApplicationWillEnterForeground)
end

function GlobalManager:OnEnteringThirdPartyApp(showMask)
  self.m_bEnteredThirdPartyApp = true
  if showMask == false then
    self.m_bEnterThirdPartyAppWithMask = false
  else
    self.m_bEnterThirdPartyAppWithMask = nil
    self.UIManager:ShowMask()
  end
end

function GlobalManager:OnBackFromThirdPartyApp(hideMask)
  self.m_bEnteredThirdPartyApp = false
  if hideMask ~= false and self.m_bEnterThirdPartyAppWithMask ~= false and self.UIManager then
    self.UIManager:HideMask()
  end
  self.m_bEnterThirdPartyAppWithMask = nil
  self.m_needCheckCatalogOnApplicationWillEnterForeground = false
end

ERestartType = {
  Normal = 1,
  WithHotfix = 2,
  WithLocalization = 3
}

function GlobalManager:RestartGame(eRestartType, eRestartGameAction)
  eRestartType = eRestartType or ERestartType.Normal
  Log.Assert(eRestartGameAction ~= nil, "未指定重启类型")
  Log.Info("GlobalManager:RestartGame, type:" .. tostring(eRestartType) .. ", action:" .. tostring(eRestartGameAction))
  if self.destroying then
    return
  end
  self.destroying = true
  if self.UIManager then
    self.UIManager:SetEventLock(true)
  end
  if self.BIManager then
    self.BIManager:LogProject(EBIProjectType.RestartGame, eRestartGameAction, {
      isNewUser = ApplicationManager.Instance.isNewUser,
      isLocalNewUser = ApplicationManager.Instance.isLocalNewUser
    })
  end
  self.restartGameAction = eRestartGameAction
  if eRestartType == ERestartType.Normal then
    ApplicationManager.Instance:NormalRestart()
  elseif eRestartType == ERestartType.WithHotfix then
    ApplicationManager.Instance:CheckRemoteHotfixRestart()
  elseif eRestartType == ERestartType.WithLocalization then
    ApplicationManager.Instance:LocalizationRestart()
  end
end

function GlobalManager:CheckSceneChanged()
  if self.SceneManager then
  end
end
