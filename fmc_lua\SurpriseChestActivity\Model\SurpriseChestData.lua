SurpriseChestData = {}
SurpriseChestData.__index = SurpriseChestData

function SurpriseChestData.Create(config)
  local instance = setmetatable(config, SurpriseChestData)
  instance:_Init()
  return instance
end

function SurpriseChestData:_Init()
  if not Table.IsEmpty(self.rewardsWeight) then
    RewardApi.CryptRewards(self.rewardsWeight, true)
  end
end

function SurpriseChestData:CheckOrderScoreRange(score)
  local score_min = self.score_min or 0
  local score_max = self.score_max or math.maxinteger
  return score > score_min and score <= score_max
end

function SurpriseChestData:GetUICode()
  return self.uiCode
end

function SurpriseChestData:RandomSelectRewards()
  local rewardsWeightPairs = self:GetValidRewards()
  if Table.IsEmpty(rewardsWeightPairs) then
    return
  end
  local arrRewards = {}
  local target = Table.ListWeightSelectOne(rewardsWeightPairs)
  table.insert(arrRewards, target)
  return arrRewards
end

function SurpriseChestData:GetValidRewards()
  return self.rewardsWeight
end

function SurpriseChestData:ToSerilization(orderScore)
  local realRewards = self.rewardsWeight
  if orderScore then
    realRewards = ActivityTokenHelper.GetRatioRewards(self, orderScore)
  end
  return {
    rewardsWeight = realRewards,
    uiCode = self.uiCode
  }
end
