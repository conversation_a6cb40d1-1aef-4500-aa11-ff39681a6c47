MainBoardView = setmetatable({}, BaseSceneBoardView)
MainBoardView.__index = MainBoardView
EBoardBackgroundSpecial = {
  BakeOut = ImageFileConfigName.board_bg_bakeout
}

function MainBoardView.GetInstance()
  return MainBoardView.s_instance
end

function MainBoardView:Awake()
  BaseSceneBoardView.Init(self, GM.MainBoardModel)
  self.m_orderArea:Init(self)
  EventDispatcher.AddListener(EEventType.NewChapterUnlocked, self, self._UpdateBoardDisplay)
  EventDispatcher.AddListener(EEventType.NewReleasedOrderGroupUnlocked, self, self._UpdateBoardDisplay)
  EventDispatcher.AddListener(EEventType.EnterMainScene, self, self._UpdateBoardDisplay)
  EventDispatcher.AddListener(EEventType.BakeOutModeChanged, self, self._UpdateBoardDisplay)
  EventDispatcher.AddListener(EEventType.BakeOutStateChanged, self, self._UpdateBoardDisplay)
  REGISTER_BOARD_EVENT_HANDLER(self, "StoreItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "RetrieveStoredItem")
  REGISTER_BOARD_EVENT_HANDLER(self, "StoreFailed")
  REGISTER_BOARD_EVENT_HANDLER(self, "RemoverOrderImmediate")
  self.m_retrieveList = {}
  MainBoardView.s_instance = self
  self:OnOrderStateChanged()
  self.m_initFinished = true
  self.m_boardBgConfigs = {}
  local configs = require("Data.Config.MainBoardBgConfig")
  for _, v in ipairs(configs) do
    self.m_boardBgConfigs[v.RoomID] = v.PicID
  end
end

function MainBoardView:OnDestroy()
  BaseSceneBoardView.OnDestroy(self)
  MainBoardView.s_instance = nil
end

function MainBoardView:_ShowRetrieveLight()
  for i = #self.m_retrieveList, 1, -1 do
    self.m_retrieveList[i]:ShowRetrieveLight()
    self.m_retrieveList[i] = nil
  end
end

function MainBoardView:_OnCloseView(msg)
  BaseSceneBoardView._OnCloseView(self)
  if msg and GM.UIManager.allWindowClosed then
    self:_ShowRetrieveLight()
  end
end

function MainBoardView:_OnCollectItem(message)
  local rewards = message.Source:GetComponent(ItemCollectable):GetRewards()
  if rewards[1][PROPERTY_TYPE] ~= LollipopModel.TokenType and rewards[1][PROPERTY_TYPE] ~= CoconutModel.TokenType and rewards[1][PROPERTY_TYPE] ~= PinataModel.TokenType then
    BaseSceneBoardView._OnCollectItem(self, message)
    return
  end
  if rewards[1][PROPERTY_TYPE] ~= PinataModel.TokenType then
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxMergeCollectDiamond)
  else
    GM.AudioModel:PlayEffect(AudioFileConfigName.sfxBatFly)
  end
  local delayTime = 0.2
  local iconArea = self.m_orderArea:GetDashActivityOrder():GetIconArea()
  local targetScreenPosition = Vector3.zero
  if iconArea == nil then
    Log.Assert(false, "错误：收集活动棋子时iconArea不存在")
  else
    targetScreenPosition = self:ConvertWorldPositionToScreenPosition(iconArea.transform.position)
  end
  if targetScreenPosition.x < 0 or targetScreenPosition.x > Screen.width then
    self.m_orderArea:ScrollToDashActivityOrder(true)
  end
  local itemView = self:GetItemView(message.Source)
  itemView.toBeRemoved = true
  local sequence = DOTween.Sequence()
  sequence:InsertCallback(delayTime + 0.05, function()
    local targetScreenPosition = Vector3.zero
    if iconArea == nil then
      Log.Assert(false, "错误：收集活动棋子时iconArea不存在")
    else
      targetScreenPosition = self:ConvertWorldPositionToScreenPosition(iconArea.transform.position)
    end
    local targetUIWorldPosition = PositionUtil.UICameraScreen2World(targetScreenPosition)
    local sourceScreenPosition = self:ConvertWorldPositionToScreenPosition(itemView.transform.position)
    local sourceUIWorldPosition = PositionUtil.UICameraScreen2World(sourceScreenPosition)
    local message = {
      arrProperties = rewards,
      uiWorldPos = sourceUIWorldPosition,
      customData = {
        {
          endPos = targetUIWorldPosition,
          targetButton = iconArea,
          eventLock = true,
          singleFlyInterval = rewards[1][PROPERTY_TYPE] == PinataModel.TokenType and 0.03 or nil
        }
      }
    }
    EventDispatcher.DispatchEvent(EEventType.PlayCollectAnimation, message)
  end)
  sequence:Insert(delayTime, itemView.transform:DOScale(Vector3.zero, 0.2))
  sequence:InsertCallback(delayTime + 0.2, function()
    self:_RemoveItemView(itemView)
  end)
end

function MainBoardView:_RemoveOrderItemWhenFinishOrder(message, targetCell)
  BaseSceneBoardView._RemoveOrderItemWhenFinishOrder(self, message, targetCell)
  for i, item in pairs(message.RemoveItemInfo.RemovedFromInventory) do
    local index = message.RemoveItemInfo.RemovedCellIndexFromInventory[i]
    self:_PlayInventory2OrderItemAnimation(targetCell, index, item:GetCode())
  end
end

function MainBoardView:_PlayInventory2OrderItemAnimation(targetCell, index, itemType)
  local sceneView = GM.UIManager:GetOpenedTopViewByType(EViewType.SceneView)
  local sourceWorldPos = sceneView:GetHudButton(ESceneViewHudButtonKey.Inventory).transform.position
  local targetIcon = targetCell:GetIcon(index)
  local targetPosition = targetIcon.transform.position
  targetPosition = Vector3(targetPosition.x, targetPosition.y, 0)
  targetPosition = PositionUtil.UICameraScreen2World(self.m_camera:WorldToScreenPoint(targetPosition))
  PropertyAnimationManager.PlayFlyElementAnimation(itemType, self.finishOrderRemoveItemDt, sourceWorldPos, targetPosition, 1, 1, false, false, nil, nil, nil, Ease.OutQuad)
end

function MainBoardView:_OnRemoverOrderImmediate(message)
  self.m_orderArea:RemoveCell(message.Order)
end

function MainBoardView:_OnStoreItem(message)
  local itemView = self:GetItemView(message.Source)
  self:_RemoveItemView(itemView)
  self:_UpdateIndicator()
  self:UpdateBoardInfoBar()
end

function MainBoardView:_OnRetrieveStoredItem(message)
  local itemView = self:_AddItemView(message.Source)
  if itemView == nil then
    return
  end
  self.m_retrieveList[#self.m_retrieveList + 1] = itemView
  itemView:OnRetrived()
  self:_UpdateIndicator(message.Source, true)
  self:UpdateBoardInfoBar(message.Source)
end

function MainBoardView:_OnStoreFailed(message)
  local onStoreFailed = function()
    local itemView = self:GetItemView(message.Item)
    local targetPosition = itemView.transform.position + Vector3(0, 100, 0)
    local screenPosition = self:ConvertWorldPositionToScreenPosition(targetPosition)
    local key = message.Reason == StoreFailedReason.CannotStore and "hint_cannot_store" or "hint_inventory_full"
    GM.UIManager:ShowPromptWithKey(key, screenPosition)
  end
  DOVirtual.DelayedCall(ItemView.MoveDuration, onStoreFailed)
  PlatformInterface.Vibrate(EVibrationType.Heavy)
end

function MainBoardView:_UpdateBoardDisplay()
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  local bakeOutOn = bakeOutModel ~= nil and bakeOutModel:CanAcquireToken()
  if bakeOutOn then
    if self.m_curShowBg ~= EBoardBackgroundSpecial.BakeOut then
      self.m_boardBg:UpdateBgTop(EBoardBackgroundSpecial.BakeOut)
      self.m_curShowBg = EBoardBackgroundSpecial.BakeOut
    end
    return
  end
  local config = self.m_boardBgConfigs[GM.TaskManager:GetOngoingChapterId()]
  if self.m_curShowBg ~= config then
    self.m_boardBg:UpdateBgTop(ImageFileConfigName[config])
    self.m_curShowBg = config
  end
end
