"""
测试增强版订单生成器
验证基于实际配置结构的订单生成功能
"""

import os
import sys
import tempfile

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    
    try:
        from enhanced_order_generator import EnhancedOrderGenerator
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = EnhancedOrderGenerator(root)
        
        print(f"✓ 配置加载成功")
        print(f"  物品总数: {len(generator.items_data)}")
        
        # 统计物品类型
        type_count = {}
        for item in generator.items_data.values():
            item_type = item['type']
            type_count[item_type] = type_count.get(item_type, 0) + 1
        
        print("  物品类型分布:")
        for item_type, count in type_count.items():
            print(f"    {item_type}: {count}个")
        
        # 检查玩家状态初始化
        print(f"  默认生成器数量: {len(generator.player_state.generators)}")
        print(f"  默认器械数量: {len(generator.player_state.equipment)}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_generation():
    """测试订单生成"""
    print("\n=== 测试订单生成 ===")
    
    try:
        from enhanced_order_generator import EnhancedOrderGenerator
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = EnhancedOrderGenerator(root)
        
        # 设置生成参数
        generator.selected_chapter.set(4)
        generator.start_day.set(34)
        generator.generation_days.set(3)
        generator.start_group_id.set(1)
        generator.difficulty_level.set("普通")
        generator.reward_mode.set("平衡")
        
        # 生成订单
        generator._generate_orders()
        
        if generator.generated_groups:
            print(f"✓ 订单生成成功")
            print(f"  生成天数: {len(generator.generated_groups)}")
            
            total_orders = sum(len(group.orders) for group in generator.generated_groups)
            print(f"  总订单数: {total_orders}")
            
            flambe_count = sum(1 for group in generator.generated_groups if group.flambe)
            print(f"  Flambe天数: {flambe_count}")
            
            # 检查订单结构
            for i, group in enumerate(generator.generated_groups):
                print(f"\n  第{i+1}天订单组:")
                print(f"    组ID: {group.group_id}")
                print(f"    章节: {group.chapter_id}")
                print(f"    天数: {group.day}")
                print(f"    订单数: {len(group.orders)}")
                print(f"    Flambe: {'是' if group.flambe else '否'}")
                print(f"    组奖励数: {len(group.group_rewards)}")
                
                # 检查每个订单
                for j, order in enumerate(group.orders[:3]):  # 只显示前3个
                    print(f"      订单{j+1}: {order.id}")
                    print(f"        需求1: {order.requirement_1.type} x{order.requirement_1.count}")
                    if order.requirement_2:
                        print(f"        需求2: {order.requirement_2.type} x{order.requirement_2.count}")
                    if order.rewards:
                        print(f"        订单奖励: {len(order.rewards)}个")
        else:
            print("✗ 订单生成失败: 没有生成订单组")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 订单生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_export():
    """测试配置导出"""
    print("\n=== 测试配置导出 ===")
    
    try:
        from enhanced_order_generator import EnhancedOrderGenerator
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = EnhancedOrderGenerator(root)
        
        # 生成订单
        generator.selected_chapter.set(4)
        generator.generation_days.set(2)
        generator._generate_orders()
        
        if not generator.generated_groups:
            print("✗ 没有生成的订单可供导出")
            return False
        
        # 创建临时目录
        import tempfile
        temp_dir = tempfile.mkdtemp()
        
        # 导出配置
        generator._export_order_fixed_config(temp_dir)
        generator._export_order_group_config(temp_dir)
        
        # 检查导出文件
        fixed_config_file = os.path.join(temp_dir, "OrderFixedConfig_4.lua")
        group_config_file = os.path.join(temp_dir, "OrderGroupConfig_4.lua")
        
        if os.path.exists(fixed_config_file) and os.path.exists(group_config_file):
            print(f"✓ 配置导出成功")
            
            # 检查文件内容
            with open(fixed_config_file, 'r', encoding='utf-8') as f:
                fixed_content = f.read()
            
            with open(group_config_file, 'r', encoding='utf-8') as f:
                group_content = f.read()
            
            print(f"  OrderFixedConfig文件大小: {len(fixed_content)}字符")
            print(f"  OrderGroupConfig文件大小: {len(group_content)}字符")
            
            # 验证格式
            if "return {" in fixed_content and "Id =" in fixed_content:
                print("  ✓ OrderFixedConfig格式正确")
            else:
                print("  ✗ OrderFixedConfig格式错误")
            
            if "return {" in group_content and "GroupId =" in group_content:
                print("  ✓ OrderGroupConfig格式正确")
            else:
                print("  ✗ OrderGroupConfig格式错误")
            
            # 显示部分内容
            print("\n  OrderFixedConfig示例:")
            lines = fixed_content.split('\n')
            for line in lines[1:6]:  # 显示前几行
                print(f"    {line}")
            
            print("\n  OrderGroupConfig示例:")
            lines = group_content.split('\n')
            for line in lines[1:6]:  # 显示前几行
                print(f"    {line}")
        else:
            print("✗ 配置文件导出失败")
            return False
        
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir)
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 配置导出失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_player_state_management():
    """测试玩家状态管理"""
    print("\n=== 测试玩家状态管理 ===")
    
    try:
        from enhanced_order_generator import EnhancedOrderGenerator, PlayerState
        import tkinter as tk
        import json
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = EnhancedOrderGenerator(root)
        
        # 修改玩家状态
        original_gen_count = len(generator.player_state.generators)
        generator.player_state.generators['pd_7_3'] = 5
        generator.player_state.equipment['eq_7_3'] = 3
        
        # 创建临时文件保存状态
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        temp_file.close()
        
        # 保存状态
        state_data = {
            'generators': generator.player_state.generators,
            'equipment': generator.player_state.equipment,
            'chapter_progress': generator.player_state.chapter_progress,
            'day_progress': generator.player_state.day_progress,
            'unlocked_items': generator.player_state.unlocked_items
        }
        
        with open(temp_file.name, 'w', encoding='utf-8') as f:
            json.dump(state_data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 玩家状态保存成功")
        print(f"  生成器数量: {len(generator.player_state.generators)}")
        print(f"  器械数量: {len(generator.player_state.equipment)}")
        
        # 重置状态
        generator.player_state = PlayerState()
        generator._init_default_player_state()
        
        # 加载状态
        with open(temp_file.name, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        generator.player_state.generators = loaded_data['generators']
        generator.player_state.equipment = loaded_data['equipment']
        
        print(f"✓ 玩家状态加载成功")
        print(f"  加载后生成器数量: {len(generator.player_state.generators)}")
        print(f"  加载后器械数量: {len(generator.player_state.equipment)}")
        
        # 验证特定物品
        if 'pd_7_3' in generator.player_state.generators:
            print(f"  ✓ 特定生成器pd_7_3数量: {generator.player_state.generators['pd_7_3']}")
        
        if 'eq_7_3' in generator.player_state.equipment:
            print(f"  ✓ 特定器械eq_7_3数量: {generator.player_state.equipment['eq_7_3']}")
        
        # 清理临时文件
        os.unlink(temp_file.name)
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 玩家状态管理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试增强版订单生成器...")
    
    tests = [
        test_config_loading,
        test_order_generation,
        test_config_export,
        test_player_state_management
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！增强版订单生成器工作正常。")
        print("\n核心功能:")
        print("• 基于实际OrderFixedConfig_4.lua结构生成订单")
        print("• 支持设置玩家的母棋子和器械情况")
        print("• 每天生成7个订单，每个订单1-2个需求")
        print("• 支持Flambe和组奖励机制")
        print("• 导出标准的Lua配置文件格式")
        print("\n可以运行以下命令启动程序:")
        print("python enhanced_order_generator.py")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
