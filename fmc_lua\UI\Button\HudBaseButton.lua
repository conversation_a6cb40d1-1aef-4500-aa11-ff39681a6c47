local ICON_SCALE = 1.18
local ICON_SCALE_TIME = 0.08
local SCALE_EFFECT_DEFAULT_SORTING_ORDER = 20
HudBaseButton = {}
HudBaseButton.__index = HudBaseButton

function HudBaseButton:Awake()
  self.m_bScaled = false
  self.m_scale = self.m_scaleTrans.localScale
  self.m_name = self.gameObject.name
end

function HudBaseButton:SetFlyTargetPosition()
  if self.flyTarget then
    self.flyTargetPosition = self.flyTarget.position
  else
    self.flyTargetPosition = self.transform.position
  end
end

function HudBaseButton:GetFlyTargetPosition()
  return self.flyTargetPosition
end

function HudBaseButton:SetInBoardView()
  self.m_bInBoardView = true
end

function HudBaseButton:IsInBoardView()
  return self.m_bInBoardView
end

function HudBaseButton:OnHighlight(highlight)
  UIUtil.UpdateSortingOrder(self.m_scaleEffect.gameObject, highlight and ESpecialViewSortingOrder.HudHighlight + 1 or SCALE_EFFECT_DEFAULT_SORTING_ORDER)
end

function HudBaseButton:IconScaleAnimation(needEffect)
  if needEffect then
    self.m_scaleEffect:Play()
  end
  if self.m_scale == nil or self.m_bScaled then
    return
  end
  if self.m_seq then
    self.m_seq:Kill()
    self.m_seq = nil
  end
  local scaleFactor = 1 - (self.m_scaleTrans.localScale.x - self.m_scale.x) / (ICON_SCALE - 1)
  local seq = DOTween.Sequence()
  seq:AppendCallback(function()
    self.m_bScaled = true
  end)
  seq:Append(self.m_scaleTrans:DOScale(self.m_scale * ICON_SCALE, ICON_SCALE_TIME * scaleFactor))
  seq:AppendCallback(function()
    self.m_bScaled = false
  end)
  seq:Append(self.m_scaleTrans:DOScale(self.m_scale, ICON_SCALE_TIME))
  seq:AppendCallback(function()
    self.m_seq = nil
  end)
  self.m_seq = seq
end

function HudBaseButton:UpdateTextAnimation(checkType, valueChange, isMiddleStep)
end

function HudBaseButton:GetScaleTrans()
  return self.m_scaleTrans
end

function HudBaseButton:OnTutorialHighlight(bHighlight)
end
