import pandas as pd
import re

def parse_lua_table(lua_content):
    # 提取所有物品配置项
    items = []
    current_item = {}
    
    # 使用正则表达式匹配每个配置项
    pattern = r'\{Type\s*=\s*"([^"]*)".*?(?=\{Type|$)'
    matches = re.finditer(pattern, lua_content, re.DOTALL)
    
    for match in matches:
        item_str = match.group(0)
        item = {}
        
        # 提取基本属性
        item['Type'] = re.search(r'Type\s*=\s*"([^"]*)"', item_str)
        item['Type'] = item['Type'].group(1) if item['Type'] else ''
        
        item['MergedType'] = re.search(r'MergedType\s*=\s*"([^"]*)"', item_str)
        item['MergedType'] = item['MergedType'].group(1) if item['MergedType'] else ''
        
        item['UnlockPrice'] = re.search(r'UnlockPrice\s*=\s*(\d+)', item_str)
        item['UnlockPrice'] = item['UnlockPrice'].group(1) if item['UnlockPrice'] else ''
        
        item['UseEnergy'] = re.search(r'UseEnergy\s*=\s*(\d+)', item_str)
        item['UseEnergy'] = item['UseEnergy'].group(1) if item['UseEnergy'] else ''
        
        # 提取BookReward
        book_reward = re.search(r'BookReward\s*=\s*\{[^}]*\{Currency\s*=\s*"([^"]*)",\s*Amount\s*=\s*(\d+)\}', item_str)
        if book_reward:
            item['BookRewardCurrency'] = book_reward.group(1)
            item['BookRewardAmount'] = book_reward.group(2)
        else:
            item['BookRewardCurrency'] = ''
            item['BookRewardAmount'] = ''
            
        # 提取GeneratedItems
        generated_items = []
        for gen_item in re.finditer(r'\{Code\s*=\s*"([^"]*)",\s*Weight\s*=\s*(\d+)\}', item_str):
            generated_items.append(f"{gen_item.group(1)}:{gen_item.group(2)}")
        item['GeneratedItems'] = '; '.join(generated_items)
        
        # 提取其他数值属性
        for attr in ['Cd', 'InitialNumber', 'Frequency', 'Capacity', 'SpeedUpPrice']:
            value = re.search(rf'{attr}\s*=\s*(\d+)', item_str)
            item[attr] = value.group(1) if value else ''
            
        items.append(item)
        
    return items

def export_to_excel(items, output_file):
    df = pd.DataFrame(items)
    
    # 设置列顺序
    columns = ['Type', 'MergedType', 'UnlockPrice', 'UseEnergy', 
              'BookRewardCurrency', 'BookRewardAmount', 'GeneratedItems',
              'Cd', 'InitialNumber', 'Frequency', 'Capacity', 'SpeedUpPrice']
    
    df = df[columns]
    
    # 导出到Excel,创建两个sheet
    with pd.ExcelWriter(output_file) as writer:
        # 基本信息sheet
        df.to_excel(writer, sheet_name='基本信息', index=False)
        
        # GeneratedItems单独一个sheet
        items_df = pd.DataFrame([
            {'Type': item['Type'], 'GeneratedItems': item['GeneratedItems']} 
            for item in items if item['GeneratedItems']
        ])
        items_df.to_excel(writer, sheet_name='生成物品', index=False)

def main():
    # 读取Lua文件
    input_file = 'fmc_lua/Data/Config/ItemModelConfig.lua'
    output_file = 'ItemModelConfig.xlsx'
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lua_content = f.read()
            
        # 解析数据
        items = parse_lua_table(lua_content)
        
        # 导出Excel
        export_to_excel(items, output_file)
        print(f"成功导出到 {output_file}")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")

if __name__ == '__main__':
    main()