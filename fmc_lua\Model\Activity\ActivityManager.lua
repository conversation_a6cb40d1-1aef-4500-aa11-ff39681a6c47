ActivityManager = {}
ActivityManager.__index = ActivityManager

function ActivityManager:Init()
  ActivityDefinitions = {
    CoinRaceActivityDefinition,
    PkRaceDefinition,
    PassActivityDefinition,
    DigActivityDefinition,
    ProgressActivityDefinition,
    ExtraBoardActivityDefinition,
    BlindChestDefinition
  }
  VIRTUAL_DEFINE_SYNC_DATA(self, "GeneralData", GM.DBTableManager:GetTable(EDBTableConfigs.Activity))
  VIRTUAL_DEFINE_SYNC_DATA(self, "EventActivityData", GM.DBTableManager:GetTable(EDBTableConfigs.WeddingDay))
  ModelHelper.DefineDispatchEvent(self, "LoadServerConfig")
  ModelHelper.DefineDispatchEvent(self, "Update")
  ModelHelper.DefineDispatchEvent(self, "LateInit")
  ModelHelper.DefineBoolEvent(self, "IsSendingRankRequest")
  ModelHelper.DefineBoolEvent(self, "CanHeartBeat")
  EventDispatcher.AddListener(EEventType.CollectGold, self, self._OnCollectGold)
end

function ActivityManager:Destroy()
  if self.m_models ~= nil then
    for _, model in pairs(self.m_models) do
      model:Destroy()
    end
  end
  EventDispatcher.RemoveTarget(self)
end

function ActivityManager:OnSyncDataFinished()
  self.m_models = {}
  for _, type in pairs(ActivityType) do
    ActivityModelFactory.AddModel(type)
  end
end

function ActivityManager:UpdatePerSecond()
  if not GM.CheckResourcesStageFinished then
    return
  end
  for _, model in pairs(self.m_models) do
    model:UpdatePerSecond()
  end
end

function ActivityManager:OnCheckResourcesFinished()
  for _, model in pairs(self.m_models) do
    model:OnCheckResourcesFinished()
  end
  for _, model in pairs(self.m_models) do
    model:UpdatePerSecond()
  end
end

function ActivityManager:AddModel(type, model)
  self.m_models[type] = model
end

function ActivityManager:GetModel(type)
  return self.m_models and self.m_models[type] or nil
end

function ActivityManager:GetModels()
  return self.m_models
end

function ActivityManager:GetStartedSpreeActivity()
  for activityType, _ in pairs(SpreeActivityDefinition) do
    local model = self:GetModel(activityType)
    if model and model:GetState() == ActivityState.Started then
      return model
    end
  end
end

function ActivityManager:FromHeartBeat(data)
  if self.m_models ~= nil then
    for _, model in pairs(self.m_models) do
      model:FromHeartBeat(data)
    end
  end
end

function ActivityManager:ToHeartBeat(data)
  if self.m_models == nil then
    return nil
  end
  local result = {}
  for _, model in pairs(self.m_models) do
    model:ToHeartBeat(result)
  end
  return result
end

function ActivityManager:GetResourceLabels()
  local labels = {}
  for _, model in pairs(self.m_models) do
    if model:NeedCheckResource() then
      for _, label in ipairs(model:GetResourceLabels()) do
        table.insert(labels, label)
      end
    end
  end
  return labels
end

function ActivityManager:RestoreIapRewards(iapType)
  Log.Assert(self.m_models ~= nil, "活动管理器实现错误")
  for _, model in pairs(self.m_models) do
    local state = model:GetState()
    if state == ActivityState.Started or state == ActivityState.Ended then
      local isOk, result = SafeCall(model.RestoreIapRewards, nil, model, iapType)
      if isOk and result then
        return true
      end
    end
  end
  return false
end

function ActivityManager:_OnCollectGold(goldCount)
  for _, model in pairs(self.m_models) do
    if model.OnCollectGold and model:GetState() == ActivityState.Started then
      model:OnCollectGold(goldCount)
    end
  end
end

function ActivityManager:GetOneActivityModel(eModelType, eActivityState)
  eActivityState = eActivityState or ActivityState.Started
  for _, model in pairs(self.m_models) do
    if model.eModelType == eModelType and model:GetState() == eActivityState then
      return model
    end
  end
  return nil
end
