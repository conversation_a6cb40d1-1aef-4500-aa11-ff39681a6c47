import pandas as pd

# Lua表格数据
data = [
    ["pb#c#pd_1_3", "pb#c#it_2_3_1", "pb#c#it_2_2_2", "c#it_3_1_2", "c#pd_2_4", "pb#c#pd_1_3", "pb#c#pd_1_4"],
    ["c#eq_1_3", "c#it_2_3_1_1", "c#it_2_3_1", "c#pd_3_3", "c#pd_1_4", "c#eq_2_3", "c#it_2_2_2"],
    ["pb#c#it_1_2_3", "pb#c#pd_2_1", "pb#c#pd_1_3", "pb#c#pd_1_2", "pb#c#pd_1_3", "pb#c#pd_1_4", "pb#c#it_1_1_5"],
    ["pb#c#it_1_2_1", "c#pd_2_2", "c#it_1_1_3", "c#it_1_1_2", "c#pd_1_3", "pb#c#it_1_1_4", "pb#c#it_2_2_1"],
    ["pb#c#pd_2_3", "c#pd_2_3", "pd_1_1", "pd_1_1", "c#pd_1_2", "pb#c#it_2_1_1", "pb#c#it_1_1_4"],
    ["pb#c#pd_2_3", "pb#c#it_1_1_4", "c#pd_2_1", "c#pd_2_2", "c#pd_2_3", "pb#c#pd_1_1", "pb#c#pd_1_3"],
    ["c#it_3_1_1", "c#it_3_2_1", "c#it_1_2_1", "c#it_1_1_1", "c#it_3_1_1", "c#it_3_1_2", "c#it_2_1_1"],
    ["c#it_3_2_1", "c#it_3_2_2", "c#it_4_1_1", "pd_4_1", "c#pd_4_3", "c#it_5_2_2", "c#it_5_2_1"],
    ["pb#c#it_5_1_1", "c#it_2_3_1_1", "pb#c#it_4_2_1", "c#pd_4_2", "pb#c#it_4_1_4", "c#it_5_1_2", "pb#c#it_5_1_1"]
]

# 创建DataFrame
df = pd.DataFrame(data)

# 保存为Excel文件
df.to_excel('BoardModelConfig.xlsx', index=False, header=False)