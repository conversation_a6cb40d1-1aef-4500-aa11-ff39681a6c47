OrderGroupRefreshWindow = setmetatable({
  canCloseByAndroidBack = false,
  windowMaskAlpha = EWindowMaskAlpha.Dark
}, BaseWindow)
OrderGroupRefreshWindow.__index = OrderGroupRefreshWindow

function OrderGroupRefreshWindow:Init()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local curDay = orderModel:GetCurOrderDay()
  orderModel:TryFillNextGroupOrder()
  if self.m_numberOld then
    self.m_numberOld.text = curDay
  end
  if self.m_backEffectGo then
    UIUtil.UpdateSortingOrder(self.m_backEffectGo, self:GetSortingOrder() - 1)
    self.m_backEffectGo:SetActive(false)
    DelayExecuteFuncInView(function()
      self.m_backEffectGo:SetActive(true)
    end, 0.2, self, true)
  end
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxNewDay)
  DelayExecuteFuncInView(function()
    self:Close()
  end, 4, self)
  local options = LocalizationModel:GetLanguageOptions()
  local currentLanguage = LocalizationModel:GetCurLanguageInString()
  local showed = false
  for i = 1, options.Length do
    local language = options[i - 1]:ToString()
    if self["m_newday" .. language] ~= nil then
      self["m_newday" .. language]:SetActive(language == currentLanguage)
      if language == currentLanguage then
        showed = true
      end
    end
  end
  if not showed then
    self.m_newdayEN_US:SetActive(true)
  end
  if self.m_calenderSpine then
    self.m_calenderSpine:Init()
    self.m_calenderSpine.gameObject:SetActive(false)
  end
  DelayExecuteFuncInView(function()
    self.m_calenderSpine.gameObject:SetActive(true)
    self.m_calenderSpine:PlayAnimation("remove", nil, false, true)
    self.m_numberNew.text = curDay + 1
    self.m_ribbonEffects:SetActive(true)
    DelayExecuteFuncInView(function()
      self.m_numberOld.gameObject:SetActive(false)
    end, 0.3, self)
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxDishOut)
  end, 1.1, self)
end

function OrderGroupRefreshWindow:OnSkipButtonClicked()
  if self.m_spineGo then
    self.m_spineGo:SetActive(false)
  end
  if self.m_calenderSpine then
    self.m_calenderSpine.gameObject:SetActive(false)
  end
  self:Close()
end

function OrderGroupRefreshWindow:OnCloseView()
  BaseWindow.OnCloseView(self)
  local message = {
    arrProperties = {
      {
        [PROPERTY_TYPE] = "day",
        [PROPERTY_COUNT] = 1
      }
    },
    uiWorldPos = self.m_orderDayCalender.transform.position,
    customData = {
      {
        eventLock = true,
        spriteScale = 1,
        endScale = 0.25
      }
    }
  }
  EventDispatcher.DispatchEvent(EEventType.PlayCollectAnimation, message)
  self.m_orderDayCalender.gameObject:SetActive(false)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxLevelUp)
end
