EItemCookState = {
  Empty = 1,
  Prepare = 2,
  CanCook = 3,
  Cooking = 4,
  Cooked = 5
}
ItemCookEventType = {
  StateChanged = 1,
  SpeedUp = 2,
  UpdateFire = 3,
  HideFire = 4
}
ItemCook = setmetatable({}, BaseItemComponent)
ItemCook.__index = ItemCook

function ItemCook.Create(itemConfig)
  local itemCook = setmetatable({}, ItemCook)
  itemCook:Init(itemConfig)
  return itemCook
end

function ItemCook:Init(itemConfig)
  self.event = PairEvent.Create(self)
  self.m_mapRecipes = {}
  local itemDataModel = GM.ItemDataModel
  for _, v in ipairs(itemConfig.Recipes) do
    self.m_mapRecipes[v.Recipe] = {
      Dur = v.Dur,
      Materials = itemDataModel:GetMaterials(v.Recipe, false, false),
      SpeedUpPrice = itemDataModel:GetModelConfig(v.Recipe).SkipPrice_gem
    }
  end
  self:_Reset()
  EventDispatcher.AddListener(EEventType.FlambeTimeChanged, self, self._OnFlambeTimeChanged)
  EventDispatcher.AddListener(EEventType.BeforeFlambeEnded, self, self._BeforeFlambeEnded)
end

function ItemCook:Destroy()
  BaseItemComponent.Destroy(self)
  self:_ReleasePairedOrder()
end

function ItemCook:_ReleasePairedOrder()
  if self.__belongOrder then
    self.__belongOrder:TryUnlockCookCmp(self)
  end
end

function ItemCook:FromSerialization(dbTable)
  if not dbTable.cookState then
    return
  end
  local array = StringUtil.Split(dbTable.materialInfo, ";")
  for _, item in ipairs(array) do
    local parts = StringUtil.Split(item, "-")
    table.insert(self.m_curMaterials, {
      ItemCode = parts[1],
      ItemId = parts[2]
    })
  end
  self.m_state = dbTable.cookState
  self.m_cookStartTimer = dbTable.cookStartTimer
  self.m_cookSpeedTime = dbTable.cookSpeedTime
  self.m_cookLastUpdateTime = dbTable.cookLastUpdateTime or 0
  self.m_curRecipe = dbTable.cookRecipe
  if self.m_state == EItemCookState.CanCook or self.m_state == EItemCookState.Prepare then
    self:_UpdateStateAfterMaterialChange()
  end
  if self.m_state == EItemCookState.Cooking or self.m_state == EItemCookState.Cooked then
    local config = self.m_mapRecipes[self.m_curRecipe]
    if StringUtil.IsNilOrEmpty(self.m_curRecipe) then
      self.m_curRecipe, self.m_curCookDuration, self.m_curSpeedUpPrice = self:_GetPerfectMatchedRecipe()
    elseif config == nil then
      if GM.ItemDataModel:GetModelConfig(self.m_curRecipe, true) == nil then
        self.m_curRecipe = ""
      else
        self.m_curCookDuration = 1
        self.m_curSpeedUpPrice = 1
      end
    else
      self.m_curCookDuration = config.Dur
      self.m_curSpeedUpPrice = config.SpeedUpPrice
    end
    if StringUtil.IsNilOrEmpty(self.m_curRecipe) then
      self.m_state = EItemCookState.Prepare
      self.m_cookStartTimer = -1
      self.m_cookSpeedTime = 0
      self.m_cookLastUpdateTime = 0
    end
  end
end

function ItemCook:ToSerialization(dbTable)
  local data = ""
  for index, material in ipairs(self.m_curMaterials) do
    data = data .. material.ItemCode .. "-" .. material.ItemId
    if index ~= #self.m_curMaterials then
      data = data .. ";"
    end
  end
  dbTable.materialInfo = data
  dbTable.cookState = self.m_state
  dbTable.cookStartTimer = self.m_cookStartTimer
  dbTable.cookSpeedTime = self.m_cookSpeedTime
  dbTable.cookLastUpdateTime = self.m_cookLastUpdateTime
  dbTable.cookRecipe = self.m_curRecipe
end

function ItemCook:_Save()
  self.m_itemModel:GetBoardModel():SaveItemProperty(self.m_itemModel)
end

function ItemCook:CanAddMaterial(item)
  if self.m_state == EItemCookState.Cooking or self.m_state == EItemCookState.Cooked then
    return false
  end
  local itemCode = item:GetCode()
  local curMaterialsArray = self:GetCurMaterialsArray()
  if Table.ListContain(curMaterialsArray, itemCode) then
    return false
  end
  for recipe, recipeConfig in pairs(self.m_mapRecipes) do
    local needMaterials = recipeConfig.Materials
    if Table.IsSubArray(needMaterials, curMaterialsArray) and Table.ListContain(needMaterials, itemCode) then
      return true
    end
  end
  return false
end

function ItemCook:AddMaterial(item)
  if not self:CanAddMaterial(item) then
    return false
  end
  local itemCode = item:GetCode()
  table.insert(self.m_curMaterials, {
    ItemCode = itemCode,
    ItemId = item:GetId()
  })
  self:_UpdateStateAfterMaterialChange()
  self:_Save()
  EventDispatcher.DispatchEvent(EEventType.ItemCookAddMaterial, {itemCook = self})
  GM.BIManager:LogAction(EBIType.CookAddMaterial, GM.BIManager:TableToString({
    eq = self.m_itemModel:GetCode(),
    i = itemCode
  }))
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxPlaceFood)
  return true
end

function ItemCook:_UpdateStateAfterMaterialChange()
  self.m_curRecipe, self.m_curCookDuration, self.m_curSpeedUpPrice = self:_GetPerfectMatchedRecipe()
  if not StringUtil.IsNilOrEmpty(self.m_curRecipe) then
    self.m_state = EItemCookState.CanCook
  elseif #self.m_curMaterials == 0 then
    self.m_state = EItemCookState.Empty
  else
    self.m_state = EItemCookState.Prepare
  end
  self.event:Call(ItemCookEventType.StateChanged)
end

function ItemCook:PutBackMaterial(itemCode, startWorldPos)
  local boardModel = self.m_itemModel:GetBoardModel()
  if boardModel:IsBoardFull() then
    self:_NotifySpreadFailed(startWorldPos)
    return false
  end
  for index, curMaterial in ipairs(self.m_curMaterials) do
    if curMaterial.ItemCode == itemCode then
      table.remove(self.m_curMaterials, index)
      self:_UpdateStateAfterMaterialChange()
      self:_Save()
      local item = self.m_itemModel:GetBoardModel():PutBackMaterial(itemCode, curMaterial.ItemId, startWorldPos)
      GM.BIManager:LogAction(EBIType.CookPutbackMaterial, GM.BIManager:TableToString({
        eq = self.m_itemModel:GetCode(),
        i = itemCode
      }))
      return true, item
    end
  end
  return false
end

function ItemCook:RemoveInnerMaterials()
  local materialIds = self:GetMaterialIds()
  local boardModel = self.m_itemModel:GetBoardModel()
  local itemManager = boardModel.m_itemManager
  for _, materialId in ipairs(materialIds) do
    local item = itemManager:GetItem(materialId)
    if item then
      boardModel:RemoveItem(item)
    end
  end
end

function ItemCook:RemoveMaterial(itemModel)
  if not itemModel then
    return false
  end
  local itemId = itemModel:GetId()
  local found = false
  for index, curMaterial in ipairs(self.m_curMaterials) do
    if curMaterial.ItemId == itemId then
      table.remove(self.m_curMaterials, index)
      found = true
    end
  end
  if not found then
    return false
  end
  self:_UpdateStateAfterMaterialChange()
  self:_Save()
  local boardModel = itemModel:GetBoardModel()
  boardModel:RemoveItem(itemModel)
  return true
end

function ItemCook:TestDeleteContent()
  if not GameConfig.IsTestMode() then
    return 0
  end
  self:TryRevertCook()
  self:_ReleasePairedOrder()
  local count = #self.m_curMaterials
  if self:GetState() ~= EItemCookState.Empty then
    self:RemoveInnerMaterials()
    self:_Reset()
    self:_Save()
  end
  return count
end

function ItemCook:_NotifySpreadFailed(startWorldPos)
  local boardModel = self.m_itemModel:GetBoardModel()
  boardModel.event:Call(BoardEventType.SpreadFailed, {
    Item = self.m_itemModel,
    Pos = startWorldPos
  })
  EventDispatcher.DispatchEvent(EEventType.ItemSpreadFailed, {
    reason = SpreadFailedReason.BoardFull,
    gameMode = self:GetGameMode()
  })
end

function ItemCook:_GetPerfectMatchedRecipe()
  local curMaterialsArray = self:GetCurMaterialsArray()
  local needMaterials
  for recipe, recipeConfig in pairs(self.m_mapRecipes) do
    needMaterials = recipeConfig.Materials
    if Table.CheckListEqual(curMaterialsArray, needMaterials) then
      return recipe, recipeConfig.Dur, recipeConfig.SpeedUpPrice
    end
  end
  return ""
end

function ItemCook:StartCook()
  if self.m_state ~= EItemCookState.CanCook then
    return
  end
  self.m_state = EItemCookState.Cooking
  self.m_cookStartTimer = GM.GameModel:GetServerTime()
  self.m_cookSpeedTime = 0
  self.m_cookLastUpdateTime = 0
  self:_Save()
  self:UpdatePerSecond()
  self:LogCook("start", self.m_curCookDuration)
  EventDispatcher.DispatchEvent(EEventType.ItemCookStarted, {
    item = self.m_itemModel
  })
  self.event:Call(ItemCookEventType.StateChanged)
  self.m_itemModel:GetBoardModel():UpdateOrderState()
  local cookEquipmentAudioName = self.m_itemModel:GetCookEquipmentAudio()
  if cookEquipmentAudioName then
    GM.AudioModel:StopCookEffect()
    GM.AudioModel:PlayCookEffect(cookEquipmentAudioName)
  end
end

function ItemCook:TryRevertCook()
  if self.m_state ~= EItemCookState.Cooking then
    return true
  end
  local cost = GM.ItemDataModel:GetModelConfig(self.m_curRecipe).RevocationPrice or 1
  local gemNumber = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem)
  if cost > gemNumber then
    local boardModel = self.m_itemModel:GetBoardModel()
    boardModel.event:Call(BoardEventType.LackGem, {
      LackNumber = cost - gemNumber
    })
    return false
  end
  GM.PropertyDataManager:Consume(EPropertyType.Gem, cost, EBIType.UndoCook, false, self.m_itemModel:GetCode())
  local costTime = self:_GetRealCostTime()
  self.m_cookStartTimer = -1
  self.m_cookSpeedTime = 0
  self.m_cookLastUpdateTime = 0
  self.m_itemModel:ClearCookCost()
  self:_UpdateStateAfterMaterialChange()
  self:_Save()
  self:LogCook("cancel", costTime)
  EventDispatcher.DispatchEvent(EEventType.ItemCookEnded, {
    item = self.m_itemModel
  })
  self.m_itemModel:GetBoardModel():UpdateOrderState()
  GM.AudioModel:StopCookEffect()
  return true
end

function ItemCook:OnTap()
  if self.m_state ~= EItemCookState.Cooked then
    return
  end
  self:_TakeOutCookedRecipe()
end

function ItemCook:_TakeOutCookedRecipe()
  if self.m_state ~= EItemCookState.Cooked then
    return
  end
  local replace = self:IsDisposable()
  local boardModel = self.m_itemModel:GetBoardModel()
  if not replace and boardModel:IsBoardFull() then
    self:_NotifySpreadFailed()
    return
  end
  self:_ReleasePairedOrder()
  local materialIds = self:GetMaterialIds()
  self.m_itemModel:GetBoardModel():TakeOutCookedRecipe(self.m_itemModel, materialIds, self.m_curRecipe, replace)
  if not replace then
    self:_Reset()
    self:_Save()
  end
  self.m_itemModel:GetBoardModel():UpdateOrderState()
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxDishOut)
end

function ItemCook:IsDisposable()
  return not StringUtil.StartWith(self.m_itemModel:GetCode(), "eq_")
end

function ItemCook:_Reset()
  self.m_curMaterials = {}
  self.m_state = EItemCookState.Empty
  self.m_curRecipe = ""
  self.m_curCookDuration = nil
  self.m_curSpeedUpPrice = nil
  self.m_cookStartTimer = -1
  self.m_cookSpeedTime = 0
  self.m_cookLastUpdateTime = 0
  self.event:Call(ItemCookEventType.StateChanged)
end

function ItemCook:UpdatePerSecond()
  if self.m_cookStartTimer == -1 then
    return
  end
  local origin = self.m_cookLastUpdateTime
  local cur = GM.GameModel:GetServerTime()
  local isFromBG = self:_BeforeFlambeEnded(cur)
  if not isFromBG and self:IsFlambeTime() then
    if self.m_cookLastUpdateTime == 0 then
      self.m_cookLastUpdateTime = self.m_cookStartTimer
    end
    local elapsedTime = cur - self.m_cookLastUpdateTime
    local speedTime = (GM.FlambeTimeModel:GetFlambeTimeInstruSpeed() - 1) * elapsedTime
    speedTime = math.min(speedTime, self:GetRemainCookDuration())
    self.m_cookSpeedTime = self.m_cookSpeedTime + speedTime
    self.m_cookStartTimer = self.m_cookStartTimer - speedTime
    self.m_cookLastUpdateTime = cur
  elseif not isFromBG then
    self.m_cookLastUpdateTime = cur
  end
  if 1 <= self:GetTimerAmount() and self.m_itemModel:IsInBoard() then
    local costTime = self:_GetRealCostTime()
    self.testInfoCostTime = costTime
    self.m_cookStartTimer = -1
    self.m_state = EItemCookState.Cooked
    self:_Save()
    self:LogCook("finish", costTime)
    EventDispatcher.DispatchEvent(EEventType.ItemCookEnded, {
      item = self.m_itemModel
    })
    self.event:Call(ItemCookEventType.StateChanged)
    self.m_itemModel:GetBoardModel():UpdateOrderState()
    GM.AudioModel:PlayEffect(AudioFileConfigName.SfxDishDone)
    GM.AudioModel:StopCookEffect()
  elseif self.m_cookLastUpdateTime ~= origin then
    self:_Save()
  end
end

function ItemCook:_BeforeFlambeEnded(flambeEndTime)
  if self.m_cookStartTimer == -1 or GM.GameModel:GetServerTime() - self.m_cookLastUpdateTime <= 2 or not self:IsFlambeTime() then
    return false
  end
  if self.m_cookLastUpdateTime == 0 then
    self.m_cookLastUpdateTime = self.m_cookStartTimer
  end
  local elapsedTime = self.m_cookLastUpdateTime - self.m_cookStartTimer
  local remainTime = self.m_curCookDuration - elapsedTime
  if remainTime <= 0 then
    return false
  end
  local needTime = math.ceil(remainTime / GM.FlambeTimeModel:GetFlambeTimeInstruSpeed())
  local cookFinishTime = self.m_cookLastUpdateTime + needTime
  if flambeEndTime >= cookFinishTime then
    local speedTime = (GM.FlambeTimeModel:GetFlambeTimeInstruSpeed() - 1) * (needTime - 1)
    self.m_cookSpeedTime = self.m_cookSpeedTime + speedTime
    self.m_cookStartTimer = self.m_cookStartTimer - speedTime
    self.m_cookLastUpdateTime = cookFinishTime - 1
    local elapsedTime = self.m_cookLastUpdateTime - self.m_cookStartTimer
    local remainTime = self.m_curCookDuration - elapsedTime
    speedTime = math.min(GM.FlambeTimeModel:GetFlambeTimeInstruSpeed() - 1, remainTime - 1)
    speedTime = math.max(speedTime, 0)
    self.m_cookSpeedTime = self.m_cookSpeedTime + speedTime
    self.m_cookStartTimer = self.m_cookStartTimer - speedTime
    self.m_cookLastUpdateTime = cookFinishTime
    self:_Save()
  else
    local elapsedTime = flambeEndTime - self.m_cookLastUpdateTime
    local speedTime = (GM.FlambeTimeModel:GetFlambeTimeInstruSpeed() - 1) * elapsedTime
    self.m_cookSpeedTime = self.m_cookSpeedTime + speedTime
    self.m_cookStartTimer = self.m_cookStartTimer - speedTime
    self.m_cookLastUpdateTime = flambeEndTime
    self:_Save()
  end
  return true
end

function ItemCook:_GetRealCostTime()
  local costTime = GM.GameModel:GetServerTime() - self.m_cookStartTimer - self.m_cookSpeedTime
  local maxCostTime = self.m_curCookDuration - self.m_cookSpeedTime
  costTime = math.min(costTime, maxCostTime)
  costTime = math.max(costTime, 1)
  return costTime
end

function ItemCook:GetTimerAmount()
  return self:_GetTimerAmount(GM.GameModel:GetServerTime() - self.m_cookStartTimer)
end

function ItemCook:GetSkipTimerAmount(skipDt)
  return self:_GetTimerAmount(GM.GameModel:GetServerTime() - self.m_cookStartTimer + skipDt)
end

function ItemCook:GetNextTimerAmount()
  local delta = 1
  if self:IsFlambeTime() then
    delta = GM.FlambeTimeModel:GetFlambeTimeInstruSpeed()
  end
  return self:_GetTimerAmount(GM.GameModel:GetServerTime() - self.m_cookStartTimer + delta)
end

function ItemCook:_GetTimerAmount(elapsedTime)
  if self.m_cookStartTimer == -1 then
    return 1
  end
  local duration = self.m_curCookDuration or 0
  if duration == 0 then
    return 1
  end
  local amount = math.min(elapsedTime / duration, 1)
  if self.m_bInTutorial then
    return math.min(0.9, amount)
  end
  return amount
end

function ItemCook:GetCurMaterialsArray()
  local result = {}
  for _, material in ipairs(self.m_curMaterials) do
    table.insert(result, material.ItemCode)
  end
  return result
end

function ItemCook:GetMaterialIds()
  local materialIds = {}
  for _, v in ipairs(self.m_curMaterials) do
    table.insert(materialIds, v.ItemId)
  end
  return materialIds
end

function ItemCook:GetMaterialItems()
  local materialItems = {}
  local boardModel = self.m_itemModel:GetBoardModel()
  local itemManager = boardModel.m_itemManager
  local materialIds = self:GetMaterialIds()
  for _, materialId in ipairs(materialIds) do
    local item = itemManager:GetItem(materialId)
    table.insert(materialItems, item)
  end
  return materialItems
end

function ItemCook:GetState()
  return self.m_state
end

function ItemCook:GetRecipe()
  return self.m_curRecipe
end

function ItemCook:GetRemainCookDuration()
  local elapsedTime = GM.GameModel:GetServerTime() - self.m_cookStartTimer
  local duration = self.m_curCookDuration or 0
  duration = math.max(0, duration - elapsedTime)
  if self.m_bInTutorial then
    return math.max(1, duration)
  end
  return duration
end

function ItemCook:CanCook(recipe)
  if self:GetState() == EItemCookState.Cooked or self:GetState() == EItemCookState.Cooking then
    return false
  end
  if self.m_mapRecipes[recipe] == nil then
    return false
  end
  local needMaterials = self.m_mapRecipes[recipe].Materials
  local curMaterialsArray = self:GetCurMaterialsArray()
  if not Table.IsSubArray(needMaterials, curMaterialsArray) then
    return false
  end
  local lackMaterials = {}
  for _, mat in ipairs(needMaterials) do
    if not Table.ListContain(curMaterialsArray, mat) then
      lackMaterials[#lackMaterials + 1] = mat
    end
  end
  return true, lackMaterials
end

function ItemCook:CanCookAllowTakeOut(recipe)
  if self:GetState() == EItemCookState.Cooked or self:GetState() == EItemCookState.Cooking then
    return false
  end
  if self.m_mapRecipes[recipe] == nil then
    return false
  end
  local needMaterials = self.m_mapRecipes[recipe].Materials
  local curMaterialsArray = self:GetCurMaterialsArray()
  local lackMaterials = {}
  for _, mat in ipairs(needMaterials) do
    if not Table.ListContain(curMaterialsArray, mat) then
      lackMaterials[#lackMaterials + 1] = mat
    end
  end
  local unwantedMaterials = {}
  for _, mat in ipairs(curMaterialsArray) do
    if not Table.ListContain(needMaterials, mat) then
      unwantedMaterials[#unwantedMaterials + 1] = mat
    end
  end
  return true, lackMaterials, unwantedMaterials
end

function ItemCook:GetSpeedUpCost()
  if self.m_state ~= EItemCookState.Cooking then
    Log.Error("非菜品制作中，不涉及加速！")
    return 0
  end
  if self.m_bInTutorial then
    return 0, true
  end
  local remainTime = self:GetRemainCookDuration()
  local skipProsNumber = GM.PropertyDataManager:GetPropertyNum(EPropertyType.SkipProp)
  if 0 < skipProsNumber then
    local cost = remainTime / SKIPPROP_EFFECT_TIME
    cost = math.ceil(cost)
    cost = math.min(skipProsNumber, cost)
    return math.max(cost, 1), true
  end
  local originalCost = self.m_curSpeedUpPrice or 0
  local cost = originalCost * remainTime / self.m_curCookDuration
  cost = math.ceil(cost)
  return math.max(cost, 1), false
end

function ItemCook:OnSpeedUp(isFree)
  if self.m_bInTutorial then
    isFree = true
    self.m_bInTutorial = nil
  end
  if self.m_cookStartTimer == -1 then
    return
  end
  local cost, isSkipProp = self:GetSpeedUpCost()
  if not isFree then
    local property = isSkipProp and EPropertyType.SkipProp or EPropertyType.Gem
    local gemNumber = GM.PropertyDataManager:GetPropertyNum(property)
    if cost > gemNumber then
      local boardModel = self.m_itemModel:GetBoardModel()
      boardModel.event:Call(BoardEventType.LackGem, {
        LackNumber = cost - gemNumber
      })
      return
    end
    if isSkipProp then
      self.m_itemModel.cookSkipPropCost = self.m_itemModel.cookSkipPropCost + cost
    else
      self.m_itemModel.cookGemCost = self.m_itemModel.cookGemCost + cost
    end
    GM.PropertyDataManager:Consume(property, cost, EBIType.SpeedUpCook, false, self.m_itemModel:GetCode())
  end
  local speedUpDuration = self:GetRemainCookDuration()
  local source = isFree and "ad" or "gem"
  if not isFree and isSkipProp then
    speedUpDuration = math.min(speedUpDuration, cost * SKIPPROP_EFFECT_TIME)
    source = "skip"
  end
  self:_SkipTime(speedUpDuration, source)
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxFoodSpeed)
end

function ItemCook:_SkipTime(speedUpDuration, source)
  self.m_cookSpeedTime = self.m_cookSpeedTime + speedUpDuration
  self.m_cookStartTimer = self.m_cookStartTimer - speedUpDuration
  self:_Save()
  self.event:Call(ItemCookEventType.SpeedUp)
  self.m_itemModel:GetBoardModel():UpdateOrderState()
  self:UpdatePerSecond()
  GM.BIManager:LogAction(EBIType.SpeedUp, GM.BIManager:TableToString({
    t = self.m_itemModel:GetCode(),
    d = speedUpDuration,
    s = source
  }))
end

function ItemCook:SetTutorialCD()
  self.m_bInTutorial = true
  EventDispatcher.DispatchEvent(EEventType.ItemTutorial, {
    item = self.m_itemModel
  })
end

function ItemCook:LogCook(action, duration)
  local materials = self:GetCurMaterialsArray()
  local biMat = ""
  for i, mat in ipairs(materials) do
    biMat = biMat .. mat
    if i ~= #materials then
      biMat = biMat .. ","
    end
  end
  GM.BIManager:LogCook(action, self.m_curRecipe, self.m_itemModel:GetCode(), biMat, duration)
end

function ItemCook:IsFlambeTime()
  return GM.FlambeTimeModel:IsFlambeTimeInstru(self.m_itemModel:GetCode())
end

function ItemCook:_OnFlambeTimeChanged()
  self.event:Call(ItemCookEventType.UpdateFire)
end

function ItemCook:HideFire()
  self.event:Call(ItemCookEventType.HideFire)
end

function ItemCook:TryShowFire()
  self:_OnFlambeTimeChanged()
end
