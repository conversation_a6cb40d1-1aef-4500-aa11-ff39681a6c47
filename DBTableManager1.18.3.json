{"finishedTasks": [[{"chapterId": 1, "taskId": 1, "value": 25}, {"chapterId": 1, "taskId": 2, "value": 55}, {"chapterId": 1, "taskId": 3, "value": 49}, {"chapterId": 1, "taskId": 4, "value": 33}, {"chapterId": 1, "taskId": 5, "value": 93}, {"chapterId": 1, "taskId": 6, "value": 80}, {"chapterId": 1, "taskId": 7, "value": 51}, {"chapterId": 1, "taskId": 8, "value": 68}, {"chapterId": 1, "taskId": 9, "value": 38}, {"chapterId": 1, "taskId": 10, "value": 100}], [{"chapterId": 2, "taskId": 1, "value": 135}, {"chapterId": 2, "taskId": 2, "value": 135}, {"chapterId": 2, "taskId": 3, "value": 135}, {"chapterId": 2, "taskId": 4, "value": 135}, {"chapterId": 2, "taskId": 5, "value": 135}, {"chapterId": 2, "taskId": 6, "value": 135}, {"chapterId": 2, "taskId": 7, "value": 149}, {"chapterId": 2, "taskId": 8, "value": 149}, {"chapterId": 2, "taskId": 9, "value": 149}, {"chapterId": 2, "taskId": 10, "value": 149}, {"chapterId": 2, "taskId": 11, "value": 149}, {"chapterId": 2, "taskId": 12, "value": 149}, {"chapterId": 2, "taskId": 13, "value": 149}, {"chapterId": 2, "taskId": 14, "value": 149}, {"chapterId": 2, "taskId": 15, "value": 149}, {"chapterId": 2, "taskId": 16, "value": 149}, {"chapterId": 2, "taskId": 17, "value": 149}, {"chapterId": 2, "taskId": 18, "value": 149}, {"chapterId": 2, "taskId": 19, "value": 149}, {"chapterId": 2, "taskId": 20, "value": 149}, {"chapterId": 2, "taskId": 21, "value": 149}, {"chapterId": 2, "taskId": 22, "value": 149}, {"chapterId": 2, "taskId": 23, "value": 216}, {"chapterId": 2, "taskId": 24, "value": 216}, {"chapterId": 2, "taskId": 25, "value": 216}, {"chapterId": 2, "taskId": 26, "value": 216}, {"chapterId": 2, "taskId": 27, "value": 216}, {"chapterId": 2, "taskId": 28, "value": 216}, {"chapterId": 2, "taskId": 29, "value": 216}, {"chapterId": 2, "taskId": 30, "value": 216}], [{"chapterId": 3, "taskId": 1, "value": 335}, {"chapterId": 3, "taskId": 2, "value": 335}, {"chapterId": 3, "taskId": 3, "value": 335}, {"chapterId": 3, "taskId": 4, "value": 335}, {"chapterId": 3, "taskId": 5, "value": 335}, {"chapterId": 3, "taskId": 6, "value": 352}, {"chapterId": 3, "taskId": 7, "value": 352}, {"chapterId": 3, "taskId": 8, "value": 352}, {"chapterId": 3, "taskId": 9, "value": 355}, {"chapterId": 3, "taskId": 10, "value": 307}, {"chapterId": 3, "taskId": 11, "value": 307}, {"chapterId": 3, "taskId": 12, "value": 177}, {"chapterId": 3, "taskId": 13, "value": 307}, {"chapterId": 3, "taskId": 14, "value": 268}, {"chapterId": 3, "taskId": 15, "value": 361}, {"chapterId": 3, "taskId": 16, "value": 338}, {"chapterId": 3, "taskId": 17, "value": 361}, {"chapterId": 3, "taskId": 18, "value": 269}, {"chapterId": 3, "taskId": 19, "value": 291}, {"chapterId": 3, "taskId": 20, "value": 245}, {"chapterId": 3, "taskId": 21, "value": 285}, {"chapterId": 3, "taskId": 22, "value": 321}, {"chapterId": 3, "taskId": 23, "value": 538}, {"chapterId": 3, "taskId": 24, "value": 249}, {"chapterId": 3, "taskId": 25, "value": 430}, {"chapterId": 3, "taskId": 26, "value": 394}, {"chapterId": 3, "taskId": 27, "value": 357}, {"chapterId": 3, "taskId": 28, "value": 249}, {"chapterId": 3, "taskId": 29, "value": 240}, {"chapterId": 3, "taskId": 30, "value": 643}, {"chapterId": 3, "taskId": 31, "value": 285}, {"chapterId": 3, "taskId": 32, "value": 240}, {"chapterId": 3, "taskId": 33, "value": 598}, {"chapterId": 3, "taskId": 34, "value": 240}, {"chapterId": 3, "taskId": 35, "value": 509}, {"chapterId": 3, "taskId": 36, "value": 374}, {"chapterId": 3, "taskId": 37, "value": 474}, {"chapterId": 3, "taskId": 38, "value": 448}, {"chapterId": 3, "taskId": 39, "value": 367}, {"chapterId": 3, "taskId": 40, "value": 393}, {"chapterId": 3, "taskId": 41, "value": 448}, {"chapterId": 3, "taskId": 42, "value": 367}, {"chapterId": 3, "taskId": 43, "value": 394}, {"chapterId": 3, "taskId": 44, "value": 395}], [{"chapterId": 4, "taskId": 1, "value": 499}, {"chapterId": 4, "taskId": 2, "value": 563}, {"chapterId": 4, "taskId": 3, "value": 531}, {"chapterId": 4, "taskId": 4, "value": 467}, {"chapterId": 4, "taskId": 5, "value": 436}, {"chapterId": 4, "taskId": 6, "value": 467}, {"chapterId": 4, "taskId": 7, "value": 467}, {"chapterId": 4, "taskId": 8, "value": 521}, {"chapterId": 4, "taskId": 9, "value": 571}, {"chapterId": 4, "taskId": 10, "value": 521}, {"chapterId": 4, "taskId": 11, "value": 571}, {"chapterId": 4, "taskId": 12, "value": 521}, {"chapterId": 4, "taskId": 13, "value": 521}, {"chapterId": 4, "taskId": 14, "value": 521}, {"chapterId": 4, "taskId": 15, "value": 595}, {"chapterId": 4, "taskId": 16, "value": 595}, {"chapterId": 4, "taskId": 17, "value": 544}, {"chapterId": 4, "taskId": 18, "value": 696}, {"chapterId": 4, "taskId": 19, "value": 493}, {"chapterId": 4, "taskId": 20, "value": 544}, {"chapterId": 4, "taskId": 21, "value": 592}, {"chapterId": 4, "taskId": 22, "value": 572}, {"chapterId": 4, "taskId": 23, "value": 572}, {"chapterId": 4, "taskId": 24, "value": 615}, {"chapterId": 4, "taskId": 25, "value": 615}, {"chapterId": 4, "taskId": 26, "value": 657}, {"chapterId": 4, "taskId": 27, "value": 699}, {"chapterId": 4, "taskId": 28, "value": 742}, {"chapterId": 4, "taskId": 29, "value": 614}, {"chapterId": 4, "taskId": 30, "value": 724}, {"chapterId": 4, "taskId": 31, "value": 613}, {"chapterId": 4, "taskId": 32, "value": 778}, {"chapterId": 4, "taskId": 33, "value": 724}, {"chapterId": 4, "taskId": 34, "value": 669}, {"chapterId": 4, "taskId": 35, "value": 613}, {"chapterId": 4, "taskId": 36, "value": 613}, {"chapterId": 4, "taskId": 37, "value": 668}, {"chapterId": 4, "taskId": 38, "value": 792}, {"chapterId": 4, "taskId": 39, "value": 737}, {"chapterId": 4, "taskId": 40, "value": 681}, {"chapterId": 4, "taskId": 41, "value": 625}, {"chapterId": 4, "taskId": 42, "value": 681}, {"chapterId": 4, "taskId": 43, "value": 681}, {"chapterId": 4, "taskId": 44, "value": 624}, {"chapterId": 4, "taskId": 45, "value": 680}], [{"chapterId": 5, "taskId": 1, "value": 666}, {"chapterId": 5, "taskId": 2, "value": 593}, {"chapterId": 5, "taskId": 3, "value": 519}, {"chapterId": 5, "taskId": 4, "value": 519}, {"chapterId": 5, "taskId": 5, "value": 593}, {"chapterId": 5, "taskId": 6, "value": 629}, {"chapterId": 5, "taskId": 7, "value": 666}, {"chapterId": 5, "taskId": 8, "value": 594}, {"chapterId": 5, "taskId": 9, "value": 697}, {"chapterId": 5, "taskId": 10, "value": 571}, {"chapterId": 5, "taskId": 11, "value": 571}, {"chapterId": 5, "taskId": 12, "value": 655}, {"chapterId": 5, "taskId": 13, "value": 655}, {"chapterId": 5, "taskId": 14, "value": 655}, {"chapterId": 5, "taskId": 15, "value": 614}, {"chapterId": 5, "taskId": 16, "value": 697}, {"chapterId": 5, "taskId": 17, "value": 633}]], "cacheItem": {"1749450891001": {"type": 2, "codeStr": "freebox_1", "cost": "{}", "id": "1749450891001"}}, "config": {"key": {"value": {"bp5": {"md5": "ae8d39467011b97685ed90531640db21", "config": {"sTime": 1747188000, "battlePassReward": [{"level": 1, "reward": [{"Amount": 30, "Crypt": "pH", "Currency": "energy"}], "require": 0, "golden_reward": [{"Amount": 150, "Crypt": "rMZ", "Currency": "energy"}]}, {"level": 2, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_1_2_2"}], "require": 60, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}]}, {"level": 3, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_2"}], "require": 90, "golden_reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}]}, {"level": 4, "reward": [{"Amount": 30, "Crypt": "pH", "Currency": "skipprop"}], "require": 120, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}]}, {"level": 5, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_1_1_5"}], "require": 150, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_3"}]}, {"level": 6, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_2"}], "require": 180, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_1_1_6"}]}, {"level": 7, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "freebox_1"}], "require": 220, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}]}, {"level": 8, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}], "require": 260, "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "skipprop"}]}, {"level": 9, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_1"}], "require": 280, "golden_reward": [{"Amount": 2, "Crypt": "q", "Currency": "additem_1"}]}, {"level": 10, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "greenbox_1"}], "require": 340, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}]}, {"level": 11, "reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}], "require": 380, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_2_3_2"}]}, {"level": 12, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_2_3_1"}], "require": 410, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_3"}]}, {"level": 13, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_1"}], "require": 460, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}]}, {"level": 14, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_2"}], "require": 500, "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "skipprop"}]}, {"level": 15, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "freebox_1"}], "require": 540, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}]}, {"level": 16, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}], "require": 580, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "freebox_1"}]}, {"level": 17, "reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}], "require": 640, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}]}, {"level": 18, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}], "require": 700, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_3_1_7"}]}, {"level": 19, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_3_1_6"}], "require": 780, "golden_reward": [{"Amount": 2, "Crypt": "q", "Currency": "additem_1"}]}, {"level": 20, "reward": [{"Amount": 30, "Crypt": "pH", "Currency": "energy"}], "require": 850, "golden_reward": [{"Amount": 50, "Crypt": "vH", "Currency": "energy"}]}, {"level": 21, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_3"}], "require": 940, "golden_reward": [{"Amount": 10, "Crypt": "rH", "Currency": "gem"}]}, {"level": 22, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}], "require": 1020, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_5"}]}, {"level": 23, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}], "require": 1120, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_3_2_5"}]}, {"level": 24, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}], "require": 1220, "golden_reward": [{"Amount": 2, "Crypt": "q", "Currency": "additem_1"}]}, {"level": 25, "reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}], "require": 1320, "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "skipprop"}]}, {"level": 26, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_3_2_4"}], "require": 1430, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}]}, {"level": 27, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_2"}], "require": 1540, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_3"}]}, {"level": 28, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}], "require": 1650, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_4_2_5"}]}, {"level": 29, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "it_4_2_4"}], "require": 1760, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}]}, {"level": 30, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "additem_1"}], "require": 1880, "golden_reward": [{"Amount": 15, "Crypt": "rM", "Currency": "gem"}]}, {"level": 31, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_3"}, {"Amount": 1, "Crypt": "r", "Currency": "greenbox_1"}], "require": 2000, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}, {"Amount": 1, "Crypt": "r", "Currency": "greenbox_1"}]}, {"level": 32, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_4"}], "require": 2120, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "ene_5"}]}, {"level": 33, "reward": [{"Amount": 50, "Crypt": "vH", "Currency": "skipprop"}], "require": 2220, "golden_reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_4"}]}, {"level": 34, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "gem_4"}], "require": 2360, "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "skipprop"}]}, {"level": 35, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "enebox_1"}], "require": 2400, "golden_reward": [{"Amount": 25, "Crypt": "qM", "Currency": "gem"}]}, {"level": 36, "reward": [{"Amount": 1, "Crypt": "r", "Currency": "skiptime_1"}], "require": 2480, "golden_reward": [{"Amount": 100, "Crypt": "rHZ", "Currency": "energy"}, {"Amount": 1, "Crypt": "r", "Currency": "skiptime_1"}, {"Amount": 1, "Crypt": "r", "Currency": "greenbox_1"}]}], "sLv": 7, "id": 79, "include": ["battlePassReward#test1", "battlePassTask#test1"], "rTime": 1749780000, "eTime": 1749607200, "battlePassTask": [{"tasks": [{"type": "customer", "tokenNum": 40, "count": 2}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "gem", "tokenNum": 25, "count": 5}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}], "type": "timelimit", "time": 1, "order": 1}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "gold", "tokenNum": 50, "count": 500}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 70, "count": 700}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 100, "count": 1000}, {"type": "gold", "tokenNum": 120, "count": 1200}], "type": "timelimit", "time": 2, "order": 2}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "gold", "tokenNum": 45, "count": 450}, {"type": "gold", "tokenNum": 50, "count": 500}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gem", "tokenNum": 25, "count": 5}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}], "type": "timelimit", "time": 3, "order": 3}, {"tasks": [{"type": "customer", "tokenNum": 40, "count": 2}, {"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 110, "count": 1100}], "type": "timelimit", "time": 4, "order": 4}, {"tasks": [{"type": "customer", "tokenNum": 40, "count": 2}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 120, "count": 1200}], "type": "timelimit", "time": 5, "order": 5}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 220, "count": 11}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}, {"type": "gem", "tokenNum": 100, "count": 20}], "type": "timelimit", "time": 6, "order": 6}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "gold", "tokenNum": 50, "count": 500}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 90, "count": 900}, {"type": "gold", "tokenNum": 110, "count": 1100}, {"type": "gold", "tokenNum": 130, "count": 1300}], "type": "timelimit", "time": 7, "order": 7}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "gold", "tokenNum": 50, "count": 500}, {"type": "gold", "tokenNum": 65, "count": 650}, {"type": "gold", "tokenNum": 100, "count": 1000}, {"type": "gem", "tokenNum": 25, "count": 5}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}], "type": "timelimit", "time": 8, "order": 8}, {"tasks": [{"type": "customer", "tokenNum": 40, "count": 2}, {"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 90, "count": 900}, {"type": "gold", "tokenNum": 120, "count": 1200}], "type": "timelimit", "time": 9, "order": 9}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "gold", "tokenNum": 45, "count": 450}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gem", "tokenNum": 25, "count": 5}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}], "type": "timelimit", "time": 10, "order": 10}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 90, "count": 900}, {"type": "gold", "tokenNum": 120, "count": 1200}], "type": "timelimit", "time": 11, "order": 11}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 220, "count": 11}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 100, "count": 1000}], "type": "timelimit", "time": 12, "order": 12}, {"tasks": [{"type": "customer", "tokenNum": 40, "count": 2}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "gold", "tokenNum": 70, "count": 700}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 110, "count": 1100}], "type": "timelimit", "time": 13, "order": 13}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 95, "count": 950}, {"type": "gold", "tokenNum": 110, "count": 1100}], "type": "timelimit", "time": 14, "order": 14}, {"tasks": [{"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 220, "count": 11}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 90, "count": 900}, {"type": "gold", "tokenNum": 120, "count": 1200}], "type": "timelimit", "time": 15, "order": 15}, {"tasks": [{"type": "customer", "tokenNum": 40, "count": 2}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "customer", "tokenNum": 220, "count": 11}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 90, "count": 900}, {"type": "gold", "tokenNum": 120, "count": 1200}], "type": "timelimit", "time": 16, "order": 16}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}, {"type": "gem", "tokenNum": 100, "count": 20}], "type": "timelimit", "time": 17, "order": 17}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "gold", "tokenNum": 50, "count": 500}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 85, "count": 850}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}, {"type": "gem", "tokenNum": 100, "count": 20}], "type": "timelimit", "time": 18, "order": 18}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 70, "count": 700}, {"type": "gold", "tokenNum": 100, "count": 1000}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}, {"type": "gem", "tokenNum": 100, "count": 20}], "type": "timelimit", "time": 19, "order": 19}, {"tasks": [{"type": "customer", "tokenNum": 40, "count": 2}, {"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gold", "tokenNum": 70, "count": 700}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 90, "count": 900}], "type": "timelimit", "time": 20, "order": 20}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gold", "tokenNum": 60, "count": 600}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 100, "count": 1000}], "type": "timelimit", "time": 21, "order": 21}, {"tasks": [{"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}, {"type": "gem", "tokenNum": 100, "count": 20}], "type": "timelimit", "time": 22, "order": 22}, {"tasks": [{"type": "customer", "tokenNum": 40, "count": 2}, {"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gold", "tokenNum": 70, "count": 700}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 100, "count": 1000}], "type": "timelimit", "time": 23, "order": 23}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "gold", "tokenNum": 45, "count": 450}, {"type": "gold", "tokenNum": 50, "count": 500}, {"type": "gold", "tokenNum": 80, "count": 600}, {"type": "gold", "tokenNum": 70, "count": 700}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 95, "count": 950}], "type": "timelimit", "time": 24, "order": 24}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "gold", "tokenNum": 50, "count": 500}, {"type": "gold", "tokenNum": 65, "count": 650}, {"type": "gold", "tokenNum": 85, "count": 850}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}, {"type": "gem", "tokenNum": 100, "count": 20}], "type": "timelimit", "time": 25, "order": 25}, {"tasks": [{"type": "customer", "tokenNum": 80, "count": 4}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 140, "count": 7}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "customer", "tokenNum": 220, "count": 11}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 90, "count": 900}, {"type": "gold", "tokenNum": 100, "count": 1000}], "type": "timelimit", "time": 26, "order": 26}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gold", "tokenNum": 80, "count": 800}, {"type": "gold", "tokenNum": 90, "count": 900}, {"type": "gold", "tokenNum": 100, "count": 1000}], "type": "timelimit", "time": 27, "order": 27}, {"tasks": [{"type": "customer", "tokenNum": 60, "count": 3}, {"type": "customer", "tokenNum": 100, "count": 5}, {"type": "customer", "tokenNum": 120, "count": 6}, {"type": "customer", "tokenNum": 160, "count": 8}, {"type": "customer", "tokenNum": 180, "count": 9}, {"type": "customer", "tokenNum": 200, "count": 10}, {"type": "gem", "tokenNum": 50, "count": 10}, {"type": "gem", "tokenNum": 75, "count": 15}, {"type": "gem", "tokenNum": 100, "count": 20}], "type": "timelimit", "time": 28, "order": 28}, {"tasks": [{"type": "merge", "tokenNum": 100, "count": 100}], "type": "cycle", "time": 0, "order": 29}]}}, "pkRace": {"md5": "cf9858f1707f5d75c27767dfa779973f", "config": {"sTime": 1749175200, "rTime": 1749607200, "sLv": 7, "id": 91, "include": ["pk_race#pk_race", "order_token_control#pk_race"], "pk_race": [{"playerNumWeight": [{"weight": 10, "group_type": "pk", "child_type": "easy"}, {"weight": 10, "group_type": "pk", "child_type": "normal"}, {"weight": 80, "group_type": "pk", "child_type": "fail"}], "rankReward2": [{"Currency": "energy", "Amount": 10}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "energy", "Amount": 30}], "target": 30, "round": 1}, {"rankReward2": [{"Currency": "cbox1_1", "Amount": 1}], "round": 2, "playerNumWeight": [{"weight": 60, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 10, "group_type": "pk", "child_type": "hard"}], "roundRewards": [{"Currency": "skipprop", "Amount": 20}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "greenbox_1", "Amount": 1}], "target": 30, "uiCode": "dig_chest_2"}, {"playerNumWeight": [{"weight": 30, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 30, "group_type": "pk", "child_type": "hard"}], "rankReward2": [{"Currency": "energy", "Amount": 15}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "energy", "Amount": 40}], "target": 50, "round": 3}, {"rankReward2": [{"Currency": "energy", "Amount": 15}], "round": 4, "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 40, "group_type": "pk", "child_type": "normal"}, {"weight": 40, "group_type": "pk", "child_type": "hard"}], "roundRewards": [{"Currency": "additem_1", "Amount": 1}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "energy", "Amount": 40}], "target": 50, "uiCode": "dig_chest_2"}, {"playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward2": [{"Currency": "energy", "Amount": 20}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "energy", "Amount": 50}], "target": 70, "round": 5}, {"playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward2": [{"Currency": "cbox1_1", "Amount": 1}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "greenbox_1", "Amount": 1}], "target": 70, "round": 6}, {"rankReward2": [{"Currency": "energy", "Amount": 25}], "round": 7, "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "roundRewards": [{"Currency": "skipprop", "Amount": 60}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "energy", "Amount": 60}], "target": 100, "uiCode": "dig_chest_2"}, {"playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward2": [{"Currency": "energy", "Amount": 30}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "energy", "Amount": 80}], "target": 120, "round": 8}, {"playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "rankReward2": [{"Currency": "cbox2_1", "Amount": 1}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "greenbox2_1", "Amount": 1}], "target": 150, "round": 9}, {"rankReward2": [{"Currency": "energy", "Amount": 30}], "round": 10, "playerNumWeight": [{"weight": 20, "group_type": "pk", "child_type": "easy"}, {"weight": 30, "group_type": "pk", "child_type": "normal"}, {"weight": 50, "group_type": "pk", "child_type": "hard"}], "roundRewards": [{"Currency": "energy", "Amount": 150}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "rankReward1": [{"Currency": "energy", "Amount": 80}], "target": 150, "uiCode": "dig_chest_3"}], "eTime": 1749434400, "order_token_control": [{"ratio": 0.33, "roundType": "down_1", "rewardsWeight": [{"Weight": 100, "Currency": "pkRace", "Amount": 1}]}]}}, "flambeTime": {"md5": "26b2de3f4ac083e934bee92fdfee3100", "config": {"sTime": 1737597600, "eTime": 1771034400, "sLv": 7, "id": 26}}, "chapterUpdate": {"md5": "1005f1a2ddb0e044580474de216b4cf2", "config": [{"enable": 1, "chapter": 5, "openDay": 1735178400, "dayLevel": 75, "preDay": 1735005600}, {"enable": 1, "chapter": 6, "openDay": 1736388000, "dayLevel": 100, "preDay": 1736215200}, {"enable": 1, "chapter": 7, "openDay": 1737597600, "dayLevel": 125, "preDay": 1737424800}, {"enable": 1, "chapter": 8, "openDay": 1739412000, "dayLevel": 150, "preDay": 1739239200}, {"enable": 1, "chapter": 9, "openDay": 1740621600, "dayLevel": 165, "preDay": 1740448800}, {"enable": 1, "chapter": 9, "openDay": 1741226400, "dayLevel": 180, "preDay": 1741053600}, {"enable": 1, "chapter": 10, "openDay": 1741831200, "dayLevel": 195, "preDay": 1741658400}, {"enable": 1, "chapter": 10, "openDay": 1742436000, "dayLevel": 210, "preDay": 1741831200}, {"enable": 1, "chapter": 11, "openDay": 1743040800, "dayLevel": 225, "preDay": 1742868000}, {"enable": 1, "chapter": 11, "openDay": 1743645600, "dayLevel": 240, "preDay": 1743040800}, {"enable": 1, "chapter": 12, "openDay": 1744250400, "dayLevel": 255, "preDay": 1744077600}, {"enable": 1, "chapter": 12, "openDay": 1744855200, "dayLevel": 270, "preDay": 1744250400}, {"enable": 1, "chapter": 13, "openDay": 1745460000, "dayLevel": 285, "preDay": 1745287200}, {"enable": 1, "chapter": 13, "openDay": 1746064800, "dayLevel": 300, "preDay": 1745460000}, {"enable": 1, "chapter": 14, "openDay": 1746669600, "dayLevel": 315, "preDay": 1746496800}, {"enable": 1, "chapter": 14, "openDay": 1747274400, "dayLevel": 330, "preDay": 1746669600}, {"enable": 1, "chapter": 15, "openDay": 1747879200, "dayLevel": 345, "preDay": 1747706400}, {"enable": 1, "chapter": 15, "openDay": 1748484000, "dayLevel": 360, "preDay": 1747879200}, {"enable": 1, "chapter": 16, "openDay": 1749088800, "dayLevel": 375, "preDay": 1748916000}, {"enable": 1, "chapter": 16, "openDay": **********, "dayLevel": 390, "preDay": 1749088800}]}, "extraBoard2": {"config": {"produce_token_control": [{"energyNum": 15, "rewardsWeight": [{"Amount": 1, "Currency": "eb_1_1", "Weight": 100}]}], "id": 98, "include": ["extraBoardConfig#extraBoard2", "order_token_control#extraBoardA7", "produce_token_control#extraBoardA7"], "eTime": 1750039200, "order_token_control": [{"score_min": 15, "score_max": 30, "rewardsWeight": [{"Amount": 1, "Currency": "eb_1_1", "Weight": 100}]}, {"score_min": 30, "score_max": 60, "rewardsWeight": [{"Amount": 1, "Currency": "eb_1_2", "Weight": 100}]}, {"score_min": 60, "score_max": 90, "rewardsWeight": [{"Amount": 2, "Currency": "eb_1_2", "Weight": 100}]}, {"score_min": 90, "score_max": 120, "rewardsWeight": [{"Amount": 3, "Currency": "eb_1_2", "Weight": 100}]}, {"score_min": 120, "score_max": 180, "rewardsWeight": [{"Amount": 2, "Currency": "eb_1_3", "Weight": 100}]}, {"score_min": 180, "score_max": 240, "rewardsWeight": [{"Amount": 3, "Currency": "eb_1_3", "Weight": 100}]}, {"rewardsWeight": [{"Amount": 4, "Currency": "eb_1_3", "Weight": 100}], "score_min": 240}], "sTime": 1749434400, "sLv": 8, "rTime": 1750125600, "extraBoardConfig": [{"board": "extraBoardA7", "itemPrefix": "eb2", "maxLevel": 12, "maxReward": [{"Currency": "energy", "Amount": 100}]}]}, "md5": "8ffda95c90c5310efd8534b953366481"}, "bakeOut": {"md5": "cb9512fa52a7f36e4aec16c3ec1147f4", "config": {"sTime": 1749089400, "staticInclude": ["bakeout_rank_reward#1st", "bakeout_rank_exchange#default", "bakeout_rank_parameter#1st"], "rTime": 1749694200, "id": 100020, "bakeout_rank_parameter": [{"settlementTime": 300, "delayTime": 90, "uploadTime": 60, "maxNum": 20, "noRegisterTime": 300, "retainTime": 601200}], "bakeout_rank_exchange": [{"cost": 10, "time": 1}, {"cost": 11, "time": 2}, {"cost": 14, "time": 3}, {"cost": 17, "time": 4}, {"cost": 21, "time": 5}, {"cost": 28, "time": 6}, {"cost": 37, "time": 7}, {"cost": 51, "time": 8}, {"cost": 72, "time": 9}, {"cost": 100, "time": 10}], "eTime": **********, "bakeout_rank_reward": [{"start_rank": 1, "end_rank": 1, "rewards": ["gem-25", "energy-100", "skiptime_1-1"]}, {"start_rank": 2, "end_rank": 2, "rewards": ["gem-20", "energy-80", "additem_1-1"]}, {"start_rank": 3, "end_rank": 3, "rewards": ["gem-15", "energy-50"]}, {"start_rank": 4, "end_rank": 6, "rewards": ["gem-10"]}, {"start_rank": 7, "end_rank": 10, "rewards": ["energy-50"]}, {"start_rank": 11, "end_rank": 15, "rewards": ["skipprop-50"]}, {"start_rank": 16, "end_rank": 20, "rewards": ["skipprop-25"]}]}}, "bundleController": {"md5": "6f1c31041cee40a7a9967686116ff5c5", "config": [{"specialType": "starter", "bundleTrigger": [{"trigger": "task_finished", "popNum": 1, "popOrder": ["starter"]}], "bundleCondition": [{"taskFinished": {"ChapterId": 2, "TaskCount": 3}}], "id": 1, "include": ["bundleContent#starter_499", "bundleTrigger#task_finished", "bundleCondition#starter_499"], "popCD": 360, "groupId": "starter", "dailyBuyNum": 1, "uiCode": "starter", "dailyShowNum": 2, "is_open": 1, "maxBuyNum": 1, "bundleContent": [{"payID": "starter_bundle_1", "content": [{"Currency": "gem", "Amount": 240}, {"Currency": "energy", "Amount": 200}, {"Currency": "additem_1", "Amount": 4}, {"Currency": "enebox_1", "Amount": 1}], "bundleId": "starter_499", "price": 4.99, "discountTag": "200%"}], "buyCD": 10080, "duration": 720, "order": ["starter_499"]}, {"include": ["bundleContent#cd_199", "bundleTrigger#pd_cd"], "groupId": "cd1", "dailyShowNum": 10, "bundleContent": [{"payID": "rush_order_199", "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "additem_1", "Amount": 4}, {"Currency": "energy", "Amount": 50}], "bundleId": "cd_199", "price": 1.99, "discountTag": "125%"}], "buyCD": 30, "order": ["cd_199"], "id": 8, "specialType": "cd", "dailyBuyNum": 2, "sTime": 1733875200, "bundleTrigger": [{"trigger": "pd_cd", "popNum": 1, "popOrder": ["cd"]}], "sLv": 8, "popCD": 60, "eTime": 1765411200, "is_open": 1, "duration": 360, "uiCode": "cd"}, {"include": ["bundleContent#order_199", "bundleTrigger#finish_order_group"], "groupId": "order1", "dailyShowNum": 5, "bundleContent": [{"payID": "finish_order_199", "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "energy", "Amount": 100}, {"Currency": "cbox3_1", "Amount": 2}], "bundleId": "order_199", "price": 1.99, "discountTag": "120%"}], "buyCD": 30, "order": ["order_199"], "id": 14, "specialType": "orderGroup", "dailyBuyNum": 2, "sTime": 1741053600, "bundleTrigger": [{"trigger": "finish_order_group", "popNum": 1, "popOrder": ["orderGroup"]}], "sLv": 8, "popCD": 60, "eTime": 1765411200, "is_open": 1, "duration": 60, "uiCode": "orderGroup"}, {"include": ["bundleContent#et1.99", "bundleContent#et3.99", "bundleContent#et5.99", "bundleTrigger#lack_energy", "generalBundleConf#3.99"], "groupId": "energytier1", "dailyShowNum": 10, "bundleContent": [{"payID": "energytier_199", "content": [{"Currency": "energy", "Amount": 250}], "bundleId": "et1.99", "discountTag": "125%", "price": 1.99, "originPrice": 2.99}, {"payID": "energytier_399", "content": [{"Currency": "energy", "Amount": 520}], "bundleId": "et3.99", "discountTag": "130%", "price": 3.99, "originPrice": 5.99}, {"payID": "energytier_599", "content": [{"Currency": "energy", "Amount": 820}], "bundleId": "et5.99", "discountTag": "140%", "price": 5.99, "originPrice": 8.99}], "buyCD": 30, "order": ["et1.99", "et3.99", "et5.99"], "id": 20, "popCD": 5, "bundleTrigger": [{"trigger": "lack_energy", "popNum": 1, "popOrder": ["energy"]}], "dailyBuyNum": 5, "sTime": 1742522400, "specialType": "multiTier", "sLv": 8, "eTime": 1765411200, "uiCode": "multiTier", "is_open": 1, "duration": 30, "generalBundleConf": [{"param_string": "et3.99", "confType": "defaultOrder"}]}, {"bundleContentChain": [{"content": [{"Currency": "skipprop", "Amount": 5}], "step": 1, "bundleId": "chain_skip_1", "price": 0, "skin": "1"}, {"content": [{"Currency": "skipprop", "Amount": 10}], "step": 2, "bundleId": "chain_skip_2", "price": 0, "skin": "1"}, {"content": [{"Currency": "ene_1", "Amount": 1}], "step": 3, "bundleId": "chain_skip_3", "price": 0, "skin": "1"}, {"payID": "chaingift_199", "content": [{"Currency": "gem", "Amount": 30}, {"Currency": "energy", "Amount": 150}], "step": 4, "bundleId": "chain_skip_4", "price": 1.99, "skin": "3"}, {"content": [{"Currency": "skipprop", "Amount": 15}], "step": 5, "bundleId": "chain_skip_5", "price": 0, "skin": "1"}, {"content": [{"Currency": "ene_2", "Amount": 1}], "step": 6, "bundleId": "chain_skip_6", "price": 0, "skin": "2"}, {"payID": "chaingift_299", "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "energy", "Amount": 200}], "step": 7, "bundleId": "chain_skip_7", "price": 2.99, "skin": "3"}, {"content": [{"Currency": "ene_1", "Amount": 1}], "step": 8, "bundleId": "chain_skip_8", "price": 0, "skin": "1"}, {"content": [{"Currency": "skipprop", "Amount": 30}], "step": 9, "bundleId": "chain_skip_9", "price": 0, "skin": "2"}, {"content": [{"Currency": "gem", "Amount": 10}], "step": 10, "bundleId": "chain_skip_10", "price": 0, "skin": "1"}, {"content": [{"Currency": "ene_3", "Amount": 1}], "step": 11, "bundleId": "chain_skip_11", "price": 0, "skin": "2"}, {"payID": "chaingift_399", "content": [{"Currency": "gem", "Amount": 60}, {"Currency": "energy", "Amount": 250}], "step": 12, "bundleId": "chain_skip_12", "price": 3.99, "skin": "3"}, {"content": [{"Currency": "skipprop", "Amount": 40}], "step": 13, "bundleId": "chain_skip_13", "price": 0, "skin": "1"}, {"content": [{"Currency": "greenbox_1", "Amount": 1}], "step": 14, "bundleId": "chain_skip_14", "price": 0, "skin": "2"}, {"content": [{"Currency": "skipprop", "Amount": 50}], "step": 15, "bundleId": "chain_skip_15", "price": 0, "skin": "1"}, {"content": [{"Currency": "ene_4", "Amount": 1}], "step": 16, "bundleId": "chain_skip_16", "price": 0, "skin": "2"}, {"payID": "chaingift_499", "content": [{"Currency": "gem", "Amount": 80}, {"Currency": "energy", "Amount": 300}], "step": 17, "bundleId": "chain_skip_17", "price": 4.99, "skin": "3"}, {"content": [{"Currency": "skipprop", "Amount": 60}], "step": 18, "bundleId": "chain_skip_18", "price": 0, "skin": "1"}, {"content": [{"Currency": "gem", "Amount": 15}], "step": 19, "bundleId": "chain_skip_19", "price": 0, "skin": "2"}, {"content": [{"Currency": "skipprop", "Amount": 70}], "step": 20, "bundleId": "chain_skip_20", "price": 0, "skin": "1"}, {"content": [{"Currency": "greenbox2_1", "Amount": 1}], "step": 21, "bundleId": "chain_skip_21", "price": 0, "skin": "2"}, {"payID": "chaingift_699", "content": [{"Currency": "gem", "Amount": 100}, {"Currency": "energy", "Amount": 450}], "step": 22, "bundleId": "chain_skip_22", "price": 6.99, "skin": "3"}, {"content": [{"Currency": "skipprop", "Amount": 100}], "step": 23, "bundleId": "chain_skip_23", "price": 0, "skin": "1"}, {"content": [{"Currency": "greenbox2_1", "Amount": 1}], "step": 24, "bundleId": "chain_skip_24", "price": 0, "skin": "2"}, {"content": [{"Currency": "skipprop", "Amount": 150}], "step": 25, "bundleId": "chain_skip_25", "price": 0, "skin": "1"}, {"content": [{"Currency": "gem", "Amount": 20}], "step": 26, "bundleId": "chain_skip_26", "price": 0, "skin": "2"}, {"payID": "chaingift_1599", "content": [{"Currency": "gem", "Amount": 240}, {"Currency": "energy", "Amount": 1000}], "step": 27, "bundleId": "chain_skip_27", "price": 15.99, "skin": "3"}, {"content": [{"Currency": "greenbox2_1", "Amount": 1}], "step": 28, "bundleId": "chain_skip_28", "price": 0, "skin": "2"}, {"content": [{"Currency": "skipprop", "Amount": 200}], "step": 29, "bundleId": "chain_skip_29", "price": 0, "skin": "1"}, {"content": [{"Currency": "additem_3", "Amount": 1}], "step": 30, "bundleId": "chain_skip_30", "price": 0, "skin": "2"}, {"content": [{"Currency": "skipprop", "Amount": 400}], "step": 31, "bundleId": "chain_skip_31", "price": 0, "skin": "1"}, {"content": [{"Currency": "gem", "Amount": 50}], "step": 32, "bundleId": "chain_skip_32", "price": 0, "skin": "2"}, {"payID": "chaingift_1999", "content": [{"Currency": "gem", "Amount": 320}, {"Currency": "energy", "Amount": 1200}], "step": 33, "bundleId": "chain_skip_33", "price": 19.99, "skin": "3"}, {"content": [{"Currency": "skipprop", "Amount": 200}], "step": 34, "bundleId": "chain_skip_34", "price": 0, "skin": "2"}, {"content": [{"Currency": "greenbox2_1", "Amount": 2}], "step": 35, "bundleId": "chain_skip_35", "price": 0, "skin": "1"}, {"content": [{"Currency": "skipprop", "Amount": 400}], "step": 36, "bundleId": "chain_skip_36", "price": 0, "skin": "2"}, {"content": [{"Currency": "additem_3", "Amount": 2}], "step": 37, "bundleId": "chain_skip_37", "price": 0, "skin": "1"}, {"content": [{"Currency": "gem", "Amount": 100}], "step": 38, "bundleId": "chain_skip_38", "price": 0, "skin": "2"}], "id": 75, "include": ["bundleContentChain#chain_skip", "bundleTrigger#login"], "is_open": 1, "eTime": 1749434400, "groupId": "chain5", "sTime": 1749175200, "dailyShowNum": 2, "sLv": 8, "rTime": 1749520800, "bundleTrigger": [{"trigger": "login"}], "specialType": "chain", "duration": 4320, "uiCode": "chain"}]}, "rateUs": {"md5": "cc9b438acb2247f9676ad310935caae2", "config": [{"sLv": 5, "id": 2, "link": "market://details?id=com.cola.game", "contact": 0, "task": [{"ChapterId": 2, "TaskCount": 30}, {"ChapterId": 3, "TaskCount": 29}]}]}, "general_conf": {"config": [{"param_int": 1, "confType": "balance_gold", "id": 1}, {"param_int": 1, "confType": "hint_inner_dish", "id": 3}, {"param_int": 1, "confType": "level_trans_day", "id": 4}, {"param_int": 1, "confType": "social_bind", "id": 5}, {"param_int": 1, "confType": "skipprop_not_enough", "id": 6}, {"param_int": 1, "confType": "ChangeInventory", "id": 7}, {"param_int": 1, "confType": "ingredient_recipe_hint", "id": 8}, {"confType": "doubleEnergy_trigger", "param_int": 200, "sLv": 16, "id": 9}, {"param_int": 1, "confType": "cache", "id": 10}, {"param_int": 1, "confType": "new_order_reward_anim", "id": 11}, {"param_int": 1, "confType": "new_order_reward_anim_skip", "id": 12}, {"confType": "item_auto_recycle", "id": 14, "param_int_array": [3, 10]}, {"param_int": 1, "confType": "order_seq_energy_diff", "id": 15}, {"param_int": 1, "confType": "board_cook_bubble", "id": 16}, {"param_int": 1, "confType": "item_delete_corn_husk", "id": 17}, {"param_int": 1, "confType": "order_new_tag", "id": 18}], "md5": "0c452676dbe6fba5117a6814cbfdfbd7"}, "notify": {"md5": "47c1751e7551193850791d81b02617b6", "config": [{"pushtimetype": 0, "id": 1, "scene": ["EnergyRefill", "ComeBack_24", "ComeBack_48", "ItemCooldown"], "maxNum": 2, "interval": 86400}]}, "fileReplace": {"config": [{"file": "ExtraBoardItemModelConfig", "id": 35, "suffix": "_energy"}], "md5": "2b8d9fe1c1b9776da07120db730897c2"}, "coinRace": {"config": {"sTime": 1749434400, "generalActivityConf": [{"param_int": 22, "confType": "divided"}], "sLv": 7, "id": 96, "include": ["coin_race#round10", "generalActivityConf#round10"], "rTime": 1749952800, "eTime": 1749780000, "coin_race": [{"rankReward3": [{"Currency": "skipprop", "Amount": 10}, {"Currency": "energy", "Amount": 10}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "skipprop", "Amount": 15}, {"Currency": "energy", "Amount": 15}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 20}], "round": 1, "target": 20}, {"rankReward3": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 10}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 20}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 25}, {"Currency": "energy", "Amount": 30}], "round": 2, "target": 25}, {"rankReward3": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 15}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "skipprop", "Amount": 25}, {"Currency": "energy", "Amount": 25}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 30}, {"Currency": "energy", "Amount": 35}], "round": 3, "target": 30}, {"rankReward3": [{"Currency": "skipprop", "Amount": 20}, {"Currency": "energy", "Amount": 20}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "additem_1", "Amount": 1}, {"Currency": "energy", "Amount": 25}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "additem_1", "Amount": 1}, {"Currency": "energy", "Amount": 45}], "round": 4, "target": 35}, {"rankReward3": [{"Currency": "skipprop", "Amount": 25}, {"Currency": "energy", "Amount": 20}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "gem", "Amount": 5}, {"Currency": "greenbox_1", "Amount": 1}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "gem", "Amount": 5}, {"Currency": "skipprop", "Amount": 40}, {"Currency": "greenbox_1", "Amount": 1}], "round": 5, "target": 40}, {"rankReward3": [{"Currency": "skipprop", "Amount": 30}, {"Currency": "energy", "Amount": 20}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "skipprop", "Amount": 30}, {"Currency": "energy", "Amount": 30}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 40}, {"Currency": "energy", "Amount": 45}], "round": 6, "target": 40}, {"rankReward3": [{"Currency": "skipprop", "Amount": 30}, {"Currency": "energy", "Amount": 25}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "additem_1", "Amount": 1}, {"Currency": "energy", "Amount": 35}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 50}], "round": 7, "target": 50}, {"rankReward3": [{"Currency": "skipprop", "Amount": 40}, {"Currency": "energy", "Amount": 25}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "skipprop", "Amount": 50}, {"Currency": "energy", "Amount": 40}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "gem", "Amount": 5}, {"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 50}], "round": 8, "target": 55}, {"rankReward3": [{"Currency": "skipprop", "Amount": 40}, {"Currency": "energy", "Amount": 30}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 45}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "greenbox_1", "Amount": 1}, {"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 50}], "round": 9, "target": 65}, {"rankReward3": [{"Currency": "skipprop", "Amount": 60}, {"Currency": "energy", "Amount": 30}], "playerNumWeight": [{"weight": 20, "group_type": "new_suc", "child_type": "easy"}, {"weight": 30, "group_type": "new_suc", "child_type": "normal"}, {"weight": 40, "group_type": "new_suc", "child_type": "hard"}], "rankReward2": [{"Currency": "skipprop", "Amount": 70}, {"Currency": "energy", "Amount": 50}], "playerNum": [{"group_num": 2, "group_type": "new_suc"}, {"group_num": 2, "group_type": "new_fail"}], "rankReward1": [{"Currency": "gem", "Amount": 10}, {"Currency": "skipprop", "Amount": 180}, {"Currency": "energy", "Amount": 50}], "round": 10, "target": 75}]}, "md5": "de0c8bc13dd9251647b06eff6ca734eb"}}, "key": "key"}}, "shop": {"energyRefreshTime": {"value": 1749434608, "key": "energyRefreshTime"}, "eventEnergyBuyCost": {"value": 10, "key": "eventEnergyBuyCost"}, "energyBuyCost": {"value": 10, "key": "energyBuyCost"}, "refreshCost": {"value": 10, "key": "refreshCost"}, "refreshCostResetTime": {"value": 1749434608, "key": "refreshCostResetTime"}, "flashRefreshTime": {"value": 1749437134, "key": "flashRefreshTime"}, "dailyRefreshTime": {"value": 1749434608, "key": "dailyRefreshTime"}}, "newSkin": {}, "weddingDay": {}, "taskMeta": {"OngoingChapterId": {"value": 5, "key": "OngoingChapterId"}, "OngoingTaskIds": {"value": "18", "key": "OngoingTaskIds"}, "ProgressId": {"value": "8", "key": "ProgressId"}, "OngoingChapterName": {"value": "Wine", "key": "OngoingChapterName"}, "TaskProgress": {"value": 5017, "key": "TaskProgress"}, "ProgressTaskFinishCount": {"value": "3", "key": "ProgressTaskFinishCount"}, "CleanGoldCost": {"value": "1#19;2#17;3#15;4#15;5#17;6#18;7#19;8#17;9#20;10#16;11#16;12#19;13#19;14#19;15#18;16#20;17#18;18#19;19#18;20#22;21#19;22#21;23#18;24#21;25#20;26#24;27#19;28#20;29#16;30#22;31#22;32#23;33#22;34#22;35#22;36#25;37#23;38#21;39#21;40#19;41#26;42#24;43#22;44#23;45#19;46#26;47#21", "key": "CleanGoldCost"}, "TaskCleanGold": {"value": 759, "key": "TaskCleanGold"}, "ChapterFinishWin": {"value": 4, "key": "ChapterFinishWin"}}, "activity": {"extraBoard2Item": {"data": "{\"1749436688002\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749450810001\":{\"spreadStartTimer\":-1,\"cookSkipPropCost\":0,\"spreadEnergyFree\":0,\"spreadTierUpLevel\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"spreadCount\":1,\"codeStr\":\"eb2_1_6\",\"cookGemCost\":0,\"spreadWeightType\":2,\"spreadState\":5,\"spreadItemBoxChain\":\"\",\"spreadTierUpCount\":0,\"spreadAddItem\":0,\"shopGemCost\":0,\"costEnergyCurDay\":0,\"spreadCodeWeightPairs\":\"skipprop_2-1\",\"spreadInherit\":0,\"spreadStorageRestNumber\":0},\"1749450801001\":{\"spreadStartTimer\":-1,\"cookSkipPropCost\":0,\"spreadEnergyFree\":0,\"spreadTierUpLevel\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"spreadCount\":3,\"codeStr\":\"eb2_1_8\",\"cookGemCost\":0,\"spreadWeightType\":2,\"spreadState\":5,\"spreadItemBoxChain\":\"\",\"spreadTierUpCount\":0,\"spreadAddItem\":0,\"shopGemCost\":0,\"costEnergyCurDay\":0,\"spreadCodeWeightPairs\":\"skipprop_3-1;additem_1_1-1;eb2_2_1-1\",\"spreadInherit\":0,\"spreadStorageRestNumber\":0},\"1749434608018\":{\"codeStr\":\"pb#c#eb2_1_6\",\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"shopGemCost\":0,\"bubbleGemCost\":0},\"1749436733003\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749436733004\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"additem_1_1\"},\"1749436730003\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749450817002\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749436730002\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749434608006\":{\"codeStr\":\"pb#c#eb2_2_4\",\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"shopGemCost\":0,\"bubbleGemCost\":0},\"1749450833001\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"skipprop_4\"},\"1749436733001\":{\"spreadStartTimer\":-1,\"cookSkipPropCost\":0,\"spreadEnergyFree\":0,\"spreadTierUpLevel\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"spreadCount\":3,\"codeStr\":\"eb2_1_8\",\"cookGemCost\":0,\"spreadWeightType\":2,\"spreadState\":5,\"spreadItemBoxChain\":\"\",\"spreadTierUpCount\":0,\"spreadCodeWeightPairs\":\"skipprop_3-1;additem_1_1-1;eb2_2_1-1\",\"spreadAddItem\":0,\"costEnergyCurDay\":0,\"shopGemCost\":0,\"spreadInherit\":0,\"spreadStorageRestNumber\":0},\"1749434608012\":{\"codeStr\":\"pb#c#eb2_1_7\",\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"shopGemCost\":0,\"bubbleGemCost\":0},\"1749450799002\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749450851001\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"ene_3\"},\"1749436659001\":{\"spreadStartTimer\":-1,\"cookSkipPropCost\":0,\"spreadEnergyFree\":0,\"spreadTierUpLevel\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"spreadCount\":3,\"codeStr\":\"eb2_1_8\",\"cookGemCost\":0,\"spreadWeightType\":2,\"spreadState\":5,\"spreadItemBoxChain\":\"\",\"spreadTierUpCount\":0,\"spreadCodeWeightPairs\":\"skipprop_3-1;additem_1_1-1;eb2_2_1-1\",\"spreadAddItem\":0,\"costEnergyCurDay\":0,\"shopGemCost\":0,\"spreadInherit\":0,\"spreadStorageRestNumber\":0},\"1749450820002\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749450784007\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"eb2_1_2\"},\"1749450820001\":{\"spreadStartTimer\":-1,\"cookSkipPropCost\":0,\"spreadEnergyFree\":0,\"spreadTierUpLevel\":0,\"costEnergy\":0,\"bubbleGemCost\":0,\"spreadCount\":0,\"codeStr\":\"eb2_2_5\",\"cookGemCost\":0,\"spreadWeightType\":2,\"spreadState\":3,\"spreadItemBoxChain\":\"\",\"spreadTierUpCount\":0,\"spreadAddItem\":0,\"shopGemCost\":0,\"costEnergyCurDay\":0,\"spreadCodeWeightPairs\":\"ene_1-2;ene_2-6;ene_3-1\",\"spreadInherit\":0,\"spreadStorageRestNumber\":9},\"1749434608001\":{\"codeStr\":\"pb#c#eb2_2_4\",\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"shopGemCost\":0,\"bubbleGemCost\":0},\"1749436645003\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749450819004\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"ene_2\"},\"1749450852001\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"additem_1_2\"},\"1749436650002\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749436665004\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"skipprop_2\"},\"1749450810002\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749436651002\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"},\"1749436666003\":{\"shopGemCost\":0,\"costEnergy\":0,\"cookGemCost\":0,\"costEnergyCurDay\":0,\"cookSkipPropCost\":0,\"bubbleGemCost\":0,\"codeStr\":\"c#eb2_1_7\"}}", "name": "extraBoard2Item"}, "extraBoard2": {"data": "{\"ath_o_50550\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Amount\\\":3}\"},\"extraBoardItemGived\":{\"value\":1},\"ath_ce\":{\"value\":14},\"windowOpened2\":{\"value\":1},\"ath_o_50510\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Amount\\\":1}\"},\"id\":{\"value\":98},\"ath_o_50560\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"eb_1_3\\\"@\\\"Amount\\\":2}\"},\"ath_o_50530\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Amount\\\":3}\"},\"currentMergeLevel\":{\"value\":8},\"ath_o_50520\":{\"value\":\"{\\\"Weight\\\":100@\\\"Currency\\\":\\\"eb_1_2\\\"@\\\"Amount\\\":2}\"}}", "name": "extraBoard2"}, "coinRace": {"data": "{\"round\":{\"value\":3},\"entryTime\":{\"value\":1749437126},\"lastScore\":{\"value\":\"{\\\"round\\\":3,\\\"1\\\":0,\\\"10000025575\\\":0,\\\"10000025607\\\":0,\\\"10000025599\\\":0,\\\"10000025583\\\":0}\"},\"windowOpened2\":{\"value\":1},\"roundRanks\":{\"value\":\"{\\\"2\\\":2,\\\"1\\\":1}\"},\"myScore\":{\"time\":13596,\"score\":20},\"id\":{\"value\":96},\"playerDatas\":{\"value\":\"[{\\\"icon\\\":\\\"head8\\\",\\\"id\\\":10000025583,\\\"track\\\":1,\\\"name\\\":\\\"KK3VZAH\\\",\\\"group_type\\\":\\\"new_fail\\\",\\\"score\\\":\\\"[{\\\\\\\"seconds\\\\\\\":168256,\\\\\\\"score\\\\\\\":173},{\\\\\\\"seconds\\\\\\\":168266,\\\\\\\"score\\\\\\\":346},{\\\\\\\"seconds\\\\\\\":168996,\\\\\\\"score\\\\\\\":495}]\\\"},{\\\"icon\\\":\\\"head4\\\",\\\"id\\\":10000025599,\\\"track\\\":2,\\\"name\\\":\\\"8JSVZAH\\\",\\\"group_type\\\":\\\"new_fail\\\",\\\"score\\\":\\\"[{\\\\\\\"seconds\\\\\\\":164,\\\\\\\"score\\\\\\\":114},{\\\\\\\"seconds\\\\\\\":83721,\\\\\\\"score\\\\\\\":241},{\\\\\\\"seconds\\\\\\\":83951,\\\\\\\"score\\\\\\\":336},{\\\\\\\"seconds\\\\\\\":174056,\\\\\\\"score\\\\\\\":509}]\\\"},{\\\"icon\\\":\\\"head5\\\",\\\"id\\\":10000025607,\\\"track\\\":4,\\\"name\\\":\\\"72E2ZAH\\\",\\\"group_type\\\":\\\"new_suc_hard\\\",\\\"score\\\":\\\"[{\\\\\\\"seconds\\\\\\\":538,\\\\\\\"score\\\\\\\":391},{\\\\\\\"seconds\\\\\\\":20257,\\\\\\\"score\\\\\\\":586},{\\\\\\\"seconds\\\\\\\":20397,\\\\\\\"score\\\\\\\":856}]\\\"},{\\\"icon\\\":\\\"head6\\\",\\\"id\\\":10000025575,\\\"track\\\":5,\\\"name\\\":\\\"P2PVZAH\\\",\\\"group_type\\\":\\\"new_suc_normal\\\",\\\"score\\\":\\\"[{\\\\\\\"seconds\\\\\\\":19730,\\\\\\\"score\\\\\\\":73},{\\\\\\\"seconds\\\\\\\":20067,\\\\\\\"score\\\\\\\":176},{\\\\\\\"seconds\\\\\\\":20208,\\\\\\\"score\\\\\\\":295},{\\\\\\\"seconds\\\\\\\":37329,\\\\\\\"score\\\\\\\":627},{\\\\\\\"seconds\\\\\\\":54895,\\\\\\\"score\\\\\\\":889}]\\\"}]\"}}", "name": "coinRace"}, "extraBoard2ItemLayer": {"data": "{\"3_4\":{\"itemId\":\"1749450784007\"},\"6_4\":{\"itemId\":\"1749436730002\"},\"1_1\":{\"itemId\":\"1749434608001\"},\"2_8\":{\"itemId\":\"1749450819004\"},\"4_1\":{\"itemId\":\"1749450820001\"},\"6_1\":{\"itemId\":\"1749434608006\"},\"6_6\":{\"itemId\":\"1749436665004\"},\"5_6\":{\"itemId\":\"1749450833001\"},\"2_1\":{\"itemId\":\"1749450817002\"},\"1_5\":{\"itemId\":\"1749436650002\"},\"2_5\":{\"itemId\":\"1749436659001\"},\"3_5\":{\"itemId\":\"1749450851001\"},\"5_5\":{\"itemId\":\"1749436645003\"},\"5_2\":{\"itemId\":\"1749450810002\"},\"6_2\":{\"itemId\":\"1749434608012\"},\"1_4\":{\"itemId\":\"1749436688002\"},\"4_2\":{\"itemId\":\"1749450810001\"},\"1_2\":{\"itemId\":\"1749450799002\"},\"2_4\":{\"itemId\":\"1749436733004\"},\"5_1\":{\"itemId\":\"1749450820002\"},\"2_2\":{\"itemId\":\"1749436733003\"},\"6_3\":{\"itemId\":\"1749434608018\"},\"5_3\":{\"itemId\":\"1749436730003\"},\"4_3\":{\"itemId\":\"1749436666003\"},\"2_3\":{\"itemId\":\"1749436733001\"},\"3_2\":{\"itemId\":\"1749450801001\"},\"3_6\":{\"itemId\":\"1749450852001\"},\"6_5\":{\"itemId\":\"1749436651002\"}}", "name": "extraBoard2ItemLayer"}, "extraBoard2ItemCache": {"data": "{}", "name": "extraBoard2ItemCache"}, "BP5": {"data": "{\"TimelimitTaskFinishedCount5\":{\"value\":7},\"RewardTaken15_0\":{\"value\":1},\"ProgressEffect_7\":{\"value\":1},\"ProgressEffect_12\":{\"value\":1},\"RewardEffect15_0\":{\"value\":1},\"id\":{\"value\":79},\"RewardEffect11_0\":{\"value\":1},\"ProgressEffect_15\":{\"value\":1},\"TimelimitTaskFinishedCount9\":{\"value\":1000},\"TimelimitTaskFinished4\":{\"value\":0},\"ProgressEffect_14\":{\"value\":1},\"ProgressEffect_10\":{\"value\":1},\"TimelimitTaskFinished1\":{\"value\":1},\"ProgressEffect_9\":{\"value\":1},\"TimelimitTaskFinishedCount1\":{\"value\":3},\"RewardTaken10_0\":{\"value\":1},\"RewardEffect2_0\":{\"value\":1},\"RewardEffect7_0\":{\"value\":1},\"windowOpened2\":{\"value\":1},\"ProgressEffect_8\":{\"value\":1},\"windowOpenedBP5MainWindow\":{\"value\":1},\"RewardEffect1_0\":{\"value\":1},\"CycleTaskIndex\":{\"value\":1},\"RewardEffect13_0\":{\"value\":1},\"TimelimitTaskFinishedCount2\":{\"value\":5},\"ProgressEffect_13\":{\"value\":1},\"RewardTaken8_0\":{\"value\":1},\"ProgressEffect_4\":{\"value\":1},\"ProgressEffect_3\":{\"value\":1},\"RewardEffect6_0\":{\"value\":1},\"TimelimitTaskFinishedCount3\":{\"value\":6},\"TimelimitTaskFinishedCount7\":{\"value\":800},\"TimelimitTaskOrder\":{\"value\":27},\"RewardTaken1_0\":{\"value\":1},\"TimelimitTaskFinished5\":{\"value\":0},\"ProgressEffect_2\":{\"value\":1},\"TimelimitTaskFinished3\":{\"value\":1},\"TimelimitTaskFinishedCount8\":{\"value\":900},\"TimelimitTaskFinished2\":{\"value\":1},\"TimelimitTaskFinished7\":{\"value\":1},\"RewardEffect3_0\":{\"value\":1},\"CycleTaskFinishedCount\":{\"value\":15},\"TimelimitTaskFinished9\":{\"value\":1},\"RewardEffect4_0\":{\"value\":1},\"TimelimitTaskFinishedCount4\":{\"value\":7},\"RewardEffect10_0\":{\"value\":1},\"RewardTaken7_0\":{\"value\":1},\"ProgressEffect_5\":{\"value\":1},\"ProgressEffect_6\":{\"value\":1},\"RewardTaken2_0\":{\"value\":1},\"TimelimitTaskFinishedCount6\":{\"value\":7},\"TimelimitTaskFinished6\":{\"value\":0},\"RewardEffect5_0\":{\"value\":1},\"ProgressEffect_1\":{\"value\":1},\"FinishedLevel\":{\"value\":15},\"TimelimitTaskFinished8\":{\"value\":1},\"RewardEffect12_0\":{\"value\":1},\"FinTLtask\":{\"value\":19},\"ProgressEffect_11\":{\"value\":1},\"TokenNumber\":{\"value\":4510},\"RewardEffect8_0\":{\"value\":1},\"RewardEffect14_0\":{\"value\":1},\"RewardEffect9_0\":{\"value\":1}}", "name": "BP5"}, "pkRace": {"data": "{\"windowOpened3\":{\"value\":1},\"windowOpened2\":{\"value\":1},\"entryTime\":{\"value\":1749175847},\"myScore\":{\"time\":17579,\"score\":24},\"round\":{\"value\":1},\"id\":{\"value\":91},\"lastScore\":{\"value\":\"{\\\"1\\\":0,\\\"round\\\":1,\\\"10000009881\\\":0}\"},\"settled\":{\"value\":true},\"playerDatas\":{\"value\":\"[{\\\"name\\\":\\\"Danielle Salkil\\\",\\\"icon\\\":\\\"head4\\\",\\\"id\\\":10000009881,\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":53.33,\\\\\\\"seconds\\\\\\\":366}]\\\",\\\"group_type\\\":\\\"pk_fail\\\"}]\"}}", "name": "pkRace"}, "bakeOut": {"data": "{\"cachedData\":{\"value\":\"{\\\"sTime\\\":1749089400,\\\"bakeout_rank_reward\\\":[{\\\"end_rank\\\":1,\\\"rewards\\\":[\\\"gem-25\\\",\\\"energy-100\\\",\\\"skiptime_1-1\\\"],\\\"start_rank\\\":1},{\\\"end_rank\\\":2,\\\"rewards\\\":[\\\"gem-20\\\",\\\"energy-80\\\",\\\"additem_1-1\\\"],\\\"start_rank\\\":2},{\\\"end_rank\\\":3,\\\"rewards\\\":[\\\"gem-15\\\",\\\"energy-50\\\"],\\\"start_rank\\\":3},{\\\"end_rank\\\":6,\\\"rewards\\\":[\\\"gem-10\\\"],\\\"start_rank\\\":4},{\\\"end_rank\\\":10,\\\"rewards\\\":[\\\"energy-50\\\"],\\\"start_rank\\\":7},{\\\"end_rank\\\":15,\\\"rewards\\\":[\\\"skipprop-50\\\"],\\\"start_rank\\\":11},{\\\"end_rank\\\":20,\\\"rewards\\\":[\\\"skipprop-25\\\"],\\\"start_rank\\\":16}],\\\"bakeout_rank_parameter\\\":[{\\\"retainTime\\\":601200,\\\"settlementTime\\\":300,\\\"delayTime\\\":90,\\\"noRegisterTime\\\":300,\\\"maxNum\\\":20,\\\"uploadTime\\\":60}],\\\"rTime\\\":1749694200,\\\"staticInclude\\\":[\\\"bakeout_rank_reward#1st\\\",\\\"bakeout_rank_exchange#default\\\",\\\"bakeout_rank_parameter#1st\\\"],\\\"bakeout_rank_exchange\\\":[{\\\"cost\\\":10,\\\"time\\\":1},{\\\"cost\\\":11,\\\"time\\\":2},{\\\"cost\\\":14,\\\"time\\\":3},{\\\"cost\\\":17,\\\"time\\\":4},{\\\"cost\\\":21,\\\"time\\\":5},{\\\"cost\\\":28,\\\"time\\\":6},{\\\"cost\\\":37,\\\"time\\\":7},{\\\"cost\\\":51,\\\"time\\\":8},{\\\"cost\\\":72,\\\"time\\\":9},{\\\"cost\\\":100,\\\"time\\\":10}],\\\"eTime\\\":**********,\\\"id\\\":100020}\"},\"cachedDataMd5\":{\"value\":\"cb9512fa52a7f36e4aec16c3ec1147f4\"},\"id\":{\"value\":100020},\"delayedGetRewardTime\":{\"value\":38}}", "name": "bakeOut"}}, "userProfile": {}, "localRewards": {}, "account": {"SocialPictureUrl": {"value": "https://graph.facebook.com/***************/picture?type=large&access_token=EAAcKmT4gWFsBO3cf0kjmMLCJf71NZCAFzQOHZCETlhBxU2UN6yrYFBYbZAofmtvmkoQ8Alb97XI7S6nbG3fz68ZCTsKdbpklKczMFuCp9wkg3j64yal7lhQ2zsR7vfGdZCUx1jT6qRjQ9WtwA3WY61Pw1tTkb4OoKuZCDhqdaPl1Ku1GrGOrRkkdbZBDO9qlyKmWLvBczefZCZBw6MjZAQ0YeWlPbT5m1ADMcA5z844YFAlGhMbv40PAZDZD", "key": "SocialPictureUrl"}, "SocialType": {"value": "1", "key": "SocialType"}, "SocialName": {"value": "<PERSON>", "key": "SocialName"}, "SocialId": {"value": "***************", "key": "SocialId"}}, "tutorial": {"cook1": {"state": 2, "id": "cook1", "ongoingDatas": ""}, "tutorial_extraboard_start": {"state": 2, "id": "tutorial_extraboard_start", "ongoingDatas": ""}, "order10070": {"state": 2, "id": "order10070", "ongoingDatas": ""}, "order10080": {"state": 2, "id": "order10080", "ongoingDatas": ""}, "timeline": {"state": 2, "id": "timeline", "ongoingDatas": ""}, "tutorial_digactivity_seconddig": {"state": 2, "id": "tutorial_digactivity_seconddig", "ongoingDatas": ""}, "tutorial_progress": {"state": 2, "id": "tutorial_progress", "ongoingDatas": ""}, "CD_pd_1_7": {"state": 2, "id": "CD_pd_1_7", "ongoingDatas": ""}, "cook2": {"state": 2, "id": "cook2", "ongoingDatas": ""}, "tutorial_coin_race_entry": {"state": 2, "id": "tutorial_coin_race_entry", "ongoingDatas": ""}, "order_group": {"state": 2, "id": "order_group", "ongoingDatas": ""}, "tutorial_coin_race_second_main_window": {"state": 2, "id": "tutorial_coin_race_second_main_window", "ongoingDatas": ""}, "additem_+8": {"state": 2, "id": "additem_+8", "ongoingDatas": ""}, "energy": {"state": 2, "id": "energy", "ongoingDatas": ""}, "tutorial_coin_race_first_main_window": {"state": 2, "id": "tutorial_coin_race_first_main_window", "ongoingDatas": ""}, "tutorial_cg": {"state": 2, "id": "tutorial_cg", "ongoingDatas": ""}, "tutorial_energy_boost": {"state": 2, "id": "tutorial_energy_boost", "ongoingDatas": ""}, "merge2": {"state": 2, "id": "merge2", "ongoingDatas": ""}, "tutorial_producer_inventory": {"state": 1, "id": "tutorial_producer_inventory", "ongoingDatas": ""}, "task1_2": {"state": 2, "id": "task1_2", "ongoingDatas": ""}, "cd_speed": {"state": 2, "id": "cd_speed", "ongoingDatas": ""}, "task1_1": {"state": 2, "id": "task1_1", "ongoingDatas": ""}, "tutorial_battlepass_loop": {"state": 1, "id": "tutorial_battlepass_loop", "ongoingDatas": ""}, "order10020": {"state": 2, "id": "order10020", "ongoingDatas": ""}, "weakGesture": {"state": 1, "id": "weakGesture", "ongoingDatas": ""}, "task1_3": {"state": 2, "id": "task1_3", "ongoingDatas": ""}, "order_item_info": {"state": 2, "id": "order_item_info", "ongoingDatas": ""}, "additem_old_user": {"state": 2, "id": "additem_old_user", "ongoingDatas": ""}, "cook3": {"state": 2, "id": "cook3", "ongoingDatas": ""}, "bubble": {"state": 2, "id": "bubble", "ongoingDatas": ""}, "order10040": {"state": 2, "id": "order10040", "ongoingDatas": ""}, "CD_pd_2_6": {"state": 2, "id": "CD_pd_2_6", "ongoingDatas": ""}, "tutorial_coin_race_order": {"state": 2, "id": "tutorial_coin_race_order", "ongoingDatas": ""}, "order10010": {"state": 2, "id": "order10010", "ongoingDatas": ""}, "merge1": {"state": 2, "id": "merge1", "ongoingDatas": ""}, "tutorial_blind_chest": {"state": 1, "id": "tutorial_blind_chest", "ongoingDatas": ""}, "order10140": {"state": 2, "id": "order10140", "ongoingDatas": ""}, "tutorial_extraboard_cobweb_unlock": {"state": 1, "id": "tutorial_extraboard_cobweb_unlock", "ongoingDatas": ""}, "tutorial_pk_race_start": {"state": 2, "id": "tutorial_pk_race_start", "ongoingDatas": ""}, "shop": {"state": 2, "id": "shop", "ongoingDatas": ""}, "order10030": {"state": 2, "id": "order10030", "ongoingDatas": ""}, "tutorial_digactivity_firstdig": {"state": 2, "id": "tutorial_digactivity_firstdig", "ongoingDatas": ""}, "tutorial_battlepass": {"state": 2, "id": "tutorial_battlepass", "ongoingDatas": ""}, "toboard": {"state": 2, "id": "toboard", "ongoingDatas": ""}, "order10050": {"state": 2, "id": "order10050", "ongoingDatas": ""}, "cache": {"state": 2, "id": "cache", "ongoingDatas": ""}, "tutorial_extraboard_cobweb_merge": {"state": 1, "id": "tutorial_extraboard_cobweb_merge", "ongoingDatas": ""}, "clickPD": {"state": 2, "id": "clickPD", "ongoingDatas": ""}, "task1_4": {"state": 2, "id": "task1_4", "ongoingDatas": ""}, "tutorial_extraboard_item_delete": {"state": 1, "id": "tutorial_extraboard_item_delete", "ongoingDatas": ""}}, "item": {"1742351309001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "codeStr": "eq_4_6", "shopGemCost": 0, "choices": "", "id": "1742351309001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 1, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1749434951003": {"spreadCodeWeightPairs": "", "costEnergy": 1, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_5_1_1", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749434951003", "cookGemCost": 0}, "1749450478001": {"spreadCodeWeightPairs": "", "costEnergy": 3, "materialInfo": "", "shopGemCost": 0, "codeStr": "ds_juice_1", "costEnergyCurDay": 1, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450478001", "cookGemCost": 0}, "1749450766001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_3_1_3", "costEnergyCurDay": 1, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450766001", "cookGemCost": 0}, "1741054354001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "pd_4_3", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1741054354001", "cookGemCost": 0}, "1740484341001": {"spreadCodeWeightPairs": "it_2_2_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_2_6", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 579, "choices": "", "spreadAddItem": 0, "id": "1740484341001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": 1749450627, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 19, "cookRecipe": ""}, "1749176303001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "it_2_1_6-1749450408001", "codeStr": "eq_1_4", "shopGemCost": 0, "choices": "", "id": "1749176303001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 2, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1749450629001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_2_2_2", "costEnergyCurDay": 2, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450629001", "cookGemCost": 0}, "1746585834001": {"spreadCodeWeightPairs": "it_7_2_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_7_5", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 439, "choices": "", "spreadAddItem": 0, "id": "1746585834001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": 1749446669, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 2, "spreadItemBoxChain": "", "spreadStorageRestNumber": 24, "cookRecipe": ""}, "1749450715001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_4_1_5", "costEnergyCurDay": 12, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450715001", "cookGemCost": 0}, "1749450537001": {"spreadCodeWeightPairs": "", "costEnergy": 3, "materialInfo": "", "shopGemCost": 0, "codeStr": "ds_friedmt_4", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450537001", "cookGemCost": 0}, "1738834040001": {"spreadCodeWeightPairs": "it_4_1_1-1;it_4_1_2-1;it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_2-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_4_5", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 546, "choices": "", "spreadAddItem": 0, "id": "1738834040001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 30, "cookRecipe": ""}, "1749450638001": {"spreadCodeWeightPairs": "it_2_3_1_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "it_2_3_2", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 0, "choices": "", "spreadAddItem": 0, "id": "1749450638001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "shopGemCost": 0, "spreadInherit": 0, "costEnergyCurDay": 2, "spreadWeightType": 2, "spreadItemBoxChain": "", "spreadStorageRestNumber": 1, "cookRecipe": ""}, "1740574630001": {"spreadCodeWeightPairs": "", "costEnergy": 21.5, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_1_1_1_2", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1740574630001", "cookGemCost": 0}, "1749435128002": {"spreadCodeWeightPairs": "", "costEnergy": 1, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_1_1_1", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749435128002", "cookGemCost": 0}, "1749003761001": {"spreadCodeWeightPairs": "", "costEnergy": 44, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_4_1_7", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749003761001", "cookGemCost": 0}, "1749437467001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "eq_3_3", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749437467001", "cookGemCost": 0}, "1749176305001": {"spreadCodeWeightPairs": "it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_1_4", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 0, "choices": "", "spreadAddItem": 0, "id": "1749176305001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 24, "cookRecipe": ""}, "1749450398001": {"spreadCodeWeightPairs": "", "costEnergy": 1, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_2_2_2", "costEnergyCurDay": 1, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450398001", "cookGemCost": 0}, "1745164158001": {"spreadCodeWeightPairs": "it_1_1_3-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_1_8", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 616, "choices": "", "spreadAddItem": 0, "id": "1745164158001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 48, "cookRecipe": ""}, "1749450878001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "ds_fd_5", "costEnergyCurDay": 17, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 23, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450878001", "cookGemCost": 0}, "1749434949001": {"spreadCodeWeightPairs": "", "costEnergy": 2, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_5_2_2", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749434949001", "cookGemCost": 0}, "1749176183001": {"spreadCodeWeightPairs": "", "costEnergy": 44, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_4_1_7", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749176183001", "cookGemCost": 0}, "1739932709001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "codeStr": "eq_3_6", "shopGemCost": 0, "choices": "", "id": "1739932709001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 1, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1743846671001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "codeStr": "eq_4_5", "shopGemCost": 0, "choices": "", "id": "1743846671001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 1, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1749436598001": {"spreadCodeWeightPairs": "", "costEnergy": 7, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_5_1_4", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749436598001", "cookGemCost": 0}, "1749437427001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_7_1_2", "costEnergyCurDay": 2, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749437427001", "cookGemCost": 0}, "1749437441002": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_7_2_3", "costEnergyCurDay": 4, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749437441002", "cookGemCost": 0}, "1734438341001": {"spreadCodeWeightPairs": "it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_4_6", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 2975, "choices": "", "spreadAddItem": 0, "id": "1734438341001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": 1749450451, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 10, "cookRecipe": ""}, "1749175987002": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "ene_1", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749175987002", "cookGemCost": 0}, "1749450408001": {"spreadCodeWeightPairs": "", "costEnergy": 13, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_2_1_6", "costEnergyCurDay": 3, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450408001", "cookGemCost": 0}, "1748059869001": {"spreadCodeWeightPairs": "", "costEnergy": 8, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_2_2_4", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1748059869001", "cookGemCost": 0}, "1749436606002": {"spreadCodeWeightPairs": "", "costEnergy": 6, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_1_1_4", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749436606002", "cookGemCost": 0}, "1742869211001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "codeStr": "eq_2_6", "shopGemCost": 0, "choices": "", "id": "1742869211001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 1, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1749450744001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_4_1_5", "costEnergyCurDay": 10, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450744001", "cookGemCost": 0}, "1749450654001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_2_3_1", "costEnergyCurDay": 1, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450654001", "cookGemCost": 0}, "1747715534001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "codeStr": "eq_5_5", "shopGemCost": 0, "choices": "", "id": "1747715534001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 1, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1749450651001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_2_2_1", "costEnergyCurDay": 1, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450651001", "cookGemCost": 0}, "1740969017001": {"spreadCodeWeightPairs": "it_5_1_1-8;it_5_1_2-2;it_5_1_3-1;it_5_2_1-9", "spreadState": 3, "materialInfo": "", "codeStr": "pd_5_6", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 1240, "choices": "", "spreadAddItem": 0, "id": "1740969017001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 2, "spreadItemBoxChain": "", "spreadStorageRestNumber": 30, "cookRecipe": ""}, "1741572578001": {"spreadCodeWeightPairs": "it_6_1_1-13", "spreadState": 3, "materialInfo": "", "codeStr": "pd_6_4", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 67, "choices": "", "spreadAddItem": 0, "id": "1741572578001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 2, "spreadItemBoxChain": "", "spreadStorageRestNumber": 24, "cookRecipe": ""}, "1749437455002": {"spreadCodeWeightPairs": "", "costEnergy": 1, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_4_1_4", "costEnergyCurDay": 6, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749437455002", "cookGemCost": 0}, "1749437468001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "pd_2_3", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749437468001", "cookGemCost": 0}, "1749434900001": {"spreadCodeWeightPairs": "", "costEnergy": 8, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_2_2_4", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749434900001", "cookGemCost": 0}, "1748056448001": {"spreadCodeWeightPairs": "", "costEnergy": 44, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_4_1_7", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1748056448001", "cookGemCost": 0}, "1749193701001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "it_1_1_2-1749436609001;it_1_1_1-1749435128002;ds_friedmt_4-1749450537001;ds_fd_5-1749450878001", "codeStr": "eq_1_6", "shopGemCost": 0, "choices": "", "id": "1749193701001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 1749450884, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": 1749450881, "cookState": 4, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": "ds_fd_8"}, "1749450872001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "additem_2", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450872001", "cookGemCost": 0}, "1740969176001": {"spreadCodeWeightPairs": "it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_4_4", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 153, "choices": "", "spreadAddItem": 0, "id": "1740969176001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 24, "cookRecipe": ""}, "1749437427002": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_7_1_1", "costEnergyCurDay": 1, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749437427002", "cookGemCost": 0}, "1745913071002": {"spreadCodeWeightPairs": "", "costEnergy": 16, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_1_2_1_2", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1745913071002", "cookGemCost": 0}, "1741837046001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "codeStr": "eq_3_5", "shopGemCost": 0, "choices": "", "id": "1741837046001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 1, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1730095941001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "codeStr": "eq_2_5", "shopGemCost": 0, "choices": "", "id": "1730095941001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 1, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1743128763001": {"spreadCodeWeightPairs": "it_5_1_1-2;it_5_1_2-1;it_5_2_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_5_5", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 176, "choices": "", "spreadAddItem": 0, "id": "1743128763001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 2, "spreadItemBoxChain": "", "spreadStorageRestNumber": 24, "cookRecipe": ""}, "1745806494001": {"spreadCodeWeightPairs": "it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_3_4", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 102, "choices": "", "spreadAddItem": 0, "id": "1745806494001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 24, "cookRecipe": ""}, "1744683794001": {"spreadCodeWeightPairs": "it_3_1_1-1;it_3_1_1-1;it_3_1_2-1;it_3_1_1-1;it_3_2_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_3_7", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 1055, "choices": "", "spreadAddItem": 0, "id": "1744683794001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": 1749450766, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 40, "cookRecipe": ""}, "1749436609001": {"spreadCodeWeightPairs": "", "costEnergy": 1, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_1_1_2", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749436609001", "cookGemCost": 0}, "1743991993001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "codeStr": "eq_3_4", "shopGemCost": 0, "choices": "", "id": "1743991993001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 1, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1748593961002": {"spreadCodeWeightPairs": "it_1_1_1_1-1", "spreadState": 3, "materialInfo": "", "codeStr": "it_1_1_6", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 0, "choices": "", "spreadAddItem": 0, "id": "1748593961002", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": -1, "costEnergy": 15, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 2, "spreadItemBoxChain": "", "spreadStorageRestNumber": 1, "cookRecipe": ""}, "1737340454001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "it_4_1_4-1749437455002", "codeStr": "eq_1_6", "shopGemCost": 0, "choices": "", "id": "1737340454001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 2, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}, "1749127842002": {"spreadCodeWeightPairs": "", "costEnergy": 7, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_3_1_4", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749127842002", "cookGemCost": 0}, "1749004167001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "eq_4_3", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749004167001", "cookGemCost": 0}, "1744683906001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "eq_2_3", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1744683906001", "cookGemCost": 0}, "1747967704002": {"spreadCodeWeightPairs": "", "costEnergy": 45, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_4_1_7", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1747967704002", "cookGemCost": 0}, "1749450590001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "ene_4", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450590001", "cookGemCost": 0}, "1749450583001": {"spreadCodeWeightPairs": "", "costEnergy": 122, "materialInfo": "", "shopGemCost": 0, "codeStr": "ds_friedve_4", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450583001", "cookGemCost": 0}, "1749435153001": {"spreadCodeWeightPairs": "", "costEnergy": 1, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_1_1_3", "costEnergyCurDay": 0, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749435153001", "cookGemCost": 0}, "1749437428005": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_7_2_1", "costEnergyCurDay": 1, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749437428005", "cookGemCost": 0}, "1749450704001": {"spreadCodeWeightPairs": "", "costEnergy": 4, "materialInfo": "", "shopGemCost": 0, "codeStr": "it_2_2_4", "costEnergyCurDay": 4, "bubbleGemCost": 0, "choiceDate": "", "cookSkipPropCost": 0, "choices": "", "cookRecipe": "", "spreadItemBoxChain": "", "id": "1749450704001", "cookGemCost": 0}, "1748574866001": {"spreadCodeWeightPairs": "it_7_1_1-9;it_7_2_1-3", "spreadState": 3, "materialInfo": "", "codeStr": "pd_7_4", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 88, "choices": "", "spreadAddItem": 0, "id": "1748574866001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": 1749447969, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 2, "spreadItemBoxChain": "", "spreadStorageRestNumber": 16, "cookRecipe": ""}, "1738287944001": {"spreadCodeWeightPairs": "it_6_1_1-12;it_6_1_2-4", "spreadState": 3, "materialInfo": "", "codeStr": "pd_6_5", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 944, "choices": "", "spreadAddItem": 0, "id": "1738287944001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": 1749450748, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 2, "spreadItemBoxChain": "", "spreadStorageRestNumber": 16, "cookRecipe": ""}, "1730096039001": {"spreadCodeWeightPairs": "it_2_2_1-1;it_2_1_1-1;it_2_1_2-1", "spreadState": 3, "materialInfo": "", "codeStr": "pd_2_7", "spreadTierUpLevel": 0, "choiceDate": "", "spreadCount": 5037, "choices": "", "spreadAddItem": 0, "id": "1730096039001", "cookGemCost": 0, "cookSkipPropCost": 0, "spreadEnergyFree": 0, "spreadStartTimer": 1749450394, "costEnergy": 0, "bubbleGemCost": 0, "spreadTierUpCount": 0, "spreadInherit": 0, "shopGemCost": 0, "costEnergyCurDay": 0, "spreadWeightType": 1, "spreadItemBoxChain": "", "spreadStorageRestNumber": 0, "cookRecipe": ""}, "1746852265001": {"spreadCodeWeightPairs": "", "costEnergy": 0, "materialInfo": "it_5_1_4-1749436598001", "codeStr": "eq_6_4", "shopGemCost": 0, "choices": "", "id": "1746852265001", "cookGemCost": 0, "cookSkipPropCost": 0, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "bubbleGemCost": 0, "cookSpeedTime": 0, "cookStartTimer": -1, "cookState": 2, "spreadItemBoxChain": "", "choiceDate": "", "cookRecipe": ""}}, "board": {"2_4": {"id": "2_4", "itemId": "1749434949001"}, "1_4": {"id": "1_4", "itemId": "1749434900001"}, "2_8": {"id": "2_8", "itemId": "1749450704001"}, "3_8": {"id": "3_8", "itemId": "1748574866001"}, "6_3": {"id": "6_3", "itemId": "1749450872001"}, "7_7": {"id": "7_7", "itemId": "1748056448001"}, "4_3": {"id": "4_3", "itemId": "1749450766001"}, "2_5": {"id": "2_5", "itemId": "1748593961002"}, "4_2": {"id": "4_2", "itemId": "1745806494001"}, "6_2": {"id": "6_2", "itemId": "1741572578001"}, "1_2": {"id": "1_2", "itemId": "1730095941001"}, "2_2": {"id": "2_2", "itemId": "1743846671001"}, "1_1": {"id": "1_1", "itemId": "1742869211001"}, "1_7": {"id": "1_7", "itemId": "1743991993001"}, "2_7": {"id": "2_7", "itemId": "1749450478001"}, "3_7": {"id": "3_7", "itemId": "1749450398001"}, "5_8": {"id": "5_8", "itemId": "1745913071002"}, "4_8": {"id": "4_8", "itemId": "1749437427001"}, "3_9": {"id": "3_9", "itemId": "1746585834001"}, "5_2": {"id": "5_2", "itemId": "1743128763001"}, "5_9": {"id": "5_9", "itemId": "1738834040001"}, "6_9": {"id": "6_9", "itemId": "1734438341001"}, "6_6": {"id": "6_6", "itemId": "1749450651001"}, "7_2": {"id": "7_2", "itemId": "1749435153001"}, "1_5": {"id": "1_5", "itemId": "1739932709001"}, "2_9": {"id": "2_9", "itemId": "1740484341001"}, "3_5": {"id": "3_5", "itemId": "1749450638001"}, "3_2": {"id": "3_2", "itemId": "1749193701001"}, "1_9": {"id": "1_9", "itemId": "1730096039001"}, "2_1": {"id": "2_1", "itemId": "1742351309001"}, "7_1": {"id": "7_1", "itemId": "1745164158001"}, "4_6": {"id": "4_6", "itemId": "1749450744001"}, "5_1": {"id": "5_1", "itemId": "1740969017001"}, "6_1": {"id": "6_1", "itemId": "1738287944001"}, "3_1": {"id": "3_1", "itemId": "1737340454001"}, "4_1": {"id": "4_1", "itemId": "1744683794001"}, "6_8": {"id": "6_8", "itemId": "1749127842002"}, "7_8": {"id": "7_8", "itemId": "1749176183001"}, "1_3": {"id": "1_3", "itemId": "1747715534001"}, "2_3": {"id": "2_3", "itemId": "1746852265001"}, "4_4": {"id": "4_4", "itemId": "1749434951003"}, "3_4": {"id": "3_4", "itemId": "1749436606002"}, "6_4": {"id": "6_4", "itemId": "1749450629001"}, "4_9": {"id": "4_9", "itemId": "1740969176001"}, "7_3": {"id": "7_3", "itemId": "1749450654001"}, "6_7": {"id": "6_7", "itemId": "1749450715001"}, "5_7": {"id": "5_7", "itemId": "1747967704002"}, "4_7": {"id": "4_7", "itemId": "1749437427002"}, "3_3": {"id": "3_3", "itemId": "1749176303001"}, "1_6": {"id": "1_6", "itemId": "1741837046001"}, "2_6": {"id": "2_6", "itemId": "1749437428005"}, "3_6": {"id": "3_6", "itemId": "1749437441002"}}, "ad": {}, "bundles": {"energy": {"data": "{}", "name": "energy"}, "multiTier": {"data": "{\"energytier1\":{\"data\":\"{\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"is_open\\\\\\\":1@\\\\\\\"duration\\\\\\\":30@\\\\\\\"specialType\\\\\\\":\\\\\\\"multiTier\\\\\\\"@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popNum\\\\\\\":1@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"energy\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"lack_energy\\\\\\\"}]@\\\\\\\"id\\\\\\\":20@\\\\\\\"groupId\\\\\\\":\\\\\\\"energytier1\\\\\\\"@\\\\\\\"dailyBuyNum\\\\\\\":5@\\\\\\\"dailyShowNum\\\\\\\":10@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#et1.99\\\\\\\"@\\\\\\\"bundleContent#et3.99\\\\\\\"@\\\\\\\"bundleContent#et5.99\\\\\\\"@\\\\\\\"bundleTrigger#lack_energy\\\\\\\"@\\\\\\\"generalBundleConf#3.99\\\\\\\"]@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"discountTag\\\\\\\":\\\\\\\"125%\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"originPrice\\\\\\\":2.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et1.99\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":250}]@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_199\\\\\\\"}@{\\\\\\\"discountTag\\\\\\\":\\\\\\\"130%\\\\\\\"@\\\\\\\"price\\\\\\\":3.99@\\\\\\\"originPrice\\\\\\\":5.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et3.99\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":520}]@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_399\\\\\\\"}@{\\\\\\\"discountTag\\\\\\\":\\\\\\\"140%\\\\\\\"@\\\\\\\"price\\\\\\\":5.99@\\\\\\\"originPrice\\\\\\\":8.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et5.99\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":820}]@\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_599\\\\\\\"}]@\\\\\\\"order\\\\\\\":[\\\\\\\"et1.99\\\\\\\"@\\\\\\\"et3.99\\\\\\\"@\\\\\\\"et5.99\\\\\\\"]@\\\\\\\"buyCD\\\\\\\":30@\\\\\\\"sTime\\\\\\\":1742522400@\\\\\\\"popCD\\\\\\\":5@\\\\\\\"generalBundleConf\\\\\\\":[{\\\\\\\"confType\\\\\\\":\\\\\\\"defaultOrder\\\\\\\"@\\\\\\\"param_string\\\\\\\":\\\\\\\"et3.99\\\\\\\"}]@\\\\\\\"uiCode\\\\\\\":\\\\\\\"multiTier\\\\\\\"@\\\\\\\"eTime\\\\\\\":1765411200}\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749434957\\\"},\\\"tgDailyShowNum_lack_energy\\\":{\\\"value\\\":1},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20248\\\"},\\\"lastGear\\\":{\\\"value\\\":\\\"0\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20248\\\"},\\\"curGearBundleId\\\":{\\\"value\\\":\\\"et3.99\\\"},\\\"missedNum\\\":{\\\"value\\\":\\\"0\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1749434957\\\"}}\"}}", "name": "multiTier"}, "chain": {"data": "{\"chain5\":{\"data\":\"{\\\"triggerTime\\\":{\\\"value\\\":\\\"1749175797\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"groupId\\\\\\\":\\\\\\\"chain5\\\\\\\"@\\\\\\\"bundleContentChain\\\\\\\":[{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":1@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":5}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_1\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":2@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":10}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_2\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":3@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_3\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_4\\\\\\\"@\\\\\\\"step\\\\\\\":4@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":30}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":150}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_199\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":5@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":15}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_5\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":6@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_2\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_6\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"price\\\\\\\":2.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_7\\\\\\\"@\\\\\\\"step\\\\\\\":7@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_299\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":8@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_8\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":9@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":30}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_9\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":10@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":10}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_10\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":11@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_11\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"price\\\\\\\":3.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_12\\\\\\\"@\\\\\\\"step\\\\\\\":12@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":60}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":250}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_399\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":13@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_13\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":14@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_14\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":15@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":50}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_15\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":16@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_4\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_16\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"price\\\\\\\":4.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_17\\\\\\\"@\\\\\\\"step\\\\\\\":17@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":80}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":300}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_499\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":18@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":60}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_18\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":19@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":15}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_19\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":20@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":70}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_20\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":21@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_21\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"price\\\\\\\":6.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_22\\\\\\\"@\\\\\\\"step\\\\\\\":22@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":450}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_699\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":23@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_23\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":24@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_24\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":25@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":150}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_25\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":26@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":20}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_26\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"price\\\\\\\":15.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_27\\\\\\\"@\\\\\\\"step\\\\\\\":27@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":240}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":1000}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_1599\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":28@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_28\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":29@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_29\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":30@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_30\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":31@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":400}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_31\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":32@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":50}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_32\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"price\\\\\\\":19.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_33\\\\\\\"@\\\\\\\"step\\\\\\\":33@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":320}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":1200}]@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_1999\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":34@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_34\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":35@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_35\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":36@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":400}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_36\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":37@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_37\\\\\\\"}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":38@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}]@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_38\\\\\\\"}]@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContentChain#chain_skip\\\\\\\"@\\\\\\\"bundleTrigger#login\\\\\\\"]@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"uiCode\\\\\\\":\\\\\\\"chain\\\\\\\"@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"duration\\\\\\\":4320@\\\\\\\"specialType\\\\\\\":\\\\\\\"chain\\\\\\\"@\\\\\\\"rTime\\\\\\\":1749520800@\\\\\\\"sTime\\\\\\\":1749175200@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"login\\\\\\\"}]@\\\\\\\"eTime\\\\\\\":1749434400@\\\\\\\"id\\\\\\\":75@\\\\\\\"dailyShowNum\\\\\\\":2}\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749175841\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"2\\\"},\\\"curIndex\\\":{\\\"value\\\":\\\"4\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20245\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20245\\\"},\\\"tgDailyShowNum_login\\\":{\\\"value\\\":2}}\"}}", "name": "chain"}, "cd": {"data": "{\"cd1\":{\"data\":\"{\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"is_open\\\\\\\":1@\\\\\\\"duration\\\\\\\":360@\\\\\\\"specialType\\\\\\\":\\\\\\\"cd\\\\\\\"@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popNum\\\\\\\":1@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"cd\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"pd_cd\\\\\\\"}]@\\\\\\\"id\\\\\\\":8@\\\\\\\"groupId\\\\\\\":\\\\\\\"cd1\\\\\\\"@\\\\\\\"dailyBuyNum\\\\\\\":2@\\\\\\\"dailyShowNum\\\\\\\":10@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#cd_199\\\\\\\"@\\\\\\\"bundleTrigger#pd_cd\\\\\\\"]@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"discountTag\\\\\\\":\\\\\\\"125%\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"cd_199\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":4}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":50}]@\\\\\\\"payID\\\\\\\":\\\\\\\"rush_order_199\\\\\\\"}]@\\\\\\\"order\\\\\\\":[\\\\\\\"cd_199\\\\\\\"]@\\\\\\\"buyCD\\\\\\\":30@\\\\\\\"sTime\\\\\\\":1733875200@\\\\\\\"popCD\\\\\\\":60@\\\\\\\"uiCode\\\\\\\":\\\\\\\"cd\\\\\\\"@\\\\\\\"eTime\\\\\\\":1765411200}\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1749436609\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749436610\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20248\\\"},\\\"tgDailyShowNum_pd_cd\\\":{\\\"value\\\":1},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20248\\\"}}\"}}", "name": "cd"}, "orderGroup": {"data": "{\"order1\":{\"data\":\"{\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"is_open\\\\\\\":1@\\\\\\\"duration\\\\\\\":60@\\\\\\\"specialType\\\\\\\":\\\\\\\"orderGroup\\\\\\\"@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popNum\\\\\\\":1@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"orderGroup\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"finish_order_group\\\\\\\"}]@\\\\\\\"id\\\\\\\":14@\\\\\\\"groupId\\\\\\\":\\\\\\\"order1\\\\\\\"@\\\\\\\"dailyBuyNum\\\\\\\":2@\\\\\\\"dailyShowNum\\\\\\\":5@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#order_199\\\\\\\"@\\\\\\\"bundleTrigger#finish_order_group\\\\\\\"]@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"discountTag\\\\\\\":\\\\\\\"120%\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"order_199\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"cbox3_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]@\\\\\\\"payID\\\\\\\":\\\\\\\"finish_order_199\\\\\\\"}]@\\\\\\\"order\\\\\\\":[\\\\\\\"order_199\\\\\\\"]@\\\\\\\"buyCD\\\\\\\":30@\\\\\\\"sTime\\\\\\\":1741053600@\\\\\\\"popCD\\\\\\\":60@\\\\\\\"uiCode\\\\\\\":\\\\\\\"orderGroup\\\\\\\"@\\\\\\\"eTime\\\\\\\":1765411200}\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1749437128\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749437144\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20248\\\"},\\\"tgDailyShowNum_finish_order_group\\\":{\\\"value\\\":1},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20248\\\"}}\"}}", "name": "orderGroup"}, "starter": {"data": "{\"starter\":{\"data\":\"{\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"groupId\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"dailyBuyNum\\\\\\\":1@\\\\\\\"order\\\\\\\":[\\\\\\\"starter_499\\\\\\\"]@\\\\\\\"dailyShowNum\\\\\\\":2@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#starter_499\\\\\\\"@\\\\\\\"bundleTrigger#task_finished\\\\\\\"@\\\\\\\"bundleCondition#starter_499\\\\\\\"]@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"discountTag\\\\\\\":\\\\\\\"200%\\\\\\\"@\\\\\\\"price\\\\\\\":4.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"starter_499\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":240}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":4}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"enebox_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]@\\\\\\\"payID\\\\\\\":\\\\\\\"starter_bundle_1\\\\\\\"}]@\\\\\\\"maxBuyNum\\\\\\\":1@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"duration\\\\\\\":720@\\\\\\\"buyCD\\\\\\\":10080@\\\\\\\"uiCode\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"specialType\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"bundleCondition\\\\\\\":[{\\\\\\\"taskFinished\\\\\\\":{\\\\\\\"TaskCount\\\\\\\":3@\\\\\\\"ChapterId\\\\\\\":2}}]@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popNum\\\\\\\":1@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"starter\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"task_finished\\\\\\\"}]@\\\\\\\"id\\\\\\\":1@\\\\\\\"popCD\\\\\\\":360}\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1749193827\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749194238\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20245\\\"},\\\"tgDailyShowNum_task_finished\\\":{\\\"value\\\":1},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20245\\\"}}\"}}", "name": "starter"}}, "cdn2": {}, "openFunc": {"inventory": {"id": "inventory", "state": 1}, "shop": {"id": "shop", "state": 1}, "discoveries": {"id": "discoveries", "state": 1}, "bubble": {"id": "bubble", "state": 1}}, "biSync": {"GameDuration": {"value": "122183", "key": "GameDuration"}, "ConsumedFreeGem": {"value": "239", "key": "ConsumedFreeGem"}, "ConsumedEnergy": {"value": "27093", "key": "ConsumedEnergy"}}, "ItemUnlock": {"ds_juice_4": {"state": 3, "type": "ds_juice_4"}, "pd_1_4": {"state": 3, "type": "pd_1_4"}, "pd_1_1": {"state": 3, "type": "pd_1_1"}, "it_1_2_5": {"state": 3, "type": "it_1_2_5"}, "it_2_2_5": {"state": 3, "type": "it_2_2_5"}, "ds_grillmt_8": {"state": 3, "type": "ds_grillmt_8"}, "ds_fd_19": {"state": 2, "type": "ds_fd_19"}, "ds_grillve_3": {"state": 3, "type": "ds_grillve_3"}, "greenbox_1": {"state": 2, "type": "greenbox_1"}, "pd_5_4": {"state": 3, "type": "pd_5_4"}, "it_2_3_4": {"state": 3, "type": "it_2_3_4"}, "pd_2_2": {"state": 3, "type": "pd_2_2"}, "it_2_1_5": {"state": 3, "type": "it_2_1_5"}, "pd_7_3": {"state": 3, "type": "pd_7_3"}, "eq_6_4": {"state": 2, "type": "eq_6_4"}, "gold_1": {"state": 3, "type": "gold_1"}, "ds_fd_10": {"state": 2, "type": "ds_fd_10"}, "it_2_3_1_3": {"state": 3, "type": "it_2_3_1_3"}, "it_6_1_3": {"state": 3, "type": "it_6_1_3"}, "ds_juice_6": {"state": 3, "type": "ds_juice_6"}, "it_1_1_1_3": {"state": 3, "type": "it_1_1_1_3"}, "it_7_2_1": {"state": 2, "type": "it_7_2_1"}, "ds_fd_9": {"state": 2, "type": "ds_fd_9"}, "ds_fd_18": {"state": 2, "type": "ds_fd_18"}, "it_5_1_2": {"state": 3, "type": "it_5_1_2"}, "it_1_1_5": {"state": 3, "type": "it_1_1_5"}, "it_1_2_4": {"state": 3, "type": "it_1_2_4"}, "it_4_2_1": {"state": 3, "type": "it_4_2_1"}, "eb2_2_2": {"state": 2, "type": "eb2_2_2"}, "ds_juice_9": {"state": 3, "type": "ds_juice_9"}, "ds_e1cockt_2": {"state": 2, "type": "ds_e1cockt_2"}, "pd_4_4": {"state": 3, "type": "pd_4_4"}, "ds_grillsf_1": {"state": 3, "type": "ds_grillsf_1"}, "it_3_1_6": {"state": 3, "type": "it_3_1_6"}, "pd_3_4": {"state": 3, "type": "pd_3_4"}, "ene_5": {"state": 3, "type": "ene_5"}, "eb2_2_1": {"state": 2, "type": "eb2_2_1"}, "eq_2_6": {"state": 3, "type": "eq_2_6"}, "ds_friedve_3": {"state": 3, "type": "ds_friedve_3"}, "ds_e1cockt_9": {"state": 2, "type": "ds_e1cockt_9"}, "it_1_1_9": {"state": 3, "type": "it_1_1_9"}, "ds_grillmt_6": {"state": 3, "type": "ds_grillmt_6"}, "pd_3_6": {"state": 3, "type": "pd_3_6"}, "it_1_2_7": {"state": 3, "type": "it_1_2_7"}, "pd_3_7": {"state": 3, "type": "pd_3_7"}, "eb4_1_5": {"state": 2, "type": "eb4_1_5"}, "pd_1_7": {"state": 3, "type": "pd_1_7"}, "it_3_1_5": {"state": 3, "type": "it_3_1_5"}, "ds_e1cockt_6": {"state": 2, "type": "ds_e1cockt_6"}, "it_5_2_2": {"state": 3, "type": "it_5_2_2"}, "ds_grillmt_3": {"state": 3, "type": "ds_grillmt_3"}, "eq_4_4": {"state": 3, "type": "eq_4_4"}, "ds_grillve_2": {"state": 3, "type": "ds_grillve_2"}, "ene_2": {"state": 3, "type": "ene_2"}, "ds_friedmt_1": {"state": 3, "type": "ds_friedmt_1"}, "ds_friedve_2": {"state": 3, "type": "ds_friedve_2"}, "it_4_1_5": {"state": 3, "type": "it_4_1_5"}, "ds_fd_23": {"state": 2, "type": "ds_fd_23"}, "it_2_2_3": {"state": 3, "type": "it_2_2_3"}, "it_1_2_2": {"state": 3, "type": "it_1_2_2"}, "eq_2_4": {"state": 3, "type": "eq_2_4"}, "ds_friedve_4": {"state": 3, "type": "ds_friedve_4"}, "eb2_1_6": {"state": 2, "type": "eb2_1_6"}, "it_3_2_5": {"state": 3, "type": "it_3_2_5"}, "ds_mixdrk_7": {"state": 3, "type": "ds_mixdrk_7"}, "eq_4_5": {"state": 3, "type": "eq_4_5"}, "skipprop_3": {"state": 2, "type": "skipprop_3"}, "ds_fd_20": {"state": 2, "type": "ds_fd_20"}, "ds_e4friedmt_6": {"state": 2, "type": "ds_e4friedmt_6"}, "it_2_3_1_2": {"state": 3, "type": "it_2_3_1_2"}, "additem_2": {"state": 2, "type": "additem_2"}, "it_2_1_7": {"state": 3, "type": "it_2_1_7"}, "it_5_2_5": {"state": 3, "type": "it_5_2_5"}, "eq_1_5": {"state": 3, "type": "eq_1_5"}, "it_2_3_1_1": {"state": 3, "type": "it_2_3_1_1"}, "it_1_1_8": {"state": 3, "type": "it_1_1_8"}, "it_7_1_8": {"state": 2, "type": "it_7_1_8"}, "ds_grillmt_5": {"state": 3, "type": "ds_grillmt_5"}, "it_1_1_2_1": {"state": 3, "type": "it_1_1_2_1"}, "it_5_1_7": {"state": 3, "type": "it_5_1_7"}, "it_5_1_6": {"state": 3, "type": "it_5_1_6"}, "eq_4_2": {"state": 3, "type": "eq_4_2"}, "ds_grillmt_10": {"state": 3, "type": "ds_grillmt_10"}, "it_3_2_3": {"state": 3, "type": "it_3_2_3"}, "eb1_1_4": {"state": 2, "type": "eb1_1_4"}, "it_2_3_3": {"state": 3, "type": "it_2_3_3"}, "ds_mixdrk_9": {"state": 3, "type": "ds_mixdrk_9"}, "pd_7_5": {"state": 2, "type": "pd_7_5"}, "ds_fd_21": {"state": 2, "type": "ds_fd_21"}, "it_1_1_2_5": {"state": 3, "type": "it_1_1_2_5"}, "ene_4": {"state": 3, "type": "ene_4"}, "eb1_2_2": {"state": 2, "type": "eb1_2_2"}, "ds_mixdrk_3": {"state": 3, "type": "ds_mixdrk_3"}, "ds_friedmt_4": {"state": 3, "type": "ds_friedmt_4"}, "ds_juice_3": {"state": 3, "type": "ds_juice_3"}, "it_4_1_4": {"state": 3, "type": "it_4_1_4"}, "pd_1_5": {"state": 3, "type": "pd_1_5"}, "gold_3": {"state": 3, "type": "gold_3"}, "it_1_2_8": {"state": 3, "type": "it_1_2_8"}, "it_4_1_7": {"state": 3, "type": "it_4_1_7"}, "pd_1_2": {"state": 3, "type": "pd_1_2"}, "it_3_2_2": {"state": 3, "type": "it_3_2_2"}, "eq_1_4": {"state": 3, "type": "eq_1_4"}, "it_6_1_7": {"state": 3, "type": "it_6_1_7"}, "ds_e1cockt_11": {"state": 2, "type": "ds_e1cockt_11"}, "eq_4_1": {"state": 3, "type": "eq_4_1"}, "it_1_1_1_2": {"state": 3, "type": "it_1_1_1_2"}, "eb2_1_8": {"state": 2, "type": "eb2_1_8"}, "pd_7_1": {"state": 3, "type": "pd_7_1"}, "it_4_1_2": {"state": 3, "type": "it_4_1_2"}, "it_3_1_7": {"state": 3, "type": "it_3_1_7"}, "eb4_1_8": {"state": 2, "type": "eb4_1_8"}, "it_2_3_5": {"state": 3, "type": "it_2_3_5"}, "pd_4_2": {"state": 3, "type": "pd_4_2"}, "eq_5_4": {"state": 3, "type": "eq_5_4"}, "eb1_1_8": {"state": 2, "type": "eb1_1_8"}, "it_6_1_4": {"state": 3, "type": "it_6_1_4"}, "gem_4": {"state": 3, "type": "gem_4"}, "eb1_1_5": {"state": 2, "type": "eb1_1_5"}, "it_6_1_6": {"state": 3, "type": "it_6_1_6"}, "ds_juice_2": {"state": 3, "type": "ds_juice_2"}, "pd_3_5": {"state": 3, "type": "pd_3_5"}, "it_5_2_6": {"state": 3, "type": "it_5_2_6"}, "gem_1_1": {"state": 2, "type": "gem_1_1"}, "it_4_2_6": {"state": 3, "type": "it_4_2_6"}, "it_1_1_2_4": {"state": 3, "type": "it_1_1_2_4"}, "ds_fd_15": {"state": 2, "type": "ds_fd_15"}, "ds_fd_4": {"state": 2, "type": "ds_fd_4"}, "ds_chopfs_1": {"state": 2, "type": "ds_chopfs_1"}, "eb2_1_7": {"state": 2, "type": "eb2_1_7"}, "skipprop_5": {"state": 2, "type": "skipprop_5"}, "eb4_2_2": {"state": 1, "type": "eb4_2_2"}, "ds_grillmt_2": {"state": 3, "type": "ds_grillmt_2"}, "ds_grillmt_12": {"state": 2, "type": "ds_grillmt_12"}, "ds_chopve_2": {"state": 3, "type": "ds_chopve_2"}, "eq_3_5": {"state": 3, "type": "eq_3_5"}, "ds_mixdrk_8": {"state": 3, "type": "ds_mixdrk_8"}, "it_1_1_7": {"state": 3, "type": "it_1_1_7"}, "eb4_1_4": {"state": 2, "type": "eb4_1_4"}, "eq_2_3": {"state": 3, "type": "eq_2_3"}, "pd_5_3": {"state": 3, "type": "pd_5_3"}, "it_7_1_2": {"state": 3, "type": "it_7_1_2"}, "ds_friedsf_1": {"state": 3, "type": "ds_friedsf_1"}, "eb1_1_1": {"state": 2, "type": "eb1_1_1"}, "it_7_2_4": {"state": 2, "type": "it_7_2_4"}, "pd_1_6": {"state": 3, "type": "pd_1_6"}, "gold_4": {"state": 3, "type": "gold_4"}, "ds_grillsf_3": {"state": 3, "type": "ds_grillsf_3"}, "ds_mixdrk_2": {"state": 3, "type": "ds_mixdrk_2"}, "eb2_1_4": {"state": 2, "type": "eb2_1_4"}, "ds_e1cockt_13": {"state": 1, "type": "ds_e1cockt_13"}, "pd_1_8": {"state": 3, "type": "pd_1_8"}, "pd_7_4": {"state": 3, "type": "pd_7_4"}, "skipprop_1": {"state": 2, "type": "skipprop_1"}, "it_6_1_2": {"state": 3, "type": "it_6_1_2"}, "ds_chopfr_1": {"state": 3, "type": "ds_chopfr_1"}, "pd_2_7": {"state": 3, "type": "pd_2_7"}, "additem_1_3": {"state": 2, "type": "additem_1_3"}, "it_7_1_6": {"state": 2, "type": "it_7_1_6"}, "pd_7_2": {"state": 3, "type": "pd_7_2"}, "ds_grillsf_6": {"state": 3, "type": "ds_grillsf_6"}, "it_3_1_8": {"state": 3, "type": "it_3_1_8"}, "it_4_1_9": {"state": 3, "type": "it_4_1_9"}, "ds_mixdrk_5": {"state": 3, "type": "ds_mixdrk_5"}, "eq_2_2": {"state": 3, "type": "eq_2_2"}, "eb1_1_10": {"state": 2, "type": "eb1_1_10"}, "ds_friedsf_3": {"state": 3, "type": "ds_friedsf_3"}, "ds_friedmt_2": {"state": 3, "type": "ds_friedmt_2"}, "pd_3_3": {"state": 3, "type": "pd_3_3"}, "pd_5_6": {"state": 3, "type": "pd_5_6"}, "it_3_1_3": {"state": 3, "type": "it_3_1_3"}, "ds_e6soup_1": {"state": 1, "type": "ds_e6soup_1"}, "skipprop_2": {"state": 2, "type": "skipprop_2"}, "ds_grillmt_9": {"state": 3, "type": "ds_grillmt_9"}, "ds_e1cockt_14": {"state": 2, "type": "ds_e1cockt_14"}, "eb2_1_2": {"state": 2, "type": "eb2_1_2"}, "it_4_1_8": {"state": 3, "type": "it_4_1_8"}, "it_3_2_6": {"state": 3, "type": "it_3_2_6"}, "it_2_1_1": {"state": 3, "type": "it_2_1_1"}, "it_2_1_2": {"state": 3, "type": "it_2_1_2"}, "eq_1_3": {"state": 3, "type": "eq_1_3"}, "eq_2_5": {"state": 3, "type": "eq_2_5"}, "ds_mixdrk_4": {"state": 3, "type": "ds_mixdrk_4"}, "it_4_1_1": {"state": 3, "type": "it_4_1_1"}, "pd_6_2": {"state": 3, "type": "pd_6_2"}, "pd_2_4": {"state": 3, "type": "pd_2_4"}, "eb1_1_9": {"state": 2, "type": "eb1_1_9"}, "eb4_1_1": {"state": 2, "type": "eb4_1_1"}, "it_2_1_9": {"state": 3, "type": "it_2_1_9"}, "it_4_2_7": {"state": 3, "type": "it_4_2_7"}, "it_4_1_10": {"state": 3, "type": "it_4_1_10"}, "it_5_1_3": {"state": 3, "type": "it_5_1_3"}, "eb4_2_1": {"state": 2, "type": "eb4_2_1"}, "eb4_1_2": {"state": 2, "type": "eb4_1_2"}, "gem_2": {"state": 3, "type": "gem_2"}, "ds_e4sf_13": {"state": 2, "type": "ds_e4sf_13"}, "eb1_2_4": {"state": 2, "type": "eb1_2_4"}, "ds_fd_11": {"state": 2, "type": "ds_fd_11"}, "ds_grillsf_2": {"state": 3, "type": "ds_grillsf_2"}, "ds_fd_16": {"state": 2, "type": "ds_fd_16"}, "pd_4_6": {"state": 3, "type": "pd_4_6"}, "pd_6_4": {"state": 3, "type": "pd_6_4"}, "it_5_1_5": {"state": 3, "type": "it_5_1_5"}, "ds_juice_1": {"state": 3, "type": "ds_juice_1"}, "it_2_1_10": {"state": 3, "type": "it_2_1_10"}, "ds_chopve_4": {"state": 3, "type": "ds_chopve_4"}, "ds_juice_7": {"state": 3, "type": "ds_juice_7"}, "eq_6_1": {"state": 2, "type": "eq_6_1"}, "it_2_2_2": {"state": 3, "type": "it_2_2_2"}, "ds_grillve_4": {"state": 3, "type": "ds_grillve_4"}, "it_4_1_3": {"state": 3, "type": "it_4_1_3"}, "cbox2_1": {"state": 2, "type": "cbox2_1"}, "skiptime_1": {"state": 2, "type": "skiptime_1"}, "it_7_1_7": {"state": 2, "type": "it_7_1_7"}, "it_1_1_2": {"state": 3, "type": "it_1_1_2"}, "it_3_1_1": {"state": 3, "type": "it_3_1_1"}, "pd_2_5": {"state": 3, "type": "pd_2_5"}, "pd_6_5": {"state": 3, "type": "pd_6_5"}, "it_7_1_5": {"state": 2, "type": "it_7_1_5"}, "pd_4_1": {"state": 3, "type": "pd_4_1"}, "pd_4_5": {"state": 3, "type": "pd_4_5"}, "pd_5_5": {"state": 3, "type": "pd_5_5"}, "it_1_2_1_1": {"state": 3, "type": "it_1_2_1_1"}, "it_2_3_6": {"state": 3, "type": "it_2_3_6"}, "ds_grillsf_5": {"state": 3, "type": "ds_grillsf_5"}, "eb2_2_3": {"state": 2, "type": "eb2_2_3"}, "eb2_1_1": {"state": 2, "type": "eb2_1_1"}, "it_5_1_4": {"state": 3, "type": "it_5_1_4"}, "gold_2": {"state": 3, "type": "gold_2"}, "it_7_2_3": {"state": 2, "type": "it_7_2_3"}, "it_1_1_4": {"state": 3, "type": "it_1_1_4"}, "ds_e1cockt_3": {"state": 1, "type": "ds_e1cockt_3"}, "it_3_1_2": {"state": 3, "type": "it_3_1_2"}, "eb1_2_3": {"state": 2, "type": "eb1_2_3"}, "ds_fd_5": {"state": 2, "type": "ds_fd_5"}, "pd_1_3": {"state": 3, "type": "pd_1_3"}, "ds_e1cockt_4": {"state": 2, "type": "ds_e1cockt_4"}, "eb2_1_3": {"state": 2, "type": "eb2_1_3"}, "eq_4_6": {"state": 3, "type": "eq_4_6"}, "it_1_1_1": {"state": 3, "type": "it_1_1_1"}, "eq_4_3": {"state": 3, "type": "eq_4_3"}, "pd_6_1": {"state": 3, "type": "pd_6_1"}, "ds_fd_8": {"state": 2, "type": "ds_fd_8"}, "ds_mixdrk_1": {"state": 3, "type": "ds_mixdrk_1"}, "ds_grillmt_1": {"state": 3, "type": "ds_grillmt_1"}, "pd_6_3": {"state": 3, "type": "pd_6_3"}, "it_1_1_6": {"state": 3, "type": "it_1_1_6"}, "ds_fd_7": {"state": 2, "type": "ds_fd_7"}, "it_5_1_1": {"state": 3, "type": "it_5_1_1"}, "eb1_1_2": {"state": 2, "type": "eb1_1_2"}, "ds_friedve_5": {"state": 3, "type": "ds_friedve_5"}, "eb4_1_7": {"state": 2, "type": "eb4_1_7"}, "ds_friedve_1": {"state": 3, "type": "ds_friedve_1"}, "it_2_3_2": {"state": 3, "type": "it_2_3_2"}, "it_4_2_5": {"state": 3, "type": "it_4_2_5"}, "eq_5_3": {"state": 3, "type": "eq_5_3"}, "it_5_1_8": {"state": 3, "type": "it_5_1_8"}, "eq_6_2": {"state": 2, "type": "eq_6_2"}, "pd_2_3": {"state": 3, "type": "pd_2_3"}, "it_1_1_2_2": {"state": 3, "type": "it_1_1_2_2"}, "pd_2_1": {"state": 3, "type": "pd_2_1"}, "ds_fd_1": {"state": 2, "type": "ds_fd_1"}, "it_5_2_7": {"state": 3, "type": "it_5_2_7"}, "it_7_1_4": {"state": 2, "type": "it_7_1_4"}, "it_3_1_9": {"state": 3, "type": "it_3_1_9"}, "it_3_2_7": {"state": 3, "type": "it_3_2_7"}, "gold_5": {"state": 3, "type": "gold_5"}, "ds_fd_2": {"state": 3, "type": "ds_fd_2"}, "pd_2_6": {"state": 3, "type": "pd_2_6"}, "ds_friedmt_5": {"state": 3, "type": "ds_friedmt_5"}, "eb1_2_1": {"state": 2, "type": "eb1_2_1"}, "ds_fd_6": {"state": 2, "type": "ds_fd_6"}, "enebox_1": {"state": 1, "type": "enebox_1"}, "eb1_1_6": {"state": 2, "type": "eb1_1_6"}, "it_5_2_3": {"state": 3, "type": "it_5_2_3"}, "gem_3": {"state": 3, "type": "gem_3"}, "eb1_1_7": {"state": 2, "type": "eb1_1_7"}, "eq_3_3": {"state": 3, "type": "eq_3_3"}, "eb2_2_5": {"state": 2, "type": "eb2_2_5"}, "ds_grillve_1": {"state": 3, "type": "ds_grillve_1"}, "it_1_1_2_3": {"state": 3, "type": "it_1_1_2_3"}, "ds_e3juice_11": {"state": 2, "type": "ds_e3juice_11"}, "eb4_1_3": {"state": 2, "type": "eb4_1_3"}, "it_2_1_6": {"state": 3, "type": "it_2_1_6"}, "eq_6_3": {"state": 2, "type": "eq_6_3"}, "ds_e1hotdrk_1": {"state": 2, "type": "ds_e1hotdrk_1"}, "ds_e1cockt_5": {"state": 2, "type": "ds_e1cockt_5"}, "it_3_1_4": {"state": 3, "type": "it_3_1_4"}, "it_4_2_4": {"state": 3, "type": "it_4_2_4"}, "eb2_1_5": {"state": 2, "type": "eb2_1_5"}, "it_5_2_8": {"state": 3, "type": "it_5_2_8"}, "it_1_1_10": {"state": 3, "type": "it_1_1_10"}, "it_2_2_6": {"state": 3, "type": "it_2_2_6"}, "ds_e1cockt_10": {"state": 2, "type": "ds_e1cockt_10"}, "it_2_3_1": {"state": 3, "type": "it_2_3_1"}, "ds_dst_1": {"state": 3, "type": "ds_dst_1"}, "it_3_2_1": {"state": 3, "type": "it_3_2_1"}, "pd_3_1": {"state": 3, "type": "pd_3_1"}, "ds_fd_17": {"state": 2, "type": "ds_fd_17"}, "it_1_2_1_3": {"state": 3, "type": "it_1_2_1_3"}, "eb1_1_3": {"state": 2, "type": "eb1_1_3"}, "it_4_1_6": {"state": 3, "type": "it_4_1_6"}, "additem_1_2": {"state": 2, "type": "additem_1_2"}, "ds_e4sf_12": {"state": 2, "type": "ds_e4sf_12"}, "ds_e1icytre_2": {"state": 2, "type": "ds_e1icytre_2"}, "eb1_2_5": {"state": 2, "type": "eb1_2_5"}, "ds_e1cockt_12": {"state": 2, "type": "ds_e1cockt_12"}, "pd_3_2": {"state": 3, "type": "pd_3_2"}, "it_6_1_5": {"state": 3, "type": "it_6_1_5"}, "ds_fd_3": {"state": 2, "type": "ds_fd_3"}, "ene_1": {"state": 3, "type": "ene_1"}, "ds_friedsf_2": {"state": 3, "type": "ds_friedsf_2"}, "ds_fd_13": {"state": 2, "type": "ds_fd_13"}, "it_2_1_8": {"state": 3, "type": "it_2_1_8"}, "ds_friedmt_3": {"state": 3, "type": "ds_friedmt_3"}, "pd_4_3": {"state": 3, "type": "pd_4_3"}, "ds_grillmt_7": {"state": 3, "type": "ds_grillmt_7"}, "it_4_2_3": {"state": 3, "type": "it_4_2_3"}, "ds_fd_14": {"state": 2, "type": "ds_fd_14"}, "eq_1_6": {"state": 3, "type": "eq_1_6"}, "it_1_2_1": {"state": 3, "type": "it_1_2_1"}, "additem_1": {"state": 2, "type": "additem_1"}, "it_4_2_2": {"state": 3, "type": "it_4_2_2"}, "ds_juice_8": {"state": 3, "type": "ds_juice_8"}, "it_1_2_1_2": {"state": 3, "type": "it_1_2_1_2"}, "it_2_3_1_4": {"state": 3, "type": "it_2_3_1_4"}, "it_7_2_6": {"state": 2, "type": "it_7_2_6"}, "gem_1": {"state": 3, "type": "gem_1"}, "it_1_1_1_4": {"state": 3, "type": "it_1_1_1_4"}, "it_1_1_3": {"state": 3, "type": "it_1_1_3"}, "ds_chopfru_1": {"state": 3, "type": "ds_chopfru_1"}, "ene_3": {"state": 3, "type": "ene_3"}, "additem_1_1": {"state": 2, "type": "additem_1_1"}, "it_1_2_3": {"state": 3, "type": "it_1_2_3"}, "ds_e3juice_10": {"state": 2, "type": "ds_e3juice_10"}, "it_3_2_4": {"state": 3, "type": "it_3_2_4"}, "ds_grillsf_7": {"state": 3, "type": "ds_grillsf_7"}, "it_2_2_1": {"state": 3, "type": "it_2_2_1"}, "it_2_2_4": {"state": 3, "type": "it_2_2_4"}, "it_7_2_2": {"state": 2, "type": "it_7_2_2"}, "eq_3_1": {"state": 3, "type": "eq_3_1"}, "eq_3_6": {"state": 3, "type": "eq_3_6"}, "eb4_1_6": {"state": 2, "type": "eb4_1_6"}, "ds_fd_12": {"state": 2, "type": "ds_fd_12"}, "ds_chopve_3": {"state": 3, "type": "ds_chopve_3"}, "it_2_1_3": {"state": 3, "type": "it_2_1_3"}, "it_7_1_1": {"state": 2, "type": "it_7_1_1"}, "ds_grillmt_4": {"state": 3, "type": "ds_grillmt_4"}, "ds_grillmt_11": {"state": 3, "type": "ds_grillmt_11"}, "it_1_2_6": {"state": 3, "type": "it_1_2_6"}, "it_7_2_5": {"state": 2, "type": "it_7_2_5"}, "eq_5_5": {"state": 2, "type": "eq_5_5"}, "ds_mixdrk_6": {"state": 3, "type": "ds_mixdrk_6"}, "eb2_2_4": {"state": 2, "type": "eb2_2_4"}, "ds_e6stewmt_1": {"state": 2, "type": "ds_e6stewmt_1"}, "ds_e1cockt_1": {"state": 2, "type": "ds_e1cockt_1"}, "ds_sal_1": {"state": 2, "type": "ds_sal_1"}, "it_6_1_1": {"state": 3, "type": "it_6_1_1"}, "cbox1_1": {"state": 2, "type": "cbox1_1"}, "eq_2_1": {"state": 3, "type": "eq_2_1"}, "eq_3_4": {"state": 3, "type": "eq_3_4"}, "it_2_1_4": {"state": 3, "type": "it_2_1_4"}, "it_5_2_1": {"state": 3, "type": "it_5_2_1"}, "ds_grillsf_4": {"state": 3, "type": "ds_grillsf_4"}, "it_1_1_1_1": {"state": 3, "type": "it_1_1_1_1"}, "it_7_1_3": {"state": 3, "type": "it_7_1_3"}, "ds_e1cockt_8": {"state": 2, "type": "ds_e1cockt_8"}, "ds_friedsf_4": {"state": 3, "type": "ds_friedsf_4"}, "freebox_1": {"state": 2, "type": "freebox_1"}, "skipprop_4": {"state": 2, "type": "skipprop_4"}, "it_5_2_4": {"state": 3, "type": "it_5_2_4"}, "it_3_1_10": {"state": 3, "type": "it_3_1_10"}, "eq_3_2": {"state": 3, "type": "eq_3_2"}, "ds_chopve_1": {"state": 3, "type": "ds_chopve_1"}}, "slot": {}, "inventory": {"1740574630001": {"itemId": "1740574630001", "codeStr": "it_1_1_1_2", "storeTime": 3000}, "1748059869001": {"itemId": "1748059869001", "codeStr": "it_2_2_4", "storeTime": 4000}, "1749004167001": {"itemId": "1749004167001", "codeStr": "eq_4_3", "storeTime": 5000}, "1749437467001": {"itemId": "1749437467001", "codeStr": "eq_3_3", "storeTime": 1749450665}, "1749176305001": {"itemId": "1749176305001", "codeStr": "pd_1_4", "storeTime": 8000}, "1749450583001": {"itemId": "1749450583001", "codeStr": "ds_friedve_4", "storeTime": 11000}, "1744683906001": {"itemId": "1744683906001", "codeStr": "eq_2_3", "storeTime": 2000}, "1749437468001": {"itemId": "1749437468001", "codeStr": "pd_2_3", "storeTime": 1749450664}, "1749175987002": {"itemId": "1749175987002", "codeStr": "ene_1", "storeTime": 6000}, "1749450590001": {"itemId": "1749450590001", "codeStr": "ene_4", "storeTime": 1749450591}, "1749003761001": {"itemId": "1749003761001", "codeStr": "it_4_1_7", "storeTime": 10000}, "1741054354001": {"itemId": "1741054354001", "codeStr": "pd_4_3", "storeTime": 1000}}, "survey": {}, "rate": {"1": {"activeConfigId": 0, "canPopup": 0, "triggeredConfig": "", "id": "1", "rated": 0}}, "energy": {"1": {"generateTime": 1749461074, "energyValue": 86, "id": "1"}, "2": {"id": "2", "energyValue": 100}}, "shopItem": {"1749434608001": {"leftCount": 1, "costCount": 20, "shopType": "DailySpecial", "costType": "gem", "itemCode": "enebox_1", "id": "1749434608001"}, "1749437134005": {"leftCount": 5, "costCount": 16, "shopType": "FlashSale", "costType": "gem", "itemCode": "it_5_2_4", "id": "1749437134005", "startCount": 5}, "1749434608002": {"leftCount": 0, "costCount": 0, "shopType": "DailySpecial", "costType": "gem", "itemCode": "freebox_1", "id": "1749434608002"}, "1749437134002": {"leftCount": 5, "costCount": 16, "shopType": "FlashSale", "costType": "gem", "itemCode": "it_2_1_6", "id": "1749437134002", "startCount": 5}, "1749437134001": {"leftCount": 5, "costCount": 5, "shopType": "FlashSale", "costType": "gem", "itemCode": "it_3_2_1", "id": "1749437134001", "startCount": 5}, "1749437134006": {"leftCount": 5, "costCount": 98, "shopType": "FlashSale", "costType": "gem", "itemCode": "it_1_2_6", "id": "1749437134006", "startCount": 5}, "1749437134004": {"leftCount": 5, "costCount": 28, "shopType": "FlashSale", "costType": "gem", "itemCode": "it_1_2_4", "id": "1749437134004", "startCount": 5}, "1749437134003": {"leftCount": 5, "costCount": 47, "shopType": "FlashSale", "costType": "gem", "itemCode": "it_7_2_5", "id": "1749437134003", "startCount": 5}}, "bundle": {}, "orderMeta": {"TotalFinishedGroupCount": {"value": "60", "key": "TotalFinishedGroupCount"}, "CurOrderGroupId": {"value": "8", "key": "CurOrderGroupId"}, "OrderGroupCostCurDayEnergy": {"value": "63", "key": "OrderGroupCostCurDayEnergy"}, "OrderGroupCostPastDayEnergy": {"value": "44", "key": "OrderGroupCostPastDayEnergy"}, "FirstOrder": {"value": "1", "key": "FirstOrder"}, "SecondOrder": {"value": "1", "key": "SecondOrder"}, "CurChapterId": {"value": "5", "key": "CurChapterId"}, "OrderGroupConsumeEnergy": {"value": "138", "key": "OrderGroupConsumeEnergy"}, "CurGroupFinishedOrderIds": {"value": "50540;50500;", "key": "CurGroupFinishedOrderIds"}, "CurFinishedGroupCount": {"value": "60", "key": "CurFinishedGroupCount"}}, "bundleMeta": {"energy_199PurchaseBundleId": {"value": "{\"groupId\":\"energy1\"@\"rewards\":[{\"Currency\":\"gem\"@\"Crypt\":\"wH\"@\"Amount\":40}@{\"Currency\":\"energy\"@\"Crypt\":\"rMZ\"@\"Amount\":150}@{\"Currency\":\"skipprop\"@\"Crypt\":\"rH\"@\"Amount\":10}]@\"bundleType\":\"energy\"}", "key": "energy_199PurchaseBundleId"}, "starter_bundle_1PurchaseBundleId": {"value": "{\"rewards\":[{\"Amount\":240@\"Crypt\":\"qLZ\"@\"Currency\":\"gem\"}@{\"Amount\":200@\"Crypt\":\"qHZ\"@\"Currency\":\"energy\"}@{\"Amount\":4@\"Crypt\":\"w\"@\"Currency\":\"additem_1\"}@{\"Amount\":1@\"Crypt\":\"r\"@\"Currency\":\"enebox_1\"}]@\"groupId\":\"starter\"@\"bundleType\":\"starter\"@\"bundleId\":\"starter_499\"}", "key": "starter_bundle_1PurchaseBundleId"}, "rush_order_199PurchaseBundleId": {"value": "{\"groupId\":\"cd1\"@\"rewards\":[{\"Amount\":40@\"Currency\":\"gem\"@\"Crypt\":\"wH\"}@{\"Amount\":4@\"Currency\":\"additem_1\"@\"Crypt\":\"w\"}@{\"Amount\":50@\"Currency\":\"energy\"@\"Crypt\":\"vH\"}]@\"bundleType\":\"cd\"}", "key": "rush_order_199PurchaseBundleId"}, "energyTriggerRefreshTime": {"value": "1733307400", "key": "energyTriggerRefreshTime"}}, "cached_requests2": {}, "mainTask": {}, "local": {"VId": {"value": "fa28c8e34b67dc2df6fec0c182067320", "key": "VId"}, "DId": {"value": "fa28c8e34b67dc2df6fec0c182067320", "key": "DId"}, "DataInconsistent": {"value": "false", "key": "DataInconsistent"}, "RegisterVersion": {"value": "1.1.0", "key": "RegisterVersion"}, "UserId": {"value": "11614", "key": "UserId"}, "Authorization": {"value": "Xi0AAAAAAADwb1NoAAAAABhhNmU3ZWTqHADw7ULESidwC/upxFXZ", "key": "Authorization"}, "Icon": {"value": "head7", "key": "Icon"}, "Name": {"value": "father", "key": "Name"}, "LastSyncTime": {"value": "1749461082", "key": "LastSyncTime"}}, "iapOrder": {}, "noticeState": {}, "returnUser": {"returnReward": {"value": "0", "key": "returnReward"}, "rewardexpiredTime": {"value": "0", "key": "rewardexpiredTime"}, "rewardLeaveDay": {"value": "0", "key": "rewardLeaveDay"}}, "misc": {"DataBalanceVersion": {"value": "1.18", "key": "DataBalanceVersion"}, "FlambeTimeType": {"value": "mode", "key": "FlambeTimeType"}, "FlambeTimeLinkOrder": {"value": "", "key": "FlambeTimeLinkOrder"}, "CheckItemRecycle": {"value": "0", "key": "CheckItemRecycle"}, "DataBalanceDiff": {"value": "eq_2#-8", "key": "DataBalanceDiff"}, "EnergyBoostWindowOpenState": {"value": "0", "key": "EnergyBoostWindowOpenState"}, "FreeRefillEnergy": {"value": "1", "key": "FreeRefillEnergy"}, "PDItemSPIndex": {"value": "pd_1_4-7;greenbox_1-1;pd_3_4-1;pd_2_6-1;pd_1_5-4;pd_1_6-1;pd_2_4-4;pd_2_5-2", "key": "PDItemSPIndex"}, "EnergyBoostTriggerEndTime": {"value": "1748932519", "key": "EnergyBoostTriggerEndTime"}, "IsFlambeTimeOrderGroup": {"value": 1, "key": "IsFlambeTimeOrderGroup"}, "EQPieceRemove": {"value": "1", "key": "EQPieceRemove"}, "FlambeTimeInstruChains": {"value": "", "key": "FlambeTimeInstruChains"}, "ItemTypeDeleteStateit_1_1_2": {"value": 1, "key": "ItemTypeDeleteStateit_1_1_2"}, "EnergyBoostUserOn": {"value": "1", "key": "EnergyBoostUserOn"}, "FlambeTimePDChains": {"value": "", "key": "FlambeTimePDChains"}, "FlambeTimeInstruSpeed": {"value": 1, "key": "FlambeTimeInstruSpeed"}, "InventoryBoughtCap": {"value": "6", "key": "InventoryBoughtCap"}, "FlambeTimeFinishTime": {"value": 0, "key": "FlambeTimeFinishTime"}}, "skin": {}, "orders": {"50510": {"groupId": 8, "chapterId": 5, "cleanGoldCount": 0, "type": 1, "createTime": 1749437134, "avatarId": 2, "requirementStr": "ds_e1cockt_10", "id": "50510", "rewards": "gold-111"}, "50560": {"groupId": 8, "chapterId": 5, "cleanGoldCount": 0, "type": 1, "createTime": 1749437134, "avatarId": 7, "requirementStr": "ds_friedve_4;ds_e1cockt_13", "id": "50560", "rewards": "gold-414"}, "50550": {"groupId": 8, "chapterId": 5, "cleanGoldCount": 0, "type": 1, "createTime": 1749437134, "avatarId": 6, "requirementStr": "it_2_1_7;ds_e6soup_1", "id": "50550", "rewards": "gold-238"}, "50520": {"groupId": 8, "chapterId": 5, "cleanGoldCount": 0, "type": 1, "createTime": 1749437134, "avatarId": 3, "requirementStr": "ds_grillmt_9;ds_e1cockt_3", "id": "50520", "rewards": "gold-173;additem_1-1"}, "50530": {"groupId": 8, "chapterId": 5, "cleanGoldCount": 0, "type": 1, "createTime": 1749437134, "avatarId": 4, "requirementStr": "ds_fd_8;it_7_2_6", "id": "50530", "rewards": "gold-246"}}, "user": {"User_Level": {"value": "24", "key": "User_Level"}, "skipprop_Give": {"value": "186", "key": "skipprop_Give"}, "taskprgs_Give": {"value": "51", "key": "taskprgs_Give"}, "gem_Buy": {"value": "0", "key": "gem_Buy"}, "skipprop_Buy": {"value": "0", "key": "skipprop_Buy"}, "gold_Buy": {"value": "0", "key": "gold_Buy"}, "gem_Give": {"value": "31", "key": "gem_Give"}, "exp_Buy": {"value": "0", "key": "exp_Buy"}, "gold_Give": {"value": "1729", "key": "gold_Give"}, "exp_Give": {"value": "0", "key": "exp_Give"}}}