import os
from openpyxl import load_workbook, Workbook
from openpyxl.utils import get_column_letter
from pathlib import Path

def compare_sheets(ws1, ws2, sheet1_name, sheet2_name):
    """比较两个sheet的差异并返回对比结果"""
    # 获取表头
    headers = [cell.value for cell in ws1[1]]
    
    # 创建字典来存储每个sheet的数据，使用Id作为键
    data1 = {row[0].value: [cell.value for cell in row] for row in ws1.iter_rows(min_row=2)}
    data2 = {row[0].value: [cell.value for cell in row] for row in ws2.iter_rows(min_row=2)}
    
    # 记录所有出现的Id
    all_ids = sorted(set(list(data1.keys()) + list(data2.keys())))
    
    # 存储对比结果
    compare_data = []
    
    for id_value in all_ids:
        if id_value in data1 and id_value in data2:
            # 两个sheet都有这个Id
            row1 = data1[id_value]
            row2 = data2[id_value]
            if row1 != row2:
                # 数据不同，记录两行
                compare_data.append(row1 + [f'In {sheet1_name}'])
                compare_data.append(row2 + [f'In {sheet2_name}'])
        elif id_value in data1:
            # 只在sheet1中有
            compare_data.append(data1[id_value] + [f'Only in {sheet1_name}'])
        else:
            # 只在sheet2中有
            compare_data.append(data2[id_value] + [f'Only in {sheet2_name}'])
    
    return headers, compare_data

def write_comparison(headers, compare_data, output_file):
    """将对比结果写入新的Excel文件"""
    wb = Workbook()
    ws = wb.active
    ws.title = 'Comparison'
    
    # 写入表头
    headers.append('Diff_Type')
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header)
    
    # 写入数据
    for row_idx, row_data in enumerate(compare_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            ws.cell(row=row_idx, column=col_idx, value=value)
    
    # 调整列宽
    for col in range(1, len(headers) + 1):
        ws.column_dimensions[get_column_letter(col)].width = 20
    
    wb.save(output_file)
    print(f"对比结果已保存到: {output_file}")

def main():
    print("Excel对比工具")
    print("="*50)
    
    # 获取第一个Excel文件
    while True:
        file1 = input("请输入第一个Excel文件的路径: ").strip('"')
        if os.path.exists(file1):
            break
        print("文件不存在，请重新输入")
    
    # 获取第二个Excel文件
    while True:
        file2 = input("请输入第二个Excel文件的路径: ").strip('"')
        if os.path.exists(file2):
            break
        print("文件不存在，请重新输入")
    
    # 加载工作簿
    wb1 = load_workbook(file1)
    wb2 = load_workbook(file2)
    
    # 显示可用的sheet
    print(f"\n第一个文件的sheet列表:")
    for i, sheet in enumerate(wb1.sheetnames, 1):
        print(f"{i}. {sheet}")
    
    while True:
        try:
            sheet1_idx = int(input("请选择第一个sheet的序号: ")) - 1
            if 0 <= sheet1_idx < len(wb1.sheetnames):
                break
            print("无效的序号，请重新输入")
        except ValueError:
            print("请输入有效的数字")
    
    print(f"\n第二个文件的sheet列表:")
    for i, sheet in enumerate(wb2.sheetnames, 1):
        print(f"{i}. {sheet}")
    
    while True:
        try:
            sheet2_idx = int(input("请选择第二个sheet的序号: ")) - 1
            if 0 <= sheet2_idx < len(wb2.sheetnames):
                break
            print("无效的序号，请重新输入")
        except ValueError:
            print("请输入有效的数字")
    
    # 获取选择的sheet
    sheet1_name = wb1.sheetnames[sheet1_idx]
    sheet2_name = wb2.sheetnames[sheet2_idx]
    ws1 = wb1[sheet1_name]
    ws2 = wb2[sheet2_name]
    
    # 进行对比
    print("\n开始对比...")
    headers, compare_data = compare_sheets(ws1, ws2, sheet1_name, sheet2_name)
    
    # 生成输出文件名
    output_file = os.path.join(os.path.expanduser("~"), "Desktop", 
                              f"Compare_{sheet1_name}_vs_{sheet2_name}.xlsx")
    
    # 写入结果
    write_comparison(headers, compare_data, output_file)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
    finally:
        input("\n按Enter键退出...") 