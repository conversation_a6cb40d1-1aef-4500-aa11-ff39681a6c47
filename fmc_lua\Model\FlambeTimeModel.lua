FlambeTimeModel = {}
FlambeTimeModel.__index = FlambeTimeModel
FlambeTimeConfigKey = "flambeTime"
FlambeTypeConfig = "fromOrderGroupConfig"
LinkInstruSpeed = "linkInstruSpeed"
ModeInstruSpeed = "modeInstruSpeed"
FlambeTimeModeTriggerCount = 5
FlambeTimeDuration = 7200
EFlambeTimeType = {mode = "mode", link = "link"}

function FlambeTimeModel:LateInit()
  self:_LoadFromDB()
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self._OnLoginFinished)
end

function FlambeTimeModel:Destroy()
  EventDispatcher.RemoveTarget(self)
end

function FlambeTimeModel:_OnLoginFinished()
  self.m_serverConfig = nil
end

function FlambeTimeModel:_ParseConfig()
  if self.m_serverConfig ~= nil then
    return self.m_serverConfig
  end
  local config = GM.ConfigModel:GetServerConfig(FlambeTimeConfigKey) or {}
  self.m_serverConfig = {}
  self.m_serverConfig.eTime = config.eTime
  local arrGeneralActivityConf = config.generalActivityConf
  self.m_serverConfig.generalActivityConf = {}
  if arrGeneralActivityConf then
    for _, conf in ipairs(arrGeneralActivityConf) do
      Log.Assert(self.m_serverConfig.generalActivityConf[conf.confType] == nil, "Same confType overwrite!" .. tostring(conf.confType))
      self.m_serverConfig.generalActivityConf[conf.confType] = conf
    end
  end
  return self.m_serverConfig
end

function FlambeTimeModel:_LoadFromDB()
  self.m_bIsFlambeTimeOrderGroup = GM.MiscModel:GetInNumber(EMiscKey.IsFlambeTimeOrderGroup) == 1
  self.m_eFlambeTimeType = GM.MiscModel:Get(EMiscKey.FlambeTimeType)
  if EFlambeTimeType[self.m_eFlambeTimeType] == nil then
    self.m_eFlambeTimeType = EFlambeTimeType.mode
  end
  self.m_nFlambeTimeFinishTime = GM.MiscModel:GetInNumber(EMiscKey.FlambeTimeFinishTime)
  self.m_arrFlambeTimePDChains = StringUtil.Split(GM.MiscModel:Get(EMiscKey.FlambeTimePDChains), ";")
  self.m_arrFlambeTimeInstruChains = StringUtil.Split(GM.MiscModel:Get(EMiscKey.FlambeTimeInstruChains), ";")
  self.m_nFlambeTimeInstruSpeed = GM.MiscModel:GetInNumber(EMiscKey.FlambeTimeInstruSpeed) or 1
  self.m_arrFlambeTimeLinkOrderId = StringUtil.Split(GM.MiscModel:Get(EMiscKey.FlambeTimeLinkOrder), ";")
end

function FlambeTimeModel:_SaveToDB()
  GM.MiscModel:Set(EMiscKey.IsFlambeTimeOrderGroup, self.m_bIsFlambeTimeOrderGroup and 1 or 0)
  GM.MiscModel:Set(EMiscKey.FlambeTimeType, self.m_eFlambeTimeType)
  GM.MiscModel:Set(EMiscKey.FlambeTimeFinishTime, self.m_nFlambeTimeFinishTime)
  GM.MiscModel:Set(EMiscKey.FlambeTimePDChains, table.concat(self.m_arrFlambeTimePDChains, ";"))
  GM.MiscModel:Set(EMiscKey.FlambeTimeInstruChains, table.concat(self.m_arrFlambeTimeInstruChains, ";"))
  GM.MiscModel:Set(EMiscKey.FlambeTimeInstruSpeed, self.m_nFlambeTimeInstruSpeed)
  GM.MiscModel:Set(EMiscKey.FlambeTimeLinkOrder, table.concat(self.m_arrFlambeTimeLinkOrderId, ";"))
end

function FlambeTimeModel:IsFlambeTimeOrderGroup()
  return self.m_bIsFlambeTimeOrderGroup
end

function FlambeTimeModel:GetFlambeTimeType()
  return self.m_eFlambeTimeType
end

function FlambeTimeModel:IsFlambeTime()
  return self:GetFlambeTimeRemainDur() ~= nil
end

function FlambeTimeModel:IsOrderShowFire(orderId)
  if not self:IsFlambeTime() then
    return false
  end
  if self.m_eFlambeTimeType == EFlambeTimeType.mode then
    return true
  elseif self.m_eFlambeTimeType == EFlambeTimeType.link and self:GetLinkOrderId() == orderId then
    return true
  end
  return false
end

function FlambeTimeModel:GetLinkOrderId()
  if self:IsFlambeTime() then
    return self.m_arrFlambeTimeLinkOrderId[#self.m_arrFlambeTimeLinkOrderId]
  end
end

function FlambeTimeModel:GetAndResetCurFiredItemChains()
  local result = self.m_arrCurFiredChains
  self.m_arrCurFiredChains = nil
  return result
end

function FlambeTimeModel:NeedPlayOrderLinkAnim(orderId)
  if not self:IsFlambeTimeOrderGroup() or self:GetFlambeTimeType() ~= EFlambeTimeType.link then
    return false
  end
  local isLink = self.m_strCurFiredOrderId == orderId
  self.m_strCurFiredOrderId = nil
  return isLink
end

function FlambeTimeModel:GetFlambeTimeRemainDur()
  if not self:IsFlambeTimeOrderGroup() or self.m_nFlambeTimeFinishTime == 0 then
    return
  end
  return self.m_nFlambeTimeFinishTime - GM.GameModel:GetServerTime()
end

function FlambeTimeModel:OnNewOrderGroupReleased(orderConfigs, groupConfig)
  local config = self:_ParseConfig()
  local isOpen = config ~= nil and GM.GameModel:GetServerTime() < (config.eTime or 0)
  self.m_bIsFlambeTimeOrderGroup = isOpen and orderConfigs ~= nil and #orderConfigs == FlambeTimeModeTriggerCount + 2
  self.m_eFlambeTimeType = EFlambeTimeType.mode
  local generalActivityConf = config.generalActivityConf
  if generalActivityConf[FlambeTypeConfig] ~= nil and generalActivityConf[FlambeTypeConfig][EConfigParamType.Int] == 1 then
    local type = groupConfig and groupConfig.Flambe
    if EFlambeTimeType[type] ~= nil then
      self.m_eFlambeTimeType = type
    end
  end
  local instruKey = ModeInstruSpeed
  if self.m_eFlambeTimeType == EFlambeTimeType.link then
    instruKey = LinkInstruSpeed
  end
  self.m_nFlambeTimeInstruSpeed = 1
  if generalActivityConf[instruKey] ~= nil and generalActivityConf[instruKey][EConfigParamType.Int] ~= nil then
    self.m_nFlambeTimeInstruSpeed = generalActivityConf[instruKey][EConfigParamType.Int]
    if 1 > self.m_nFlambeTimeInstruSpeed then
      Log.Error("配置错误，厨具加速倍数不能小于1")
      self.m_nFlambeTimeInstruSpeed = 1
    end
  end
  self.m_nFlambeTimeFinishTime = 0
  self.m_arrFlambeTimePDChains = {}
  self.m_arrFlambeTimeInstruChains = {}
  self.m_arrFlambeTimeLinkOrderId = {}
  self:_SaveToDB()
  EventDispatcher.DispatchEvent(EEventType.FlambeTimeChanged)
end

function FlambeTimeModel:OnOrderGroupFinished()
  self:_StopFlambeTime()
end

function FlambeTimeModel:_StopFlambeTime()
  if not self:IsFlambeTimeOrderGroup() then
    return
  end
  self.m_bIsFlambeTimeOrderGroup = false
  self.m_eFlambeTimeType = EFlambeTimeType.mode
  self.m_nFlambeTimeFinishTime = 0
  self.m_arrFlambeTimePDChains = {}
  self.m_arrFlambeTimeInstruChains = {}
  self.m_nFlambeTimeInstruSpeed = 1
  self.m_arrFlambeTimeLinkOrderId = {}
  self:_SaveToDB()
  EventDispatcher.DispatchEvent(EEventType.FlambeTimeChanged)
end

function FlambeTimeModel:IsFlambeTimeSpreader(itemCode)
  if not self:IsFlambeTimeOrderGroup() or self.m_nFlambeTimeFinishTime == 0 then
    return false
  end
  local chain = GM.ItemDataModel:GetChainId(itemCode)
  if Table.ListContain(self.m_arrFlambeTimePDChains, chain) then
    return true
  end
  return false
end

function FlambeTimeModel:IsFlambeTimeInstru(itemCode)
  if not self:IsFlambeTimeOrderGroup() or self.m_nFlambeTimeFinishTime == 0 then
    return false
  end
  local chain = GM.ItemDataModel:GetChainId(itemCode)
  if Table.ListContain(self.m_arrFlambeTimeInstruChains, chain) then
    return true
  end
  return false
end

function FlambeTimeModel:GetInBoardFiredItems(sort)
  local items = GM.MainBoardModel:FilterItems(function(itemModel)
    local spread = itemModel:GetComponent(ItemSpread)
    if spread then
      return spread:IsFlambeTime()
    end
    local cook = itemModel:GetComponent(ItemCook)
    if cook then
      return cook:IsFlambeTime()
    end
    return false
  end)
  if sort then
    table.sort(items, function(a, b)
      local posA = a:GetPosition()
      local posB = b:GetPosition()
      if posA:GetY() ~= posB:GetY() then
        return posA:GetY() < posB:GetY()
      elseif posA:GetX() ~= posB:GetX() then
        return posA:GetX() < posB:GetX()
      end
    end)
  end
  return items
end

function FlambeTimeModel:_GetInBoardFiredItemCodes()
  local items = self:GetInBoardFiredItems()
  local itemCodes = {}
  for _, item in ipairs(items) do
    itemCodes[#itemCodes + 1] = item:GetCode()
  end
  return itemCodes
end

function FlambeTimeModel:GetFlambeTimeInstruSpeed()
  return self.m_nFlambeTimeInstruSpeed
end

function FlambeTimeModel:UpdatePerSecond()
  if not self:IsFlambeTimeOrderGroup() or self.m_nFlambeTimeFinishTime == 0 then
    return
  end
  if GM.GameModel:GetServerTime() <= self.m_nFlambeTimeFinishTime then
    return
  end
  EventDispatcher.DispatchEvent(EEventType.BeforeFlambeEnded, self.m_nFlambeTimeFinishTime)
  if self.m_eFlambeTimeType == EFlambeTimeType.link then
    local _, remainOrderCount = self:GetFinishedOrderCount()
    if remainOrderCount <= 1 then
      self:_StopFlambeTime()
    else
      self.m_nFlambeTimeFinishTime = 0
      self.m_arrFlambeTimePDChains = {}
      self.m_arrFlambeTimeInstruChains = {}
      self:_SaveToDB()
      EventDispatcher.DispatchEvent(EEventType.FlambeTimeChanged)
    end
  else
    self:_StopFlambeTime()
  end
end

function FlambeTimeModel:_GetOrderPDAndInstruChains(selectOrder)
  local pdChains = {}
  local instruChains = {}
  local directDishes, directNonDishes, indirectDishes, indirectNonDishes = GM.MainBoardModel:GetOrderRequirements(true, selectOrder)
  for _, requires in ipairs({directNonDishes, indirectNonDishes}) do
    for require, _ in pairs(requires) do
      self:_GetPDGenerator(require, pdChains)
    end
  end
  local instru, instruChain
  for _, requires in ipairs({directDishes, indirectDishes}) do
    for require, _ in pairs(requires) do
      instru = GM.ItemDataModel:GetModelConfig(require).Instrument[1].Instru
      instruChain = GM.ItemDataModel:GetChainId(instru)
      instruChains[instruChain] = true
    end
  end
  return Table.GetKeys(pdChains), Table.GetKeys(instruChains)
end

function FlambeTimeModel:_GetPDGenerator(itemCode, pdChains)
  local generator = GM.ItemDataModel:GetItemGenerators(itemCode)
  if not generator then
    return
  end
  for _, gen in ipairs(generator) do
    local chain = GM.ItemDataModel:GetChainId(gen)
    if GM.ItemDataModel:IsPd(chain) then
      pdChains[chain] = true
    else
      self:_GetPDGenerator(gen, pdChains)
    end
  end
end

function FlambeTimeModel:OnOrderFinished()
  if not self:IsFlambeTimeOrderGroup() then
    return
  end
  if self.m_eFlambeTimeType == EFlambeTimeType.mode then
    self:_TryStartFlambeTimeModeType()
  elseif self.m_eFlambeTimeType == EFlambeTimeType.link then
    self:_TryStartFlambeTimeLinkType()
  end
end

function FlambeTimeModel:GetFinishedOrderCount()
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local finishedCount, totalCount = orderModel:GetGroupProgress()
  return finishedCount, totalCount - finishedCount
end

function FlambeTimeModel:_TryStartFlambeTimeModeType()
  if self:IsFlambeTime() then
    return
  end
  if self:GetFinishedOrderCount() == FlambeTimeModeTriggerCount then
    self:_StartFlambeTimeModeType()
  end
end

function FlambeTimeModel:_StartFlambeTimeModeType()
  self.m_nFlambeTimeFinishTime = GM.GameModel:GetServerTime() + FlambeTimeDuration
  local pdChains, instruChains = self:_GetOrderPDAndInstruChains()
  self.m_arrFlambeTimePDChains = pdChains
  if self.m_nFlambeTimeInstruSpeed > 1 then
    self.m_arrFlambeTimeInstruChains = instruChains
  end
  self:_SaveToDB()
  local orders = GM.MainBoardModel:GetOrders()
  local orderIds = {}
  for id, _ in pairs(orders) do
    orderIds[#orderIds + 1] = id
  end
  local biDict = {
    order_id = table.concat(orderIds, ","),
    items = table.concat(self:_GetInBoardFiredItemCodes(), ","),
    t = FlambeTimeDuration
  }
  GM.BIManager:LogAction(EBIType.StartFlambeMode, GM.BIManager:TableToString(biDict))
  EventDispatcher.DispatchEvent(EEventType.FlambeTimePopup)
  if GM.ConfigModel:CanNewOrderRewardAnimationSkip() then
    return
  end
  GM.UIManager:SetEventLockUntilNextPopup()
end

function FlambeTimeModel:_TryStartFlambeTimeLinkType()
  local orders = GM.MainBoardModel:GetOrders()
  local nextTargetOrder = self:_GetNextTargetOrder(orders)
  if not nextTargetOrder then
    return
  end
  local curLinkId = self:GetLinkOrderId()
  if curLinkId == nil and self:GetFinishedOrderCount() >= 1 or curLinkId ~= nil and orders[curLinkId] == nil then
    self:_StartFlambeTimeLinkType(nextTargetOrder)
  end
end

function FlambeTimeModel:_StartFlambeTimeLinkType(nextTargetOrder)
  self.m_strCurFiredOrderId = self:GetLinkOrderId() or ""
  self.m_arrCurFiredChains = {}
  Table.ListAppend(self.m_arrCurFiredChains, self.m_arrFlambeTimePDChains)
  Table.ListAppend(self.m_arrCurFiredChains, self.m_arrFlambeTimeInstruChains)
  self.m_arrFlambeTimeLinkOrderId[#self.m_arrFlambeTimeLinkOrderId + 1] = nextTargetOrder:GetId()
  self.m_nFlambeTimeFinishTime = GM.GameModel:GetServerTime() + FlambeTimeDuration
  local pdChains, instruChains = self:_GetOrderPDAndInstruChains(nextTargetOrder)
  self.m_arrFlambeTimePDChains = pdChains
  if 1 < self.m_nFlambeTimeInstruSpeed then
    self.m_arrFlambeTimeInstruChains = instruChains
  end
  self:_SaveToDB()
  local biDict = {
    id = #self.m_arrFlambeTimeLinkOrderId,
    order_id = nextTargetOrder:GetId(),
    items = table.concat(self:_GetInBoardFiredItemCodes(), ","),
    t = FlambeTimeDuration
  }
  GM.BIManager:LogAction(EBIType.StartFlambeLink, GM.BIManager:TableToString(biDict))
  if self.m_strCurFiredOrderId == "" then
    EventDispatcher.DispatchEvent(EEventType.FlambeTimePopup)
    if GM.ConfigModel:CanNewOrderRewardAnimationSkip() then
      return
    end
    GM.UIManager:SetEventLockUntilNextPopup()
  end
end

function FlambeTimeModel:_GetNextTargetOrder(orders)
  local energyDiff, minEnergyDiff, nextTargetOrder
  for id, order in pairs(orders) do
    energyDiff = GM.MainBoardModel:GetOrderFillEnergyDiff(order)
    if minEnergyDiff == nil or minEnergyDiff > energyDiff then
      minEnergyDiff = energyDiff
      nextTargetOrder = order
    end
  end
  return nextTargetOrder
end
