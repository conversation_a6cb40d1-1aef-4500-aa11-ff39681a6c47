HudGeneralButton = setmetatable({}, HudBaseButton)
HudGeneralButton.__index = HudGeneralButton

function HudGeneralButton:Awake()
  HudBaseButton.Awake(self)
  self.m_originScale = self.transform.localScale
  self.m_bDisplay = true
  self:OnGameModeChanged()
  EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self.OnGameModeChanged)
end

function HudGeneralButton:OnDestroy()
  self.transform:DOKill(false)
  EventDispatcher.RemoveTarget(self)
end

function HudGeneralButton:OnGameModeChanged()
  if self:_NeedDisplay() then
    if not self.m_bDisplay then
      self.m_bDisplay = true
      self:_OnDisplay()
      self.transform.localScale = self.m_originScale
    end
  elseif self.m_bDisplay then
    self.m_bDisplay = false
    if self.m_seq then
      self.m_seq:Kill()
      self.m_seq = nil
      self.m_bScaled = false
    end
    self.transform.localScale = V3Zero
  end
end

function HudGeneralButton:_NeedDisplay()
  if self.m_strDisplayGameMode == "all" then
    return true
  end
  if self.m_strDisplayGameMode == nil or EGameMode[self.m_strDisplayGameMode] == nil then
    return false
  end
  return GM.SceneManager:GetGameMode() == EGameMode[self.m_strDisplayGameMode]
end

function HudGeneralButton:_OnDisplay()
end

function HudGeneralButton:IconScaleAnimation(needEffect)
  if self.m_bDisplay then
    HudBaseButton.IconScaleAnimation(self, needEffect)
  end
end

function HudGeneralButton:PlayClickEffect()
  if string.len(self.m_strSoundEffectName) > 0 then
    GM.AudioModel:PlayEffect(self.m_strSoundEffectName)
  end
end

function HudGeneralButton:SetEnabled(enabled)
  self.m_button.interactable = enabled
end

HudCountDownButton = setmetatable({}, HudGeneralButton)
HudCountDownButton.__index = HudCountDownButton

function HudCountDownButton:UpdatePerSecond()
  if self.m_finishTime ~= nil then
    self.m_countDownText.text = TimeUtil.ParseTimeDescription(math.max(0, self.m_finishTime - GM.GameModel:GetServerTime()), 2, false, false)
  end
end
