ShopContainerDailyDeals = setmetatable({}, BaseShopListContainer)
ShopContainerDailyDeals.__index = ShopContainerDailyDeals

function ShopContainerDailyDeals:Awake()
  BaseShopListContainer.Init(self, EShopType.DailyDeals, false, 3)
end

function ShopContainerDailyDeals:_OnAdEnd(message)
  if message.eAdType == EAdType.ShopBox and message.bSuccess then
    for _, cell in pairs(self.m_cells) do
      if cell:GetData().Code == GM.ShopDataModel:GetDailyDealsAdItemCode() then
        GM.ShopModel:BuyItem(self.m_shopType, cell:GetData().Id, true)
        GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, {
          {
            [PROPERTY_TYPE] = GM.ShopDataModel:GetDailyDealsAdItemCode(),
            [PROPERTY_COUNT] = 1
          }
        }, "rewards_window_title_shop", false)
        GM.AudioModel:PlayEffect(AudioFileConfigName.SfxShopBuy)
      end
    end
  end
  self:_UpdateContent()
end

function ShopContainerDailyDeals:_GetCellData()
  local data = {}
  local ids = GM.ShopModel:GetItemIdsDailyDeals() or {}
  for _, id in ipairs(ids) do
    local itemData = GM.ShopModel:GetItemData(id)
    table.insert(data, {
      Id = id,
      Code = itemData.itemCode,
      Count = itemData.leftCount,
      Price = itemData.costCount
    })
  end
  return data
end

function ShopContainerDailyDeals:_OnCellClicked(cell)
  local success, type, count = GM.ShopModel:BuyItem(self.m_shopType, cell:GetData().Id, false, cell.transform.position)
  if success then
    self:_UpdateContent()
  else
    GM.ShopModel:OnLackOfGem(count)
  end
end
