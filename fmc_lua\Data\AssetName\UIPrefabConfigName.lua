UIPrefabConfigName = {
  AccountNoticeWindow = "AccountNoticeWindow",
  AccountStatusWindow = "AccountStatusWindow",
  AddItemChangeInfoWindow = "AddItemChangeInfoWindow",
  AvatarSelectWindow = "AvatarSelectWindow",
  BakeOutBubble = "BakeOutBubble",
  BakeOutCoinExchangeWindow = "BakeOutCoinExchangeWindow",
  BakeOutDetailWindow = "BakeOutDetailWindow",
  BakeOutEntry = "BakeOutEntry",
  BakeOutInSettlementWindow = "BakeOutInSettlementWindow",
  BakeOutMainWindow = "BakeOutMainWindow",
  BakeOutOwnRankCell = "BakeOutOwnRankCell",
  BakeOutRankCell = "BakeOutRankCell",
  BakeOutRankCellTransparent = "BakeOutRankCellTransparent",
  BakeOutReadyWindow = "BakeOutReadyWindow",
  BakeOutResultWindow = "BakeOutResultWindow",
  BakeOutTopThreeWindow = "BakeOutTopThreeWindow",
  BaseSceneView = "BaseSceneView",
  BlindChest1BoardEntry = "BlindChest1BoardEntry",
  BlindChest1ChainBundleButton = "BlindChest1ChainBundleButton",
  BlindChest1ChainBundleWindow = "BlindChest1ChainBundleWindow",
  BlindChest1EndConfirmWindow = "BlindChest1EndConfirmWindow",
  BlindChest1EndWindow = "BlindChest1EndWindow",
  BlindChest1Entry = "BlindChest1Entry",
  BlindChest1HelpWindow = "BlindChest1HelpWindow",
  BlindChest1MainWindow = "BlindChest1MainWindow",
  BlindChest1OnePlusNBundleButton = "BlindChest1OnePlusNBundleButton",
  BlindChest1OnePlusNBundleWindow = "BlindChest1OnePlusNBundleWindow",
  BlindChest1ReadyWindow = "BlindChest1ReadyWindow",
  BlindChest1SuccessWindow = "BlindChest1SuccessWindow",
  BlindChestFinalRewardWindow = "BlindChestFinalRewardWindow",
  BlindChestFlyItem = "BlindChestFlyItem",
  BlindChestTopRewardWindow = "BlindChestTopRewardWindow",
  BoardBoostPrompt = "BoardBoostPrompt",
  BoxRewardWindow = "BoxRewardWindow",
  BP1ActivityBoardBubble = "BP1ActivityBoardBubble",
  BP1BuyTicketPopupWindow = "BP1BuyTicketPopupWindow",
  BP1BuyTicketSuccessWindow2 = "BP1BuyTicketSuccessWindow2",
  BP1BuyTicketWindow = "BP1BuyTicketWindow",
  BP1CycleNoticeBubble = "BP1CycleNoticeBubble",
  BP1EndWindow = "BP1EndWindow",
  BP1Entry = "BP1Entry",
  BP1FinishTaskWindow = "BP1FinishTaskWindow",
  BP1MainWindow = "BP1MainWindow",
  BP1NewTimelimitTaskWindow = "BP1NewTimelimitTaskWindow",
  BP1ReadyWindow = "BP1ReadyWindow",
  BP1RewardRecoverWindow = "BP1RewardRecoverWindow",
  BP1ShopCell = "BP1ShopCell",
  BP1TimelimitNoticeBubble = "BP1TimelimitNoticeBubble",
  BP1TokenWindow = "BP1TokenWindow",
  BP2ActivityBoardBubble = "BP2ActivityBoardBubble",
  BP2BuyTicketPopupWindow = "BP2BuyTicketPopupWindow",
  BP2BuyTicketSuccessWindow2 = "BP2BuyTicketSuccessWindow2",
  BP2BuyTicketWindow = "BP2BuyTicketWindow",
  BP2CycleNoticeBubble = "BP2CycleNoticeBubble",
  BP2EndWindow = "BP2EndWindow",
  BP2Entry = "BP2Entry",
  BP2FinishTaskWindow = "BP2FinishTaskWindow",
  BP2MainWindow = "BP2MainWindow",
  BP2NewTimelimitTaskWindow = "BP2NewTimelimitTaskWindow",
  BP2ReadyWindow = "BP2ReadyWindow",
  BP2RewardRecoverWindow = "BP2RewardRecoverWindow",
  BP2ShopCell = "BP2ShopCell",
  BP2TimelimitNoticeBubble = "BP2TimelimitNoticeBubble",
  BP2TokenWindow = "BP2TokenWindow",
  BP3ActivityBoardBubble = "BP3ActivityBoardBubble",
  BP3BuyTicketPopupWindow = "BP3BuyTicketPopupWindow",
  BP3BuyTicketSuccessWindow2 = "BP3BuyTicketSuccessWindow2",
  BP3BuyTicketWindow = "BP3BuyTicketWindow",
  BP3CycleNoticeBubble = "BP3CycleNoticeBubble",
  BP3EndWindow = "BP3EndWindow",
  BP3Entry = "BP3Entry",
  BP3FinishTaskWindow = "BP3FinishTaskWindow",
  BP3MainWindow = "BP3MainWindow",
  BP3NewTimelimitTaskWindow = "BP3NewTimelimitTaskWindow",
  BP3ReadyWindow = "BP3ReadyWindow",
  BP3RewardRecoverWindow = "BP3RewardRecoverWindow",
  BP3ShopCell = "BP3ShopCell",
  BP3TimelimitNoticeBubble = "BP3TimelimitNoticeBubble",
  BP3TokenWindow = "BP3TokenWindow",
  BP4ActivityBoardBubble = "BP4ActivityBoardBubble",
  BP4BuyTicketPopupWindow = "BP4BuyTicketPopupWindow",
  BP4BuyTicketSuccessWindow2 = "BP4BuyTicketSuccessWindow2",
  BP4BuyTicketWindow = "BP4BuyTicketWindow",
  BP4CycleNoticeBubble = "BP4CycleNoticeBubble",
  BP4EndWindow = "BP4EndWindow",
  BP4Entry = "BP4Entry",
  BP4FinishTaskWindow = "BP4FinishTaskWindow",
  BP4MainWindow = "BP4MainWindow",
  BP4NewTimelimitTaskWindow = "BP4NewTimelimitTaskWindow",
  BP4ReadyWindow = "BP4ReadyWindow",
  BP4RewardRecoverWindow = "BP4RewardRecoverWindow",
  BP4ShopCell = "BP4ShopCell",
  BP4TimelimitNoticeBubble = "BP4TimelimitNoticeBubble",
  BP4TokenWindow = "BP4TokenWindow",
  BP5ActivityBoardBubble = "BP5ActivityBoardBubble",
  BP5BuyMaxTicketSuccessWindow = "BP5BuyMaxTicketSuccessWindow",
  BP5BuyMaxTicketWindow = "BP5BuyMaxTicketWindow",
  BP5BuyTicketPopupWindow = "BP5BuyTicketPopupWindow",
  BP5BuyTicketSuccessWindow2 = "BP5BuyTicketSuccessWindow2",
  BP5BuyTicketWindow = "BP5BuyTicketWindow",
  BP5BuyUpTicketSuccessWindow = "BP5BuyUpTicketSuccessWindow",
  BP5BuyUpTicketWindow = "BP5BuyUpTicketWindow",
  BP5CycleNoticeBubble = "BP5CycleNoticeBubble",
  BP5EndWindow = "BP5EndWindow",
  BP5Entry = "BP5Entry",
  BP5FinishTaskWindow = "BP5FinishTaskWindow",
  BP5MainWindow = "BP5MainWindow",
  BP5NewTimelimitTaskWindow = "BP5NewTimelimitTaskWindow",
  BP5ReadyWindow = "BP5ReadyWindow",
  BP5RewardRecoverWindow = "BP5RewardRecoverWindow",
  BP5ShopCell = "BP5ShopCell",
  BP5TimelimitNoticeBubble = "BP5TimelimitNoticeBubble",
  BP5TokenWindow = "BP5TokenWindow",
  BP6ActivityBoardBubble = "BP6ActivityBoardBubble",
  BP6BuyTicketPopupWindow = "BP6BuyTicketPopupWindow",
  BP6BuyTicketSuccessWindow2 = "BP6BuyTicketSuccessWindow2",
  BP6BuyTicketWindow = "BP6BuyTicketWindow",
  BP6CycleNoticeBubble = "BP6CycleNoticeBubble",
  BP6EndWindow = "BP6EndWindow",
  BP6Entry = "BP6Entry",
  BP6FinishTaskWindow = "BP6FinishTaskWindow",
  BP6MainWindow = "BP6MainWindow",
  BP6NewTimelimitTaskWindow = "BP6NewTimelimitTaskWindow",
  BP6ReadyWindow = "BP6ReadyWindow",
  BP6RewardRecoverWindow = "BP6RewardRecoverWindow",
  BP6ShopCell = "BP6ShopCell",
  BP6TimelimitNoticeBubble = "BP6TimelimitNoticeBubble",
  BP6TokenWindow = "BP6TokenWindow",
  BP7ActivityBoardBubble = "BP7ActivityBoardBubble",
  BP7BuyMaxTicketSuccessWindow = "BP7BuyMaxTicketSuccessWindow",
  BP7BuyMaxTicketWindow = "BP7BuyMaxTicketWindow",
  BP7BuyTicketPopupWindow = "BP7BuyTicketPopupWindow",
  BP7BuyTicketSuccessWindow2 = "BP7BuyTicketSuccessWindow2",
  BP7BuyTicketWindow = "BP7BuyTicketWindow",
  BP7BuyUpTicketSuccessWindow = "BP7BuyUpTicketSuccessWindow",
  BP7BuyUpTicketWindow = "BP7BuyUpTicketWindow",
  BP7CycleNoticeBubble = "BP7CycleNoticeBubble",
  BP7EndWindow = "BP7EndWindow",
  BP7Entry = "BP7Entry",
  BP7FinishTaskWindow = "BP7FinishTaskWindow",
  BP7MainWindow = "BP7MainWindow",
  BP7NewTimelimitTaskWindow = "BP7NewTimelimitTaskWindow",
  BP7ReadyWindow = "BP7ReadyWindow",
  BP7RewardRecoverWindow = "BP7RewardRecoverWindow",
  BP7ShopCell = "BP7ShopCell",
  BP7TimelimitNoticeBubble = "BP7TimelimitNoticeBubble",
  BP7TokenWindow = "BP7TokenWindow",
  BundleRewardWindow = "BundleRewardWindow",
  BuyEnergyDiscountWindow = "BuyEnergyDiscountWindow",
  BuyEnergyWindow = "BuyEnergyWindow",
  BuyEventEnergyWindow = "BuyEventEnergyWindow",
  CDBundleButton = "CDBundleButton",
  CDBundleWindow = "CDBundleWindow",
  CDFillBundleButton = "CDFillBundleButton",
  CDFillBundleWindow = "CDFillBundleWindow",
  ChapterFinishWindow = "ChapterFinishWindow",
  ChapterSelectWindow = "ChapterSelectWindow",
  ChapterTransition = "ChapterTransition",
  ChapterUnlockWindow = "ChapterUnlockWindow",
  CoinRaceBoardEntry = "CoinRaceBoardEntry",
  CoinRaceCompleteWindow = "CoinRaceCompleteWindow",
  CoinRaceEntry = "CoinRaceEntry",
  CoinRaceHelpWindow = "CoinRaceHelpWindow",
  CoinRaceMainWindow = "CoinRaceMainWindow",
  CoinRaceNoticeWindow = "CoinRaceNoticeWindow",
  CoinRaceRewardWindow = "CoinRaceRewardWindow",
  CPTaskCell = "CPTaskCell",
  CrossPromotionWindow = "CrossPromotionWindow",
  DataBalanceWindow = "DataBalanceWindow",
  DataConflictWindow = "DataConflictWindow",
  DeleteItemConfirmWindow = "DeleteItemConfirmWindow",
  DiscoveriesWindow = "DiscoveriesWindow",
  DownloadConfirmWindow = "DownloadConfirmWindow",
  effect_jb_1 = "effect_jb_1",
  effect_jy_1 = "effect_jy_1",
  effect_shop_tishi = "effect_shop_tishi",
  effect_tili_1 = "effect_tili_1",
  effect_tl_1 = "effect_tl_1",
  effect_UI_xingxing_1 = "effect_UI_xingxing_1",
  effect_UI_xingxing_mz = "effect_UI_xingxing_mz",
  effect_zs_1 = "effect_zs_1",
  effect_zs_11 = "effect_zs_11",
  EnergyBoostSettingWindow = "EnergyBoostSettingWindow",
  EnergyBundleButton = "EnergyBundleButton",
  EnergyBundleWindow = "EnergyBundleWindow",
  ExtraBoard1BoardEntry = "ExtraBoard1BoardEntry",
  ExtraBoard1EndWindow = "ExtraBoard1EndWindow",
  ExtraBoard1Entry = "ExtraBoard1Entry",
  ExtraBoard1MainWindow = "ExtraBoard1MainWindow",
  ExtraBoard1ReadyWindow = "ExtraBoard1ReadyWindow",
  ExtraBoard1RewardRecoverWindow = "ExtraBoard1RewardRecoverWindow",
  ExtraBoard2BoardEntry = "ExtraBoard2BoardEntry",
  ExtraBoard2EndWindow = "ExtraBoard2EndWindow",
  ExtraBoard2Entry = "ExtraBoard2Entry",
  ExtraBoard2LevelProgress = "ExtraBoard2LevelProgress",
  ExtraBoard2MainWindow = "ExtraBoard2MainWindow",
  ExtraBoard2ReadyWindow = "ExtraBoard2ReadyWindow",
  ExtraBoard2RewardRecoverWindow = "ExtraBoard2RewardRecoverWindow",
  ExtraBoard3BoardEntry = "ExtraBoard3BoardEntry",
  ExtraBoard3EndWindow = "ExtraBoard3EndWindow",
  ExtraBoard3Entry = "ExtraBoard3Entry",
  ExtraBoard3LevelProgress = "ExtraBoard3LevelProgress",
  ExtraBoard3MainWindow = "ExtraBoard3MainWindow",
  ExtraBoard3ReadyWindow = "ExtraBoard3ReadyWindow",
  ExtraBoard3RewardRecoverWindow = "ExtraBoard3RewardRecoverWindow",
  ExtraBoard4BoardEntry = "ExtraBoard4BoardEntry",
  ExtraBoard4EndWindow = "ExtraBoard4EndWindow",
  ExtraBoard4Entry = "ExtraBoard4Entry",
  ExtraBoard4LevelProgress = "ExtraBoard4LevelProgress",
  ExtraBoard4MainWindow = "ExtraBoard4MainWindow",
  ExtraBoard4ReadyWindow = "ExtraBoard4ReadyWindow",
  ExtraBoard4RewardRecoverWindow = "ExtraBoard4RewardRecoverWindow",
  ExtraBoard5BoardEntry = "ExtraBoard5BoardEntry",
  ExtraBoard5EndWindow = "ExtraBoard5EndWindow",
  ExtraBoard5Entry = "ExtraBoard5Entry",
  ExtraBoard5LevelProgress = "ExtraBoard5LevelProgress",
  ExtraBoard5MainWindow = "ExtraBoard5MainWindow",
  ExtraBoard5ReadyWindow = "ExtraBoard5ReadyWindow",
  ExtraBoard5RewardRecoverWindow = "ExtraBoard5RewardRecoverWindow",
  ExtraBoardActivityHelpWindow = "ExtraBoardActivityHelpWindow",
  ExtraBoardCompleteWindow = "ExtraBoardCompleteWindow",
  FlambeLinkHelpWindow = "FlambeLinkHelpWindow",
  FlambeModeHelpWindow = "FlambeModeHelpWindow",
  FlambeTimeWindow = "FlambeTimeWindow",
  FlyElement = "FlyElement",
  FlyElementWithTail = "FlyElementWithTail",
  FlyOrderDay = "FlyOrderDay",
  GeneralGemConfirmWindow = "GeneralGemConfirmWindow",
  GeneralMsgWindow = "GeneralMsgWindow",
  GoPlayWindow = "GoPlayWindow",
  HowToCookWindow = "HowToCookWindow",
  InputConfirmWindow = "InputConfirmWindow",
  InventoryFlyItem = "InventoryFlyItem",
  InventoryWindow = "InventoryWindow",
  ItemChooseWindow = "ItemChooseWindow",
  ItemDescDetailWindow = "ItemDescDetailWindow",
  ItemDetailWindow = "ItemDetailWindow",
  ItemDishDetailWindow = "ItemDishDetailWindow",
  ItemRecycleAnimationWindow = "ItemRecycleAnimationWindow",
  ItemRecycleWindow = "ItemRecycleWindow",
  ItemTypeDeleteNoticeWindow = "ItemTypeDeleteNoticeWindow",
  ItemTypeDeleteRewardsWindow = "ItemTypeDeleteRewardsWindow",
  LanguageCurrentCell = "LanguageCurrentCell",
  LanguageOptionCell = "LanguageOptionCell",
  LanguageSettingWindow = "LanguageSettingWindow",
  LevelDownConfirmWindow = "LevelDownConfirmWindow",
  MailDataWindow = "MailDataWindow",
  MaskLayer = "MaskLayer",
  MoreGameCell = "MoreGameCell",
  MoreGameWindow = "MoreGameWindow",
  MultiTierBundleButton = "MultiTierBundleButton",
  MultiTierBundleWindow = "MultiTierBundleWindow",
  NameWindow = "NameWindow",
  NetworkErrorWindow = "NetworkErrorWindow",
  NoticeContentWindow = "NoticeContentWindow",
  NoticeWindow = "NoticeWindow",
  NotificationOpenWindow = "NotificationOpenWindow",
  NotificationTutorialWindow = "NotificationTutorialWindow",
  OrderAnna = "OrderAnna",
  OrderDayBoxRewardWindow = "OrderDayBoxRewardWindow",
  OrderDayClearWindow = "OrderDayClearWindow",
  OrderDayRefreshWindow = "OrderDayRefreshWindow",
  OrderDayWindow = "OrderDayWindow",
  OrderFinishRewardWindow = "OrderFinishRewardWindow",
  OrderGourmet = "OrderGourmet",
  OrderGrandma = "OrderGrandma",
  OrderGrandpa = "OrderGrandpa",
  OrderGroupBundleButton = "OrderGroupBundleButton",
  OrderGroupBundleWindow = "OrderGroupBundleWindow",
  OrderGroupFinishWindow = "OrderGroupFinishWindow",
  OrderJenny = "OrderJenny",
  OrderKathy = "OrderKathy",
  OrderTom = "OrderTom",
  OrderZoey = "OrderZoey",
  PassActivityBuyTicketSuccessWindow1 = "PassActivityBuyTicketSuccessWindow1",
  PassActivityBuyTicketSuccessWindow3 = "PassActivityBuyTicketSuccessWindow3",
  PassActivityTaskOrderDay = "PassActivityTaskOrderDay",
  PkRaceBoardEntry = "PkRaceBoardEntry",
  PkRaceCompleteWindow = "PkRaceCompleteWindow",
  PkRaceEntry = "PkRaceEntry",
  PkRaceHelpWindow = "PkRaceHelpWindow",
  PkRaceMainWindow = "PkRaceMainWindow",
  PkRaceMatchWindow = "PkRaceMatchWindow",
  PkRaceNoticeWindow = "PkRaceNoticeWindow",
  PkRaceRewardWindow = "PkRaceRewardWindow",
  PkRaceRoundRewardWindow = "PkRaceRoundRewardWindow",
  PrivacyConfirmWindow = "PrivacyConfirmWindow",
  PrivacySettingWindow = "PrivacySettingWindow",
  ProfilingWindow = "ProfilingWindow",
  ProgressActivity1BoardEntry = "ProgressActivity1BoardEntry",
  ProgressActivity1Entry = "ProgressActivity1Entry",
  ProgressActivity1FinishFailWindow = "ProgressActivity1FinishFailWindow",
  ProgressActivity1FinishSuccessWindow = "ProgressActivity1FinishSuccessWindow",
  ProgressActivity1MainWindow = "ProgressActivity1MainWindow",
  ProgressActivity1NoticeWindow = "ProgressActivity1NoticeWindow",
  ProgressActivity1RewardProgressWindow = "ProgressActivity1RewardProgressWindow",
  Prompt = "Prompt",
  PromptOnWindow = "PromptOnWindow",
  PromptOnWindow2 = "PromptOnWindow2",
  PurchaseFailWindow = "PurchaseFailWindow",
  RateWindow = "RateWindow",
  ReloadConfirmWindow = "ReloadConfirmWindow",
  RemoveBubbleConfirmWindow = "RemoveBubbleConfirmWindow",
  ResourceDownloadWindow = "ResourceDownloadWindow",
  ReturnUserRewardWindow = "ReturnUserRewardWindow",
  RevertCookConfirmWindow = "RevertCookConfirmWindow",
  RewardWindow = "RewardWindow",
  ScrollBackToOrderWindow = "ScrollBackToOrderWindow",
  ServiceFloat = "ServiceFloat",
  SettingWindow = "SettingWindow",
  ShopWindow = "ShopWindow",
  SignInWindow = "SignInWindow",
  SignOutWindow = "SignOutWindow",
  SkipPropHelpWindow = "SkipPropHelpWindow",
  SpringChainBundleButton = "SpringChainBundleButton",
  SpringChainBundleWindow = "SpringChainBundleWindow",
  StarterBundleButton = "StarterBundleButton",
  StarterBundleWindow = "StarterBundleWindow",
  StoryWindow = "StoryWindow",
  SurpriseChestHelpWindow = "SurpriseChestHelpWindow",
  SurpriseChestMainWindow = "SurpriseChestMainWindow",
  SurpriseChestRewardBubble = "SurpriseChestRewardBubble",
  SurpriseChestView = "SurpriseChestView",
  SurveyFillBlank = "SurveyFillBlank",
  SurveyFillBlankImg = "SurveyFillBlankImg",
  SurveyMultiChoice = "SurveyMultiChoice",
  SurveyMultiChoiceFillBlank = "SurveyMultiChoiceFillBlank",
  SurveyMultiChoiceFillBlankImg = "SurveyMultiChoiceFillBlankImg",
  SurveyMultiChoiceImg = "SurveyMultiChoiceImg",
  SurveySingleChoice = "SurveySingleChoice",
  SurveySingleChoiceFillBlankImg = "SurveySingleChoiceFillBlankImg",
  SurveySingleChoiceImg = "SurveySingleChoiceImg",
  SurveyTipWindow = "SurveyTipWindow",
  SurveyWindow = "SurveyWindow",
  SystemGeneralMsgWindow = "SystemGeneralMsgWindow",
  TabView = "TabView",
  TaskClearWindow = "TaskClearWindow",
  TaskGroupFinishWindow = "TaskGroupFinishWindow",
  TaskWindow = "TaskWindow",
  TestAllItemContent = "TestAllItemContent",
  TestAutoTimelineView = "TestAutoTimelineView",
  TestBackupCell = "TestBackupCell",
  TestCenterPointView = "TestCenterPointView",
  TestCodeEditWindow = "TestCodeEditWindow",
  TestCommandView = "TestCommandView",
  TestDebugAddressWindow = "TestDebugAddressWindow",
  TestDigActivityToolWindow = "TestDigActivityToolWindow",
  TestFavoriteItemContent = "TestFavoriteItemContent",
  TestFileReplaceCell = "TestFileReplaceCell",
  TestFileReplaceWindow = "TestFileReplaceWindow",
  TestItemListWindow = "TestItemListWindow",
  TestLanguageSettingWindow = "TestLanguageSettingWindow",
  TestLocalBackupWindow = "TestLocalBackupWindow",
  TestLuaRemoteWindow = "TestLuaRemoteWindow",
  TestMapBorderToolView = "TestMapBorderToolView",
  TestMaskButton = "TestMaskButton",
  TestOrderEnergyDiffWindow = "TestOrderEnergyDiffWindow",
  TestOrderGroupEditWindow = "TestOrderGroupEditWindow",
  TestOrderGroupItemListWindow = "TestOrderGroupItemListWindow",
  TestOrderGroupWindow = "TestOrderGroupWindow",
  TestPlayerprefsWindow = "TestPlayerprefsWindow",
  TestRemoveItemWindow = "TestRemoveItemWindow",
  TestRequirementEditCell = "TestRequirementEditCell",
  TestRoleToolView = "TestRoleToolView",
  TestScreenDragView = "TestScreenDragView",
  TestServerBackupWindow = "TestServerBackupWindow",
  TestServerConfigInfoWindow = "TestServerConfigInfoWindow",
  TestTextWindow = "TestTextWindow",
  TestTutorialInfoWindow = "TestTutorialInfoWindow",
  TestTwoButtonWindow = "TestTwoButtonWindow",
  TestWindow = "TestWindow",
  TreasureDigBoardEntry = "TreasureDigBoardEntry",
  TreasureDigChainBundleButton = "TreasureDigChainBundleButton",
  TreasureDigChainBundleWindow = "TreasureDigChainBundleWindow",
  TreasureDigEndWindow = "TreasureDigEndWindow",
  TreasureDigEntry = "TreasureDigEntry",
  TreasureDigHelpWindow = "TreasureDigHelpWindow",
  TreasureDigMainWindow = "TreasureDigMainWindow",
  TreasureDigOnePlusNBundleButton = "TreasureDigOnePlusNBundleButton",
  TreasureDigOnePlusNBundleWindow = "TreasureDigOnePlusNBundleWindow",
  TreasureDigStartWindow = "TreasureDigStartWindow",
  TreasureDigSuccessWindow = "TreasureDigSuccessWindow",
  TreasureDigTwoButtonWindow = "TreasureDigTwoButtonWindow",
  TutorialBoardWindow = "TutorialBoardWindow",
  TwoButtonWindow = "TwoButtonWindow",
  UIRoot = "UIRoot",
  UpcomingEventWindow = "UpcomingEventWindow",
  UpdateHintWindow = "UpdateHintWindow",
  UseInventoryItemConfirmWindow = "UseInventoryItemConfirmWindow",
  WarningTwoButtonWindow = "WarningTwoButtonWindow"
}
setmetatable(UIPrefabConfigName, {
  __index = function(_, key)
    Log.Error("UIPrefabConfigName try to index a nil key: " .. tostring(key))
    return nil
  end
})

function UIPrefabConfigName.HasConfig(name)
  if name == nil then
    return false
  end
  return rawget(UIPrefabConfigName, name) ~= nil
end
