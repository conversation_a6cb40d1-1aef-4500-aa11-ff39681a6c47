ProgressActivityNoticeWindow = setmetatable({}, ProgressActivityBaseWindow)
ProgressActivityNoticeWindow.__index = ProgressActivityNoticeWindow

function ProgressActivityNoticeWindow:Init(...)
  ProgressActivityBaseWindow.Init(self, ...)
  self:UpdatePerSecond()
  local descText = self.m_definition.WindowDescKey
  local iconKey = self.m_definition.ActivityTokenIconName
  local totalTokenNum = self.m_model:GetTotalRequire()
  self.m_descText.text = GM.GameTextModel:GetText(descText, iconKey, iconKey, totalTokenNum)
end

function ProgressActivityNoticeWindow:UpdatePerSecond()
  if self.m_nextStateTime ~= nil then
    local delta = math.max(0, self.m_nextStateTime - GM.GameModel:GetServerTime())
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  end
end

ProgressActivityFinishSuccessWindow = setmetatable({}, ProgressActivityBaseWindow)
ProgressActivityFinishSuccessWindow.__index = ProgressActivityFinishSuccessWindow

function ProgressActivityFinishSuccessWindow:Init(...)
  ProgressActivityBaseWindow.Init(self, ...)
end

ProgressActivityFinishFailWindow = setmetatable({}, ProgressActivityBaseWindow)
ProgressActivityFinishFailWindow.__index = ProgressActivityFinishFailWindow
ProgressActivitySlider = {}
ProgressActivitySlider.__index = ProgressActivitySlider

function ProgressActivitySlider:Init(model)
  self.m_model = model
  self:UpdateContent()
end

function ProgressActivitySlider:OnDestroy()
  if self.m_tween then
    self.m_tween:Complete(true)
    self.m_tween = nil
  end
end

function ProgressActivitySlider:UpdateContent(level, score)
  self.m_nCurLevel = level or self.m_model:GetLevel()
  local nCurScore = score or self.m_model:GetActivityTokenNumber()
  local nEndScore = self.m_model:GetLevelRequire(self.m_nCurLevel)
  if nEndScore and nEndScore ~= 0 then
    self.m_ProgressActivitySlider.value = nCurScore / nEndScore
    self.m_percentSonText.text = tostring(nCurScore)
    self.m_percentMomText.text = tostring(nEndScore)
  end
  local levelRewradConfig = self.m_model:GetLevelConfigs()
  if levelRewradConfig[self.m_nCurLevel] ~= nil then
    local EleConfig = levelRewradConfig[self.m_nCurLevel]
    local normalReward = EleConfig.Rewards
    UIUtil.SetActive(self.m_singleGo, true)
    if normalReward ~= nil then
      self.m_rewardItem:Init(normalReward)
    end
  end
end

function ProgressActivitySlider:PlayProgressAnimation(score, callback)
  local nEndScore = self.m_model:GetLevelRequire(self.m_nCurLevel)
  if not nEndScore or nEndScore <= 0 then
    Log.Error("进度条活动逻辑错误")
    return
  end
  local newSliderValue = score / nEndScore
  local curScore = tonumber(self.m_percentSonText.text) or 0
  local s = DOTween.Sequence()
  s:Append(self.m_ProgressActivitySlider:DOValue(newSliderValue, 0.8))
  s:Join(self.m_percentSonText:DOCounter(curScore, score, 0.8))
  s:AppendCallback(function()
    if callback then
      callback()
    end
    self.m_tween = nil
  end)
  self.m_tween = s
end

function ProgressActivitySlider:OnLevelRewradProgressClicked()
  local ui = ProgressActivityDefinition[self.m_model:GetType()].RewardProgressWindowPrefabName
  if ui == nil then
    return
  end
  if GM.UIManager:GetOpenedViewByName(ui) then
    return
  end
  GM.UIManager:OpenView(ui, self.m_model:GetType())
end

function ProgressActivitySlider:SetCanClicked(canClicked)
  self.m_BackgroundBtn.enabled = canClicked
end

ProgressActivityRewardItem = setmetatable({}, RewardItemWithItemTipButton)
ProgressActivityRewardItem.__index = ProgressActivityRewardItem

function ProgressActivityRewardItem:Init(RewardItem)
  RewardItemWithItemTipButton.Init(self, RewardItem)
end

function ProgressActivityRewardItem:PlayShowAnimation()
  self.transform.localScale = Vector3(0.1, 0.1, 1)
  if self.m_tween ~= nil then
    self.m_tween:Kill()
  end
  self.m_tween = DOTween.Sequence()
  self.m_tween:Append(self.transform:DOScaleX(1.1, 0.3))
  self.m_tween:Join(self.transform:DOScaleY(1.1, 0.3))
  self.m_tween:Append(self.transform:DOScaleX(1, 0.1))
  self.m_tween:Join(self.transform:DOScaleY(1, 0.1))
  self.m_tween:OnComplete(function()
    self.m_tween = nil
  end)
end

function ProgressActivityRewardItem:ShowEffect()
  self.m_EffectGo:SetActive(false)
  self.m_EffectGo:SetActive(true)
end

ProgressActivityScoreItem = {}
ProgressActivityScoreItem.__index = ProgressActivityScoreItem

function ProgressActivityScoreItem:Init(num, tokenIcon)
  SpriteUtil.SetImage(self.m_ProgressActivityScoreItemImg, tokenIcon, false, function()
    UIUtil.SetActive(self.gameObject, true)
  end)
  self.m_numText.text = "+" .. tostring(num)
end
