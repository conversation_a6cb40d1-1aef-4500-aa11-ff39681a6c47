"""
测试店铺订单生成器
验证核心功能是否正常工作
"""

import os
import sys

def test_config_loading():
    """测试配置加载功能"""
    print("=== 测试配置加载 ===")
    
    try:
        # 导入简化版生成器的核心类
        from shop_order_simple import SimpleShopOrderGenerator, ShopItem
        import tkinter as tk
        
        # 创建临时根窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建生成器实例
        generator = SimpleShopOrderGenerator(root)
        
        # 测试配置加载
        generator._load_config_data()
        
        print(f"✓ 配置加载成功")
        print(f"  物品总数: {len(generator.items_data)}")
        
        # 统计物品类型
        type_count = {}
        for item in generator.items_data.values():
            type_count[item.type] = type_count.get(item.type, 0) + 1
        
        print("  物品类型分布:")
        for item_type, count in type_count.items():
            print(f"    {item_type}: {count}个")
        
        # 显示一些示例物品
        print("  示例物品:")
        count = 0
        for code, item in generator.items_data.items():
            if count >= 5:
                break
            print(f"    {code}: {item.type}, 等级{item.level}, 能量{item.energy_cost}")
            count += 1
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_generation():
    """测试订单生成功能"""
    print("\n=== 测试订单生成 ===")
    
    try:
        from shop_order_simple import SimpleShopOrderGenerator, ShopOrderPlan, DayOrder
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = SimpleShopOrderGenerator(root)
        generator._load_config_data()
        
        # 设置测试参数
        generator.selected_shop.set("BBQ")
        generator.selected_chapter.set(1)
        generator.generation_days.set(3)  # 生成3天的订单
        generator.energy_mode.set("平衡")
        generator.max_energy_per_day.set(50)
        generator.min_energy_per_day.set(10)
        
        # 生成订单
        generator._generate_orders()
        
        if generator.current_plan:
            plan = generator.current_plan
            print(f"✓ 订单生成成功")
            print(f"  店铺: {plan.shop_name}")
            print(f"  章节: {plan.chapter_id}")
            print(f"  总天数: {plan.total_days}")
            print(f"  总能量: {plan.total_energy:.1f}")
            print(f"  平均每日能量: {plan.total_energy/plan.total_days:.1f}")
            
            print("  每日订单详情:")
            for day_order in plan.daily_orders:
                print(f"    第{day_order.day}天: {len(day_order.requirements)}个需求, "
                      f"能量{day_order.total_energy:.1f}, 难度{day_order.difficulty}")
                
                for req in day_order.requirements:
                    item_code = req['Type']
                    count = req['Count']
                    energy = req['Energy']
                    item_name = generator.items_data.get(item_code, type('obj', (object,), {'name': item_code})).name or item_code
                    print(f"      - {item_name} x{count} (能量: {energy:.1f})")
        else:
            print("✗ 订单生成失败: 没有生成计划")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 订单生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_item_filtering():
    """测试物品筛选功能"""
    print("\n=== 测试物品筛选 ===")
    
    try:
        from shop_order_simple import SimpleShopOrderGenerator
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = SimpleShopOrderGenerator(root)
        generator._load_config_data()
        
        # 测试筛选功能
        constraints = {
            'allow_generators': True,
            'allow_items': True,
            'allow_equipment': False,
            'allow_dishes': False,
            'max_item_level': 5
        }
        
        available_items = generator._filter_available_items(constraints)
        
        print(f"✓ 物品筛选成功")
        print(f"  筛选前物品数: {len(generator.items_data)}")
        print(f"  筛选后物品数: {len(available_items)}")
        
        # 统计筛选后的物品类型
        type_count = {}
        level_count = {}
        for item in available_items.values():
            type_count[item.type] = type_count.get(item.type, 0) + 1
            level_count[item.level] = level_count.get(item.level, 0) + 1
        
        print("  筛选后类型分布:")
        for item_type, count in type_count.items():
            print(f"    {item_type}: {count}个")
        
        print("  筛选后等级分布:")
        for level in sorted(level_count.keys()):
            count = level_count[level]
            print(f"    等级{level}: {count}个")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 物品筛选失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_energy_calculation():
    """测试能量计算功能"""
    print("\n=== 测试能量计算 ===")
    
    try:
        from shop_order_simple import SimpleShopOrderGenerator, DayOrder
        import tkinter as tk
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例
        generator = SimpleShopOrderGenerator(root)
        generator._load_config_data()
        
        # 创建测试订单
        day_order = DayOrder(day=1, group_id=1, chapter_id=1)
        
        # 添加一些测试需求
        if generator.items_data:
            test_items = list(generator.items_data.keys())[:3]  # 取前3个物品
            
            for item_code in test_items:
                item = generator.items_data[item_code]
                requirement = {
                    'Type': item_code,
                    'Count': 2,
                    'Energy': item.energy_cost * 2
                }
                day_order.requirements.append(requirement)
                day_order.total_energy += requirement['Energy']
        
        # 测试时间和难度计算
        estimated_time = generator._calculate_estimated_time(day_order)
        difficulty = generator._calculate_difficulty(day_order, {
            'max_energy_per_day': 100,
            'min_energy_per_day': 10
        })
        
        print(f"✓ 能量计算成功")
        print(f"  测试订单需求数: {len(day_order.requirements)}")
        print(f"  总能量消耗: {day_order.total_energy:.1f}")
        print(f"  预估时间: {estimated_time}分钟")
        print(f"  难度等级: {difficulty}")
        
        print("  需求详情:")
        for req in day_order.requirements:
            item_code = req['Type']
            count = req['Count']
            energy = req['Energy']
            item = generator.items_data[item_code]
            print(f"    {item_code} x{count}: 能量{energy:.1f}, 类型{item.type}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 能量计算失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_plan_serialization():
    """测试计划序列化功能"""
    print("\n=== 测试计划序列化 ===")
    
    try:
        from shop_order_simple import SimpleShopOrderGenerator
        import tkinter as tk
        import json
        import tempfile
        import os
        
        # 创建临时根窗口
        root = tk.Tk()
        root.withdraw()
        
        # 创建生成器实例并生成计划
        generator = SimpleShopOrderGenerator(root)
        generator._load_config_data()
        generator.generation_days.set(2)
        generator._generate_orders()
        
        if not generator.current_plan:
            print("✗ 没有生成计划可供测试")
            return False
        
        # 测试序列化
        plan_data = {
            'shop_name': generator.current_plan.shop_name,
            'chapter_id': generator.current_plan.chapter_id,
            'total_days': generator.current_plan.total_days,
            'total_energy': generator.current_plan.total_energy,
            'total_rewards': generator.current_plan.total_rewards,
            'constraints': generator.current_plan.constraints,
            'daily_orders': []
        }
        
        for day_order in generator.current_plan.daily_orders:
            plan_data['daily_orders'].append({
                'day': day_order.day,
                'group_id': day_order.group_id,
                'chapter_id': day_order.chapter_id,
                'requirements': day_order.requirements,
                'rewards': day_order.rewards,
                'total_energy': day_order.total_energy,
                'estimated_time': day_order.estimated_time,
                'difficulty': day_order.difficulty
            })
        
        # 写入临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(plan_data, f, ensure_ascii=False, indent=2)
            temp_file = f.name
        
        # 读取并验证
        with open(temp_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        # 清理临时文件
        os.unlink(temp_file)
        
        print(f"✓ 计划序列化成功")
        print(f"  原始计划天数: {generator.current_plan.total_days}")
        print(f"  序列化后天数: {loaded_data['total_days']}")
        print(f"  原始总能量: {generator.current_plan.total_energy:.1f}")
        print(f"  序列化后总能量: {loaded_data['total_energy']:.1f}")
        print(f"  每日订单数: {len(loaded_data['daily_orders'])}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"✗ 计划序列化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试店铺订单生成器...")
    
    tests = [
        test_config_loading,
        test_order_generation,
        test_item_filtering,
        test_energy_calculation,
        test_plan_serialization
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！店铺订单生成器工作正常。")
        print("\n可以运行以下命令启动程序:")
        print("python shop_order_simple.py")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
