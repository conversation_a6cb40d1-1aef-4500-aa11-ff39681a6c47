import os
import json
import re
import datetime
import math
import traceback
import shutil
from tkinter import *
from tkinter import ttk, messagebox, filedialog, simpledialog
from collections import defaultdict
from typing import Dict, List, Optional, Any, Tuple, Callable
import webbrowser

# 导入新的模块
try:
    from config_loader import ConfigLoader
    from order_generator import OrderGenerator
    from energy_calculator import EnergyCalculator
    from data_models import OrderData, GeneratorData, ItemData, OrderGenerationConfig, BetMode, EfficiencyMode
except ImportError as e:
    print(f"导入模块失败: {e}")
    # 如果导入失败，定义空类以避免错误
    class ConfigLoader: pass
    class OrderGenerator: pass
    class EnergyCalculator: pass
    class OrderData: pass
    class GeneratorData: pass
    class ItemData: pass
    class OrderGenerationConfig: pass
    class BetMode: pass
    class EfficiencyMode: pass

# 类型别名
OrderType = Dict[str, Any]
ChapterOrdersType = Dict[int, List[OrderType]]


class OrderEditor:
    """
    浮岛物语订单编辑器主类
    功能：
    1. 加载和保存订单配置
    2. 创建、编辑和删除订单
    3. 管理订单需求和奖励
    4. 支持多章节管理
    """
    
    def __init__(self, root):
        """初始化订单编辑器"""
        self.root = root
        self.root.title("订单编辑器 - 浮岛物语")
        self.root.geometry("1200x800")
        
        # 加载物品数据
        self.items = self.load_items()
        self.pd_items = [item for item in self.items if item.startswith(('it_', 'ds_'))]
        self.currency_items = ['Coin', 'Gem', 'Exp']  # 货币类型
        
        # 修改状态标记
        self.modified = False
        
        # 初始化核心数据结构
        self.initialize_data_structures()
        
        # 设置窗口图标（如果存在）
        self.set_window_icon()
        
        # 添加快捷键
        self.setup_shortcuts()
        
        # 初始化UI
        self.setup_ui()
        
        # 加载配置
        self.load_configuration()
        
        # 窗口关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"设置窗口图标失败: {e}")
    
    def setup_shortcuts(self):
        """设置快捷键"""
        shortcuts = [
            ("<Control-s>", self.save_order),
            ("<Control-n>", self.add_order),
            ("<Control-d>", self.delete_order),
            ("<Control-o>", self.load_config),
            ("<Control-q>", self.confirm_exit),
            ("<F1>", self.show_help),
        ]
        
        for key, func in shortcuts:
            self.root.bind(key, lambda e, f=func: f())
    
    def initialize_data_structures(self):
        """初始化核心数据结构"""
        # 配置路径
        self.config_path = os.path.abspath("d:/MCWork/GHabout/FMC/fmc_lua_1.18.5/fmc_lua/Data/Config")
        self.item_config_path = os.path.join(self.config_path, "ItemModelConfig.lua")
        self.max_chapters = 16  # 最大章节数
        
        # 数据存储
        self.orders = {}  # 按章节存储订单 {chapter: [order1, order2, ...]}
        self.all_orders = []  # 当前显示的订单列表
        self.items = []  # 物品类型列表
        self.current_group = 1
        self.current_chapter = 1
        self.loaded_chapters = set()  # 已加载的章节
        self.pd_types = {}  # PD类型配置
        self.current_order = None  # 当前选中的订单
        
        # 状态变量
        self.status_var = StringVar(value="就绪")
        self.energy_var = StringVar(value="0")
        
        # UI元素占位
        self.req_tree = None
        self.rew_tree = None
        self.order_tree = None
        self.chapter_var = IntVar(value=1)
        self.order_id_var = StringVar()
        self.group_id_var = IntVar(value=1)
    
    def load_configuration(self):
        """加载配置"""
        try:
            # 初始化加载器
            self._load_pd_types = self._create_pd_loader()
            self.load_item_config = self._create_item_loader()
            self.load_order_config = self._create_order_loader()
            
            # 加载物品配置
            self.load_item_config()
            
            # 加载所有章节 (1-16)
            for chapter in range(1, self.max_chapters + 1):
                self.load_order_config(chapter)
            
            # 加载PD类型
            self.pd_types = self._load_pd_types() or {}
            
            self.update_status("配置加载完成")
        except Exception as e:
            self.show_error(f"加载配置失败: {e}")
            traceback.print_exc()
    
    def update_status(self, message: str):
        """更新状态栏"""
        self.status_var.set(f"{datetime.datetime.now().strftime('%H:%M:%S')} - {message}")
    
    def show_error(self, message: str):
        """显示错误消息"""
        messagebox.showerror("错误", message)
        self.update_status(f"错误: {message}")
    
    def show_info(self, message: str):
        """显示信息消息"""
        messagebox.showinfo("提示", message)
        self.update_status(message)
    
    def confirm_exit(self):
        """确认退出"""
        if self.modified:
            if messagebox.askyesno("确认", "有未保存的更改，确定要退出吗？"):
                self.root.destroy()
        else:
            self.root.destroy()
    
    def on_closing(self):
        """窗口关闭事件处理"""
        self.confirm_exit()
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """订单编辑器 - 帮助
        
基本操作:
- 新建订单: Ctrl+N
- 保存订单: Ctrl+S
- 删除订单: Ctrl+D
- 加载配置: Ctrl+O
- 退出: Ctrl+Q
- 帮助: F1

使用说明:
1. 从左侧选择章节
2. 点击"新建订单"或双击现有订单进行编辑
3. 在右侧编辑订单详情
4. 使用Ctrl+S保存更改

提示:
- 双击需求或奖励可以编辑
- 右键点击订单可进行更多操作
"""
        messagebox.showinfo("帮助", help_text.strip())
        
    def setup_ui(self):
        """初始化用户界面"""
        # 创建主菜单
        self.create_menu()
        
        # 添加快捷键提示和帮助按钮
        shortcut_frame = ttk.Frame(self.root, padding="5")
        shortcut_frame.pack(fill=X, pady=(0, 5))
        
        ttk.Label(shortcut_frame, 
                 text="快捷键: Ctrl+N新建 | Ctrl+S保存 | Ctrl+D删除 | F1帮助 | Ctrl+Q退出",
                 font=('Arial', 8)).pack(side=LEFT)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=BOTH, expand=True)
        
        # 左侧 - 章节和订单列表
        left_frame = ttk.LabelFrame(main_frame, text="订单列表", padding="5")
        left_frame.pack(side=LEFT, fill=Y, padx=5, pady=5, ipadx=5, ipady=5)
        
        # 章节选择
        self.setup_chapter_selector(left_frame)
        
        # 订单列表
        self.setup_order_list(left_frame)
        
        # 右侧 - 订单详情
        right_frame = ttk.LabelFrame(main_frame, text="订单详情", padding="10")
        right_frame.pack(side=RIGHT, fill=BOTH, expand=True, padx=5, pady=5, ipadx=5, ipady=5)
        
        # 订单基本信息
        self.setup_order_info(right_frame)
        
        # 需求列表
        self.setup_requirements_section(right_frame)
        
        # 奖励列表
        self.setup_rewards_section(right_frame)
        
        # 状态栏
        self.setup_status_bar()
    
    def create_menu(self):
        """创建主菜单"""
        menubar = Menu(self.root)
        
        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="新建订单 (Ctrl+N)", command=self.add_order)
        file_menu.add_command(label="保存 (Ctrl+S)", command=self.save_order)
        file_menu.add_separator()
        file_menu.add_command(label="加载配置 (Ctrl+O)", command=self.load_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出 (Ctrl+Q)", command=self.confirm_exit)
        menubar.add_cascade(label="文件", menu=file_menu)
        
        # 编辑菜单
        edit_menu = Menu(menubar, tearoff=0)
        edit_menu.add_command(label="添加需求", command=self.add_requirement)
        edit_menu.add_command(label="添加奖励", command=self.add_reward)
        edit_menu.add_separator()
        edit_menu.add_command(label="删除选中项", command=self.delete_selected_item)
        menubar.add_cascade(label="编辑", menu=edit_menu)

        # 生成菜单
        generate_menu = Menu(menubar, tearoff=0)
        generate_menu.add_command(label="智能订单生成器", command=self.open_order_generator)
        generate_menu.add_command(label="批量生成方案", command=self.batch_generate_schemes)
        menubar.add_cascade(label="生成", menu=generate_menu)
        
        # 帮助菜单
        help_menu = Menu(menubar, tearoff=0)
        help_menu.add_command(label="帮助 (F1)", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        menubar.add_cascade(label="帮助", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def setup_chapter_selector(self, parent):
        """设置章节选择器"""
        chapter_frame = ttk.Frame(parent)
        chapter_frame.pack(fill=X, pady=5)
        
        ttk.Label(chapter_frame, text="章节:").pack(side=LEFT, padx=5)
        
        # 使用下拉框选择章节
        self.chapter_combo = ttk.Combobox(
            chapter_frame, 
            textvariable=self.chapter_var,
            values=list(range(1, self.max_chapters + 1)),
            state='readonly',
            width=5
        )
        self.chapter_combo.pack(side=LEFT, padx=5)
        self.chapter_combo.bind('<<ComboboxSelected>>', self.on_chapter_changed)
        
        # 刷新按钮
        ttk.Button(
            chapter_frame, 
            text="刷新", 
            command=self.refresh_chapter,
            width=6
        ).pack(side=RIGHT, padx=5)
    
    def setup_order_list(self, parent):
        """设置订单列表"""
        # 订单列表框架
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=BOTH, expand=True, pady=5)
        
        # 订单列表标题
        header_frame = ttk.Frame(list_frame)
        header_frame.pack(fill=X)
        ttk.Label(header_frame, text="订单列表", font=('Arial', 10, 'bold')).pack(side=LEFT)
        
        # 订单列表工具栏
        toolbar_frame = ttk.Frame(header_frame)
        toolbar_frame.pack(side=RIGHT)
        
        ttk.Button(
            toolbar_frame, 
            text="+", 
            width=3,
            command=self.add_order
        ).pack(side=LEFT, padx=2)
        
        ttk.Button(
            toolbar_frame, 
            text="-", 
            width=3,
            command=self.delete_order
        ).pack(side=LEFT, padx=2)
        
        # 订单列表
        columns = ("id", "group", "requirements", "rewards")
        self.order_tree = ttk.Treeview(
            list_frame, 
            columns=columns, 
            show='headings',
            selectmode='browse',
            height=20
        )
        
        # 设置列标题
        self.order_tree.heading("id", text="ID")
        self.order_tree.heading("group", text="组")
        self.order_tree.heading("requirements", text="需求")
        self.order_tree.heading("rewards", text="奖励")
        
        # 设置列宽
        self.order_tree.column("id", width=60, anchor='center')
        self.order_tree.column("group", width=40, anchor='center')
        self.order_tree.column("requirements", width=200)
        self.order_tree.column("rewards", width=150)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=VERTICAL, command=self.order_tree.yview)
        self.order_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.order_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 绑定事件
        self.order_tree.bind('<<TreeviewSelect>>', self.on_order_selected)
        self.order_tree.bind('<Double-1>', self.on_order_double_click)
        self.order_tree.bind('<Button-3>', self.show_order_context_menu)
        
        # 创建右键菜单
        self.order_context_menu = Menu(self.root, tearoff=0)
        self.order_context_menu.add_command(label="复制ID", command=self.copy_order_id)
        self.order_context_menu.add_separator()
        self.order_context_menu.add_command(label="删除订单", command=self.delete_order)
    
    def setup_order_info(self, parent):
        """设置订单基本信息区域"""
        info_frame = ttk.LabelFrame(parent, text="基本信息", padding=10)
        info_frame.pack(fill=X, pady=5)
        
        # 订单ID
        ttk.Label(info_frame, text="订单ID:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        ttk.Entry(info_frame, textvariable=self.order_id_var, width=10).grid(row=0, column=1, sticky=W, padx=5, pady=2)
        
        # 组ID
        ttk.Label(info_frame, text="组ID:").grid(row=0, column=2, sticky=W, padx=5, pady=2)
        ttk.Spinbox(info_frame, from_=1, to=100, textvariable=self.group_id_var, width=5).grid(
            row=0, column=3, sticky=W, padx=5, pady=2)
        
        # 能量消耗
        ttk.Label(info_frame, text="能量消耗:").grid(row=0, column=4, sticky=W, padx=5, pady=2)
        ttk.Label(info_frame, textvariable=self.energy_var, width=10).grid(
            row=0, column=5, sticky=W, padx=5, pady=2)
        
        # 按钮
        btn_frame = ttk.Frame(info_frame)
        btn_frame.grid(row=0, column=6, columnspan=2, sticky=E, padx=5)
        
        ttk.Button(btn_frame, text="保存", command=self.save_order).pack(side=LEFT, padx=2)
        ttk.Button(btn_frame, text="取消", command=self.cancel_edit).pack(side=LEFT, padx=2)
    
    def setup_requirements_section(self, parent):
        """设置需求区域"""
        req_frame = ttk.LabelFrame(parent, text="需求", padding=10)
        req_frame.pack(fill=BOTH, expand=True, pady=5)
        
        # 需求列表
        columns = ("type", "count", "params")
        self.req_tree = ttk.Treeview(
            req_frame, 
            columns=columns,
            show='headings',
            selectmode='browse',
            height=6
        )
        
        # 设置列标题
        self.req_tree.heading("type", text="类型")
        self.req_tree.heading("count", text="数量")
        self.req_tree.heading("params", text="参数")
        
        # 设置列宽
        self.req_tree.column("type", width=150)
        self.req_tree.column("count", width=60, anchor='center')
        self.req_tree.column("params", width=200)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(req_frame, orient=VERTICAL, command=self.req_tree.yview)
        self.req_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.req_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 添加需求按钮
        btn_frame = ttk.Frame(req_frame)
        btn_frame.pack(side=BOTTOM, fill=X, pady=(5, 0))
        
        ttk.Button(btn_frame, text="添加需求", command=self.add_requirement).pack(side=LEFT, padx=2)
        ttk.Button(btn_frame, text="删除", command=lambda: self.delete_selected_item(self.req_tree)).pack(side=LEFT, padx=2)
    
    def setup_rewards_section(self, parent):
        """设置奖励区域"""
        rew_frame = ttk.LabelFrame(parent, text="奖励", padding=10)
        rew_frame.pack(fill=BOTH, expand=True, pady=5)
        
        # 奖励列表
        columns = ("type", "count", "params")
        self.rew_tree = ttk.Treeview(
            rew_frame, 
            columns=columns,
            show='headings',
            selectmode='browse',
            height=6
        )
        
        # 设置列标题
        self.rew_tree.heading("type", text="类型")
        self.rew_tree.heading("count", text="数量")
        self.rew_tree.heading("params", text="参数")
        
        # 设置列宽
        self.rew_tree.column("type", width=150)
        self.rew_tree.column("count", width=60, anchor='center')
        self.rew_tree.column("params", width=200)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(rew_frame, orient=VERTICAL, command=self.rew_tree.yview)
        self.rew_tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        self.rew_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 添加奖励按钮
        btn_frame = ttk.Frame(rew_frame)
        btn_frame.pack(side=BOTTOM, fill=X, pady=(5, 0))
        
        ttk.Button(btn_frame, text="添加奖励", command=self.add_reward).pack(side=LEFT, padx=2)
        ttk.Button(btn_frame, text="删除", command=lambda: self.delete_selected_item(self.rew_tree)).pack(side=LEFT, padx=2)
    
    def setup_status_bar(self):
        """设置状态栏"""
        status_bar = ttk.Frame(self.root, height=20)
        status_bar.pack(fill=X, side=BOTTOM, pady=(5, 0))
        
        ttk.Label(
            status_bar, 
            textvariable=self.status_var, 
            relief=SUNKEN, 
            anchor=W,
            padding=(5, 2)
        ).pack(fill=X)
        
    # ============================================================
    # 核心功能方法
    # ============================================================
    
    def on_chapter_changed(self, event=None):
        """章节变更事件处理"""
        chapter = self.chapter_var.get()
        self.current_chapter = chapter
        self.refresh_order_list()
    
    def refresh_chapter(self):
        """刷新当前章节数据"""
        chapter = self.chapter_var.get()
        self.load_order_config(chapter, force_reload=True)
        self.refresh_order_list()
    
    def refresh_order_list(self):
        """刷新订单列表"""
        # 清空当前列表
        for item in self.order_tree.get_children():
            self.order_tree.delete(item)
        
        # 获取当前章节的订单
        chapter = self.chapter_var.get()
        orders = self.orders.get(chapter, [])
        
        # 添加到列表
        for order in orders:
            order_id = order.get('Id', '')
            group_id = order.get('GroupId', 1)
            
            # 格式化需求
            requirements = []
            for req in order.get('Requirements', []):
                req_type = req.get('Type', '')
                count = req.get('Count', 0)
                requirements.append(f"{req_type}×{count}")
            req_text = " ".join(requirements)
            
            # 格式化奖励
            rewards = []
            for rew in order.get('Rewards', []):
                rew_type = rew.get('Currency', '')
                count = rew.get('Amount', 0)
                rewards.append(f"{rew_type}×{count}")
            rew_text = " ".join(rewards)
            
            # 添加到树形视图
            self.order_tree.insert('', 'end', values=(order_id, group_id, req_text, rew_text))
    
    def on_order_selected(self, event):
        """订单选择事件处理"""
        selection = self.order_tree.selection()
        if not selection:
            return
            
        # 获取选中的订单ID
        item = self.order_tree.item(selection[0])
        order_id = item['values'][0]
        
        # 查找订单
        chapter = self.chapter_var.get()
        orders = self.orders.get(chapter, [])
        
        for order in orders:
            if str(order.get('Id', '')) == str(order_id):
                self.current_order = order
                self.load_order_details(order)
                break
    
    def load_order_details(self, order):
        """加载订单详情"""
        if not order:
            return
            
        # 基本信息
        self.order_id_var.set(order.get('Id', ''))
        self.group_id_var.set(order.get('GroupId', 1))
        
        # 清空需求列表
        for item in self.req_tree.get_children():
            self.req_tree.delete(item)
            
        # 添加需求
        for req in order.get('Requirements', []):
            req_type = req.get('Type', '')
            count = req.get('Count', 0)
            params = req.get('Params', '')
            self.req_tree.insert('', 'end', values=(req_type, count, params))
        
        # 清空奖励列表
        for item in self.rew_tree.get_children():
            self.rew_tree.delete(item)
            
        # 添加奖励
        for rew in order.get('Rewards', []):
            rew_type = rew.get('Currency', '')
            count = rew.get('Amount', 0)
            params = rew.get('Params', '')
            self.rew_tree.insert('', 'end', values=(rew_type, count, params))
        
        # 计算并显示能量消耗
        self.calculate_energy()
    
    def calculate_energy(self):
        """计算能量消耗"""
        total_energy = 0
        for item in self.req_tree.get_children():
            values = self.req_tree.item(item, 'values')
            if len(values) >= 2:
                try:
                    item_count = int(values[1])
                    # 这里可以根据需求类型计算能量消耗
                    # 简化处理，假设每个需求消耗1点能量
                    total_energy += item_count
                except (ValueError, TypeError):
                    pass
        
        if hasattr(self, 'energy_var'):
            self.energy_var.set(str(total_energy))
        return total_energy
    
    def add_order(self):
        """添加新订单"""
        # 生成新订单ID
        chapter = self.chapter_var.get()
        orders = self.orders.get(chapter, [])
        
        # 查找最大ID
        max_id = 0
        for order in orders:
            try:
                order_id = int(order.get('Id', 0))
                if order_id > max_id:
                    max_id = order_id
            except (ValueError, TypeError):
                pass
        
        # 创建新订单
        new_order = {
            'Id': str(max_id + 1),
            'GroupId': 1,
            'Requirements': [],
            'Rewards': []
        }
        
        # 添加到当前章节
        if chapter not in self.orders:
            self.orders[chapter] = []
        self.orders[chapter].append(new_order)
        
        # 刷新列表并选中新订单
        self.refresh_order_list()
        self.current_order = new_order
        self.load_order_details(new_order)
        
        # 滚动到新订单
        for item in self.order_tree.get_children():
            if self.order_tree.item(item, 'values')[0] == new_order['Id']:
                self.order_tree.selection_set(item)
                self.order_tree.see(item)
                break
        
        self.update_status(f"已创建新订单: {new_order['Id']}")
        self.modified = True
    
    def save_order(self):
        """保存当前订单"""
        if not self.current_order:
            self.show_error("没有可保存的订单")
            return
            
        try:
            # 更新订单信息
            self.current_order['Id'] = self.order_id_var.get()
            self.current_order['GroupId'] = self.group_id_var.get()
            
            # 更新需求
            requirements = []
            for item in self.req_tree.get_children():
                values = self.req_tree.item(item, 'values')
                if len(values) >= 2:
                    req = {
                        'Type': values[0],
                        'Count': int(values[1]),
                        'Params': values[2] if len(values) > 2 else ''
                    }
                    requirements.append(req)
            self.current_order['Requirements'] = requirements
            
            # 更新奖励
            rewards = []
            for item in self.rew_tree.get_children():
                values = self.rew_tree.item(item, 'values')
                if len(values) >= 2:
                    rew = {
                        'Currency': values[0],
                        'Amount': int(values[1]),
                        'Params': values[2] if len(values) > 2 else ''
                    }
                    rewards.append(rew)
            self.current_order['Rewards'] = rewards
            
            # 保存到文件
            self.save_chapter_orders()
            
            self.update_status(f"订单 {self.current_order['Id']} 已保存")
            self.modified = False
            
        except Exception as e:
            self.show_error(f"保存订单失败: {str(e)}")
            traceback.print_exc()
    
    def delete_order(self):
        """删除当前选中的订单"""
        selection = self.order_tree.selection()
        if not selection:
            self.show_error("请先选择一个订单")
            return
            
        item = selection[0]
        order_id = self.order_tree.item(item, 'values')[0]
        
        if not messagebox.askyesno("确认删除", f"确定要删除订单 {order_id} 吗？"):
            return
        
        try:
            # 从内存中删除
            chapter = self.chapter_var.get()
            orders = self.orders.get(chapter, [])
            
            for i, order in enumerate(orders):
                if str(order.get('Id', '')) == str(order_id):
                    del orders[i]
                    break
            
            # 刷新列表
            self.refresh_order_list()
            self.current_order = None
            
            # 清空详情
            self.order_id_var.set('')
            self.group_id_var.set(1)
            for tree in [self.req_tree, self.rew_tree]:
                for item in tree.get_children():
                    tree.delete(item)
            
            self.update_status(f"已删除订单: {order_id}")
            self.modified = True
            
        except Exception as e:
            self.show_error(f"删除订单失败: {str(e)}")
    
    def add_requirement(self):
        """添加需求"""
        if not hasattr(self, 'pd_items') or not self.pd_items:
            messagebox.showerror("错误", "未能加载物品数据")
            return
            
        # 创建需求类型选择对话框
        dialog = Toplevel(self.root)
        dialog.title("添加需求")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 设置对话框大小和位置
        dialog.geometry("400x200")
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f'400x200+{x}+{y}')
        
        # 需求类型
        ttk.Label(dialog, text="物品类型:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        req_type_var = StringVar()
        req_type_combo = ttk.Combobox(dialog, textvariable=req_type_var, 
                                    values=self.pd_items, 
                                    state="readonly",
                                    width=30)
        req_type_combo.grid(row=0, column=1, padx=5, pady=5, sticky='we')
        
        # 数量
        ttk.Label(dialog, text="数量:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        count_var = StringVar(value="1")
        count_entry = ttk.Entry(dialog, textvariable=count_var, width=10)
        count_entry.grid(row=1, column=1, padx=5, pady=5, sticky='w')
        
        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=2, column=0, columnspan=2, pady=15)
        
        def on_ok():
            try:
                req_type = req_type_var.get().strip()
                if not req_type:
                    messagebox.showerror("错误", "请选择物品类型", parent=dialog)
                    return
                    
                count = int(count_var.get().strip())
                if count <= 0:
                    raise ValueError("数量必须大于0")
                    
                # 添加到需求列表
                self.req_tree.insert('', 'end', values=(req_type, count, ''))
                self.calculate_energy()
                self.modified = True
                dialog.destroy()
                
            except ValueError as e:
                messagebox.showerror("错误", f"无效的数量: {e}", parent=dialog)
        
        ttk.Button(button_frame, text="确定", command=on_ok, width=10).pack(side=LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=dialog.destroy, width=10).pack(side=LEFT, padx=10)
        
        # 绑定回车键
        dialog.bind('<Return>', lambda e: on_ok())
        
        # 设置焦点和输入限制
        def validate_numeric_input(P):
            if P == "" or P.isdigit():
                return True
            return False
            
        vcmd = (dialog.register(validate_numeric_input), '%P')
        count_entry.config(validate='key', validatecommand=vcmd)
        
        # 设置默认焦点
        dialog.after(100, lambda: req_type_combo.focus_set())
        
        # 选择第一个物品
        if self.pd_items:
            req_type_combo.current(0)
    
    def add_reward(self):
        """添加奖励"""
        if not hasattr(self, 'items') or not self.items:
            messagebox.showerror("错误", "未能加载物品数据")
            return
            
        # 创建奖励类型选择对话框
        dialog = Toplevel(self.root)
        dialog.title("添加奖励")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 设置对话框大小和位置
        dialog.geometry("400x200")
        dialog.update_idletasks()
        width = dialog.winfo_width()
        height = dialog.winfo_height()
        x = (dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog.winfo_screenheight() // 2) - (height // 2)
        dialog.geometry(f'400x200+{x}+{y}')
        
        # 奖励类型
        ttk.Label(dialog, text="奖励类型:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        rew_type_var = StringVar()
        
        # 奖励类型可以是物品或货币
        reward_types = ['Coin', 'Gem', 'Exp'] + self.items
        rew_type_combo = ttk.Combobox(dialog, textvariable=rew_type_var, 
                                    values=reward_types,
                                    state="readonly",
                                    width=30)
        rew_type_combo.grid(row=0, column=1, padx=5, pady=5, sticky='we')
        
        # 数量
        ttk.Label(dialog, text="数量:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        count_var = StringVar(value="1")
        count_entry = ttk.Entry(dialog, textvariable=count_var, width=10)
        count_entry.grid(row=1, column=1, padx=5, pady=5, sticky='w')
        
        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=2, column=0, columnspan=2, pady=15)
        
        def on_ok():
            try:
                rew_type = rew_type_var.get().strip()
                if not rew_type:
                    messagebox.showerror("错误", "请选择奖励类型", parent=dialog)
                    return
                    
                count = int(count_var.get().strip())
                if count <= 0:
                    raise ValueError("数量必须大于0")
                    
                # 添加到奖励列表
                self.rew_tree.insert('', 'end', values=(rew_type, count, ''))
                self.modified = True
                dialog.destroy()
                
            except ValueError as e:
                messagebox.showerror("错误", f"无效的数量: {e}", parent=dialog)
        
        ttk.Button(button_frame, text="确定", command=on_ok, width=10).pack(side=LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=dialog.destroy, width=10).pack(side=LEFT, padx=10)
        
        # 绑定回车键
        dialog.bind('<Return>', lambda e: on_ok())
        
        # 设置焦点和输入限制
        def validate_numeric_input(P):
            if P == "" or P.isdigit():
                return True
            return False
            
        vcmd = (dialog.register(validate_numeric_input), '%P')
        count_entry.config(validate='key', validatecommand=vcmd)
        
        # 设置默认焦点
        dialog.after(100, lambda: rew_type_combo.focus_set())
        
        # 选择第一个奖励类型
        if reward_types:
            rew_type_combo.current(0)
    
    def delete_selected_item(self, tree):
        """删除选中的项"""
        selection = tree.selection()
        if not selection:
            return
            
        for item in selection:
            tree.delete(item)
        
        if tree in [self.req_tree, self.rew_tree]:
            self.calculate_energy()
        
        self.modified = True
    
    def cancel_edit(self):
        """取消编辑"""
        if self.current_order:
            self.load_order_details(self.current_order)
        self.update_status("已取消编辑")
    
    def copy_order_id(self):
        """复制订单ID到剪贴板"""
        selection = self.order_tree.selection()
        if not selection:
            return
            
        order_id = self.order_tree.item(selection[0], 'values')[0]
        self.root.clipboard_clear()
        self.root.clipboard_append(str(order_id))
        self.update_status(f"已复制订单ID: {order_id}")
    
    def show_order_context_menu(self, event):
        """显示订单右键菜单"""
        item = self.order_tree.identify_row(event.y)
        if item:
            self.order_tree.selection_set(item)
            self.order_context_menu.post(event.x_root, event.y_root)
    
    def on_order_double_click(self, event):
        """订单双击事件"""
        region = self.order_tree.identify_region(event.x, event.y)
        if region == 'cell':
            item = self.order_tree.identify_row(event.y)
            column = self.order_tree.identify_column(event.x)
            
            # 获取当前值
            values = list(self.order_tree.item(item, 'values'))
            col_index = int(column[1:]) - 1
            
            if col_index < 0 or col_index >= len(values):
                return
                
            current_value = values[col_index]
            
            # 创建编辑框
            x, y, width, height = self.order_tree.bbox(item, column)
            entry = ttk.Entry(self.order_tree, width=width//8)
            entry.place(x=x, y=y, width=width, height=height)
            entry.insert(0, current_value)
            entry.focus_set()
            
            def save_edit(event=None):
                new_value = entry.get()
                values[col_index] = new_value
                self.order_tree.item(item, values=values)
                entry.destroy()
                self.modified = True
                
                # 如果是ID或组ID更改，需要更新当前订单
                if col_index in [0, 1]:  # ID或组ID列
                    self.on_order_selected(None)
            
            entry.bind('<Return>', save_edit)
            entry.bind('<FocusOut>', lambda e: entry.destroy())
    
    def save_chapter_orders(self):
        """保存当前章节的订单到文件"""
        chapter = self.chapter_var.get()
        if chapter not in self.orders:
            return
            
        # 构建保存路径
        config_dir = os.path.join(self.config_path, f"Chapter{chapter}")
        os.makedirs(config_dir, exist_ok=True)
        
        order_file = os.path.join(config_dir, "OrderConfig.lua")
        
        # 构建Lua表
        lua_content = "return {\n"
        
        for order in self.orders[chapter]:
            lua_content += f"    [{order['Id']}] = {{\n"
            # 添加组ID
            lua_content += f"        GroupId = {order.get('GroupId', 1)},\n"
            
            # 添加需求
            lua_content += "        Requirements = {\n"
            for req in order.get('Requirements', []):
                lua_content += f"            {{Type = \"{req['Type']}\", Count = {req['Count']}}},\n"
            lua_content += "        },\n"
            
            # 添加奖励
            lua_content += "        Rewards = {\n"
            for rew in order.get('Rewards', []):
                lua_content += f"            {{Currency = \"{rew['Currency']}\", Amount = {rew['Amount']}}},\n"
            lua_content += "        },\n"
            
            lua_content += "    },\n"
        
        lua_content += "}"
        
        # 写入文件
        try:
            with open(order_file, 'w', encoding='utf-8') as f:
                f.write(lua_content)
            
            self.update_status(f"已保存章节 {chapter} 的订单配置")
            self.modified = False
            
        except Exception as e:
            self.show_error(f"保存订单配置失败: {str(e)}")
    
    def load_config(self):
        """加载配置"""
        if self.modified and not messagebox.askyesno("确认", "有未保存的更改，确定要重新加载吗？"):
            return
            
        # 重新加载物品配置
        self.load_item_config()
        
        # 重新加载当前章节的订单
        self.load_order_config(self.chapter_var.get(), force_reload=True)
        
        # 刷新界面
        self.refresh_order_list()
        self.update_status("配置已重新加载")
    
    def show_about(self):
        """显示关于信息"""
        about_text = """浮岛物语 - 订单编辑器
        
版本: 1.0.0
作者: 助手

用于编辑浮岛物语游戏中的订单配置。

快捷键:
- Ctrl+N: 新建订单
- Ctrl+S: 保存订单
- Ctrl+D: 删除订单
- F1: 显示帮助
- Ctrl+Q: 退出
"""
        messagebox.showinfo("关于", about_text.strip())

    def open_order_generator(self):
        """打开智能订单生成器"""
        try:
            # 创建订单生成器窗口
            generator_window = Toplevel(self.root)
            generator_window.title("智能订单生成器")
            generator_window.geometry("800x600")
            generator_window.transient(self.root)
            generator_window.grab_set()

            # 初始化配置加载器和生成器
            config_loader = ConfigLoader(self.config_path)
            config_data = config_loader.load_all_configs()
            order_generator = OrderGenerator(config_data)

            # 创建生成器界面
            self._create_generator_ui(generator_window, order_generator, config_data)

        except Exception as e:
            self.show_error(f"打开订单生成器失败: {e}")
            traceback.print_exc()

    def batch_generate_schemes(self):
        """批量生成订单方案"""
        try:
            # 简化的批量生成对话框
            num_schemes = simpledialog.askinteger(
                "批量生成",
                "请输入要生成的方案数量:",
                initialvalue=20,
                minvalue=1,
                maxvalue=100
            )

            if num_schemes:
                # 这里可以实现批量生成逻辑
                self.show_info(f"将生成 {num_schemes} 套订单方案")

        except Exception as e:
            self.show_error(f"批量生成失败: {e}")

    def _create_generator_ui(self, window, order_generator, config_data):
        """创建订单生成器界面"""
        # 创建主框架
        main_frame = ttk.Frame(window, padding="10")
        main_frame.pack(fill=BOTH, expand=True)

        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="生成配置", padding="10")
        config_frame.pack(fill=X, pady=5)

        # 特色菜品配置
        ttk.Label(config_frame, text="特色菜品 (4-7个):").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        special_dishes_var = StringVar(value="ds_chopve_1,ds_chopve_2,ds_flb_1,ds_chopve_3")
        ttk.Entry(config_frame, textvariable=special_dishes_var, width=50).grid(
            row=0, column=1, sticky=W, padx=5, pady=2)

        # 体力消耗范围
        ttk.Label(config_frame, text="总体力消耗范围:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        energy_min_var = StringVar(value="100")
        energy_max_var = StringVar(value="200")
        energy_frame = ttk.Frame(config_frame)
        energy_frame.grid(row=1, column=1, sticky=W, padx=5, pady=2)
        ttk.Entry(energy_frame, textvariable=energy_min_var, width=10).pack(side=LEFT)
        ttk.Label(energy_frame, text=" - ").pack(side=LEFT)
        ttk.Entry(energy_frame, textvariable=energy_max_var, width=10).pack(side=LEFT)

        # 体力消耗模式
        ttk.Label(config_frame, text="体力消耗模式:").grid(row=2, column=0, sticky=W, padx=5, pady=2)
        bet_mode_var = StringVar(value="2bet")
        bet_combo = ttk.Combobox(config_frame, textvariable=bet_mode_var,
                                values=["2bet", "4bet", "8bet"], state="readonly", width=10)
        bet_combo.grid(row=2, column=1, sticky=W, padx=5, pady=2)

        # 生成器效率模式
        ttk.Label(config_frame, text="生成器效率模式:").grid(row=3, column=0, sticky=W, padx=5, pady=2)
        efficiency_mode_var = StringVar(value="平均效率")
        efficiency_combo = ttk.Combobox(config_frame, textvariable=efficiency_mode_var,
                                      values=["平均效率", "动态效率", "top3平均效率"],
                                      state="readonly", width=15)
        efficiency_combo.grid(row=3, column=1, sticky=W, padx=5, pady=2)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=X, pady=10)

        def generate_schemes():
            try:
                # 构建生成配置
                config = self._build_generation_config(
                    special_dishes_var.get(),
                    float(energy_min_var.get()),
                    float(energy_max_var.get()),
                    bet_mode_var.get(),
                    efficiency_mode_var.get()
                )

                # 生成方案
                schemes = order_generator.generate_order_schemes(config, 5)

                # 显示结果
                self._show_generation_results(window, schemes)

            except Exception as e:
                messagebox.showerror("错误", f"生成方案失败: {e}", parent=window)

        ttk.Button(button_frame, text="生成方案", command=generate_schemes).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=window.destroy).pack(side=RIGHT, padx=5)

        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="生成结果", padding="10")
        result_frame.pack(fill=BOTH, expand=True, pady=5)

        # 结果文本框
        result_text = Text(result_frame, height=15, wrap=WORD)
        result_scrollbar = ttk.Scrollbar(result_frame, orient=VERTICAL, command=result_text.yview)
        result_text.configure(yscrollcommand=result_scrollbar.set)

        result_text.pack(side=LEFT, fill=BOTH, expand=True)
        result_scrollbar.pack(side=RIGHT, fill=Y)

        # 保存结果文本框的引用
        window.result_text = result_text

    def _build_generation_config(self, special_dishes_str, energy_min, energy_max, bet_mode_str, efficiency_mode_str):
        """构建订单生成配置"""
        try:
            from data_models import OrderGenerationConfig, BetMode, EfficiencyMode, PlayerGeneratorInfo

            config = OrderGenerationConfig()

            # 解析特色菜品
            config.special_dishes = [dish.strip() for dish in special_dishes_str.split(',') if dish.strip()]

            # 设置体力范围
            config.total_energy_range = (energy_min, energy_max)

            # 设置体力消耗模式
            bet_mode_map = {"2bet": BetMode.BET_2, "4bet": BetMode.BET_4, "8bet": BetMode.BET_8}
            config.bet_mode = bet_mode_map.get(bet_mode_str, BetMode.BET_2)

            # 设置效率模式
            efficiency_mode_map = {
                "平均效率": EfficiencyMode.AVERAGE,
                "动态效率": EfficiencyMode.DYNAMIC,
                "top3平均效率": EfficiencyMode.TOP3_AVERAGE
            }
            config.efficiency_mode = efficiency_mode_map.get(efficiency_mode_str, EfficiencyMode.AVERAGE)

            # 添加示例玩家生成器信息
            config.player_generators = [
                PlayerGeneratorInfo(generator_type="pd_1_4", level=4, count=2),
                PlayerGeneratorInfo(generator_type="pd_1_5", level=5, count=1),
                PlayerGeneratorInfo(generator_type="pd_2_4", level=4, count=2),
            ]

            return config

        except Exception as e:
            print(f"构建生成配置失败: {e}")
            # 返回默认配置
            return OrderGenerationConfig()

    def _show_generation_results(self, window, schemes):
        """显示生成结果"""
        if not hasattr(window, 'result_text'):
            return

        result_text = window.result_text
        result_text.delete(1.0, END)

        if not schemes:
            result_text.insert(END, "未能生成有效的订单方案。\n")
            return

        result_text.insert(END, f"成功生成 {len(schemes)} 套订单方案:\n\n")

        for i, scheme in enumerate(schemes):
            result_text.insert(END, f"=== 方案 {i+1} ===\n")
            result_text.insert(END, f"总体力消耗: {scheme.total_energy:.2f}\n")
            result_text.insert(END, f"方案有效性: {'有效' if scheme.is_valid else '无效'}\n")

            result_text.insert(END, "订单列表:\n")
            for j, order in enumerate(scheme.orders):
                result_text.insert(END, f"  订单{j+1}: {order.order_id}\n")
                for req in order.requirements:
                    result_text.insert(END, f"    需求: {req.item_type} x{req.count}\n")
                result_text.insert(END, f"    体力消耗: {order.energy_cost:.2f}\n")

            result_text.insert(END, "\n")

        result_text.see(1.0)

    # ============================================================
    # 数据加载方法
    # ============================================================
    
    def _create_item_loader(self):
        """创建物品配置加载器"""
        def loader():
            try:
                if not os.path.exists(self.item_config_path):
                    self.show_error(f"物品配置文件不存在: {self.item_config_path}")
                    return
                
                # 这里应该添加解析ItemModelConfig.lua的代码
                # 由于Lua文件解析较复杂，这里简化为空列表
                self.items = []
                self.update_status("物品配置已加载")
                
            except Exception as e:
                self.show_error(f"加载物品配置失败: {str(e)}")
                traceback.print_exc()
        
        return loader
    
    def _create_order_loader(self):
        """创建订单配置加载器"""
        def loader(chapter=None, force_reload=False):
            try:
                if chapter is None:
                    chapter = self.chapter_var.get()
                
                # 检查是否已经加载过
                if not force_reload and chapter in self.loaded_chapters:
                    return
                
                # 构建订单文件路径
                order_file = os.path.join(self.config_path, f"Chapter{chapter}", "OrderConfig.lua")
                
                if not os.path.exists(order_file):
                    # 如果文件不存在，初始化为空列表
                    self.orders[chapter] = []
                    self.loaded_chapters.add(chapter)
                    return
                
                # 这里应该添加解析OrderConfig.lua的代码
                # 由于Lua文件解析较复杂，这里简化为空列表
                self.orders[chapter] = []
                self.loaded_chapters.add(chapter)
                
                # 如果当前章节是选中的章节，刷新列表
                if chapter == self.chapter_var.get():
                    self.refresh_order_list()
                
                self.update_status(f"已加载章节 {chapter} 的订单配置")
                
            except Exception as e:
                self.show_error(f"加载订单配置失败: {str(e)}")
                traceback.print_exc()
        
        return loader
    
    def _create_pd_loader(self):
        """创建PD类型加载器"""
        def loader():
            try:
                # 这里应该添加加载PD类型的代码
                # 返回一个字典，键为PD类型名称，值为显示名称
                return {
                    "Item_1": "物品1",
                    "Coin": "金币",
                    # 添加更多PD类型...
                }
            except Exception as e:
                self.show_error(f"加载PD类型失败: {str(e)}")
                traceback.print_exc()
                return {}
        
        return loader
        
    def load_items(self):
        """加载物品配置"""
        item_config_path = os.path.join(os.path.dirname(__file__), 'Data', 'Config', 'ItemModelConfig.lua')
        items = set()
        
        try:
            with open(item_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取所有物品代码
            import re
            # 匹配 it_ 或 ds_ 开头的物品代码
            pattern = r'\b(it_\S+?)[,\s}]'
            items.update(re.findall(pattern, content))
            
            # 添加一些默认的货币类型
            items.update(['Coin', 'Gem', 'Exp'])
            
            # 转换为列表并排序
            items = sorted(list(items))
            
            # Update the item combo box if it exists
            if hasattr(self, 'req_type_combo'):
                self.req_type_combo['values'] = items
                if items:
                    self.req_type_combo.set(items[0])
            
            if hasattr(self, 'status_var'):
                self.status_var.set(f"已加载 {len(items)} 种物品类型")
            
            return items
            
        except Exception as e:
            error_msg = f"加载物品配置失败: {str(e)}\n文件: {self.item_config_path}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)
            return ['it_1_1_1', 'it_1_1_2', 'ds_1_1']  # 默认值
        
    def _create_pd_loader(self):
        """创建PD类型加载器"""
        def loader():
            """加载PD类型配置"""
            try:
                with open(self.item_config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 匹配所有PD配置
                pd_blocks = re.findall(r'\{\s*Type\s*=\s*"(pd_\d+_\d+)"(.+?)(?=\}\s*\n\s*\})', content, re.DOTALL)
                pd_types = {}
                
                for pd_type, pd_data in pd_blocks:
                    # 提取PD名称
                    name_match = re.search(r'Name\s*=\s*"([^"]+)"', pd_data)
                    name = name_match.group(1) if name_match else pd_type
                    pd_types[pd_type] = name
                    
                return pd_types
                
            except Exception as e:
                messagebox.showerror("错误", f"加载PD类型配置失败: {str(e)}")
                return {}
        return loader
        
    def _create_order_loader(self):
        """创建订单加载器"""
        def loader(chapter=None, force_reload=False):
            """加载订单配置
            
            Args:
                chapter: 要加载的章节号，如果为None则加载所有章节
                force_reload: 是否强制重新加载已加载的章节
            """
            try:
                loaded_count = 0
                if chapter is not None:
                    # 确保chapter是整数类型
                    chapter = int(chapter)
                    # 加载指定章节
                    if force_reload or chapter not in self.loaded_chapters:
                        self._load_single_chapter(chapter)
                        loaded_count = len(self.orders.get(chapter, []))
                    # 更新当前章节
                    self.current_chapter = chapter
                    self.all_orders = self.orders.get(chapter, [])
                else:
                    # 加载所有章节
                    for ch in range(1, self.max_chapters + 1):
                        if force_reload or ch not in self.loaded_chapters:
                            self._load_single_chapter(ch)
                            loaded_count += len(self.orders.get(ch, []))
                    # 更新当前章节为第一个章节
                    if self.orders:
                        self.current_chapter = next(iter(self.orders.keys()))
                        self.all_orders = self.orders[self.current_chapter]
                
                # 更新UI
                self.update_order_list()
                if chapter is not None:
                    self.chapter_var.set(chapter)  # 更新UI中的章节选择
                    self.status_var.set(f"成功加载 {len(self.orders.get(chapter, []))} 个订单 (第{chapter}章)")
                else:
                    total_orders = sum(len(orders) for orders in self.orders.values())
                    self.status_var.set(f"成功加载 {total_orders} 个订单 (全部章节)")
                
                return loaded_count
                
            except ValueError as ve:
                error_msg = f"无效的章节号: {chapter}"
                print(f"{error_msg} - {str(ve)}")
                messagebox.showerror("错误", error_msg)
                self.status_var.set(error_msg)
                return 0
            except Exception as e:
                error_msg = f"加载订单配置失败: {str(e)}"
                print(f"{error_msg}\n{str(e)}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("错误", error_msg)
                self.status_var.set(error_msg)
                return 0
        return loader
    
    def _load_single_chapter(self, chapter):
        """加载单个章节的订单配置"""
        order_file = os.path.join(self.config_path, f"OrderFixedConfig_{chapter}.lua")
        print(f"Loading order file: {order_file}")
        
        if not os.path.exists(order_file):
            print(f"订单文件不存在: {order_file}")
            self.orders[chapter] = []
            self.loaded_chapters.add(chapter)
            return
            
        try:
            with open(order_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除注释和多余的空白字符
            content = re.sub(r'--.*?\n', '\n', content)  # 移除行注释
            content = re.sub(r'\s+', ' ', content)  # 将多个空白字符替换为单个空格
            
            # 提取所有订单块
            order_blocks = re.findall(r'\{\s*Id\s*=\s*"([^"]+)"(.*?)(?=\s*\}\s*(?:,\s*\{|\s*$))', content, re.DOTALL)
            
            chapter_orders = []
            for order_id, order_data in order_blocks:
                try:
                    order = {
                        "Id": str(order_id).strip(),
                        "ChapterId": int(chapter),
                        "GroupId": 1,  # 默认值
                        "Requirements": [],
                        "Rewards": []
                    }
                    
                    # 提取组ID
                    group_match = re.search(r'GroupId\s*=\s*(\d+)', order_data)
                    if group_match:
                        order["GroupId"] = int(group_match.group(1))
                    
                    # 提取需求
                    req_matches = re.finditer(
                        r'Requirement_(\d+)\s*=\s*\{(.*?)\}',
                        order_data, re.DOTALL
                    )
                    
                    for req_match in req_matches:
                        if len(req_match.groups()) >= 2:
                            req_num = int(req_match.group(1))
                            req_content = req_match.group(2)
                            
                            # 提取Type和Count
                            type_match = re.search(r'Type\s*=\s*"([^"]+)"', req_content)
                            count_match = re.search(r'Count\s*=\s*(\d+)', req_content)
                            
                            if type_match and count_match:
                                order["Requirements"].append({
                                    "Type": type_match.group(1).strip(),
                                    "Count": int(count_match.group(1))
                                })
                    
                    # 提取奖励
                    rewards_match = re.search(r'Rewards\s*=\s*\{(.*?)\}', order_data, re.DOTALL)
                    if rewards_match:
                        rewards_content = rewards_match.group(1)
                        reward_matches = re.finditer(
                            r'\{\s*Currency\s*=\s*"([^"]+)"\s*,\s*Amount\s*=\s*(\d+)\s*\}',
                            rewards_content
                        )
                        
                        for rew_match in reward_matches:
                            if len(rew_match.groups()) >= 2:
                                order["Rewards"].append({
                                    "Currency": rew_match.group(1).strip(),
                                    "Amount": int(rew_match.group(2))
                                })
                    
                    # 提取组ID - 改进的匹配模式
                    group_match = re.search(r'GroupId\s*=\s*(\d+)', order_data)
                    if group_match:
                        order["GroupId"] = int(group_match.group(1))
                    
                    # 提取前置订单ID
                    pre_id_match = re.search(r'PreId\s*=\s*\{\s*([^}]*)\s*\}', order_data)
                    if pre_id_match and pre_id_match.group(1):
                        pre_ids = re.findall(r'"([^"]+)"', pre_id_match.group(1))
                        order["PreId"] = [pid.strip() for pid in pre_ids if pid.strip()]
                    
                    chapter_orders.append(order)
                    
                except Exception as e:
                    print(f"解析订单 {order_id} 时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    continue
            
            self.orders[chapter] = chapter_orders
            self.loaded_chapters.add(chapter)
            print(f"成功加载 {len(chapter_orders)} 个订单 (第{chapter}章)")
            
        except Exception as e:
            error_msg = f"加载订单文件 {order_file} 时出错: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
            self.orders[chapter] = []
            self.loaded_chapters.add(chapter)

    def _load_single_chapter(self, chapter):
        """加载单个章节的订单配置"""
        order_file = os.path.join(self.config_path, f"OrderFixedConfig_{chapter}.lua")
        print(f"Loading order file: {order_file}")

        if not os.path.exists(order_file):
            print(f"订单文件不存在: {order_file}")
            self.orders[chapter] = []
            self.loaded_chapters.add(chapter)
            return

        try:
            with open(order_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取所有订单块
            order_blocks = re.findall(r'\{\s*Id\s*=\s*"([^"]+)"(.*?)(?=\s*\},?\s*\n\s*\})', content, re.DOTALL)

            chapter_orders = []
            for order_id, order_data in order_blocks:
                try:
                    order = {
                        "Id": str(order_id),
                        "ChapterId": int(chapter),
                        "GroupId": 1,
                        "Requirements": [],
                        "Rewards": []
                    }


                    # 提取需求
                    reqs = re.finditer(
                        r'Requirement_\d+\s*=\s*\{\s*Type\s*=\s*"([^"]+)"\s*,\s*Count\s*=\s*(\d+)\s*\}',
                        order_data, re.DOTALL
                    )

                    for req in reqs:
                        if req and len(req.groups()) >= 2:
                            order["Requirements"].append({
                                "Type": str(req.group(1)),
                                "Count": int(req.group(2))
                            })

                    # 提取奖励
                    rewards = re.finditer(
                        r'Reward_\d+\s*=\s*\{\s*Currency\s*=\s*"([^"]+)"\s*,\s*Amount\s*=\s*(\d+)\s*\}',
                        order_data, re.DOTALL
                    )

                    for reward in rewards:
                        if reward and len(reward.groups()) >= 2:
                            order["Rewards"].append({
                                "Currency": str(reward.group(1)),
                                "Amount": int(reward.group(2))
                            })

                    # 提取组ID
                    group_match = re.search(r'GroupId\s*=\s*(\d+)', order_data)
                    if group_match:
                        order["GroupId"] = int(group_match.group(1))


                    # 提取前置订单ID
                    pre_id_match = re.search(r'PreId\s*=\s*\{\s*([^}]*)\s*\}', order_data)
                    if pre_id_match and pre_id_match.group(1):
                        pre_ids = re.findall(r'"([^"]+)"', pre_id_match.group(1))
                        order["PreId"] = [pid.strip() for pid in pre_ids if pid.strip()]

                    chapter_orders.append(order)
                except Exception as e:
                    print(f"解析订单 {order_id} 时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    continue

            self.orders[chapter] = chapter_orders
            self.loaded_chapters.add(chapter)
            print(f"成功加载 {len(chapter_orders)} 个订单 (第{chapter}章)")

        except Exception as e:
            print(f"加载订单文件 {order_file} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            self.orders[chapter] = []
            self.loaded_chapters.add(chapter)

    def update_order_list(self):
        """更新订单列表显示"""
        if not hasattr(self, 'order_tree'):
            return

        try:
            # 清空当前显示
            for item in self.order_tree.get_children():
                self.order_tree.delete(item)

            # 获取当前章节的订单
            current_chapter_orders = self.orders.get(self.current_chapter, [])

            # 添加订单到列表
            for order in current_chapter_orders:
                if not isinstance(order, dict):
                    print(f"警告: 发现无效的订单数据: {order}")
                    continue

                order_id = order.get('Id', '')
                group_id = order.get('GroupId', 1)

                # 获取需求物品信息
                reqs = order.get('Requirements', [])
                req_text = ", ".join([f"{r.get('Type', '')}x{r.get('Count', 0)}" for r in reqs if isinstance(r, dict)])

                # 获取奖励信息
                rews = order.get('Rewards', [])
                rew_text = ", ".join([f"{r.get('Currency', '')}x{r.get('Amount', 0)}" for r in rews if isinstance(r, dict)])

                self.order_tree.insert("", "end", values=(order_id, group_id, req_text, rew_text))

            # 更新状态栏
            order_count = len(current_chapter_orders)
            status_text = f"第{self.current_chapter}章 - 共 {order_count} 个订单"
            if order_count == 0:
                status_text += " (空章节)"
            self.status_var.set(status_text)

        except Exception as e:
            error_msg = f"更新订单列表失败: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.status_var.set(error_msg)

    def on_order_select(self, event):
        """Handle order selection"""
        if not hasattr(self, 'order_tree') or not hasattr(self, 'energy_var'):
            return

        selected = self.order_tree.selection()
        if not selected:
            self.energy_var.set("")
            return

        try:
            order_id = self.order_tree.item(selected[0])['values'][0]

            # 在当前章节的订单中查找
            current_chapter_orders = self.orders.get(self.current_chapter, [])
            order = next((o for o in current_chapter_orders if str(o.get("Id")) == str(order_id)), None)

            if not order:
                # 设置标签样式
                self.order_tree.tag_configure('uncompleted', foreground='black')
                return
                
            # 更新UI显示选中的订单
            self.current_order = order
            self.order_id_var.set(order_id)
            self.group_id_var.set(str(order.get('GroupId', 1)))
            
            # 更新需求和奖励列表
            self.update_requirements_list(order.get('Requirements', []))
            self.update_rewards_list(order.get('Rewards', []))
            
            # 计算并显示能量消耗
            self.calculate_energy_consumption()
            
        except Exception as e:
            error_msg = f"选择订单时出错: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.status_var.set(error_msg)
            
        self.order_tree.tag_configure('completed', foreground='green')
        """Handle order selection"""
        if not hasattr(self, 'order_tree') or not hasattr(self, 'energy_var'):
            return
            
        selected = self.order_tree.selection()
        if not selected:
            self.energy_var.set("")
            return
            
        try:
            order_id = self.order_tree.item(selected[0])['values'][0]
            
            # 在当前章节的订单中查找
            current_chapter_orders = self.orders.get(self.current_chapter, [])
            order = next((o for o in current_chapter_orders if str(o.get("Id")) == str(order_id)), None)
            
            if not order:
                self.energy_var.set("")
                return
                
            # 更新基本信息
            if hasattr(self, 'order_id_var'):
                self.order_id_var.set(order.get("Id", ""))
            if hasattr(self, 'group_id_var'):
                self.group_id_var.set(order.get("GroupId", 1))
            if hasattr(self, 'chapter_var'):
                self.chapter_var.set(order.get("ChapterId", self.current_chapter))
            
            # 更新需求列表和奖励列表
            self.update_requirements_list(order.get("Requirements", []))
            self.update_rewards_list(order.get("Rewards", []))
            
            # 计算并显示能量消耗
            total_energy = 0
            for req in order.get("Requirements", []):
                energy = self.calculate_energy(req.get("Type", ""), req.get("Count", 0))
                total_energy += energy
            
            self.energy_var.set(f"{total_energy:.1f}")
        except Exception as e:
            print(f"选择订单时出错: {e}")
            self.energy_var.set("计算错误")
    
    def update_requirements_list(self, requirements):
        """更新需求列表"""
        if not hasattr(self, 'req_tree') or self.req_tree is None:
            return
            
        try:
            # 清空当前列表
            for item in self.req_tree.get_children():
                self.req_tree.delete(item)
            
            # 添加需求
            for req in requirements:
                item_type = req.get("Type", "")
                count = req.get("Count", 0)
                level1_count = self.calculate_level1_equivalent(item_type, count)
                energy = self.calculate_energy(item_type, count)
                
                self.req_tree.insert("", "end", values=(
                    item_type,
                    count,
                    level1_count,
                    f"{energy:.1f} 能量"
                ))
        except Exception as e:
            print(f"更新需求列表时出错: {e}")
    
    def update_rewards_list(self, rewards):
        """更新奖励列表"""
        if not hasattr(self, 'rew_tree') or self.rew_tree is None:
            return
            
        try:
            # 清空当前列表
            for item in self.rew_tree.get_children():
                self.rew_tree.delete(item)
            
            # 添加奖励
            for rew in rewards:
                self.rew_tree.insert("", "end", values=(
                    rew.get("Currency", ""),
                    rew.get("Amount", 0)
                ))
        except Exception as e:
            print(f"更新奖励列表时出错: {e}")
    
    def calculate_level1_equivalent(self, item_type, count):
        """计算1级等效数量"""
        # 这里简化处理，实际应根据物品等级计算
        # 例如：it_1_2_3 表示1级链2级物品，需要2^(2-1)=2个1级物品
        match = re.match(r'it_(\d+)_(\d+)_', item_type)
        if match:
            level = int(match.group(2))
            return count * (2 ** (level - 1))
        return count
    
    # calculate_energy 方法已在上方定义，支持更多参数
    
    def save_order(self):
        """保存订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            # 数据验证
            order_id = self.order_id_var.get().strip()
            if not order_id:
                raise ValueError("订单ID不能为空")
                
            # 验证订单ID唯一性
            current_chapter = self.current_chapter
            for order in self.orders.get(current_chapter, []):
                if order["Id"] == order_id and order is not self.current_order:
                    raise ValueError(f"订单ID {order_id} 已存在")
            
            # 验证需求物品
            requirements = []
            for item in self.req_tree.get_children():
                values = self.req_tree.item(item)['values']
                if len(values) < 2:
                    continue
                    
                item_type = values[0]
                try:
                    count = int(values[1])
                    if count <= 0:
                        raise ValueError("物品数量必须大于0")
                except ValueError:
                    raise ValueError(f"无效的物品数量: {values[1]}")
                    
                # 验证物品类型是否存在
                if item_type not in self.items:
                    raise ValueError(f"无效的物品类型: {item_type}")
                    
                requirements.append({
                    "Type": item_type,
                    "Count": count
                })
                
            if not requirements:
                raise ValueError("订单必须至少有一个需求物品")
                
            # 验证奖励
            rewards = []
            for item in self.rew_tree.get_children():
                values = self.rew_tree.item(item)['values']
                if len(values) < 2:
                    continue
                    
                currency = values[0]
                try:
                    amount = int(values[1])
                    if amount <= 0:
                        raise ValueError("奖励数量必须大于0")
                except ValueError:
                    raise ValueError(f"无效的奖励数量: {values[1]}")
                    
                rewards.append({
                    "Currency": currency,
                    "Amount": amount
                })
                
            if not rewards:
                raise ValueError("订单必须至少有一个奖励")
                
            # 标记为已修改
            self.modified = True
            self.root.title("订单编辑器 - 浮岛物语 (*)")
            
            order_id = self.order_id_var.get().strip()
            if not order_id:
                messagebox.showwarning("警告", "订单ID不能为空")
                return
                
            # 获取当前订单数据
            current_chapter = self.current_chapter
            current_orders = self.orders.get(current_chapter, [])
            
            # 查找现有订单或创建新订单
            order = next((o for o in current_orders if str(o.get("Id")) == str(order_id)), None)
            is_new = order is None
            
            if is_new:
                order = {
                    "Id": order_id,
                    "ChapterId": current_chapter,
                    "GroupId": 1,
                    "Requirements": [],
                    "Rewards": []
                }
                current_orders.append(order)
            
            # 更新订单信息
            order["GroupId"] = int(self.group_id_var.get())
            
            # 收集需求
            requirements = []
            for item in self.req_tree.get_children():
                values = self.req_tree.item(item)['values']
                if len(values) >= 2:  # 确保有足够的元素
                    requirements.append({
                        "Type": values[0],
                        "Count": int(values[1])
                    })
            order["Requirements"] = requirements
            
            # 收集奖励
            rewards = []
            for item in self.rew_tree.get_children():
                values = self.rew_tree.item(item)['values']
                if len(values) >= 2:  # 确保有足够的元素
                    rewards.append({
                        "Currency": values[0],
                        "Amount": int(values[1])
                    })
            order["Rewards"] = rewards
            
            # 更新订单列表
            self.orders[current_chapter] = current_orders
            self.update_order_list()
            
            # 保存到文件
            self._save_chapter_to_file(current_chapter)
            
            messagebox.showinfo("成功", f"订单 {order_id} 已保存")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存订单时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _save_chapter_to_file(self, chapter):
        """将章节订单保存到文件"""
        if chapter not in self.orders:
            return
            
        order_file = os.path.join(self.config_path, f"OrderFixedConfig_{chapter}.lua")
        orders = self.orders.get(chapter, [])
        
        # 创建备份
        backup_dir = os.path.join(self.config_path, "backup")
        os.makedirs(backup_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(backup_dir, f"OrderFixedConfig_{chapter}_backup_{timestamp}.lua")
        
        try:
            # 先备份原文件
            if os.path.exists(order_file):
                shutil.copy2(order_file, backup_file)
                
            # 写入新文件
            with open(order_file, 'w', encoding='utf-8') as f:
                f.write("return {")
                
                for i, order in enumerate(orders):
                    if i > 0:
                        f.write(",\n    ")
                    else:
                        f.write("\n    ")
                        
                    f.write(f'{{Id = "{order["Id"]}",\n')
                    f.write(f'     GroupId = {order.get("GroupId", 1)},\n')
                    
                    # 写入需求
                    for j, req in enumerate(order.get("Requirements", []), 1):
                        f.write(f'     Requirement_{j} = {{Type = "{req["Type"]}", Count = {req["Count"]}}},\n')
                    
                    # 写入奖励
                    for j, rew in enumerate(order.get("Rewards", []), 1):
                        f.write(f'     Reward_{j} = {{Currency = "{rew["Currency"]}", Amount = {rew["Amount"]}}},\n')
                    
                    # 写入前置订单ID
                    if "PreId" in order and order["PreId"]:
                        pre_ids = '", "'.join(order["PreId"])
                        f.write(f'     PreId = {{"{pre_ids}"}}')
                    
                    f.write('}')
                
                f.write("\n}")
                
            print(f"成功保存订单到文件: {order_file}")
            
        except Exception as e:
            print(f"保存订单文件 {order_file} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def add_order(self):
        """添加新订单"""
        try:
            # 验证当前章节
            if not hasattr(self, 'current_chapter'):
                raise ValueError("当前章节未设置")
                
            # 确保章节已加载
            if self.current_chapter not in self.loaded_chapters:
                self.load_order_config(self.current_chapter)
                
            # 确保当前章节订单列表存在
            if self.current_chapter not in self.orders:
                self.orders[self.current_chapter] = []
                
            # 生成唯一订单ID
            used_ids = set()
            for order in self.orders[self.current_chapter]:
                try:
                    if isinstance(order, dict) and "Id" in order:
                        used_ids.add(int(order["Id"]))
                except (ValueError, TypeError):
                    continue
                    
            new_id = 1
            while new_id in used_ids:
                new_id += 1
                
            # 创建新订单
            self.current_order = {
                "Id": str(new_id),
                "ChapterId": self.current_chapter,
                "GroupId": 1,
                "Requirements": [],
                "Rewards": [],
                "CreatedAt": datetime.datetime.now().isoformat()
            }
            
            # 更新UI
            self.order_id_var.set(str(new_id))
            self.group_id_var.set(1)
            self.chapter_var.set(str(self.current_chapter))
            
            # 清空需求和奖励列表
            self.update_requirements_list([])
            self.update_rewards_list([])
            
            # 添加到当前章节的订单列表
            if self.current_chapter not in self.orders:
                self.orders[self.current_chapter] = []
            self.orders[self.current_chapter].append(self.current_order)
            
            # 更新订单列表显示
            self.all_orders = self.orders.get(self.current_chapter, [])
            self.update_order_list()
            
            # 设置状态
            self.status_var.set(f"已创建新订单: {new_id}")
            
            # 滚动到新添加的订单
            self.order_tree.see(self.order_tree.get_children()[-1] if self.order_tree.get_children() else '')
            
        except Exception as e:
            error_msg = f"创建订单失败: {str(e)}"
            print(f"Error in add_order: {error_msg}")
            import traceback
            traceback.print_exc()
            self.status_var.set(error_msg)
    
    def delete_order(self):
        """删除当前选中的订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        order_id = self.order_tree.item(selected[0])['values'][0]
        
        if not messagebox.askyesno("确认删除", f"确定要删除订单 {order_id} 吗？此操作不可恢复！"):
            return
            
        try:
            # 从内存中删除订单
            current_chapter = self.current_chapter
            if current_chapter in self.orders:
                self.orders[current_chapter] = [
                    o for o in self.orders[current_chapter] 
                    if str(o.get("Id")) != str(order_id)
                ]
                
                # 保存到文件
                self._save_chapter_to_file(current_chapter)
                
                # 更新UI
                self.update_order_list()
                
                # 清空编辑区域
                self.order_id_var.set("")
                self.group_id_var.set(1)
                for item in self.req_tree.get_children():
                    self.req_tree.delete(item)
                for item in self.rew_tree.get_children():
                    self.rew_tree.delete(item)
                    
                messagebox.showinfo("成功", f"订单 {order_id} 已删除")
            
        except Exception as e:
            messagebox.showerror("错误", f"删除订单时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def add_requirement(self):
        """添加需求物品"""
        # 这里可以打开一个对话框让用户选择物品和数量
        # 简化处理，直接添加一个空的需求
        self.req_tree.insert("", "end", values=("it_1_1_1", 1, 1, "5 能量"))
    
    def delete_requirement(self):
        """删除选中的需求物品"""
        selected = self.req_tree.selection()
        if selected:
            self.req_tree.delete(selected[0])
    
    def add_reward(self):
        """添加奖励"""
        # 这里可以打开一个对话框让用户选择货币类型和数量
        # 简化处理，直接添加一个空的奖励
        self.rew_tree.insert("", "end", values=("coin", 100))
    
    def delete_reward(self):
        """删除选中的奖励"""
        selected = self.rew_tree.selection()
        if selected:
            self.rew_tree.delete(selected[0])
            
    def add_requirement(self):
        """添加需求物品"""
        try:
            # 验证当前订单选择
            selected = self.order_tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择一个订单")
                return
                
            # 验证物品类型
            item_type = self.req_type_var.get().strip()
            if not item_type:
                messagebox.showwarning("警告", "请选择物品类型")
                return
                
            # 验证物品类型是否有效
            if not hasattr(self, 'items') or item_type not in self.items:
                messagebox.showerror("错误", f"无效的物品类型: {item_type}")
                return
                
            # 验证数量
            try:
                count = int(self.req_count_var.get())
                if count <= 0:
                    raise ValueError("数量必须大于0")
            except ValueError as ve:
                messagebox.showerror("错误", f"无效的数量: {str(ve)}")
                return
                
            # 获取当前订单
            order_id = self.order_tree.item(selected[0])['values'][0]
            current_chapter = self.current_chapter
            
            # 确保当前章节订单存在
            if current_chapter not in self.orders:
                self.orders[current_chapter] = []
                
            # 查找订单
            order = None
            for o in self.orders[current_chapter]:
                if isinstance(o, dict) and str(o.get("Id")) == str(order_id):
                    order = o
                    break
                    
            if not order:
                messagebox.showerror("错误", f"未找到订单ID: {order_id}")
                return
                
            # 初始化需求列表
            if "Requirements" not in order or not isinstance(order["Requirements"], list):
                order["Requirements"] = []
                
            # 检查是否已存在相同类型的物品
            requirement_added = False
            for req in order["Requirements"]:
                if isinstance(req, dict) and req.get("Type") == item_type:
                    req["Count"] = req.get("Count", 0) + count
                    requirement_added = True
                    break
                    
            # 如果不存在相同类型，则添加新需求
            if not requirement_added:
                order["Requirements"].append({
                    "Type": item_type,
                    "Count": count
                })
                
            # 更新UI
            self.current_order = order
            self.update_requirements_list(order.get("Requirements", []))
            self.status_var.set(f"已添加需求: {item_type} x{count}")
            
            # 标记为已修改
            self.modified = True
            self.root.title("订单编辑器 - 浮岛物语 (*)")
            
        except Exception as e:
            messagebox.showerror("错误", f"添加需求失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def delete_requirement(self):
        """删除选中的需求物品"""
        selected = self.req_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个需求物品")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = next((o for o in self.orders if o["Id"] == order_id), None)
            
            if order and "Requirements" in order:
                item = self.req_tree.item(selected[0])
                item_type = item['values'][0]
                
                # 删除第一个匹配的物品类型
                for i, req in enumerate(order["Requirements"]):
                    if req["Type"] == item_type:
                        order["Requirements"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已删除需求: {item_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除需求失败: {str(e)}")
    
    def add_reward(self):
        """添加奖励"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            reward_type = self.reward_type_var.get().strip()
            if not reward_type:
                messagebox.showwarning("警告", "请选择奖励类型")
                return
                
            amount = int(self.reward_amount_var.get())
            if amount <= 0:
                raise ValueError("数量必须大于0")
                
            order_id = self.order_tree.item(selected[0])['values'][0]
            order = next((o for o in self.orders if o["Id"] == order_id), None)
            
            if order:
                if "Rewards" not in order:
                    order["Rewards"] = []
                
                # 检查是否已存在相同类型的奖励
                for reward in order["Rewards"]:
                    if reward["Currency"] == reward_type:
                        reward["Amount"] += amount
                        break
                else:
                    order["Rewards"].append({
                        "Currency": reward_type,
                        "Amount": amount
                    })
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已添加奖励: {reward_type} x{amount}")
                
        except ValueError as ve:
            messagebox.showerror("错误", f"无效的数量: {str(ve)}")
        except Exception as e:
            messagebox.showerror("错误", f"添加奖励失败: {str(e)}")
    
    def delete_reward(self):
        """删除选中的奖励"""
        selected = self.reward_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个奖励")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = next((o for o in self.orders if o["Id"] == order_id), None)
            
            if order and "Rewards" in order:
                item = self.reward_tree.item(selected[0])
                reward_type = item['values'][0]
                
                # 删除第一个匹配的奖励类型
                for i, reward in enumerate(order["Rewards"]):
                    if reward["Currency"] == reward_type:
                        order["Rewards"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已删除奖励: {reward_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除奖励失败: {str(e)}")
            
    def show_help(self):
        """显示帮助文档"""
        help_text = """订单编辑器使用指南

1. 基本操作
- 左键点击章节按钮切换不同章节
- 双击订单可查看/编辑详情
- 右键点击订单可弹出操作菜单

2. 订单编辑
- 需求物品: 添加订单需要的物品及数量
- 奖励: 设置完成订单后可获得的奖励
- PD配置: 设置生产者类型和等级来计算能量消耗

3. 快捷键
- Ctrl+S: 保存当前订单
- Ctrl+N: 新建订单
- Ctrl+D: 删除订单
- Ctrl+Q: 退出程序

4. 数据验证
- 订单ID必须唯一
- 物品数量必须大于0
- 物品类型必须有效
- 订单必须至少有一个需求和奖励

5. 其他功能
- 自动备份: 每次保存都会创建备份
- 修改标记: 标题栏显示*表示有未保存的修改
"""
        messagebox.showinfo("订单编辑器帮助", help_text)
    
    def delete_order(self):
        """删除订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        order_id = self.order_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("确认", f"确定要删除订单 {order_id} 吗？"):
            # 标记为已修改
            self.modified = True
            self.root.title("订单编辑器 - 浮岛物语 (*)")
            self.orders = [o for o in self.orders if o["Id"] != order_id]
            self.update_order_list()
            self.status_var.set(f"订单 {order_id} 已删除")

if __name__ == "__main__":
    root = Tk()
    app = OrderEditor(root)
    root.mainloop()