local EMetaDBKey = {
  CurChapterId = "CurChapterId",
  CurOrderGroupId = "CurOrderGroupId",
  CurFinishedGroupCount = "CurFinishedGroupCount",
  CurGroupFinishedOrderIds = "CurGroupFinishedOrderIds",
  TotalFinishedGroupCount = "TotalFinishedGroupCount",
  OrderGroupConsumeEnergy = "OrderGroupConsumeEnergy",
  OrderGroupCostCurDayEnergy = "OrderGroupCostCurDayEnergy",
  OrderGroupCostPastDayEnergy = "OrderGroupCostPastDayEnergy"
}
local DBColumnValue = "value"
MainOrderModel = setmetatable({}, BaseOrderModel)
MainOrderModel.__index = MainOrderModel

function MainOrderModel.Create(dbMetaTable, dbTable, boardModel)
  local mainOrderModel = setmetatable({}, MainOrderModel)
  mainOrderModel:Init(dbMetaTable, dbTable, boardModel)
  return mainOrderModel
end

function MainOrderModel:Init(dbMetaTable, dbTable, boardModel)
  self.dataModel = MainOrderDataModel.Create()
  self.m_mapCurGroupFinishedOrderIds = {}
  BaseOrderModel.Init(self, dbMetaTable, dbTable, boardModel)
end

function MainOrderModel:LoadFileConfig()
  self.dataModel:LoadFileConfig()
  self.m_avatarConfig = GM.ConfigModel:GetLocalConfig(LocalConfigKey.OrderAvatar)
end

function MainOrderModel:OnSyncDataFinished()
  BaseOrderModel.OnSyncDataFinished(self)
  local curChapterId = self:_GetCurChapterId()
  self.dataModel:LoadChapterConfig(curChapterId)
  self:_RefreshCurOrderDay()
  GM.ChapterDataModel:ConsiderOrderDay(self.m_curOrderDay)
  self.m_mapCurGroupFinishedOrderIds = self:_GetCurGroupFinishedOrderIds()
end

function MainOrderModel:LateInit()
  self:_RemoveErrorOrder()
  EventDispatcher.AddListener(EEventType.BakeOutModeChanged, self, self._BakeOutModeChanged)
  EventDispatcher.AddListener(EEventType.BakeOutStateChanged, self, self._BakeOutModeChanged)
  EventDispatcher.AddListener(EEventType.NewChapterUnlocked, self, self._OnNewChapterUnlocked)
  EventDispatcher.AddListener(EEventType.NewContentReleased, self, self._OnNewContentReleased)
  for _, definition in pairs(DashActivityDefinition) do
    if definition.UpdateMainOrderStatus then
      EventDispatcher.AddListener(definition.StateChangedEvent, self, self._UpdateMainOrderStatus)
      EventDispatcher.AddListener(definition.UpgradedEvent, self, self._UpdateMainOrderStatus)
    end
  end
  for _, activityDefinition in pairs(CoinRaceActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._UpdateMainOrderStatus)
  end
  for _, activityDefinition in pairs(DigActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._UpdateMainOrderStatus)
    EventDispatcher.AddListener(activityDefinition.DigItemStateChangedEvent, self, self._UpdateMainOrderStatus)
  end
  for _, activityDefinition in pairs(ExtraBoardActivityDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._UpdateMainOrderStatus)
  end
  for _, activityDefinition in pairs(BlindChestDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._UpdateMainOrderStatus)
  end
  for _, activityDefinition in pairs(PkRaceDefinition) do
    EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self._UpdateMainOrderStatus)
  end
  self:_UpdateMainOrderStatus()
  BaseOrderModel.LateInit(self)
end

function MainOrderModel:RemoveBakeOutOrders()
  local arrRandomOrderIds = {}
  for orderId, order in pairs(self.m_orders) do
    if order:GetType() == OrderType.BakeOut then
      arrRandomOrderIds[#arrRandomOrderIds + 1] = orderId
    end
  end
  for _, randomOrderId in ipairs(arrRandomOrderIds) do
    self:ForceRemoveOrder(randomOrderId)
    Log.Info("Bakeout Remove Order " .. randomOrderId)
  end
end

function MainOrderModel:_OnNewChapterUnlocked()
  self:_TryFillOrders()
  EventDispatcher.DispatchEvent(EEventType.OrderStateChanged)
end

function MainOrderModel:_BakeOutModeChanged()
  self:_TryFillOrders()
  self:_UpdateMainOrderStatus()
end

function MainOrderModel:_TryFillNextChapter()
  if self:_CanFillNextChapterOrders() then
    local ongoingChapterId = GM.TaskManager:GetOngoingChapterId()
    if ongoingChapterId > self:_GetCurChapterId() then
      if self:CanClaimGroupReward() then
        self:ClaimGroupReward()
      end
      self.dataModel:LoadChapterConfig(ongoingChapterId)
      self:_SetCurChapterId(ongoingChapterId)
      self:_SetCurOrderGroupId(1)
      self:_ClearFinishedOrderIds()
      self:_TryFillOrders()
      EventDispatcher.DispatchEvent(EEventType.UpdateOrderGroupButton)
      EventDispatcher.DispatchEvent(EEventType.OrderGroupRefreshed)
      GM.TaskManager:TryBalanceTaskCost()
      return true
    end
  end
end

function MainOrderModel:_RemoveFinishedOrders()
  local hasOrderFinished = false
  local arrOrderIdsToRemove = {}
  for orderId, order in pairs(self.m_orders) do
    if order:GetState() == OrderState.Finished then
      hasOrderFinished = true
      arrOrderIdsToRemove[#arrOrderIdsToRemove + 1] = orderId
    end
  end
  for _, orderIdToRemove in ipairs(arrOrderIdsToRemove) do
    self:_DBRemoveOrder(orderIdToRemove)
    self.m_orders[orderIdToRemove] = nil
    self.m_mapCurGroupFinishedOrderIds[orderIdToRemove] = true
  end
  if hasOrderFinished then
    self:_SetCurGroupFinishedOrderIds(self.m_mapCurGroupFinishedOrderIds)
    GM.FlambeTimeModel:OnOrderFinished()
  end
end

function MainOrderModel:_TryFillOrders()
  self:_RemoveFinishedOrders()
  local curGroupId = self:_GetCurOrderGroupId()
  local arrNewOrders = self:_TryCreateOrders()
  if arrNewOrders and arrNewOrders[1] then
    self:_OnCreatedOrders(arrNewOrders)
  elseif self:_CurrentlyNoFixedOrders() then
    self:_TryFillNextChapter()
  end
  if curGroupId ~= self:_GetCurOrderGroupId() then
    GM.ShopModel:RefreshFlashSaleByDayRefresh()
  end
end

function MainOrderModel:_OnCreatedOrders(arrNewOrders)
  for _, newOrder in ipairs(arrNewOrders) do
    self:_DBAddOrder(newOrder)
    self.m_orders[newOrder:GetId()] = newOrder
    self:_LogOrderAction(newOrder, EBIOrderAction.OrderUnlock)
  end
  self.m_boardModel:UpdateOrderState()
end

function MainOrderModel:_RemoveErrorOrder()
  local orders = self:GetOrders()
  if Table.IsEmpty(orders) then
    return
  end
  local arrOrderIdsToRemove = {}
  for orderId, order in pairs(orders) do
    if #order:GetRequirements() == 0 then
      arrOrderIdsToRemove[#arrOrderIdsToRemove + 1] = orderId
    end
  end
  for _, orderIdToRemove in ipairs(arrOrderIdsToRemove) do
    self:ForceRemoveOrder(orderIdToRemove)
    Log.Error("自动移除了非法订单：" .. orderIdToRemove)
  end
end

function MainOrderModel:_UpdateMainOrderStatus()
  EventDispatcher.DispatchEvent(EEventType.OrderStatusChanged)
end

local REQUIREMENT_SPLIT = ";"

function MainOrderModel:_DBGetOrders()
  local orders = {}
  local orderData = self.m_dbTable:GetValues()
  for orderId, data in pairs(orderData) do
    local requirements = StringUtil.Split(data.requirementStr, REQUIREMENT_SPLIT)
    local order = MainOrder.Create(self, data.id, data.groupId, data.chapterId, data.avatarId, requirements, data.type, data.createTime, ConfigUtil.StringToRewards(data.rewards), data.cleanGoldCount)
    orders[orderId] = order
    if #requirements ~= #order:GetRequirements() then
      self:_DBAddOrder(order)
    end
  end
  return orders
end

function MainOrderModel:_DBAddOrder(order)
  local addData = {
    [order:GetId()] = {
      type = order:GetType(),
      requirementStr = table.concat(order:GetRequirements(), REQUIREMENT_SPLIT),
      id = order:GetId(),
      groupId = order:GetGroupId(),
      chapterId = order:GetChapterId(),
      avatarId = order:GetAvatarId(),
      createTime = order:GetCreateTime(),
      rewards = ConfigUtil.Rewards2String(order:GetRewards(true)),
      cleanGoldCount = order:GetCleanGoldCount() or 0
    }
  }
  self.m_dbTable:BatchSet(addData)
end

function MainOrderModel:_DBRemoveOrder(orderId)
  self.m_dbTable:Remove(orderId)
end

function MainOrderModel:_TryCreateOrders()
  if GameConfig.IsTestMode() and GM.TestModel and not next(self.m_orders) then
    local testOrderConfigs = GM.TestModel:GetNextOrderGroupConfigs()
    if testOrderConfigs then
      return MainOrderCreatorFixed.Create(self, testOrderConfigs)
    end
  end
  local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
  if bakeOutModel:CanAcquireToken() then
    Log.Info("BakeOut CreateOrders")
    local arrCurrentOrders = Table.GetValueList(self.m_orders)
    return BakeOutOrderCreator.Create(self, arrCurrentOrders)
  else
    local arrNextOrderConfigs = {}
    local curGroupId = self:_GetCurOrderGroupId()
    self:_SelectNewOrderConfigs(curGroupId, arrNextOrderConfigs)
    local arrNewOrders = MainOrderCreatorFixed.Create(self, arrNextOrderConfigs)
    return arrNewOrders
  end
end

function MainOrderModel:_SelectNewOrderConfigs(groupId, arrNextOrderConfigs)
  local arrNewConfigs, hasLockedOrder = self:_SelectNewOrderConfigsFromGroup(groupId)
  if arrNewConfigs and arrNewConfigs[1] then
    Table.ListAppend(arrNextOrderConfigs, arrNewConfigs)
  elseif not hasLockedOrder and self:_CurrentlyNoFixedOrders() and self:HasClaimedGroupReward() and not self:IsCurChapterLastCanUnlockGroup() then
    arrNewConfigs, hasLockedOrder = self:_SelectNewOrderConfigsFromGroup(groupId + 1)
    if arrNewConfigs[1] then
      self:_SetCurOrderGroupId(groupId + 1)
      self:_ClearFinishedOrderIds()
      Table.ListAppend(arrNextOrderConfigs, arrNewConfigs)
      EventDispatcher.DispatchEvent(EEventType.UpdateOrderGroupButton)
      EventDispatcher.DispatchEvent(EEventType.OrderGroupRefreshed)
    end
  end
end

function MainOrderModel:_SelectNewOrderConfigsFromGroup(groupId, mapCurGroupFinishedOrderIds, dataModel)
  mapCurGroupFinishedOrderIds = mapCurGroupFinishedOrderIds or self.m_mapCurGroupFinishedOrderIds
  dataModel = dataModel or self.dataModel
  local arrNewConfigs = {}
  local hasLockedOrder = false
  local arrOrderConfigs = dataModel:GetGroupedOrderConfig(groupId)
  if not arrOrderConfigs then
    return arrNewConfigs, hasLockedOrder
  end
  for _, orderConfig in ipairs(arrOrderConfigs) do
    local orderId = orderConfig.Id
    if not mapCurGroupFinishedOrderIds[orderId] and not self.m_orders[orderId] then
      hasLockedOrder = true
      local arrPreIds = orderConfig.PreId
      if arrPreIds then
        local preDone = true
        for _, preId in ipairs(arrPreIds) do
          if not mapCurGroupFinishedOrderIds[preId] then
            preDone = false
            break
          end
        end
        if preDone then
          arrNewConfigs[#arrNewConfigs + 1] = orderConfig
        end
      else
        arrNewConfigs[#arrNewConfigs + 1] = orderConfig
      end
    end
  end
  return arrNewConfigs, hasLockedOrder
end

function MainOrderModel:GetMetaData()
  Log.Error("MainOrderModel:GetMetaData 对外不暴露")
end

function MainOrderModel:GetAvatarConfig()
  return self.m_avatarConfig
end

function MainOrderModel:ForceRemoveOrder(orderId)
  local order = self.m_orders[orderId]
  if not order then
    return false
  end
  self:_DBRemoveOrder(orderId)
  self.m_orders[orderId] = nil
  self:_AddFinishedOrderId(orderId)
  order:SetFinished()
  self.m_boardModel.event:Call(BoardEventType.RemoverOrderImmediate, {Order = order})
  return true
end

local FIRST_ORDER_AVATAR_IDS = {5}
local SECOND_ORDER_AVATAR_IDS = {3, 2}
local DBColumnFirstOrder = "FirstOrder"
local DBColumnSecondOrder = "SecondOrder"

function MainOrderModel:GetAvatarIds(count, eOrderType)
  Log.Assert(eOrderType ~= nil, "eOrderType == nil")
  count = count or 1
  if count == 1 and StringUtil.IsNilOrEmpty(self.m_dbMetaTable:GetValue(DBColumnFirstOrder, "value")) then
    self.m_dbMetaTable:Set(DBColumnFirstOrder, "value", "1")
    return FIRST_ORDER_AVATAR_IDS
  end
  if count == 2 and StringUtil.IsNilOrEmpty(self.m_dbMetaTable:GetValue(DBColumnSecondOrder, "value")) then
    self.m_dbMetaTable:Set(DBColumnSecondOrder, "value", "1")
    return SECOND_ORDER_AVATAR_IDS
  end
  local level = GM.LevelModel:GetCurrentLevel()
  local results = {}
  if eOrderType == OrderType.BakeOut then
    local bakeOutModel = GM.ActivityManager:GetModel(ActivityType.BakeOut)
    local bakeOutOn = bakeOutModel ~= nil and bakeOutModel:CanAcquireToken()
    if bakeOutOn then
      for id, _ in pairs(self.m_avatarConfig.BakeOutAvatars) do
        local found = false
        for _, order in pairs(self.m_orders) do
          if order:GetType() == eOrderType and order:GetAvatarId() == id then
            found = true
            break
          end
        end
        if not found then
          table.insert(results, id)
        end
      end
    end
  end
  if count <= #results then
    return results
  end
  local unlockedNormalAvatarIds = {}
  local availableNormalAvatarIds = {}
  for i = 1, #self.m_avatarConfig.NormalAvatars do
    local unlockLevel = self.m_avatarConfig.NormalAvatars[i].UnlockLevel
    if unlockLevel == nil or level >= unlockLevel then
      table.insert(unlockedNormalAvatarIds, i)
      local found = false
      for _, order in pairs(self.m_orders) do
        if order:GetType() == eOrderType and order:GetAvatarId() == i then
          found = true
          break
        end
      end
      if not found then
        table.insert(availableNormalAvatarIds, i)
      end
    end
  end
  if 0 < count - #results then
    Table.ListAppend(results, Table.ListRandomSelectN(availableNormalAvatarIds, count - #results))
  end
  if 0 < count - #results then
    Table.ListAppend(results, Table.ListRandomSelectN(unlockedNormalAvatarIds, count - #results))
  end
  if 0 < count - #results then
    for i = 1, count - #results do
      results[#results + 1] = FIRST_ORDER_AVATAR_IDS[1]
    end
  end
  return results
end

function MainOrderModel:_OnNewContentReleased()
  self:RemoveBakeOutOrders()
  if self:_CurrentlyNoFixedOrders() and self:HasClaimedGroupReward() then
    self:_TryFillOrders()
    if not self:_CurrentlyNoFixedOrders() then
      EventDispatcher.DispatchEvent(EEventType.OrderStateChanged)
      EventDispatcher.DispatchEvent(EEventType.NewReleasedOrderGroupUnlocked)
    end
  end
end

function MainOrderModel:TryFillNextGroupOrder()
  self:_TryFillOrders()
  if not next(self.m_orders) then
    EventDispatcher.DispatchEvent(EEventType.UpdateOrderEmpty)
  else
    EventDispatcher.DispatchEvent(EEventType.ShowNewOrders)
  end
end

local GROUP_REWARD_CLAIM_FLAG = "_grp_rwd_"

function MainOrderModel:ClaimGroupReward()
  if not self:CanClaimGroupReward() then
    return false
  end
  local rewards, hasDeleted = self:GetCurGroupReward()
  RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, EBIType.OrderGroup .. (hasDeleted and "_d" or ""), EGameMode.Board, CacheItemType.Stack)
  local curFinishedGroupCount = self:_GetCurFinishedGroupCount()
  self:_SetCurFinishedGroupCount(curFinishedGroupCount + 1)
  self:_AddFinishedOrderId(GROUP_REWARD_CLAIM_FLAG)
  local totalFinishedGroupCount = self:GetTotalFinishedGroupCount()
  self:_SetTotalFinishedGroupCount(totalFinishedGroupCount + 1)
  GM.FlambeTimeModel:OnOrderGroupFinished()
  EventDispatcher.DispatchEvent(EEventType.OnClaimedOrderGroupReward)
  GM.BIManager:LogDayEnd(self:_GetCurChapterId(), self:_GetCurOrderGroupId(), self:_GetOrderGroupConsumeEnergy(), self:_GetOrderGroupCostCurDayEnergy(), self:_GetOrderGroupCostPastDayEnergy())
end

function MainOrderModel:HasClaimedGroupReward()
  if self:IsGuideGroup() then
    return true
  end
  return self.m_mapCurGroupFinishedOrderIds[GROUP_REWARD_CLAIM_FLAG]
end

function MainOrderModel:CanClaimGroupReward()
  if self:IsGuideGroup() then
    return false
  end
  if self:HasClaimedGroupReward() then
    return false
  end
  if not self:_CurrentlyNoFixedOrders() then
    return false
  end
  local finishedCount, totalCount = self:GetGroupProgress()
  return totalCount <= finishedCount
end

function MainOrderModel:GetCurGroupReward()
  local groupId = self:_GetCurOrderGroupId()
  local groupConfig = self.dataModel:GetGroupConfig(groupId)
  if not groupConfig then
    return {
      {
        [PROPERTY_TYPE] = "energy",
        [PROPERTY_COUNT] = 50,
        [PROPERTY_CRYPT] = Crypt.CryptCurrency(50)
      }
    }, true
  end
  return groupConfig.Rewards
end

function MainOrderModel:GetGroupProgress()
  if self:IsGuideGroup() then
    return 0, 1
  end
  local groupId = self:_GetCurOrderGroupId()
  local arrOrderConfigs = self.dataModel:GetGroupedOrderConfig(groupId)
  if not arrOrderConfigs then
    local curOrders = Table.GetMapSize(self.m_orders)
    local finishedOrders = Table.GetMapSize(self.m_mapCurGroupFinishedOrderIds)
    return finishedOrders, finishedOrders + curOrders
  end
  local totalCount = #arrOrderConfigs
  if self:HasClaimedGroupReward() then
    return totalCount, totalCount
  end
  local finishedCount = Table.GetMapSize(self.m_mapCurGroupFinishedOrderIds)
  finishedCount = math.min(finishedCount, totalCount)
  return finishedCount, totalCount
end

function MainOrderModel:IsGuideGroup()
  local groupId = self:_GetCurOrderGroupId()
  if groupId <= 0 then
    return true
  end
  return false
end

function MainOrderModel:IsCurChapterLastCanUnlockGroup()
  local maxDay = MainOrderDataModel.GetMaxOrderDayOfChapter(self:_GetCurChapterId())
  maxDay = math.min(maxDay, GM.ChapterDataModel:GetMaxCanUnlockDay())
  return maxDay <= self.m_curOrderDay
end

function MainOrderModel:HasNextGroup()
  return not self:IsCurChapterLastCanUnlockGroup() or GM.TaskManager:GetOngoingChapterId() > self:_GetCurChapterId()
end

function MainOrderModel:_CanFillNextChapterOrders()
  local groupId = self:_GetCurOrderGroupId()
  return self:IsCurChapterCanUnlockGroupFinished() and groupId >= self.dataModel:GetGroupCount()
end

function MainOrderModel:IsAllCanUnlockGroupFinished()
  if not self.m_bLateInited then
    Log.Error("MainOrderModel:IsAllCanUnlockGroupFinished not ready before late init")
    return nil
  end
  return self:IsCurChapterCanUnlockGroupFinished() and self.m_curOrderDay >= GM.ChapterDataModel:GetMaxCanUnlockDay()
end

function MainOrderModel:IsCurChapterCanUnlockGroupFinished(logError)
  if not self.m_bLateInited then
    if logError ~= false then
      Log.Error("MainOrderModel:IsCurChapterCanUnlockGroupFinished not ready before late init")
    end
    return nil
  end
  return self:_CurrentlyNoFixedOrders() and self:IsCurChapterLastCanUnlockGroup() and self:HasClaimedGroupReward() and not self:IsGuideGroup()
end

function MainOrderModel:IsOrderGroupFinished(chapterId, groupId)
  local curChapterId = self:_GetCurChapterId()
  if chapterId < curChapterId then
    return true
  elseif chapterId > curChapterId then
    return false
  end
  local curGroupId = self:_GetCurOrderGroupId()
  return groupId < curGroupId
end

function MainOrderModel:HasOrders()
  return self:GetOrdersCount() > 0 or not self:IsCurChapterCanUnlockGroupFinished() or self:HasNextGroup()
end

function MainOrderModel:GetOrderGroupInfo()
  return self:_GetCurChapterId(), self:_GetCurOrderGroupId()
end

function MainOrderModel:_GetCurChapterId()
  return tonumber(self.m_dbMetaTable:GetValue(EMetaDBKey.CurChapterId, DBColumnValue)) or 1
end

function MainOrderModel:_SetCurChapterId(chapterId)
  self.m_dbMetaTable:Set(EMetaDBKey.CurChapterId, DBColumnValue, tostring(chapterId))
end

function MainOrderModel:_GetCurOrderGroupId()
  return tonumber(self.m_dbMetaTable:GetValue(EMetaDBKey.CurOrderGroupId, DBColumnValue)) or 0
end

function MainOrderModel:_SetCurOrderGroupId(groupId)
  local old = self:_GetCurOrderGroupId()
  self.m_dbMetaTable:Set(EMetaDBKey.CurOrderGroupId, DBColumnValue, tostring(groupId))
  self:_RefreshCurOrderDay()
  if old ~= groupId then
    self:_SetOrderGroupConsumeEnergy(0)
    self:_SetOrderGroupCostCurDayEnergy(0)
    self:_SetOrderGroupCostPastDayEnergy(0)
    GM.MainBoardModel:SetCurDayEnergyCostToPastDay()
    GM.BIManager:LogDayStart(self:_GetCurChapterId(), groupId)
    GM.FlambeTimeModel:OnNewOrderGroupReleased(self.dataModel:GetGroupedOrderConfig(groupId), self.dataModel:GetGroupConfig(groupId))
  end
end

function MainOrderModel:_GetCurFinishedGroupCount()
  return tonumber(self.m_dbMetaTable:GetValue(EMetaDBKey.CurFinishedGroupCount, DBColumnValue)) or 0
end

function MainOrderModel:_SetCurFinishedGroupCount(count)
  self.m_dbMetaTable:Set(EMetaDBKey.CurFinishedGroupCount, DBColumnValue, tostring(count))
end

function MainOrderModel:GetTotalFinishedGroupCount()
  return tonumber(self.m_dbMetaTable:GetValue(EMetaDBKey.TotalFinishedGroupCount, DBColumnValue)) or 0
end

function MainOrderModel:_SetTotalFinishedGroupCount(count)
  self.m_dbMetaTable:Set(EMetaDBKey.TotalFinishedGroupCount, DBColumnValue, tostring(count))
end

local ORDER_ID_CONNECTOR = ";"

function MainOrderModel:_GetCurGroupFinishedOrderIds()
  local str = self.m_dbMetaTable:GetValue(EMetaDBKey.CurGroupFinishedOrderIds, DBColumnValue)
  local arrFinishedOrderIds = StringUtil.Split(str, ORDER_ID_CONNECTOR)
  local mapFinishedOrderIds = {}
  local IsNilOrEmpty = StringUtil.IsNilOrEmpty
  for _, orderId in ipairs(arrFinishedOrderIds) do
    if not IsNilOrEmpty(orderId) then
      mapFinishedOrderIds[orderId] = true
    end
  end
  return mapFinishedOrderIds
end

function MainOrderModel:_AddFinishedOrderId(orderId)
  self.m_mapCurGroupFinishedOrderIds[orderId] = true
  self:_SetCurGroupFinishedOrderIds(self.m_mapCurGroupFinishedOrderIds)
end

function MainOrderModel:_ClearFinishedOrderIds()
  self.m_mapCurGroupFinishedOrderIds = {}
  self:_SetCurGroupFinishedOrderIds(self.m_mapCurGroupFinishedOrderIds)
end

function MainOrderModel:_SetCurGroupFinishedOrderIds(mapFinishedOrderIds)
  local str = ""
  for orderId, _ in pairs(mapFinishedOrderIds) do
    str = str .. orderId .. ORDER_ID_CONNECTOR
  end
  self.m_dbMetaTable:Set(EMetaDBKey.CurGroupFinishedOrderIds, DBColumnValue, str)
end

function MainOrderModel:_GetOrderGroupConsumeEnergy()
  return tonumber(self.m_dbMetaTable:GetValue(EMetaDBKey.OrderGroupConsumeEnergy, DBColumnValue)) or 0
end

function MainOrderModel:_SetOrderGroupConsumeEnergy(number)
  self.m_dbMetaTable:Set(EMetaDBKey.OrderGroupConsumeEnergy, DBColumnValue, tostring(number))
end

function MainOrderModel:_GetOrderGroupCostCurDayEnergy()
  return tonumber(self.m_dbMetaTable:GetValue(EMetaDBKey.OrderGroupCostCurDayEnergy, DBColumnValue)) or 0
end

function MainOrderModel:_SetOrderGroupCostCurDayEnergy(number)
  self.m_dbMetaTable:Set(EMetaDBKey.OrderGroupCostCurDayEnergy, DBColumnValue, tostring(number))
end

function MainOrderModel:_GetOrderGroupCostPastDayEnergy()
  return tonumber(self.m_dbMetaTable:GetValue(EMetaDBKey.OrderGroupCostPastDayEnergy, DBColumnValue)) or 0
end

function MainOrderModel:_SetOrderGroupCostPastDayEnergy(number)
  self.m_dbMetaTable:Set(EMetaDBKey.OrderGroupCostPastDayEnergy, DBColumnValue, tostring(number))
end

function MainOrderModel:GetCurOrderDay()
  if self:IsGuideGroup() then
    return 1
  end
  return self.m_curOrderDay or 0
end

function MainOrderModel:_RefreshCurOrderDay()
  local oldDay = self.m_curOrderDay
  local curGroupId = self:_GetCurOrderGroupId()
  local groupConfig = self.dataModel:GetGroupConfig(curGroupId)
  if oldDay == nil and groupConfig == nil then
    for i = curGroupId - 1, 1, -1 do
      groupConfig = self.dataModel:GetGroupConfig(i)
      if groupConfig ~= nil then
        break
      end
    end
  end
  self.m_curOrderDay = groupConfig and groupConfig.Day or 0
  if self.m_curOrderDay ~= oldDay and oldDay ~= nil then
    GM.CrossPromotionModel:SendMissionStatusData(ECPMissionType.Day)
    GM.OperBIManager:TrackDayUpEvent(self.m_curOrderDay)
    EventDispatcher.DispatchEvent(EEventType.OrderDayChanged)
  end
end

function MainOrderModel:IsSameChapterWithTask()
  return self:_GetCurChapterId() == GM.TaskManager:GetOngoingChapterId()
end

function MainOrderModel:GetChapterCanGetGolds()
  local curOwn = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold)
  curOwn = curOwn - GM.TaskManager:GetCleanGoldCount()
  local curOrdersCanGet = self:_GetCurFixedOrdersGold()
  local lockedOrdersCanGet = self:_GetCurLockedGroupOrdersGold()
  local remainGroupsCanGet = self:_GetRemainGroupsGold()
  local totalGet = curOwn + curOrdersCanGet + lockedOrdersCanGet + remainGroupsCanGet
  return totalGet
end

function MainOrderModel:_GetCurFixedOrdersGold()
  local curOrders = self:_GetCurFixedOrders()
  local count = 0
  for _, order in ipairs(curOrders) do
    count = count + ConfigUtil.GetGoldCount(order:GetRewards())
    count = count - order:GetCleanGoldCount()
  end
  return count
end

function MainOrderModel:_GetCurFixedOrders()
  local fixedOrders = {}
  for _, order in pairs(self.m_orders) do
    if order:GetType() == OrderType.Fixed then
      fixedOrders[#fixedOrders + 1] = order
    end
  end
  return fixedOrders
end

function MainOrderModel:_CurrentlyNoFixedOrders()
  return #self:_GetCurFixedOrders() == 0
end

function MainOrderModel:_GetCurLockedGroupOrdersGold()
  local count = 0
  local arrOrderConfigs = self.dataModel:GetGroupedOrderConfig(self:_GetCurOrderGroupId())
  if not arrOrderConfigs then
    return count
  end
  for _, orderConfig in ipairs(arrOrderConfigs) do
    local orderId = orderConfig.Id
    if not self.m_mapCurGroupFinishedOrderIds[orderId] and not self.m_orders[orderId] then
      count = count + MainOrderCreatorFixed.GetGoldRewardCountByConfig(orderConfig)
    end
  end
  return count
end

function MainOrderModel:_GetRemainGroupsGold()
  local curGroupId = self:_GetCurOrderGroupId()
  local groupCount = self.dataModel:GetGroupCount()
  local count = 0
  local arrOrderConfigs
  for i = curGroupId + 1, groupCount do
    arrOrderConfigs = self.dataModel:GetGroupedOrderConfig(i)
    for _, orderConfig in ipairs(arrOrderConfigs or {}) do
      count = count + MainOrderCreatorFixed.GetGoldRewardCountByConfig(orderConfig)
    end
  end
  return count
end

function MainOrderModel:AddOrderGroupConsumeEnergy(count)
  local cur = self:_GetOrderGroupConsumeEnergy()
  local new = cur + count
  self:_SetOrderGroupConsumeEnergy(new)
  Log.Info("订单组打点：当前订单组的体力消耗总和为 " .. new)
end

function MainOrderModel:_AddOrderGroupCostEnergy(curDayCount, pastDayCount)
  local cur = self:_GetOrderGroupCostCurDayEnergy()
  local newCurDay = cur + curDayCount
  self:_SetOrderGroupCostCurDayEnergy(newCurDay)
  cur = self:_GetOrderGroupCostPastDayEnergy()
  local newPastDay = cur + pastDayCount
  self:_SetOrderGroupCostPastDayEnergy(newPastDay)
  Log.Info("订单组打点：当前订单组的交付棋子消耗当天体力 " .. newCurDay .. "，之前体力 " .. newPastDay)
end

function MainOrderModel:GetNextFixedOrderConfigs()
  local mapFinishedOrderId = Table.ShallowCopy(self.m_mapCurGroupFinishedOrderIds)
  for orderId, order in pairs(self.m_orders) do
    if order:GetType() == OrderType.Fixed then
      mapFinishedOrderId[orderId] = true
    end
  end
  local groupId = self:_GetCurOrderGroupId()
  local arrNewConfigs, hasLockedOrder = self:_SelectNewOrderConfigsFromGroup(groupId, mapFinishedOrderId)
  if Table.IsEmpty(arrNewConfigs) and not hasLockedOrder then
    arrNewConfigs, hasLockedOrder = self:_SelectNewOrderConfigsFromGroup(groupId + 1, mapFinishedOrderId)
  end
  if Table.IsEmpty(arrNewConfigs) and not hasLockedOrder and not GM.TaskManager:IsLastChapter() then
    local nextChapterId = self:_GetCurChapterId() + 1
    local dataModel = MainOrderDataModel.Create()
    dataModel:LoadChapterConfig(nextChapterId)
    arrNewConfigs, hasLockedOrder = self:_SelectNewOrderConfigsFromGroup(1, {}, dataModel)
  end
  return arrNewConfigs
end

function MainOrderModel:OnCompleteRoom(chapterId)
  self.dataModel:LoadChapterConfig(chapterId)
  self:_SetCurChapterId(chapterId)
  self:_SetCurOrderGroupId(self.dataModel:GetGroupCount())
  self:_ClearFinishedOrderIds()
  self:_TryFillOrders()
  GM.ShopModel:RefreshFlashSaleByDayRefresh()
end

function MainOrderModel:GetCurChapterFinishedGroupRewards()
  local totalRewards = {}
  local groupConfig
  for i = 1, self:_GetCurOrderGroupId() - 1 do
    groupConfig = self.dataModel:GetGroupConfig(i)
    if groupConfig and groupConfig.Rewards then
      Table.ListAppend(totalRewards, groupConfig.Rewards)
    end
  end
  return totalRewards
end

function MainOrderModel:FinishCurrentAllOrders()
  for _, order in pairs(self.m_orders) do
    order:SetFinished()
  end
  self:_TryFillOrders()
  self:ClaimGroupReward()
end

function MainOrderModel:IsOrderGroupButtonVisible()
  return not self:IsGuideGroup() and (not self:IsCurChapterCanUnlockGroupFinished() or self:HasNextGroup())
end
