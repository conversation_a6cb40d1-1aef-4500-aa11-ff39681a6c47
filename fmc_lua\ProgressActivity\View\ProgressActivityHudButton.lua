ProgressActivityHudButton = setmetatable({}, HudPropertyButton)
ProgressActivityHudButton.__index = ProgressActivityHudButton

function ProgressActivityHudButton:Init(activityType)
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_activityType = activityType
  self.m_activityDefinition = ProgressActivityDefinition[self.m_activityType]
  self.m_level = self.m_model:GetLevel()
  self:UpdateReward()
  HudPropertyButton.Init(self, self.m_activityDefinition.ActivityTokenPropertyType)
end

function ProgressActivityHudButton:UpdateReward()
  local reward = self.m_model:GetLevelReward(self.m_level)
  if reward == nil then
    UIUtil.SetActive(self.m_rewardImg.gameObject, false)
    return
  end
  UIUtil.SetActive(self.m_rewardImg.gameObject, true)
  local imageName = ConfigUtil.GetCurrencyImageName(reward)
  SpriteUtil.SetImage(self.m_rewardImg, imageName)
  local rewardCount = reward[PROPERTY_COUNT]
  if 1 < rewardCount then
    self.m_rewardCountGo:SetActive(true)
    self.m_rewardCountText.text = "x" .. rewardCount
  else
    self.m_rewardCountGo:SetActive(false)
  end
end

function ProgressActivityHudButton:_AddListeners()
  if HudPropertyButton._AddListeners(self) then
    EventDispatcher.AddListener(EEventType.ChangeGameMode, self, self._OnChangeGameMode)
  end
  if self.m_activityDefinition ~= nil then
    EventDispatcher.AddListener(self.m_activityDefinition.StateChangedEvent, self, self._OnStateChangedEvent, true)
    EventDispatcher.AddListener(self.m_activityDefinition.LevelChangedEvent, self, self._OnStateChangedEvent, true)
  end
end

function ProgressActivityHudButton:_OnStateChangedEvent()
  self:SyncToModelValue()
  self:UpdateReward()
end

function ProgressActivityHudButton:_OnChangeGameMode()
  if GM.SceneManager:GetGameMode() == EGameMode.Board then
    self:SyncToModelValue()
    self:UpdateReward()
  end
end

function ProgressActivityHudButton:IsInBoardView()
  return true
end

function ProgressActivityHudButton:SyncToModelValue()
  self.m_value = self:GetPropertyNum()
  self.m_level = self.m_model ~= nil and self.m_model:GetLevel() or 0
  self.m_iTextAnimationTo = self.m_value
  self:UpdateValueText()
end

function ProgressActivityHudButton:GetPropertyNum()
  if self.m_model == nil then
    return 0
  end
  return self.m_model:GetActivityTokenNumber() or 0
end

function ProgressActivityHudButton:UpdateValueText()
  if self.m_model:GetLevelRequire(self.m_level) == nil then
    if self.m_valueText then
      local maxLevel = self.m_model:GetLevelCount()
      local maxScore = self.m_model:GetLevelRequire(maxLevel) or ""
      self.m_valueText.text = maxScore .. "/" .. maxScore
    end
    self.m_slider.value = 1
    return
  end
  if self.m_valueText then
    self.m_valueText.text = math.floor(self.m_value + 0.5) .. "/" .. self.m_model:GetLevelRequire(self.m_level)
  end
  if self.m_slider then
    self.m_slider.value = math.floor(self.m_value + 0.5) / self.m_model:GetLevelRequire(self.m_level)
  end
end

function ProgressActivityHudButton:UpdateTextAnimation(valueChange, isMiddleStep)
  self:PlayAnimation()
end

function ProgressActivityHudButton:PlayAnimation()
  if self.m_valueTween ~= nil or self.m_value == self:GetPropertyNum() and self.m_level == self.m_model:GetLevel() then
    return
  end
  if self.m_model:GetLevelRequire(self.m_level) == nil then
    self:UpdateReward()
    self:UpdateValueText()
    return
  end
  local valueFrom = self.m_value
  local valueTo = self:GetPropertyNum()
  if self.m_value == self.m_model:GetLevelRequire(self.m_level) then
    valueFrom = 0
    self.m_level = self.m_level + 1
    self:UpdateReward()
  end
  if self.m_level < self.m_model:GetLevel() then
    valueTo = self.m_model:GetLevelRequire(self.m_level)
  end
  local duration = 1
  if self.m_model:GetLevelRequire(self.m_level) ~= nil then
    duration = (valueTo - valueFrom) / self.m_model:GetLevelRequire(self.m_level) * 1
  end
  self.m_valueTween = DOVirtual.Float(valueFrom, valueTo, duration, function(x)
    self.m_value = x
    self:UpdateValueText()
  end):OnComplete(function()
    self.m_valueTween = nil
    self:PlayAnimation()
  end)
end
