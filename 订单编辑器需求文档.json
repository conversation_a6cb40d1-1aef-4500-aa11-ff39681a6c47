订单编辑器需求文档
​1. 需求背景​
设计一个订单编辑器，通过读取基础配置信息，根据策划输入的订单指标参数和指定菜品，生成符合标准的随机订单方案。输出方案需包含关键指标，提升订单配置效率。

​2. 输入内容​
​2.1 基础配置信息（程序读取）​​
​生成器信息​（LevelInitiativeProduce）：等级、产出效率、产出次数上限（capacity）
​合成树信息​（LevelGoodsBase）：合成物品的生成器来源、等级、折合1级物品数量
​器械制作信息​（LevelGoodsCooking）：菜品所需器械、原料、加工时间（取最大等级器械计算）
​器械配置​（LevelInstrument）：器械等级、属性
​菜品信息​（LevelRecipe）：物品需求、折合1级物品数量
​新增菜品表​：虚拟等级、最短间隔波数、基础系列（小数）、剩余物品（衍生物）、额外体力
​2.2 波订单指标（策划配置）​​
​类别​	​必填/选填​	​说明​
​前置波次信息​	选填	当前菜系下前N波的订单配置（用于菜品间隔校验）
​特色菜品​	必填	4-7个指定菜品（超量报错）
​波订单体力消耗范围​	必填	总体力消耗 ± 波动比例
​玩家生成器信息​	必填	生成器等级、数量
​生成器轮数范围​	必填	每类生成器单独配置（一轮=达到产出次数上限）
​玩家器械信息​	必填	器械等级、数量（与生成器同表）
​器械使用限制​	选填	使用次数、总时长、单次最大时长（后续支持按生成器单独配置）
​剩余物品范围​	选填	折合1级物品数量、实际物品数量上限
​订单体力权重​	必填	7个订单的体力消耗权重（如[10,10,15,15,15,20,30]）
​体力消耗模式​	必填	2bet/4bet/8bet
​生成器效率模式​	必填	平均效率/动态效率/top3平均效率
​新增：生成器点击次数	必填	每类生成器的总点击次数范围
​新增：器械使用指标​	必填	器械使用次数、总时长范围
​新增：物品最高等级​	必填	每类生成器必须使用的物品最高等级（且不超过该等级）

​3. 随机计算流程​
​3.1 阶段A：计算特色菜品消耗​
1.解析所有特色菜品，计算其占用的资源： 
o生成器物品最高等级
o器械使用次数、总时长
o生成器点击次数
o体力消耗
o剩余物品（折合1级）
2.从总指标中减去上述结果，得到剩余目标值。
​3.2 阶段B：分配最高等级物品​
1.根据生成器必须使用的物品最高等级要求，随机选择包含该物品的菜品。
2.校验器械指标： 
o若菜品涉及器械加工，检查是否超出器械使用次数/时长限制
o若超出则舍弃该菜品，重新选择
3.将符合条件的菜品加入订单池。
​3.3 阶段C：订单顺序匹配​
1.合并特色菜品（A阶段）和最高等级物品菜品（B阶段）。
2.按体力消耗排序，匹配7个订单位置： 
o每个订单最多包含1个特色菜品
o订单体力需求 = 总体力 × 订单权重 / 总权重
o菜品体力需在订单需求的60%-80%范围内（超出则调整位置）
3.更新剩余体力、生成器点击次数、器械使用量。
​3.4 阶段D：剩余物品补全​
1.​订单填充规则​： 
o每个订单补足1-2个物品
o物品体力占订单剩余需求的60%-80%（波动±10%→±30%）
2.​特殊处理第6-7订单​： 
o若配置要求（MC2模式），使用最高等级生成器效率计算物品消耗
o否则按全局效率模式计算
3.​校验约束​： 
o物品不得重复
o满足菜品最短间隔波数（参考前置波次）
o剩余生成器点击次数/器械指标符合目标
​3.5 阶段E：校验与修正​
1.校验整体指标： 
if (实际体力消耗 ∈ 配置范围) and 
   (生成器点击次数 ∈ 配置范围) and 
   (器械使用次数/时长 ∈ 配置范围) and 
   (物品最高等级 == 配置要求) and 
   (剩余物品 ≤ 上限):
   方案有效
else:
   重新随机
2.
3.输出前循环生成20套方案。
​3.6 阶段F：最优完成顺序计算​
1.按体力消耗升序动态排序： 
o选择当前剩余物品下体力消耗最小的订单
o提交后更新剩余物品状态
o重复直至完成7个订单
2.记录每个订单的实际体力消耗。

​4. 输出内容​
每套方案输出以下指标：
​指标类别​	​详细说明​
​订单内容​	7个订单的物品组成（含特色菜品标记）
​体力消耗​	总体力值、7个订单的分步消耗（按最优完成顺序）
​生成器指标​	各类生成器点击次数、轮数、使用物品最高等级
​器械指标​	使用次数、总时长（新增）
​剩余物品​	折合1级数量（分系列）、实际物品数量
​棋盘占用​	订单提交时的格子占用数（生成器/器械/订单物品/剩余物品）
​差距分析​	器械使用次数/时长与目标值的差值（新增，正负值表示超出/不足）

​5. 关键算法​
​5.1 体力消耗计算​
def calculate_energy(order_items, bet_mode, efficiency_mode):
   # 步骤1：拆解物品至基础生成器产出
   base_items = decompose_to_base_items(order_items) 
   
   # 步骤2：按系列汇总折合1级物品数量
   series_quantities = sum_base_items_by_series(base_items)
   
   # 步骤3：计算生成器点击次数
   clicks = {}
   for generator_type, series_data in series_quantities.items():
        efficiency = get_efficiency(generator_type, efficiency_mode)
        clicks[generator_type] = max( 
            series_qty / efficiency / bet_mode 
            for series_qty in series_data.values()
        )
   
   # 步骤4：计算总体力
   total_energy = sum(clicks.values()) * bet_mode
   return total_energy
​5.2 生成器效率模式​
​模式​	​计算逻辑​
​平均效率​	(∑(生成器等级效率×数量)) / 总数量
​动态效率​	按生成器等级从高到低依次使用，用完次数上限后降级
​Top3平均效率​	取玩家拥有的最高3级生成器的效率平均值
​5.3 器械加工时间​
加工时间 = 基础时间 / 器械等级系数（取玩家拥有的最大等级器械）
​5.4 剩余物品计算​
​折合1级数量​：(生成器点击次数 - 需求点击次数) × 效率 × bet
​实际物品数量​：将折合1级数量按2^n拆分（衍生物需叠加额外产出）

​6. 约束规则​
1.​菜品唯一性​：同一菜品不得在同一波次重复出现
2.​菜品间隔​：菜品出现间隔需 ≥ 配置的最短波数（参考前置波次）
3.​生成器限制​：菜品必须由玩家拥有的生成器生产
4.​国家特色菜品​：仅出现在对应国家餐厅（由配置表控制）
5.​数值精度​：体力、折合1级物品、基础系列支持小数累加

​7. 新增需求实现​
1.​输入指标扩展​： 
o在配置表中增加字段：GeneratorClicks, InstrumentUsage, MaxItemLevel
2.​随机逻辑优化​： 
o阶段B优先保障物品最高等级要求
o阶段D补全时动态校验器械使用指标
3.​差距分析​： 
# 输出方案时计算差值
instrument_gap = {
     "usage_count": actual_usage - config_target,
     "total_time": actual_time - config_time
}
4.
5.​效率模式配置​： 
o新增参数控制第6-7订单是否使用最高等级生成器效率

