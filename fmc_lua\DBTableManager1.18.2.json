{"finishedTasks": [[{"taskId": 1, "chapterId": 1, "value": 25}, {"taskId": 2, "chapterId": 1, "value": 55}, {"taskId": 3, "chapterId": 1, "value": 49}, {"taskId": 4, "chapterId": 1, "value": 33}, {"taskId": 5, "chapterId": 1, "value": 93}, {"taskId": 6, "chapterId": 1, "value": 80}, {"taskId": 7, "chapterId": 1, "value": 51}, {"taskId": 8, "chapterId": 1, "value": 68}, {"taskId": 9, "chapterId": 1, "value": 38}, {"taskId": 10, "chapterId": 1, "value": 100}], [{"taskId": 1, "chapterId": 2, "value": 135}, {"taskId": 2, "chapterId": 2, "value": 135}, {"taskId": 3, "chapterId": 2, "value": 135}, {"taskId": 4, "chapterId": 2, "value": 135}, {"taskId": 5, "chapterId": 2, "value": 135}, {"taskId": 6, "chapterId": 2, "value": 135}, {"taskId": 7, "chapterId": 2, "value": 149}, {"taskId": 8, "chapterId": 2, "value": 149}, {"taskId": 9, "chapterId": 2, "value": 149}, {"taskId": 10, "chapterId": 2, "value": 149}, {"taskId": 11, "chapterId": 2, "value": 149}, {"taskId": 12, "chapterId": 2, "value": 149}, {"taskId": 13, "chapterId": 2, "value": 149}, {"taskId": 14, "chapterId": 2, "value": 149}, {"taskId": 15, "chapterId": 2, "value": 149}, {"taskId": 16, "chapterId": 2, "value": 149}, {"taskId": 17, "chapterId": 2, "value": 149}, {"taskId": 18, "chapterId": 2, "value": 149}, {"taskId": 19, "chapterId": 2, "value": 149}, {"taskId": 20, "chapterId": 2, "value": 149}, {"taskId": 21, "chapterId": 2, "value": 149}, {"taskId": 22, "chapterId": 2, "value": 149}, {"taskId": 23, "chapterId": 2, "value": 216}, {"taskId": 24, "chapterId": 2, "value": 216}, {"taskId": 25, "chapterId": 2, "value": 216}, {"taskId": 26, "chapterId": 2, "value": 216}, {"taskId": 27, "chapterId": 2, "value": 216}, {"taskId": 28, "chapterId": 2, "value": 216}, {"taskId": 29, "chapterId": 2, "value": 216}, {"taskId": 30, "chapterId": 2, "value": 216}], [{"taskId": 1, "chapterId": 3, "value": 335}, {"taskId": 2, "chapterId": 3, "value": 335}, {"taskId": 3, "chapterId": 3, "value": 335}, {"taskId": 4, "chapterId": 3, "value": 335}, {"taskId": 5, "chapterId": 3, "value": 335}, {"taskId": 6, "chapterId": 3, "value": 352}, {"taskId": 7, "chapterId": 3, "value": 352}, {"taskId": 8, "chapterId": 3, "value": 352}, {"taskId": 9, "chapterId": 3, "value": 355}, {"taskId": 10, "chapterId": 3, "value": 307}, {"taskId": 11, "chapterId": 3, "value": 307}, {"taskId": 12, "chapterId": 3, "value": 177}, {"taskId": 13, "chapterId": 3, "value": 307}, {"taskId": 14, "chapterId": 3, "value": 268}, {"taskId": 15, "chapterId": 3, "value": 361}, {"taskId": 16, "chapterId": 3, "value": 338}, {"taskId": 17, "chapterId": 3, "value": 361}, {"taskId": 18, "chapterId": 3, "value": 269}, {"taskId": 19, "chapterId": 3, "value": 291}, {"taskId": 20, "chapterId": 3, "value": 245}, {"taskId": 21, "chapterId": 3, "value": 285}, {"taskId": 22, "chapterId": 3, "value": 321}, {"taskId": 23, "chapterId": 3, "value": 538}, {"taskId": 24, "chapterId": 3, "value": 249}, {"taskId": 25, "chapterId": 3, "value": 430}, {"taskId": 26, "chapterId": 3, "value": 394}, {"taskId": 27, "chapterId": 3, "value": 357}, {"taskId": 28, "chapterId": 3, "value": 249}, {"taskId": 29, "chapterId": 3, "value": 240}, {"taskId": 30, "chapterId": 3, "value": 643}, {"taskId": 31, "chapterId": 3, "value": 285}, {"taskId": 32, "chapterId": 3, "value": 240}, {"taskId": 33, "chapterId": 3, "value": 598}, {"taskId": 34, "chapterId": 3, "value": 240}, {"taskId": 35, "chapterId": 3, "value": 509}, {"taskId": 36, "chapterId": 3, "value": 374}, {"taskId": 37, "chapterId": 3, "value": 474}, {"taskId": 38, "chapterId": 3, "value": 448}, {"taskId": 39, "chapterId": 3, "value": 367}, {"taskId": 40, "chapterId": 3, "value": 393}, {"taskId": 41, "chapterId": 3, "value": 448}, {"taskId": 42, "chapterId": 3, "value": 367}, {"taskId": 43, "chapterId": 3, "value": 394}, {"taskId": 44, "chapterId": 3, "value": 395}], [{"taskId": 1, "chapterId": 4, "value": 499}, {"taskId": 2, "chapterId": 4, "value": 563}, {"taskId": 3, "chapterId": 4, "value": 531}, {"taskId": 4, "chapterId": 4, "value": 467}, {"taskId": 5, "chapterId": 4, "value": 436}, {"taskId": 6, "chapterId": 4, "value": 467}, {"taskId": 7, "chapterId": 4, "value": 467}, {"taskId": 8, "chapterId": 4, "value": 521}, {"taskId": 9, "chapterId": 4, "value": 571}, {"taskId": 10, "chapterId": 4, "value": 521}, {"taskId": 11, "chapterId": 4, "value": 571}, {"taskId": 12, "chapterId": 4, "value": 521}, {"taskId": 13, "chapterId": 4, "value": 521}, {"taskId": 14, "chapterId": 4, "value": 521}, {"taskId": 15, "chapterId": 4, "value": 595}, {"taskId": 16, "chapterId": 4, "value": 595}, {"taskId": 17, "chapterId": 4, "value": 544}, {"taskId": 18, "chapterId": 4, "value": 696}, {"taskId": 19, "chapterId": 4, "value": 493}, {"taskId": 20, "chapterId": 4, "value": 544}, {"taskId": 21, "chapterId": 4, "value": 592}, {"taskId": 22, "chapterId": 4, "value": 572}, {"taskId": 23, "chapterId": 4, "value": 572}, {"taskId": 24, "chapterId": 4, "value": 615}, {"taskId": 25, "chapterId": 4, "value": 615}, {"taskId": 26, "chapterId": 4, "value": 657}, {"taskId": 27, "chapterId": 4, "value": 699}, {"taskId": 28, "chapterId": 4, "value": 742}, {"taskId": 29, "chapterId": 4, "value": 614}, {"taskId": 30, "chapterId": 4, "value": 724}, {"taskId": 31, "chapterId": 4, "value": 613}, {"taskId": 32, "chapterId": 4, "value": 778}, {"taskId": 33, "chapterId": 4, "value": 724}, {"taskId": 34, "chapterId": 4, "value": 669}, {"taskId": 35, "chapterId": 4, "value": 613}, {"taskId": 36, "chapterId": 4, "value": 613}, {"taskId": 37, "chapterId": 4, "value": 668}, {"taskId": 38, "chapterId": 4, "value": 792}, {"taskId": 39, "chapterId": 4, "value": 737}, {"taskId": 40, "chapterId": 4, "value": 681}, {"taskId": 41, "chapterId": 4, "value": 625}, {"taskId": 42, "chapterId": 4, "value": 681}, {"taskId": 43, "chapterId": 4, "value": 681}, {"taskId": 44, "chapterId": 4, "value": 624}, {"taskId": 45, "chapterId": 4, "value": 680}], [{"taskId": 1, "chapterId": 5, "value": 666}, {"taskId": 2, "chapterId": 5, "value": 593}, {"taskId": 3, "chapterId": 5, "value": 519}, {"taskId": 4, "chapterId": 5, "value": 519}, {"taskId": 5, "chapterId": 5, "value": 593}, {"taskId": 6, "chapterId": 5, "value": 629}, {"taskId": 7, "chapterId": 5, "value": 666}, {"taskId": 8, "chapterId": 5, "value": 594}, {"taskId": 9, "chapterId": 5, "value": 697}, {"taskId": 10, "chapterId": 5, "value": 571}]], "cacheItem": {}, "config": {"key": {"value": {"notify": {"config": [{"maxNum": 2, "interval": 86400, "id": 1, "scene": ["EnergyRefill", "ComeBack_24", "ComeBack_48", "ItemCooldown"], "pushtimetype": 0}], "md5": "47c1751e7551193850791d81b02617b6"}, "bundleController": {"config": [{"bundleCondition": [{"taskFinished": {"ChapterId": 2, "TaskCount": 3}}], "uiCode": "starter", "bundleTrigger": [{"trigger": "task_finished", "popOrder": ["starter"], "popNum": 1}], "duration": 720, "dailyBuyNum": 1, "id": 1, "is_open": 1, "dailyShowNum": 2, "buyCD": 10080, "groupId": "starter", "popCD": 360, "include": ["bundleContent#starter_499", "bundleTrigger#task_finished", "bundleCondition#starter_499"], "bundleContent": [{"payID": "starter_bundle_1", "bundleId": "starter_499", "price": 4.99, "discountTag": "200%", "content": [{"Currency": "gem", "Amount": 240}, {"Currency": "energy", "Amount": 200}, {"Currency": "additem_1", "Amount": 4}, {"Currency": "enebox_1", "Amount": 1}]}], "order": ["starter_499"], "maxBuyNum": 1, "specialType": "starter"}, {"id": 8, "is_open": 1, "sLv": 8, "include": ["bundleContent#cd_199", "bundleTrigger#pd_cd"], "bundleContent": [{"payID": "rush_order_199", "bundleId": "cd_199", "price": 1.99, "discountTag": "125%", "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "additem_1", "Amount": 4}, {"Currency": "energy", "Amount": 50}]}], "specialType": "cd", "uiCode": "cd", "dailyBuyNum": 2, "bundleTrigger": [{"trigger": "pd_cd", "popOrder": ["cd"], "popNum": 1}], "buyCD": 30, "groupId": "cd1", "popCD": 60, "duration": 360, "sTime": 1733875200, "order": ["cd_199"], "eTime": 1765411200, "dailyShowNum": 10}, {"id": 14, "is_open": 1, "sLv": 8, "include": ["bundleContent#order_199", "bundleTrigger#finish_order_group"], "bundleContent": [{"payID": "finish_order_199", "bundleId": "order_199", "price": 1.99, "discountTag": "120%", "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "energy", "Amount": 100}, {"Currency": "cbox3_1", "Amount": 2}]}], "specialType": "orderGroup", "uiCode": "orderGroup", "dailyBuyNum": 2, "bundleTrigger": [{"trigger": "finish_order_group", "popOrder": ["orderGroup"], "popNum": 1}], "buyCD": 30, "groupId": "order1", "popCD": 60, "duration": 60, "sTime": 1741053600, "order": ["order_199"], "eTime": 1765411200, "dailyShowNum": 5}, {"id": 20, "is_open": 1, "sLv": 8, "include": ["bundleContent#et1.99", "bundleContent#et3.99", "bundleContent#et5.99", "bundleTrigger#lack_energy", "generalBundleConf#3.99"], "bundleContent": [{"payID": "energytier_199", "originPrice": 2.99, "bundleId": "et1.99", "price": 1.99, "discountTag": "125%", "content": [{"Currency": "energy", "Amount": 250}]}, {"payID": "energytier_399", "originPrice": 5.99, "bundleId": "et3.99", "price": 3.99, "discountTag": "130%", "content": [{"Currency": "energy", "Amount": 520}]}, {"payID": "energytier_599", "originPrice": 8.99, "bundleId": "et5.99", "price": 5.99, "discountTag": "140%", "content": [{"Currency": "energy", "Amount": 820}]}], "specialType": "multiTier", "uiCode": "multiTier", "dailyBuyNum": 5, "generalBundleConf": [{"confType": "defaultOrder", "param_string": "et3.99"}], "bundleTrigger": [{"trigger": "lack_energy", "popOrder": ["energy"], "popNum": 1}], "buyCD": 30, "groupId": "energytier1", "popCD": 5, "duration": 30, "sTime": 1742522400, "order": ["et1.99", "et3.99", "et5.99"], "eTime": 1765411200, "dailyShowNum": 10}, {"uiCode": "chain", "bundleContentChain": [{"skin": "1", "bundleId": "chain_skip_1", "price": 0, "step": 1, "content": [{"Currency": "skipprop", "Amount": 5}]}, {"skin": "1", "bundleId": "chain_skip_2", "price": 0, "step": 2, "content": [{"Currency": "skipprop", "Amount": 10}]}, {"skin": "1", "bundleId": "chain_skip_3", "price": 0, "step": 3, "content": [{"Currency": "ene_1", "Amount": 1}]}, {"step": 4, "bundleId": "chain_skip_4", "payID": "chaingift_199", "price": 1.99, "skin": "3", "content": [{"Currency": "gem", "Amount": 30}, {"Currency": "energy", "Amount": 150}]}, {"skin": "1", "bundleId": "chain_skip_5", "price": 0, "step": 5, "content": [{"Currency": "skipprop", "Amount": 15}]}, {"skin": "2", "bundleId": "chain_skip_6", "price": 0, "step": 6, "content": [{"Currency": "ene_2", "Amount": 1}]}, {"step": 7, "bundleId": "chain_skip_7", "payID": "chaingift_299", "price": 2.99, "skin": "3", "content": [{"Currency": "gem", "Amount": 40}, {"Currency": "energy", "Amount": 200}]}, {"skin": "1", "bundleId": "chain_skip_8", "price": 0, "step": 8, "content": [{"Currency": "ene_1", "Amount": 1}]}, {"skin": "2", "bundleId": "chain_skip_9", "price": 0, "step": 9, "content": [{"Currency": "skipprop", "Amount": 30}]}, {"skin": "1", "bundleId": "chain_skip_10", "price": 0, "step": 10, "content": [{"Currency": "gem", "Amount": 10}]}, {"skin": "2", "bundleId": "chain_skip_11", "price": 0, "step": 11, "content": [{"Currency": "ene_3", "Amount": 1}]}, {"step": 12, "bundleId": "chain_skip_12", "payID": "chaingift_399", "price": 3.99, "skin": "3", "content": [{"Currency": "gem", "Amount": 60}, {"Currency": "energy", "Amount": 250}]}, {"skin": "1", "bundleId": "chain_skip_13", "price": 0, "step": 13, "content": [{"Currency": "skipprop", "Amount": 40}]}, {"skin": "2", "bundleId": "chain_skip_14", "price": 0, "step": 14, "content": [{"Currency": "greenbox_1", "Amount": 1}]}, {"skin": "1", "bundleId": "chain_skip_15", "price": 0, "step": 15, "content": [{"Currency": "skipprop", "Amount": 50}]}, {"skin": "2", "bundleId": "chain_skip_16", "price": 0, "step": 16, "content": [{"Currency": "ene_4", "Amount": 1}]}, {"step": 17, "bundleId": "chain_skip_17", "payID": "chaingift_499", "price": 4.99, "skin": "3", "content": [{"Currency": "gem", "Amount": 80}, {"Currency": "energy", "Amount": 300}]}, {"skin": "1", "bundleId": "chain_skip_18", "price": 0, "step": 18, "content": [{"Currency": "skipprop", "Amount": 60}]}, {"skin": "2", "bundleId": "chain_skip_19", "price": 0, "step": 19, "content": [{"Currency": "gem", "Amount": 15}]}, {"skin": "1", "bundleId": "chain_skip_20", "price": 0, "step": 20, "content": [{"Currency": "skipprop", "Amount": 70}]}, {"skin": "2", "bundleId": "chain_skip_21", "price": 0, "step": 21, "content": [{"Currency": "greenbox2_1", "Amount": 1}]}, {"step": 22, "bundleId": "chain_skip_22", "payID": "chaingift_699", "price": 6.99, "skin": "3", "content": [{"Currency": "gem", "Amount": 100}, {"Currency": "energy", "Amount": 450}]}, {"skin": "1", "bundleId": "chain_skip_23", "price": 0, "step": 23, "content": [{"Currency": "skipprop", "Amount": 100}]}, {"skin": "2", "bundleId": "chain_skip_24", "price": 0, "step": 24, "content": [{"Currency": "greenbox2_1", "Amount": 1}]}, {"skin": "1", "bundleId": "chain_skip_25", "price": 0, "step": 25, "content": [{"Currency": "skipprop", "Amount": 150}]}, {"skin": "2", "bundleId": "chain_skip_26", "price": 0, "step": 26, "content": [{"Currency": "gem", "Amount": 20}]}, {"step": 27, "bundleId": "chain_skip_27", "payID": "chaingift_1599", "price": 15.99, "skin": "3", "content": [{"Currency": "gem", "Amount": 240}, {"Currency": "energy", "Amount": 1000}]}, {"skin": "2", "bundleId": "chain_skip_28", "price": 0, "step": 28, "content": [{"Currency": "greenbox2_1", "Amount": 1}]}, {"skin": "1", "bundleId": "chain_skip_29", "price": 0, "step": 29, "content": [{"Currency": "skipprop", "Amount": 200}]}, {"skin": "2", "bundleId": "chain_skip_30", "price": 0, "step": 30, "content": [{"Currency": "additem_3", "Amount": 1}]}, {"skin": "1", "bundleId": "chain_skip_31", "price": 0, "step": 31, "content": [{"Currency": "skipprop", "Amount": 400}]}, {"skin": "2", "bundleId": "chain_skip_32", "price": 0, "step": 32, "content": [{"Currency": "gem", "Amount": 50}]}, {"step": 33, "bundleId": "chain_skip_33", "payID": "chaingift_1999", "price": 19.99, "skin": "3", "content": [{"Currency": "gem", "Amount": 320}, {"Currency": "energy", "Amount": 1200}]}, {"skin": "2", "bundleId": "chain_skip_34", "price": 0, "step": 34, "content": [{"Currency": "skipprop", "Amount": 200}]}, {"skin": "1", "bundleId": "chain_skip_35", "price": 0, "step": 35, "content": [{"Currency": "greenbox2_1", "Amount": 2}]}, {"skin": "2", "bundleId": "chain_skip_36", "price": 0, "step": 36, "content": [{"Currency": "skipprop", "Amount": 400}]}, {"skin": "1", "bundleId": "chain_skip_37", "price": 0, "step": 37, "content": [{"Currency": "additem_3", "Amount": 2}]}, {"skin": "2", "bundleId": "chain_skip_38", "price": 0, "step": 38, "content": [{"Currency": "gem", "Amount": 100}]}], "sLv": 8, "id": 75, "is_open": 1, "dailyShowNum": 2, "sTime": 1749175200, "groupId": "chain5", "rTime": 1749520800, "include": ["bundleContentChain#chain_skip", "bundleTrigger#login"], "duration": 4320, "bundleTrigger": [{"trigger": "login"}], "eTime": 1749434400, "specialType": "chain"}], "md5": "6f1c31041cee40a7a9967686116ff5c5"}, "bakeOut": {"config": {"sTime": 1749089400, "rTime": 1749694200, "bakeout_rank_parameter": [{"delayTime": 90, "noRegisterTime": 300, "settlementTime": 300, "uploadTime": 60, "maxNum": 20, "retainTime": 601200}], "eTime": **********, "staticInclude": ["bakeout_rank_reward#1st", "bakeout_rank_exchange#default", "bakeout_rank_parameter#1st"], "id": 100020, "bakeout_rank_reward": [{"start_rank": 1, "rewards": ["gem-25", "energy-100", "skiptime_1-1"], "end_rank": 1}, {"start_rank": 2, "rewards": ["gem-20", "energy-80", "additem_1-1"], "end_rank": 2}, {"start_rank": 3, "rewards": ["gem-15", "energy-50"], "end_rank": 3}, {"start_rank": 4, "rewards": ["gem-10"], "end_rank": 6}, {"start_rank": 7, "rewards": ["energy-50"], "end_rank": 10}, {"start_rank": 11, "rewards": ["skipprop-50"], "end_rank": 15}, {"start_rank": 16, "rewards": ["skipprop-25"], "end_rank": 20}], "bakeout_rank_exchange": [{"time": 1, "cost": 10}, {"time": 2, "cost": 11}, {"time": 3, "cost": 14}, {"time": 4, "cost": 17}, {"time": 5, "cost": 21}, {"time": 6, "cost": 28}, {"time": 7, "cost": 37}, {"time": 8, "cost": 51}, {"time": 9, "cost": 72}, {"time": 10, "cost": 100}]}, "md5": "cb9512fa52a7f36e4aec16c3ec1147f4"}, "general_conf": {"config": [{"confType": "balance_gold", "param_int": 1, "id": 1}, {"confType": "hint_inner_dish", "param_int": 1, "id": 3}, {"confType": "level_trans_day", "param_int": 1, "id": 4}, {"confType": "social_bind", "param_int": 1, "id": 5}, {"confType": "skipprop_not_enough", "param_int": 1, "id": 6}, {"confType": "ChangeInventory", "param_int": 1, "id": 7}, {"confType": "ingredient_recipe_hint", "param_int": 1, "id": 8}, {"confType": "doubleEnergy_trigger", "param_int": 200, "id": 9, "sLv": 16}, {"confType": "cache", "param_int": 1, "id": 10}, {"confType": "new_order_reward_anim", "param_int": 1, "id": 11}, {"confType": "new_order_reward_anim_skip", "param_int": 1, "id": 12}, {"confType": "item_auto_recycle", "id": 14, "param_int_array": [3, 10]}, {"confType": "order_seq_energy_diff", "param_int": 1, "id": 15}, {"confType": "board_cook_bubble", "param_int": 1, "id": 16}, {"confType": "item_delete_corn_husk", "param_int": 1, "id": 17}], "md5": "5ea0bff8d7e147dca7ddce2a6895558e"}, "pkRace": {"config": {"sLv": 7, "rTime": 1749607200, "pk_race": [{"playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 1, "target": 30, "playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 10}, {"group_type": "pk", "child_type": "normal", "weight": 10}, {"group_type": "pk", "child_type": "fail", "weight": 80}], "rankReward2": [{"Currency": "energy", "Amount": 10}], "rankReward1": [{"Currency": "energy", "Amount": 30}]}, {"playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 60}, {"group_type": "pk", "child_type": "normal", "weight": 30}, {"group_type": "pk", "child_type": "hard", "weight": 10}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 2, "roundRewards": [{"Currency": "skipprop", "Amount": 20}], "target": 30, "rankReward2": [{"Currency": "cbox1_1", "Amount": 1}], "uiCode": "dig_chest_2", "rankReward1": [{"Currency": "greenbox_1", "Amount": 1}]}, {"playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 3, "target": 50, "playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 30}, {"group_type": "pk", "child_type": "normal", "weight": 30}, {"group_type": "pk", "child_type": "hard", "weight": 30}], "rankReward2": [{"Currency": "energy", "Amount": 15}], "rankReward1": [{"Currency": "energy", "Amount": 40}]}, {"playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 20}, {"group_type": "pk", "child_type": "normal", "weight": 40}, {"group_type": "pk", "child_type": "hard", "weight": 40}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 4, "roundRewards": [{"Currency": "additem_1", "Amount": 1}], "target": 50, "rankReward2": [{"Currency": "energy", "Amount": 15}], "uiCode": "dig_chest_2", "rankReward1": [{"Currency": "energy", "Amount": 40}]}, {"playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 5, "target": 70, "playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 20}, {"group_type": "pk", "child_type": "normal", "weight": 30}, {"group_type": "pk", "child_type": "hard", "weight": 50}], "rankReward2": [{"Currency": "energy", "Amount": 20}], "rankReward1": [{"Currency": "energy", "Amount": 50}]}, {"playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 6, "target": 70, "playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 20}, {"group_type": "pk", "child_type": "normal", "weight": 30}, {"group_type": "pk", "child_type": "hard", "weight": 50}], "rankReward2": [{"Currency": "cbox1_1", "Amount": 1}], "rankReward1": [{"Currency": "greenbox_1", "Amount": 1}]}, {"playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 20}, {"group_type": "pk", "child_type": "normal", "weight": 30}, {"group_type": "pk", "child_type": "hard", "weight": 50}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 7, "roundRewards": [{"Currency": "skipprop", "Amount": 60}], "target": 100, "rankReward2": [{"Currency": "energy", "Amount": 25}], "uiCode": "dig_chest_2", "rankReward1": [{"Currency": "energy", "Amount": 60}]}, {"playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 8, "target": 120, "playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 20}, {"group_type": "pk", "child_type": "normal", "weight": 30}, {"group_type": "pk", "child_type": "hard", "weight": 50}], "rankReward2": [{"Currency": "energy", "Amount": 30}], "rankReward1": [{"Currency": "energy", "Amount": 80}]}, {"playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 9, "target": 150, "playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 20}, {"group_type": "pk", "child_type": "normal", "weight": 30}, {"group_type": "pk", "child_type": "hard", "weight": 50}], "rankReward2": [{"Currency": "cbox2_1", "Amount": 1}], "rankReward1": [{"Currency": "greenbox2_1", "Amount": 1}]}, {"playerNumWeight": [{"group_type": "pk", "child_type": "easy", "weight": 20}, {"group_type": "pk", "child_type": "normal", "weight": 30}, {"group_type": "pk", "child_type": "hard", "weight": 50}], "playerNum": [{"group_num": 1, "group_type": "pk"}], "round": 10, "roundRewards": [{"Currency": "energy", "Amount": 150}], "target": 150, "rankReward2": [{"Currency": "energy", "Amount": 30}], "uiCode": "dig_chest_3", "rankReward1": [{"Currency": "energy", "Amount": 80}]}], "include": ["pk_race#pk_race", "order_token_control#pk_race"], "order_token_control": [{"rewardsWeight": [{"Currency": "pkRace", "Weight": 100, "Amount": 1}], "roundType": "down_1", "ratio": 0.33}], "id": 91, "eTime": 1749434400, "sTime": 1749175200}, "md5": "cf9858f1707f5d75c27767dfa779973f"}, "chapterUpdate": {"config": [{"preDay": 1735005600, "enable": 1, "openDay": 1735178400, "dayLevel": 75, "chapter": 5}, {"preDay": 1736215200, "enable": 1, "openDay": 1736388000, "dayLevel": 100, "chapter": 6}, {"preDay": 1737424800, "enable": 1, "openDay": 1737597600, "dayLevel": 125, "chapter": 7}, {"preDay": 1739239200, "enable": 1, "openDay": 1739412000, "dayLevel": 150, "chapter": 8}, {"preDay": 1740448800, "enable": 1, "openDay": 1740621600, "dayLevel": 165, "chapter": 9}, {"preDay": 1741053600, "enable": 1, "openDay": 1741226400, "dayLevel": 180, "chapter": 9}, {"preDay": 1741658400, "enable": 1, "openDay": 1741831200, "dayLevel": 195, "chapter": 10}, {"preDay": 1741831200, "enable": 1, "openDay": 1742436000, "dayLevel": 210, "chapter": 10}, {"preDay": 1742868000, "enable": 1, "openDay": 1743040800, "dayLevel": 225, "chapter": 11}, {"preDay": 1743040800, "enable": 1, "openDay": 1743645600, "dayLevel": 240, "chapter": 11}, {"preDay": 1744077600, "enable": 1, "openDay": 1744250400, "dayLevel": 255, "chapter": 12}, {"preDay": 1744250400, "enable": 1, "openDay": 1744855200, "dayLevel": 270, "chapter": 12}, {"preDay": 1745287200, "enable": 1, "openDay": 1745460000, "dayLevel": 285, "chapter": 13}, {"preDay": 1745460000, "enable": 1, "openDay": 1746064800, "dayLevel": 300, "chapter": 13}, {"preDay": 1746496800, "enable": 1, "openDay": 1746669600, "dayLevel": 315, "chapter": 14}, {"preDay": 1746669600, "enable": 1, "openDay": 1747274400, "dayLevel": 330, "chapter": 14}, {"preDay": 1747706400, "enable": 1, "openDay": 1747879200, "dayLevel": 345, "chapter": 15}, {"preDay": 1747879200, "enable": 1, "openDay": 1748484000, "dayLevel": 360, "chapter": 15}, {"preDay": 1748916000, "enable": 1, "openDay": 1749088800, "dayLevel": 375, "chapter": 16}, {"preDay": 1749088800, "enable": 1, "openDay": **********, "dayLevel": 390, "chapter": 16}], "md5": "1005f1a2ddb0e044580474de216b4cf2"}, "flambeTime": {"config": {"sLv": 7, "id": 26, "eTime": 1771034400, "sTime": 1737597600}, "md5": "26b2de3f4ac083e934bee92fdfee3100"}, "bp5": {"config": {"sTime": 1747188000, "rTime": 1749780000, "battlePassTask": [{"tasks": [{"count": 2, "type": "customer", "tokenNum": 40}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 5, "type": "gem", "tokenNum": 25}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}], "type": "timelimit", "time": 1, "order": 1}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 500, "type": "gold", "tokenNum": 50}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 700, "type": "gold", "tokenNum": 70}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 1000, "type": "gold", "tokenNum": 100}, {"count": 1200, "type": "gold", "tokenNum": 120}], "type": "timelimit", "time": 2, "order": 2}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 450, "type": "gold", "tokenNum": 45}, {"count": 500, "type": "gold", "tokenNum": 50}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 5, "type": "gem", "tokenNum": 25}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}], "type": "timelimit", "time": 3, "order": 3}, {"tasks": [{"count": 2, "type": "customer", "tokenNum": 40}, {"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 1100, "type": "gold", "tokenNum": 110}], "type": "timelimit", "time": 4, "order": 4}, {"tasks": [{"count": 2, "type": "customer", "tokenNum": 40}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 1200, "type": "gold", "tokenNum": 120}], "type": "timelimit", "time": 5, "order": 5}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 11, "type": "customer", "tokenNum": 220}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}, {"count": 20, "type": "gem", "tokenNum": 100}], "type": "timelimit", "time": 6, "order": 6}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 500, "type": "gold", "tokenNum": 50}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 900, "type": "gold", "tokenNum": 90}, {"count": 1100, "type": "gold", "tokenNum": 110}, {"count": 1300, "type": "gold", "tokenNum": 130}], "type": "timelimit", "time": 7, "order": 7}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 500, "type": "gold", "tokenNum": 50}, {"count": 650, "type": "gold", "tokenNum": 65}, {"count": 1000, "type": "gold", "tokenNum": 100}, {"count": 5, "type": "gem", "tokenNum": 25}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}], "type": "timelimit", "time": 8, "order": 8}, {"tasks": [{"count": 2, "type": "customer", "tokenNum": 40}, {"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 900, "type": "gold", "tokenNum": 90}, {"count": 1200, "type": "gold", "tokenNum": 120}], "type": "timelimit", "time": 9, "order": 9}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 450, "type": "gold", "tokenNum": 45}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 5, "type": "gem", "tokenNum": 25}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}], "type": "timelimit", "time": 10, "order": 10}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 900, "type": "gold", "tokenNum": 90}, {"count": 1200, "type": "gold", "tokenNum": 120}], "type": "timelimit", "time": 11, "order": 11}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 11, "type": "customer", "tokenNum": 220}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 1000, "type": "gold", "tokenNum": 100}], "type": "timelimit", "time": 12, "order": 12}, {"tasks": [{"count": 2, "type": "customer", "tokenNum": 40}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 700, "type": "gold", "tokenNum": 70}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 1100, "type": "gold", "tokenNum": 110}], "type": "timelimit", "time": 13, "order": 13}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 950, "type": "gold", "tokenNum": 95}, {"count": 1100, "type": "gold", "tokenNum": 110}], "type": "timelimit", "time": 14, "order": 14}, {"tasks": [{"count": 4, "type": "customer", "tokenNum": 80}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 11, "type": "customer", "tokenNum": 220}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 900, "type": "gold", "tokenNum": 90}, {"count": 1200, "type": "gold", "tokenNum": 120}], "type": "timelimit", "time": 15, "order": 15}, {"tasks": [{"count": 2, "type": "customer", "tokenNum": 40}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 11, "type": "customer", "tokenNum": 220}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 900, "type": "gold", "tokenNum": 90}, {"count": 1200, "type": "gold", "tokenNum": 120}], "type": "timelimit", "time": 16, "order": 16}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}, {"count": 20, "type": "gem", "tokenNum": 100}], "type": "timelimit", "time": 17, "order": 17}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 500, "type": "gold", "tokenNum": 50}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 850, "type": "gold", "tokenNum": 85}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}, {"count": 20, "type": "gem", "tokenNum": 100}], "type": "timelimit", "time": 18, "order": 18}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 700, "type": "gold", "tokenNum": 70}, {"count": 1000, "type": "gold", "tokenNum": 100}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}, {"count": 20, "type": "gem", "tokenNum": 100}], "type": "timelimit", "time": 19, "order": 19}, {"tasks": [{"count": 2, "type": "customer", "tokenNum": 40}, {"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 700, "type": "gold", "tokenNum": 70}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 900, "type": "gold", "tokenNum": 90}], "type": "timelimit", "time": 20, "order": 20}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 600, "type": "gold", "tokenNum": 60}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 1000, "type": "gold", "tokenNum": 100}], "type": "timelimit", "time": 21, "order": 21}, {"tasks": [{"count": 4, "type": "customer", "tokenNum": 80}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}, {"count": 20, "type": "gem", "tokenNum": 100}], "type": "timelimit", "time": 22, "order": 22}, {"tasks": [{"count": 2, "type": "customer", "tokenNum": 40}, {"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 700, "type": "gold", "tokenNum": 70}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 1000, "type": "gold", "tokenNum": 100}], "type": "timelimit", "time": 23, "order": 23}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 450, "type": "gold", "tokenNum": 45}, {"count": 500, "type": "gold", "tokenNum": 50}, {"count": 600, "type": "gold", "tokenNum": 80}, {"count": 700, "type": "gold", "tokenNum": 70}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 950, "type": "gold", "tokenNum": 95}], "type": "timelimit", "time": 24, "order": 24}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 500, "type": "gold", "tokenNum": 50}, {"count": 650, "type": "gold", "tokenNum": 65}, {"count": 850, "type": "gold", "tokenNum": 85}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}, {"count": 20, "type": "gem", "tokenNum": 100}], "type": "timelimit", "time": 25, "order": 25}, {"tasks": [{"count": 4, "type": "customer", "tokenNum": 80}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 7, "type": "customer", "tokenNum": 140}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 11, "type": "customer", "tokenNum": 220}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 900, "type": "gold", "tokenNum": 90}, {"count": 1000, "type": "gold", "tokenNum": 100}], "type": "timelimit", "time": 26, "order": 26}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 800, "type": "gold", "tokenNum": 80}, {"count": 900, "type": "gold", "tokenNum": 90}, {"count": 1000, "type": "gold", "tokenNum": 100}], "type": "timelimit", "time": 27, "order": 27}, {"tasks": [{"count": 3, "type": "customer", "tokenNum": 60}, {"count": 5, "type": "customer", "tokenNum": 100}, {"count": 6, "type": "customer", "tokenNum": 120}, {"count": 8, "type": "customer", "tokenNum": 160}, {"count": 9, "type": "customer", "tokenNum": 180}, {"count": 10, "type": "customer", "tokenNum": 200}, {"count": 10, "type": "gem", "tokenNum": 50}, {"count": 15, "type": "gem", "tokenNum": 75}, {"count": 20, "type": "gem", "tokenNum": 100}], "type": "timelimit", "time": 28, "order": 28}, {"tasks": [{"count": 100, "type": "merge", "tokenNum": 100}], "type": "cycle", "time": 0, "order": 29}], "include": ["battlePassReward#test1", "battlePassTask#test1"], "sLv": 7, "id": 79, "eTime": 1749607200, "battlePassReward": [{"require": 0, "reward": [{"Crypt": "pH", "Currency": "energy", "Amount": 30}], "level": 1, "golden_reward": [{"Crypt": "rMZ", "Currency": "energy", "Amount": 150}]}, {"require": 60, "reward": [{"Crypt": "r", "Currency": "it_1_2_2", "Amount": 1}], "level": 2, "golden_reward": [{"Crypt": "r", "Currency": "gem_2", "Amount": 1}]}, {"require": 90, "reward": [{"Crypt": "r", "Currency": "ene_2", "Amount": 1}], "level": 3, "golden_reward": [{"Crypt": "vH", "Currency": "skipprop", "Amount": 50}]}, {"require": 120, "reward": [{"Crypt": "pH", "Currency": "skipprop", "Amount": 30}], "level": 4, "golden_reward": [{"Crypt": "r", "Currency": "additem_1", "Amount": 1}]}, {"require": 150, "reward": [{"Crypt": "r", "Currency": "it_1_1_5", "Amount": 1}], "level": 5, "golden_reward": [{"Crypt": "r", "Currency": "ene_3", "Amount": 1}]}, {"require": 180, "reward": [{"Crypt": "r", "Currency": "ene_2", "Amount": 1}], "level": 6, "golden_reward": [{"Crypt": "r", "Currency": "it_1_1_6", "Amount": 1}]}, {"require": 220, "reward": [{"Crypt": "r", "Currency": "freebox_1", "Amount": 1}], "level": 7, "golden_reward": [{"Crypt": "r", "Currency": "gem_2", "Amount": 1}]}, {"require": 260, "reward": [{"Crypt": "r", "Currency": "additem_1", "Amount": 1}], "level": 8, "golden_reward": [{"Crypt": "rHZ", "Currency": "skipprop", "Amount": 100}]}, {"require": 280, "reward": [{"Crypt": "r", "Currency": "gem_1", "Amount": 1}], "level": 9, "golden_reward": [{"Crypt": "q", "Currency": "additem_1", "Amount": 2}]}, {"require": 340, "reward": [{"Crypt": "r", "Currency": "greenbox_1", "Amount": 1}], "level": 10, "golden_reward": [{"Crypt": "r", "Currency": "enebox_1", "Amount": 1}]}, {"require": 380, "reward": [{"Crypt": "vH", "Currency": "skipprop", "Amount": 50}], "level": 11, "golden_reward": [{"Crypt": "r", "Currency": "it_2_3_2", "Amount": 1}]}, {"require": 410, "reward": [{"Crypt": "r", "Currency": "it_2_3_1", "Amount": 1}], "level": 12, "golden_reward": [{"Crypt": "r", "Currency": "ene_3", "Amount": 1}]}, {"require": 460, "reward": [{"Crypt": "r", "Currency": "gem_1", "Amount": 1}], "level": 13, "golden_reward": [{"Crypt": "r", "Currency": "gem_2", "Amount": 1}]}, {"require": 500, "reward": [{"Crypt": "r", "Currency": "ene_2", "Amount": 1}], "level": 14, "golden_reward": [{"Crypt": "rHZ", "Currency": "skipprop", "Amount": 100}]}, {"require": 540, "reward": [{"Crypt": "r", "Currency": "freebox_1", "Amount": 1}], "level": 15, "golden_reward": [{"Crypt": "r", "Currency": "ene_4", "Amount": 1}]}, {"require": 580, "reward": [{"Crypt": "r", "Currency": "additem_1", "Amount": 1}], "level": 16, "golden_reward": [{"Crypt": "r", "Currency": "freebox_1", "Amount": 1}]}, {"require": 640, "reward": [{"Crypt": "vH", "Currency": "skipprop", "Amount": 50}], "level": 17, "golden_reward": [{"Crypt": "r", "Currency": "ene_4", "Amount": 1}]}, {"require": 700, "reward": [{"Crypt": "r", "Currency": "gem_2", "Amount": 1}], "level": 18, "golden_reward": [{"Crypt": "r", "Currency": "it_3_1_7", "Amount": 1}]}, {"require": 780, "reward": [{"Crypt": "r", "Currency": "it_3_1_6", "Amount": 1}], "level": 19, "golden_reward": [{"Crypt": "q", "Currency": "additem_1", "Amount": 2}]}, {"require": 850, "reward": [{"Crypt": "pH", "Currency": "energy", "Amount": 30}], "level": 20, "golden_reward": [{"Crypt": "vH", "Currency": "energy", "Amount": 50}]}, {"require": 940, "reward": [{"Crypt": "r", "Currency": "ene_3", "Amount": 1}], "level": 21, "golden_reward": [{"Crypt": "rH", "Currency": "gem", "Amount": 10}]}, {"require": 1020, "reward": [{"Crypt": "r", "Currency": "gem_2", "Amount": 1}], "level": 22, "golden_reward": [{"Crypt": "r", "Currency": "ene_5", "Amount": 1}]}, {"require": 1120, "reward": [{"Crypt": "r", "Currency": "enebox_1", "Amount": 1}], "level": 23, "golden_reward": [{"Crypt": "r", "Currency": "it_3_2_5", "Amount": 1}]}, {"require": 1220, "reward": [{"Crypt": "r", "Currency": "additem_1", "Amount": 1}], "level": 24, "golden_reward": [{"Crypt": "q", "Currency": "additem_1", "Amount": 2}]}, {"require": 1320, "reward": [{"Crypt": "vH", "Currency": "skipprop", "Amount": 50}], "level": 25, "golden_reward": [{"Crypt": "rHZ", "Currency": "skipprop", "Amount": 100}]}, {"require": 1430, "reward": [{"Crypt": "r", "Currency": "it_3_2_4", "Amount": 1}], "level": 26, "golden_reward": [{"Crypt": "r", "Currency": "enebox_1", "Amount": 1}]}, {"require": 1540, "reward": [{"Crypt": "r", "Currency": "gem_2", "Amount": 1}], "level": 27, "golden_reward": [{"Crypt": "r", "Currency": "gem_3", "Amount": 1}]}, {"require": 1650, "reward": [{"Crypt": "r", "Currency": "ene_4", "Amount": 1}], "level": 28, "golden_reward": [{"Crypt": "r", "Currency": "it_4_2_5", "Amount": 1}]}, {"require": 1760, "reward": [{"Crypt": "r", "Currency": "it_4_2_4", "Amount": 1}], "level": 29, "golden_reward": [{"Crypt": "r", "Currency": "ene_4", "Amount": 1}]}, {"require": 1880, "reward": [{"Crypt": "r", "Currency": "additem_1", "Amount": 1}], "level": 30, "golden_reward": [{"Crypt": "rM", "Currency": "gem", "Amount": 15}]}, {"require": 2000, "reward": [{"Crypt": "r", "Currency": "gem_3", "Amount": 1}, {"Crypt": "r", "Currency": "greenbox_1", "Amount": 1}], "level": 31, "golden_reward": [{"Crypt": "r", "Currency": "enebox_1", "Amount": 1}, {"Crypt": "r", "Currency": "greenbox_1", "Amount": 1}]}, {"require": 2120, "reward": [{"Crypt": "r", "Currency": "ene_4", "Amount": 1}], "level": 32, "golden_reward": [{"Crypt": "r", "Currency": "ene_5", "Amount": 1}]}, {"require": 2220, "reward": [{"Crypt": "vH", "Currency": "skipprop", "Amount": 50}], "level": 33, "golden_reward": [{"Crypt": "r", "Currency": "gem_4", "Amount": 1}]}, {"require": 2360, "reward": [{"Crypt": "r", "Currency": "gem_4", "Amount": 1}], "level": 34, "golden_reward": [{"Crypt": "rHZ", "Currency": "skipprop", "Amount": 100}]}, {"require": 2400, "reward": [{"Crypt": "r", "Currency": "enebox_1", "Amount": 1}], "level": 35, "golden_reward": [{"Crypt": "qM", "Currency": "gem", "Amount": 25}]}, {"require": 2480, "reward": [{"Crypt": "r", "Currency": "skiptime_1", "Amount": 1}], "level": 36, "golden_reward": [{"Crypt": "rHZ", "Currency": "energy", "Amount": 100}, {"Crypt": "r", "Currency": "skiptime_1", "Amount": 1}, {"Crypt": "r", "Currency": "greenbox_1", "Amount": 1}]}]}, "md5": "********************************"}, "rateUs": {"config": [{"sLv": 5, "id": 2, "contact": 0, "link": "market://details?id=com.cola.game", "task": [{"TaskCount": 30, "ChapterId": 2}, {"TaskCount": 29, "ChapterId": 3}]}], "md5": "cc9b438acb2247f9676ad310935caae2"}, "treasureDig": {"config": {"sTime": 1748829600, "rTime": 1749348000, "id": 90, "include": ["digLevelConfig#lessgem", "digScoreConfig#default"], "eTime": 1749175200, "digLevelConfig": [{"rewards": [{"Amount": 15, "Currency": "energy"}, {"Amount": 20, "Currency": "skipprop"}], "pool": ["1_1", "1_2", "1_3", "1_4", "1_5"], "icon_ui": "dig_chest_1", "round": 1}, {"rewards": [{"Amount": 20, "Currency": "energy"}, {"Amount": 25, "Currency": "skipprop"}], "pool": ["2_1", "2_2", "2_3", "2_4", "2_5"], "icon_ui": "dig_chest_1", "round": 2}, {"rewards": [{"Amount": 25, "Currency": "energy"}, {"Amount": 30, "Currency": "skipprop"}], "pool": ["3_1", "3_2", "3_3", "3_4", "3_5"], "icon_ui": "dig_chest_1", "round": 3}, {"rewards": [{"Amount": 1, "Currency": "gem_1"}, {"Amount": 30, "Currency": "energy"}, {"Amount": 1, "Currency": "additem_1"}], "pool": ["4_1", "4_2", "4_3", "4_4", "4_5"], "icon_ui": "dig_chest_2", "round": 4}, {"rewards": [{"Amount": 1, "Currency": "gem_1"}, {"Amount": 35, "Currency": "energy"}, {"Amount": 1, "Currency": "additem_1"}], "pool": ["5_1", "5_2", "5_3", "5_4", "5_5"], "icon_ui": "dig_chest_2", "round": 5}, {"rewards": [{"Amount": 1, "Currency": "gem_2"}, {"Amount": 40, "Currency": "energy"}, {"Amount": 1, "Currency": "additem_1"}], "pool": ["6_1", "6_2", "6_3", "6_4", "6_5"], "icon_ui": "dig_chest_2", "round": 6}, {"rewards": [{"Amount": 5, "Currency": "gem"}, {"Amount": 40, "Currency": "energy"}, {"Amount": 1, "Currency": "greenbox_1"}], "pool": ["7_1", "7_2", "7_3", "7_4", "7_5"], "icon_ui": "dig_chest_3", "round": 7}, {"rewards": [{"Amount": 10, "Currency": "gem"}, {"Amount": 50, "Currency": "energy"}, {"Amount": 1, "Currency": "greenbox_1"}], "pool": ["8_1", "8_2", "8_3", "8_4"], "icon_ui": "dig_chest_3", "round": 8}], "digScoreConfig": [{"coin_max": 50, "num": 1, "coin_min": 0}, {"coin_max": 100, "num": 2, "coin_min": 51}, {"coin_max": 150, "num": 3, "coin_min": 101}, {"coin_max": 200, "num": 4, "coin_min": 151}, {"coin_max": 250, "num": 5, "coin_min": 201}, {"coin_max": 300, "num": 6, "coin_min": 251}, {"coin_max": 350, "num": 7, "coin_min": 301}, {"coin_max": 400, "num": 8, "coin_min": 351}, {"coin_max": 450, "num": 9, "coin_min": 401}, {"coin_max": 500, "num": 10, "coin_min": 451}, {"coin_max": 600, "num": 12, "coin_min": 501}, {"coin_max": 800, "num": 14, "coin_min": 601}, {"coin_max": 9999, "num": 15, "coin_min": 801}], "sLv": 7}, "md5": "a38baf81f7619bfd090fd418d495ab70"}}, "key": "key"}}, "shop": {"eventEnergyBuyCost": {"value": 10, "key": "eventEnergyBuyCost"}, "refreshCostResetTime": {"value": 1749175796, "key": "refreshCostResetTime"}, "energyRefreshTime": {"value": 1749175796, "key": "energyRefreshTime"}, "flashRefreshTime": {"value": 1749175796, "key": "flashRefreshTime"}, "energyBuyCost": {"value": 10, "key": "energyBuyCost"}, "refreshCost": {"value": 10, "key": "refreshCost"}, "dailyRefreshTime": {"value": 1749175796, "key": "dailyRefreshTime"}}, "newSkin": {}, "weddingDay": {}, "taskMeta": {"OngoingChapterId": {"value": 5, "key": "OngoingChapterId"}, "TaskCleanGold": {"value": 888, "key": "TaskCleanGold"}, "TaskProgress": {"value": 5010, "key": "TaskProgress"}, "CleanGoldCost": {"value": "1#19;2#17;3#15;4#15;5#17;6#18;7#19;8#17;9#20;10#16;11#16;12#19;13#19;14#19;15#18;16#20;17#18;18#19;19#18;20#22;21#19;22#21;23#18;24#21;25#20;26#24;27#19;28#20;29#16;30#22;31#22;32#23;33#22;34#22;35#22;36#25;37#23;38#21;39#21;40#19;41#26;42#24;43#22;44#23;45#19;46#26;47#21", "key": "CleanGoldCost"}, "ProgressTaskFinishCount": {"value": "3", "key": "ProgressTaskFinishCount"}, "OngoingChapterName": {"value": "Wine", "key": "OngoingChapterName"}, "ProgressId": {"value": "8", "key": "ProgressId"}, "ChapterFinishWin": {"value": 4, "key": "ChapterFinishWin"}, "OngoingTaskIds": {"value": "11", "key": "OngoingTaskIds"}}, "activity": {"treasureDig": {"name": "treasureDig", "data": "{\"digPosState\":{\"value\":\"[[false@false@false@true@true]@[false@true@false@true@true]@[false@true@false@false@false]@[false@true@false@true@true]@[false@true@false@false@true]]\"},\"windowOpened3\":{\"value\":1},\"windowOpened2\":{\"value\":1},\"round\":{\"value\":4},\"score\":{\"value\":0},\"id\":{\"value\":90},\"hasAddToken\":{\"value\":1},\"level\":{\"value\":\"4_1\"}}"}, "BP5": {"name": "BP5", "data": "{\"RewardEffect9_0\":{\"value\":1},\"RewardEffect6_0\":{\"value\":1},\"RewardEffect11_0\":{\"value\":1},\"id\":{\"value\":79},\"CycleTaskIndex\":{\"value\":1},\"TimelimitTaskFinished9\":{\"value\":0},\"RewardEffect12_0\":{\"value\":1},\"TokenNumber\":{\"value\":3360},\"TimelimitTaskFinished3\":{\"value\":0},\"ProgressEffect_4\":{\"value\":1},\"windowOpened2\":{\"value\":1},\"RewardTaken8_0\":{\"value\":1},\"TimelimitTaskFinished1\":{\"value\":0},\"RewardEffect13_0\":{\"value\":1},\"FinishedLevel\":{\"value\":13},\"RewardEffect1_0\":{\"value\":1},\"TimelimitTaskFinished2\":{\"value\":0},\"ProgressEffect_13\":{\"value\":1},\"ProgressEffect_3\":{\"value\":1},\"FinTLtask\":{\"value\":13},\"RewardEffect7_0\":{\"value\":1},\"RewardEffect10_0\":{\"value\":1},\"ProgressEffect_12\":{\"value\":1},\"TimelimitTaskFinished8\":{\"value\":0},\"ProgressEffect_7\":{\"value\":1},\"CycleTaskFinishedCount\":{\"value\":65},\"TimelimitTaskOrder\":{\"value\":24},\"TimelimitTaskFinished5\":{\"value\":0},\"RewardTaken7_0\":{\"value\":1},\"TimelimitTaskFinishedCount6\":{\"value\":0},\"TimelimitTaskFinished6\":{\"value\":0},\"TimelimitTaskFinishedCount1\":{\"value\":0},\"RewardEffect3_0\":{\"value\":1},\"TimelimitTaskFinishedCount8\":{\"value\":0},\"RewardEffect2_0\":{\"value\":1},\"ProgressEffect_5\":{\"value\":1},\"TimelimitTaskFinishedCount3\":{\"value\":0},\"ProgressEffect_11\":{\"value\":1},\"ProgressEffect_9\":{\"value\":1},\"RewardEffect8_0\":{\"value\":1},\"TimelimitTaskFinished4\":{\"value\":0},\"ProgressEffect_1\":{\"value\":1},\"ProgressEffect_6\":{\"value\":1},\"TimelimitTaskFinishedCount9\":{\"value\":0},\"TimelimitTaskFinishedCount4\":{\"value\":0},\"ProgressEffect_2\":{\"value\":1},\"TimelimitTaskFinishedCount2\":{\"value\":0},\"TimelimitTaskFinishedCount5\":{\"value\":0},\"RewardEffect4_0\":{\"value\":1},\"TimelimitTaskFinished7\":{\"value\":0},\"TimelimitTaskFinishedCount7\":{\"value\":0},\"RewardEffect5_0\":{\"value\":1},\"ProgressEffect_8\":{\"value\":1},\"windowOpenedBP5MainWindow\":{\"value\":1},\"RewardTaken2_0\":{\"value\":1},\"ProgressEffect_10\":{\"value\":1}}"}, "pkRace": {"name": "pkRace", "data": "{\"windowOpened2\":{\"value\":1},\"playerDatas\":{\"value\":\"[{\\\"name\\\":\\\"<PERSON>\\\",\\\"icon\\\":\\\"head4\\\",\\\"id\\\":10000009881,\\\"score\\\":\\\"[{\\\\\\\"score\\\\\\\":53.33,\\\\\\\"seconds\\\\\\\":366}]\\\",\\\"group_type\\\":\\\"pk_fail\\\"}]\"},\"id\":{\"value\":91},\"ath_o_50430\":{\"value\":\"{\\\"Amount\\\":8@\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"}\"},\"ath_o_50450\":{\"value\":\"{\\\"Amount\\\":16@\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"}\"},\"ath_o_50470\":{\"value\":\"{\\\"Amount\\\":36@\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"}\"},\"lastScore\":{\"value\":\"{\\\"1\\\":0,\\\"round\\\":1,\\\"10000009881\\\":0}\"},\"round\":{\"value\":1},\"ath_o_50460\":{\"value\":\"{\\\"Amount\\\":39@\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"}\"},\"ath_o_50480\":{\"value\":\"{\\\"Amount\\\":35@\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"}\"},\"ath_o_50440\":{\"value\":\"{\\\"Amount\\\":40@\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"}\"},\"entryTime\":{\"value\":1749175847},\"ath_o_50490\":{\"value\":\"{\\\"Amount\\\":36@\\\"Weight\\\":100@\\\"Currency\\\":\\\"pkRace\\\"}\"}}"}, "bakeOut": {"name": "bakeOut", "data": "{\"cachedData\":{\"value\":\"{\\\"sTime\\\":1749089400,\\\"bakeout_rank_reward\\\":[{\\\"end_rank\\\":1,\\\"rewards\\\":[\\\"gem-25\\\",\\\"energy-100\\\",\\\"skiptime_1-1\\\"],\\\"start_rank\\\":1},{\\\"end_rank\\\":2,\\\"rewards\\\":[\\\"gem-20\\\",\\\"energy-80\\\",\\\"additem_1-1\\\"],\\\"start_rank\\\":2},{\\\"end_rank\\\":3,\\\"rewards\\\":[\\\"gem-15\\\",\\\"energy-50\\\"],\\\"start_rank\\\":3},{\\\"end_rank\\\":6,\\\"rewards\\\":[\\\"gem-10\\\"],\\\"start_rank\\\":4},{\\\"end_rank\\\":10,\\\"rewards\\\":[\\\"energy-50\\\"],\\\"start_rank\\\":7},{\\\"end_rank\\\":15,\\\"rewards\\\":[\\\"skipprop-50\\\"],\\\"start_rank\\\":11},{\\\"end_rank\\\":20,\\\"rewards\\\":[\\\"skipprop-25\\\"],\\\"start_rank\\\":16}],\\\"bakeout_rank_parameter\\\":[{\\\"retainTime\\\":601200,\\\"settlementTime\\\":300,\\\"delayTime\\\":90,\\\"noRegisterTime\\\":300,\\\"maxNum\\\":20,\\\"uploadTime\\\":60}],\\\"rTime\\\":1749694200,\\\"staticInclude\\\":[\\\"bakeout_rank_reward#1st\\\",\\\"bakeout_rank_exchange#default\\\",\\\"bakeout_rank_parameter#1st\\\"],\\\"bakeout_rank_exchange\\\":[{\\\"cost\\\":10,\\\"time\\\":1},{\\\"cost\\\":11,\\\"time\\\":2},{\\\"cost\\\":14,\\\"time\\\":3},{\\\"cost\\\":17,\\\"time\\\":4},{\\\"cost\\\":21,\\\"time\\\":5},{\\\"cost\\\":28,\\\"time\\\":6},{\\\"cost\\\":37,\\\"time\\\":7},{\\\"cost\\\":51,\\\"time\\\":8},{\\\"cost\\\":72,\\\"time\\\":9},{\\\"cost\\\":100,\\\"time\\\":10}],\\\"eTime\\\":**********,\\\"id\\\":100020}\"},\"cachedDataMd5\":{\"value\":\"cb9512fa52a7f36e4aec16c3ec1147f4\"},\"id\":{\"value\":100020},\"delayedGetRewardTime\":{\"value\":38}}"}}, "userProfile": {}, "localRewards": {}, "account": {"SocialId": {"value": "***************", "key": "SocialId"}, "SocialType": {"value": "1", "key": "SocialType"}, "SocialPictureUrl": {"value": "https://graph.facebook.com/***************/picture?type=large&access_token=EAAcKmT4gWFsBO3cf0kjmMLCJf71NZCAFzQOHZCETlhBxU2UN6yrYFBYbZAofmtvmkoQ8Alb97XI7S6nbG3fz68ZCTsKdbpklKczMFuCp9wkg3j64yal7lhQ2zsR7vfGdZCUx1jT6qRjQ9WtwA3WY61Pw1tTkb4OoKuZCDhqdaPl1Ku1GrGOrRkkdbZBDO9qlyKmWLvBczefZCZBw6MjZAQ0YeWlPbT5m1ADMcA5z844YFAlGhMbv40PAZDZD", "key": "SocialPictureUrl"}, "SocialName": {"value": "<PERSON>", "key": "SocialName"}}, "tutorial": {"cook1": {"id": "cook1", "state": 2, "ongoingDatas": ""}, "task1_1": {"id": "task1_1", "state": 2, "ongoingDatas": ""}, "tutorial_digactivity_firstdig": {"id": "tutorial_digactivity_firstdig", "state": 2, "ongoingDatas": ""}, "order10050": {"id": "order10050", "state": 2, "ongoingDatas": ""}, "tutorial_producer_inventory": {"id": "tutorial_producer_inventory", "state": 1, "ongoingDatas": ""}, "tutorial_cg": {"id": "tutorial_cg", "state": 2, "ongoingDatas": ""}, "additem_old_user": {"id": "additem_old_user", "state": 2, "ongoingDatas": ""}, "order10070": {"id": "order10070", "state": 2, "ongoingDatas": ""}, "cache": {"id": "cache", "state": 2, "ongoingDatas": ""}, "tutorial_battlepass_loop": {"id": "tutorial_battlepass_loop", "state": 1, "ongoingDatas": ""}, "cook3": {"id": "cook3", "state": 2, "ongoingDatas": ""}, "order10020": {"id": "order10020", "state": 2, "ongoingDatas": ""}, "task1_3": {"id": "task1_3", "state": 2, "ongoingDatas": ""}, "order10030": {"id": "order10030", "state": 2, "ongoingDatas": ""}, "cd_speed": {"id": "cd_speed", "state": 2, "ongoingDatas": ""}, "order10080": {"id": "order10080", "state": 2, "ongoingDatas": ""}, "tutorial_extraboard_start": {"id": "tutorial_extraboard_start", "state": 2, "ongoingDatas": ""}, "tutorial_battlepass": {"id": "tutorial_battlepass", "state": 2, "ongoingDatas": ""}, "task1_4": {"id": "task1_4", "state": 2, "ongoingDatas": ""}, "clickPD": {"id": "clickPD", "state": 2, "ongoingDatas": ""}, "tutorial_pk_race_start": {"id": "tutorial_pk_race_start", "state": 2, "ongoingDatas": ""}, "tutorial_coin_race_first_main_window": {"id": "tutorial_coin_race_first_main_window", "state": 2, "ongoingDatas": ""}, "cook2": {"id": "cook2", "state": 2, "ongoingDatas": ""}, "tutorial_coin_race_second_main_window": {"id": "tutorial_coin_race_second_main_window", "state": 2, "ongoingDatas": ""}, "timeline": {"id": "timeline", "state": 2, "ongoingDatas": ""}, "task1_2": {"id": "task1_2", "state": 2, "ongoingDatas": ""}, "merge1": {"id": "merge1", "state": 2, "ongoingDatas": ""}, "tutorial_blind_chest": {"id": "tutorial_blind_chest", "state": 1, "ongoingDatas": ""}, "order_group": {"id": "order_group", "state": 2, "ongoingDatas": ""}, "merge2": {"id": "merge2", "state": 2, "ongoingDatas": ""}, "additem_+8": {"id": "additem_+8", "state": 2, "ongoingDatas": ""}, "tutorial_extraboard_item_delete": {"id": "tutorial_extraboard_item_delete", "state": 1, "ongoingDatas": ""}, "tutorial_coin_race_entry": {"id": "tutorial_coin_race_entry", "state": 2, "ongoingDatas": ""}, "order10010": {"id": "order10010", "state": 2, "ongoingDatas": ""}, "energy": {"id": "energy", "state": 2, "ongoingDatas": ""}, "weakGesture": {"id": "weakGesture", "state": 1, "ongoingDatas": ""}, "toboard": {"id": "toboard", "state": 2, "ongoingDatas": ""}, "tutorial_extraboard_cobweb_merge": {"id": "tutorial_extraboard_cobweb_merge", "state": 1, "ongoingDatas": ""}, "order_item_info": {"id": "order_item_info", "state": 2, "ongoingDatas": ""}, "tutorial_energy_boost": {"id": "tutorial_energy_boost", "state": 2, "ongoingDatas": ""}, "CD_pd_2_6": {"id": "CD_pd_2_6", "state": 2, "ongoingDatas": ""}, "tutorial_extraboard_cobweb_unlock": {"id": "tutorial_extraboard_cobweb_unlock", "state": 1, "ongoingDatas": ""}, "CD_pd_1_7": {"id": "CD_pd_1_7", "state": 2, "ongoingDatas": ""}, "tutorial_coin_race_order": {"id": "tutorial_coin_race_order", "state": 2, "ongoingDatas": ""}, "shop": {"id": "shop", "state": 2, "ongoingDatas": ""}, "order10140": {"id": "order10140", "state": 2, "ongoingDatas": ""}, "bubble": {"id": "bubble", "state": 2, "ongoingDatas": ""}, "order10040": {"id": "order10040", "state": 2, "ongoingDatas": ""}, "tutorial_progress": {"id": "tutorial_progress", "state": 2, "ongoingDatas": ""}, "tutorial_digactivity_seconddig": {"id": "tutorial_digactivity_seconddig", "state": 2, "ongoingDatas": ""}}, "item": {"1740484341001": {"choices": "", "codeStr": "pd_2_6", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 523, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_2_1_1-1;it_2_2_1-1;it_2_1_1-1;it_2_1_2-1;it_2_1_1-1;it_2_1_3-1;it_2_3_1-1;it_2_1_1-1;it_2_1_2-1;it_2_1_1-1;it_2_2_1-1;it_2_1_2-1;it_2_1_1-1;it_2_3_1-1;it_2_1_1-1;it_2_1_2-1;it_2_2_1-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 36, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1740484341001", "spreadTierUpLevel": 0}, "1746852141001": {"id": "1746852141001", "codeStr": "eq_1_5", "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_7_1_5-1749037175001;it_4_2_5-1749176209001", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 2, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1749037143001": {"choices": "", "codeStr": "it_5_1_3", "shopGemCost": 0, "id": "1749037143001", "costEnergy": 3, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1744683794001": {"choices": "", "codeStr": "pd_3_7", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 1053, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_3_1_3-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_2-1;it_3_1_1-1;it_3_2_1-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 42, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1744683794001", "spreadTierUpLevel": 0}, "1743128763001": {"choices": "", "codeStr": "pd_5_5", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 2, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 160, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_5_1_1-9;it_5_1_2-2;it_5_2_1-9", "spreadEnergyFree": 0, "spreadStorageRestNumber": 24, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1743128763001", "spreadTierUpLevel": 0}, "1749176252003": {"choices": "", "codeStr": "it_2_1_1", "shopGemCost": 0, "id": "1749176252003", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 1, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176303001": {"id": "1749176303001", "codeStr": "eq_1_4", "bubbleGemCost": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "", "shopGemCost": 0, "cookStartTimer": -1, "costEnergyCurDay": 0, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "cookState": 1}, "1741572578001": {"choices": "", "codeStr": "pd_6_4", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 2, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 48, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_6_1_1-12", "spreadEnergyFree": 0, "spreadStorageRestNumber": 24, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1741572578001", "spreadTierUpLevel": 0}, "1734438341001": {"choices": "", "codeStr": "pd_4_6", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 2934, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 0, "spreadStartTimer": 1749176103, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1734438341001", "spreadTierUpLevel": 0}, "1749176070002": {"choices": "", "codeStr": "it_7_1_6", "shopGemCost": 0, "id": "1749176070002", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 31, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749003761001": {"choices": "", "codeStr": "it_4_1_7", "shopGemCost": 0, "id": "1749003761001", "costEnergy": 44, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1743846671001": {"id": "1743846671001", "codeStr": "eq_4_5", "bubbleGemCost": 0, "cookRecipe": "ds_friedve_1", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_1_1_8-1748916551001", "shopGemCost": 0, "cookStartTimer": 1749176003, "cookState": 4, "cookLastUpdateTime": 1749176391, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1742351309001": {"id": "1742351309001", "codeStr": "eq_4_6", "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_7_1_4-1749037155002;it_2_3_3-1749176288001", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 2, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1749037115002": {"choices": "", "codeStr": "it_5_1_4", "shopGemCost": 0, "id": "1749037115002", "costEnergy": 6, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1745913127002": {"choices": "", "codeStr": "it_6_1_4", "shopGemCost": 0, "id": "1745913127002", "costEnergy": 8, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1739932709001": {"id": "1739932709001", "codeStr": "eq_3_6", "bubbleGemCost": 0, "cookRecipe": "ds_juice_6", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_4_1_7-1749003761001", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 3, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1749037144001": {"choices": "", "codeStr": "it_5_1_2", "shopGemCost": 0, "id": "1749037144001", "costEnergy": 2, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749175976002": {"choices": "", "codeStr": "it_3_1_3", "shopGemCost": 0, "id": "1749175976002", "costEnergy": 3, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1748225104005": {"choices": "", "codeStr": "it_6_1_1", "shopGemCost": 0, "id": "1748225104005", "costEnergy": 1, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1746852265001": {"id": "1746852265001", "codeStr": "eq_6_4", "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_1_1_7-1748593955001", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 2, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1749176012002": {"choices": "", "codeStr": "it_7_2_2", "shopGemCost": 0, "id": "1749176012002", "costEnergy": 1, "bubbleGemCost": 0, "costEnergyCurDay": 1, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1742869211001": {"id": "1742869211001", "codeStr": "eq_2_6", "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_4_1_3-1749003899002", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 2, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1749176194001": {"choices": "", "codeStr": "it_4_1_5", "shopGemCost": 0, "id": "1749176194001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 11, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1730096039001": {"choices": "", "codeStr": "pd_2_7", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 4897, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_2_2_1-1;it_2_1_1-1;it_2_1_2-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 17, "spreadStartTimer": 1749176226, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1730096039001", "spreadTierUpLevel": 0}, "1730095941001": {"id": "1730095941001", "codeStr": "eq_2_5", "bubbleGemCost": 0, "cookRecipe": "ds_grillmt_7", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_3_1_7-1749127843001", "shopGemCost": 0, "cookStartTimer": 1749175963, "cookState": 4, "cookLastUpdateTime": 1749176391, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1743991993001": {"id": "1743991993001", "codeStr": "eq_3_4", "bubbleGemCost": 0, "cookRecipe": "ds_juice_6", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_4_1_7-1747990562001", "shopGemCost": 0, "cookStartTimer": 1749175943, "cookState": 4, "cookLastUpdateTime": 1749176391, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1749176103003": {"choices": "", "codeStr": "it_4_2_1", "shopGemCost": 0, "id": "1749176103003", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 1, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1738287944001": {"choices": "", "codeStr": "pd_6_5", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 2, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 900, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_6_1_1-16;it_6_1_2-4", "spreadEnergyFree": 0, "spreadStorageRestNumber": 30, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1738287944001", "spreadTierUpLevel": 0}, "1749089970002": {"choices": "", "codeStr": "it_1_1_7", "shopGemCost": 0, "id": "1749089970002", "costEnergy": 31, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176305001": {"choices": "", "codeStr": "pd_1_4", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 0, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1", "costEnergy": 0, "spreadStorageRestNumber": 8, "spreadStartTimer": 1749176305, "spreadInherit": 0, "spreadEnergyFree": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1749176305001", "spreadTierUpLevel": 0}, "1749176282002": {"choices": "", "codeStr": "it_2_1_2", "shopGemCost": 0, "id": "1749176282002", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 1, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176331001": {"choices": "", "codeStr": "it_2_2_4", "shopGemCost": 0, "id": "1749176331001", "costEnergy": 5, "bubbleGemCost": 0, "costEnergyCurDay": 3, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176209001": {"choices": "", "codeStr": "it_4_2_5", "shopGemCost": 0, "id": "1749176209001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 16, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176279002": {"choices": "", "codeStr": "it_2_1_5", "shopGemCost": 0, "id": "1749176279002", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 9, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1737340454001": {"id": "1737340454001", "codeStr": "eq_1_6", "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_7_2_2-1749176012002", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 2, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1749176104001": {"choices": "", "codeStr": "it_4_1_2", "shopGemCost": 0, "id": "1749176104001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 1, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176214001": {"choices": "", "codeStr": "it_4_1_2", "shopGemCost": 0, "id": "1749176214001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 2, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749127808002": {"choices": "", "codeStr": "it_1_1_7", "shopGemCost": 0, "id": "1749127808002", "costEnergy": 35, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176336001": {"choices": "", "codeStr": "ene_3", "shopGemCost": 0, "id": "1749176336001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1748593955001": {"choices": "", "codeStr": "it_1_1_7", "shopGemCost": 0, "id": "1748593955001", "costEnergy": 25, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1747715534001": {"id": "1747715534001", "codeStr": "eq_5_5", "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_4_1_1-1749176103002;it_4_1_2-1749176104001;it_4_2_1-1749176103003", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 2, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1738834040001": {"choices": "", "codeStr": "pd_4_5", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 546, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_4_1_1-1;it_4_1_2-1;it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_1_2-1;it_4_1_1-1;it_4_2_1-1;it_4_1_2-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 0, "spreadStartTimer": 1749176163, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1738834040001", "spreadTierUpLevel": 0}, "1749176103002": {"choices": "", "codeStr": "it_4_1_1", "shopGemCost": 0, "id": "1749176103002", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 1, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1745913071002": {"choices": "", "codeStr": "it_1_2_1_2", "shopGemCost": 0, "id": "1745913071002", "costEnergy": 16, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749089879001": {"choices": "", "codeStr": "it_2_1_7", "shopGemCost": 0, "id": "1749089879001", "costEnergy": 23, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749003899002": {"choices": "", "codeStr": "it_4_1_3", "shopGemCost": 0, "id": "1749003899002", "costEnergy": 3, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749004167001": {"choices": "", "codeStr": "eq_4_3", "shopGemCost": 0, "id": "1749004167001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749175952001": {"choices": "", "codeStr": "it_2_2_6", "shopGemCost": 0, "id": "1749175952001", "costEnergy": 32, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176183001": {"choices": "", "codeStr": "it_4_1_7", "shopGemCost": 0, "id": "1749176183001", "costEnergy": 12, "bubbleGemCost": 0, "costEnergyCurDay": 32, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749175987002": {"choices": "", "codeStr": "ene_1", "shopGemCost": 0, "id": "1749175987002", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176369001": {"choices": "", "codeStr": "it_2_1_3", "shopGemCost": 0, "id": "1749176369001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1741054354001": {"choices": "", "codeStr": "pd_4_3", "shopGemCost": 0, "id": "1741054354001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176076002": {"choices": "", "codeStr": "it_7_2_3", "shopGemCost": 0, "id": "1749176076002", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 4, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1744683906001": {"choices": "", "codeStr": "eq_2_3", "shopGemCost": 0, "id": "1744683906001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176344001": {"choices": "", "codeStr": "it_5_2_2", "shopGemCost": 0, "id": "1749176344001", "costEnergy": 1, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1740969017001": {"choices": "", "codeStr": "pd_5_6", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 2, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 1178, "shopGemCost": 0, "spreadAddItem": 2, "spreadCodeWeightPairs": "it_5_1_1-1;it_5_2_1-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 30, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1740969017001", "spreadTierUpLevel": 0}, "1749127843001": {"choices": "", "codeStr": "it_3_1_7", "shopGemCost": 0, "id": "1749127843001", "costEnergy": 38, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176120001": {"choices": "", "codeStr": "it_1_1_4", "shopGemCost": 0, "id": "1749176120001", "costEnergy": 4, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749037127001": {"choices": "", "codeStr": "it_5_1_1", "shopGemCost": 0, "id": "1749037127001", "costEnergy": 1, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176288001": {"choices": "", "codeStr": "it_2_3_3", "shopGemCost": 0, "id": "1749176288001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 4, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1748593961002": {"choices": "", "codeStr": "it_1_1_6", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 2, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 0, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_1_1_1_1-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 1, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 15, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1748593961002", "spreadTierUpLevel": 0}, "1747967704002": {"choices": "", "codeStr": "it_4_1_7", "shopGemCost": 0, "id": "1747967704002", "costEnergy": 45, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176221001": {"choices": "", "codeStr": "it_7_1_2", "shopGemCost": 0, "id": "1749176221001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 2, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1741837046001": {"id": "1741837046001", "codeStr": "eq_3_5", "bubbleGemCost": 0, "cookRecipe": "ds_juice_6", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_4_1_7-1748056448001", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 3, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1748056448001": {"choices": "", "codeStr": "it_4_1_7", "shopGemCost": 0, "id": "1748056448001", "costEnergy": 44, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1748574866001": {"choices": "", "codeStr": "pd_7_4", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 2, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 48, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_7_1_1-8;it_7_2_1-4", "spreadEnergyFree": 0, "spreadStorageRestNumber": 0, "spreadStartTimer": 1749176044, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1748574866001", "spreadTierUpLevel": 0}, "1749176024001": {"choices": "", "codeStr": "it_7_1_3", "shopGemCost": 0, "id": "1749176024001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 4, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749127842002": {"choices": "", "codeStr": "it_3_1_4", "shopGemCost": 0, "id": "1749127842002", "costEnergy": 7, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176309001": {"choices": "", "codeStr": "additem_1", "shopGemCost": 0, "id": "1749176309001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1748916551001": {"choices": "", "codeStr": "it_1_1_8", "shopGemCost": 0, "id": "1748916551001", "costEnergy": 67, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1745164158001": {"choices": "", "codeStr": "pd_1_8", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 568, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_1_2_1-1;it_1_1_3-1;it_1_1_1-1;it_1_1_2-1;it_1_1_1-1;it_1_2_1-1;it_1_1_1-1;it_1_1_2-1;it_1_1_3-1;it_1_1_1-1;it_1_1_1-1;it_1_2_1-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 48, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1745164158001", "spreadTierUpLevel": 0}, "1749037175001": {"choices": "", "codeStr": "it_7_1_5", "shopGemCost": 0, "id": "1749037175001", "costEnergy": 12, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1742784545001": {"id": "1742784545001", "codeStr": "eq_1_5", "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "costEnergy": 0, "cookSkipPropCost": 0, "materialInfo": "it_4_2_2-1749176124003", "shopGemCost": 0, "cookStartTimer": -1, "cookState": 2, "cookLastUpdateTime": 0, "cookSpeedTime": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "choices": "", "spreadCodeWeightPairs": ""}, "1740969176001": {"choices": "", "codeStr": "pd_4_4", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 153, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_4_1_1-1;it_4_2_1-1;it_4_1_1-1;it_4_1_1-1;it_4_1_1-1;it_4_2_1-1;it_4_1_1-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 13, "spreadStartTimer": 1749176196, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1740969176001", "spreadTierUpLevel": 0}, "1747990562001": {"choices": "", "codeStr": "it_4_1_7", "shopGemCost": 0, "id": "1747990562001", "costEnergy": 47, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176335001": {"choices": "", "codeStr": "it_1_2_1", "shopGemCost": 0, "id": "1749176335001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176248001": {"choices": "", "codeStr": "it_2_1_6", "shopGemCost": 0, "id": "1749176248001", "costEnergy": 10, "bubbleGemCost": 0, "costEnergyCurDay": 7, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1740574630001": {"choices": "", "codeStr": "it_1_1_1_2", "shopGemCost": 0, "id": "1740574630001", "costEnergy": 21.5, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176127001": {"choices": "", "codeStr": "ds_e1cockt_2", "shopGemCost": 0, "id": "1749176127001", "costEnergy": 2, "bubbleGemCost": 0, "costEnergyCurDay": 18, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176204002": {"choices": "", "codeStr": "it_4_1_3", "shopGemCost": 0, "id": "1749176204002", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 4, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1748059869001": {"choices": "", "codeStr": "it_2_2_4", "shopGemCost": 0, "id": "1748059869001", "costEnergy": 8, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749037155002": {"choices": "", "codeStr": "it_7_1_4", "shopGemCost": 0, "id": "1749037155002", "costEnergy": 7, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176370001": {"choices": "", "codeStr": "it_1_1_1", "shopGemCost": 0, "id": "1749176370001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 0, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1745806494001": {"choices": "", "codeStr": "pd_3_4", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 1, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 102, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1;it_3_2_1-1;it_3_1_1-1;it_3_1_1-1", "spreadEnergyFree": 0, "spreadStorageRestNumber": 24, "spreadStartTimer": -1, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1745806494001", "spreadTierUpLevel": 0}, "1746585834001": {"choices": "", "codeStr": "pd_7_5", "spreadTierUpCount": 0, "spreadState": 3, "bubbleGemCost": 0, "cookRecipe": "", "costEnergyCurDay": 0, "choiceDate": "", "spreadWeightType": 2, "cookSkipPropCost": 0, "materialInfo": "", "spreadCount": 351, "shopGemCost": 0, "spreadAddItem": 0, "spreadCodeWeightPairs": "it_7_1_1-4;it_7_1_2-2;it_7_2_1-3", "spreadEnergyFree": 0, "spreadStorageRestNumber": 0, "spreadStartTimer": 1749175979, "spreadInherit": 0, "costEnergy": 0, "cookGemCost": 0, "spreadItemBoxChain": "", "id": "1746585834001", "spreadTierUpLevel": 0}, "1749176199001": {"choices": "", "codeStr": "it_4_1_4", "shopGemCost": 0, "id": "1749176199001", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 7, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}, "1749176124003": {"choices": "", "codeStr": "it_4_2_2", "shopGemCost": 0, "id": "1749176124003", "costEnergy": 0, "bubbleGemCost": 0, "costEnergyCurDay": 2, "cookRecipe": "", "spreadCodeWeightPairs": "", "choiceDate": "", "cookGemCost": 0, "spreadItemBoxChain": "", "cookSkipPropCost": 0, "materialInfo": ""}}, "board": {"1_9": {"itemId": "1730096039001", "id": "1_9"}, "2_9": {"itemId": "1740484341001", "id": "2_9"}, "6_8": {"itemId": "1749127808002", "id": "6_8"}, "5_8": {"itemId": "1745913071002", "id": "5_8"}, "4_8": {"itemId": "1748225104005", "id": "4_8"}, "3_8": {"itemId": "1748574866001", "id": "3_8"}, "7_9": {"itemId": "1749176248001", "id": "7_9"}, "5_1": {"itemId": "1740969017001", "id": "5_1"}, "2_5": {"itemId": "1749176199001", "id": "2_5"}, "3_9": {"itemId": "1746585834001", "id": "3_9"}, "4_9": {"itemId": "1740969176001", "id": "4_9"}, "5_9": {"itemId": "1738834040001", "id": "5_9"}, "6_9": {"itemId": "1734438341001", "id": "6_9"}, "1_7": {"itemId": "1743991993001", "id": "1_7"}, "2_7": {"itemId": "1749037115002", "id": "2_7"}, "7_3": {"itemId": "1747967704002", "id": "7_3"}, "6_1": {"itemId": "1738287944001", "id": "6_1"}, "7_5": {"itemId": "1749176309001", "id": "7_5"}, "5_6": {"itemId": "1749176335001", "id": "5_6"}, "3_5": {"itemId": "1745913127002", "id": "3_5"}, "2_1": {"itemId": "1742351309001", "id": "2_1"}, "2_8": {"itemId": "1749176344001", "id": "2_8"}, "1_8": {"itemId": "1749089879001", "id": "1_8"}, "7_7": {"itemId": "1749176221001", "id": "7_7"}, "2_6": {"itemId": "1749037144001", "id": "2_6"}, "5_7": {"itemId": "1749176194001", "id": "5_7"}, "6_7": {"itemId": "1749176252003", "id": "6_7"}, "3_7": {"itemId": "1749037143001", "id": "3_7"}, "4_7": {"itemId": "1749176070002", "id": "4_7"}, "5_4": {"itemId": "1749176303001", "id": "5_4"}, "6_4": {"itemId": "1749127842002", "id": "6_4"}, "3_4": {"itemId": "1749176282002", "id": "3_4"}, "4_4": {"itemId": "1749176204002", "id": "4_4"}, "1_4": {"itemId": "1747715534001", "id": "1_4"}, "2_4": {"itemId": "1749176331001", "id": "2_4"}, "1_6": {"itemId": "1741837046001", "id": "1_6"}, "3_6": {"itemId": "1749176076002", "id": "3_6"}, "6_3": {"itemId": "1749175976002", "id": "6_3"}, "5_3": {"itemId": "1749176214001", "id": "5_3"}, "4_3": {"itemId": "1749037127001", "id": "4_3"}, "3_3": {"itemId": "1746852141001", "id": "3_3"}, "2_3": {"itemId": "1746852265001", "id": "2_3"}, "1_3": {"itemId": "1749175952001", "id": "1_3"}, "7_4": {"itemId": "1749176120001", "id": "7_4"}, "2_2": {"itemId": "1743846671001", "id": "2_2"}, "3_2": {"itemId": "1742784545001", "id": "3_2"}, "4_6": {"itemId": "1749176024001", "id": "4_6"}, "1_2": {"itemId": "1730095941001", "id": "1_2"}, "6_6": {"itemId": "1749176369001", "id": "6_6"}, "7_2": {"itemId": "1749176305001", "id": "7_2"}, "6_2": {"itemId": "1741572578001", "id": "6_2"}, "5_2": {"itemId": "1743128763001", "id": "5_2"}, "4_2": {"itemId": "1745806494001", "id": "4_2"}, "4_5": {"itemId": "1749176279002", "id": "4_5"}, "3_1": {"itemId": "1737340454001", "id": "3_1"}, "7_8": {"itemId": "1749176183001", "id": "7_8"}, "1_1": {"itemId": "1742869211001", "id": "1_1"}, "7_6": {"itemId": "1749176370001", "id": "7_6"}, "7_1": {"itemId": "1745164158001", "id": "7_1"}, "4_1": {"itemId": "1744683794001", "id": "4_1"}, "1_5": {"itemId": "1739932709001", "id": "1_5"}}, "ad": {}, "bundles": {"energy": {"name": "energy", "data": "{}"}, "starter": {"name": "starter", "data": "{\"starter\":{\"data\":\"{\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"payID\\\\\\\":\\\\\\\"starter_bundle_1\\\\\\\"@\\\\\\\"discountTag\\\\\\\":\\\\\\\"200%\\\\\\\"@\\\\\\\"price\\\\\\\":4.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"starter_499\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":240}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":4}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"enebox_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}]@\\\\\\\"id\\\\\\\":1@\\\\\\\"specialType\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"maxBuyNum\\\\\\\":1@\\\\\\\"dailyShowNum\\\\\\\":2@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popOrder\\\\\\\":[\\\\\\\"starter\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"task_finished\\\\\\\"@\\\\\\\"popNum\\\\\\\":1}]@\\\\\\\"popCD\\\\\\\":360@\\\\\\\"order\\\\\\\":[\\\\\\\"starter_499\\\\\\\"]@\\\\\\\"bundleCondition\\\\\\\":[{\\\\\\\"taskFinished\\\\\\\":{\\\\\\\"TaskCount\\\\\\\":3@\\\\\\\"ChapterId\\\\\\\":2}}]@\\\\\\\"dailyBuyNum\\\\\\\":1@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"duration\\\\\\\":720@\\\\\\\"groupId\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#starter_499\\\\\\\"@\\\\\\\"bundleTrigger#task_finished\\\\\\\"@\\\\\\\"bundleCondition#starter_499\\\\\\\"]@\\\\\\\"uiCode\\\\\\\":\\\\\\\"starter\\\\\\\"@\\\\\\\"buyCD\\\\\\\":10080}\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1747713924\\\"},\\\"tgDailyShowNum_task_finished\\\":{\\\"value\\\":1},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20228\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20228\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1747727905\\\"}}\"}}"}, "multiTier": {"name": "multiTier", "data": "{\"energytier1\":{\"data\":\"{\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"tgDailyShowNum_lack_energy\\\":{\\\"value\\\":1},\\\"lastGear\\\":{\\\"value\\\":\\\"0\\\"},\\\"curGearBundleId\\\":{\\\"value\\\":\\\"et3.99\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_199\\\\\\\"@\\\\\\\"discountTag\\\\\\\":\\\\\\\"125%\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"originPrice\\\\\\\":2.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et1.99\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":250}]}@{\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_399\\\\\\\"@\\\\\\\"discountTag\\\\\\\":\\\\\\\"130%\\\\\\\"@\\\\\\\"price\\\\\\\":3.99@\\\\\\\"originPrice\\\\\\\":5.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et3.99\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":520}]}@{\\\\\\\"payID\\\\\\\":\\\\\\\"energytier_599\\\\\\\"@\\\\\\\"discountTag\\\\\\\":\\\\\\\"140%\\\\\\\"@\\\\\\\"price\\\\\\\":5.99@\\\\\\\"originPrice\\\\\\\":8.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"et5.99\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":820}]}]@\\\\\\\"id\\\\\\\":20@\\\\\\\"specialType\\\\\\\":\\\\\\\"multiTier\\\\\\\"@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popOrder\\\\\\\":[\\\\\\\"energy\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"lack_energy\\\\\\\"@\\\\\\\"popNum\\\\\\\":1}]@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"uiCode\\\\\\\":\\\\\\\"multiTier\\\\\\\"@\\\\\\\"groupId\\\\\\\":\\\\\\\"energytier1\\\\\\\"@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#et1.99\\\\\\\"@\\\\\\\"bundleContent#et3.99\\\\\\\"@\\\\\\\"bundleContent#et5.99\\\\\\\"@\\\\\\\"bundleTrigger#lack_energy\\\\\\\"@\\\\\\\"generalBundleConf#3.99\\\\\\\"]@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"sTime\\\\\\\":1742522400@\\\\\\\"order\\\\\\\":[\\\\\\\"et1.99\\\\\\\"@\\\\\\\"et3.99\\\\\\\"@\\\\\\\"et5.99\\\\\\\"]@\\\\\\\"dailyBuyNum\\\\\\\":5@\\\\\\\"generalBundleConf\\\\\\\":[{\\\\\\\"param_string\\\\\\\":\\\\\\\"et3.99\\\\\\\"@\\\\\\\"confType\\\\\\\":\\\\\\\"defaultOrder\\\\\\\"}]@\\\\\\\"dailyShowNum\\\\\\\":10@\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"duration\\\\\\\":30@\\\\\\\"popCD\\\\\\\":5@\\\\\\\"buyCD\\\\\\\":30}\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1749090191\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20244\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20244\\\"},\\\"missedNum\\\":{\\\"value\\\":\\\"0\\\"},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749090191\\\"}}\"}}"}, "chain": {"name": "chain", "data": "{\"chain5\":{\"data\":\"{\\\"tgDailyShowNum_login\\\":{\\\"value\\\":2},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749175841\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20245\\\"},\\\"curIndex\\\":{\\\"value\\\":\\\"4\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"uiCode\\\\\\\":\\\\\\\"chain\\\\\\\"@\\\\\\\"bundleContentChain\\\\\\\":[{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_1\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":1@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":5}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_2\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":2@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":10}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_3\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":3@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"step\\\\\\\":4@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_4\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_199\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":30}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":150}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_5\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":5@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":15}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_6\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":6@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_2\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"step\\\\\\\":7@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_7\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_299\\\\\\\"@\\\\\\\"price\\\\\\\":2.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_8\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":8@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_9\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":9@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":30}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_10\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":10@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":10}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_11\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":11@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"step\\\\\\\":12@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_12\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_399\\\\\\\"@\\\\\\\"price\\\\\\\":3.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":60}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":250}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_13\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":13@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_14\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":14@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_15\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":15@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":50}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_16\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":16@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"ene_4\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"step\\\\\\\":17@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_17\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_499\\\\\\\"@\\\\\\\"price\\\\\\\":4.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":80}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":300}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_18\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":18@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":60}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_19\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":19@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":15}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_20\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":20@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":70}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_21\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":21@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"step\\\\\\\":22@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_22\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_699\\\\\\\"@\\\\\\\"price\\\\\\\":6.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":450}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_23\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":23@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_24\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":24@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_25\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":25@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":150}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_26\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":26@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":20}]}@{\\\\\\\"step\\\\\\\":27@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_27\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_1599\\\\\\\"@\\\\\\\"price\\\\\\\":15.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":240}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":1000}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_28\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":28@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_29\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":29@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_30\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":30@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":1}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_31\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":31@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":400}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_32\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":32@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":50}]}@{\\\\\\\"step\\\\\\\":33@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_33\\\\\\\"@\\\\\\\"payID\\\\\\\":\\\\\\\"chaingift_1999\\\\\\\"@\\\\\\\"price\\\\\\\":19.99@\\\\\\\"skin\\\\\\\":\\\\\\\"3\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":320}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":1200}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_34\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":34@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":200}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_35\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":35@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"greenbox2_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_36\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":36@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"skipprop\\\\\\\"@\\\\\\\"Amount\\\\\\\":400}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"1\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_37\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":37@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_3\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]}@{\\\\\\\"skin\\\\\\\":\\\\\\\"2\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"chain_skip_38\\\\\\\"@\\\\\\\"price\\\\\\\":0@\\\\\\\"step\\\\\\\":38@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}]}]@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"id\\\\\\\":75@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"dailyShowNum\\\\\\\":2@\\\\\\\"sTime\\\\\\\":1749175200@\\\\\\\"groupId\\\\\\\":\\\\\\\"chain5\\\\\\\"@\\\\\\\"rTime\\\\\\\":1749520800@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContentChain#chain_skip\\\\\\\"@\\\\\\\"bundleTrigger#login\\\\\\\"]@\\\\\\\"duration\\\\\\\":4320@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"login\\\\\\\"}]@\\\\\\\"eTime\\\\\\\":1749434400@\\\\\\\"specialType\\\\\\\":\\\\\\\"chain\\\\\\\"}\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1749175797\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"2\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20245\\\"}}\"}}"}, "orderGroup": {"name": "orderGroup", "data": "{\"order1\":{\"data\":\"{\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"id\\\\\\\":14@\\\\\\\"specialType\\\\\\\":\\\\\\\"orderGroup\\\\\\\"@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"popOrder\\\\\\\":[\\\\\\\"orderGroup\\\\\\\"]@\\\\\\\"trigger\\\\\\\":\\\\\\\"finish_order_group\\\\\\\"@\\\\\\\"popNum\\\\\\\":1}]@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"uiCode\\\\\\\":\\\\\\\"orderGroup\\\\\\\"@\\\\\\\"groupId\\\\\\\":\\\\\\\"order1\\\\\\\"@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#order_199\\\\\\\"@\\\\\\\"bundleTrigger#finish_order_group\\\\\\\"]@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"sTime\\\\\\\":1741053600@\\\\\\\"order\\\\\\\":[\\\\\\\"order_199\\\\\\\"]@\\\\\\\"dailyBuyNum\\\\\\\":2@\\\\\\\"dailyShowNum\\\\\\\":5@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"payID\\\\\\\":\\\\\\\"finish_order_199\\\\\\\"@\\\\\\\"discountTag\\\\\\\":\\\\\\\"120%\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"bundleId\\\\\\\":\\\\\\\"order_199\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":100}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"cbox3_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":2}]}]@\\\\\\\"popCD\\\\\\\":60@\\\\\\\"duration\\\\\\\":60@\\\\\\\"buyCD\\\\\\\":30}\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1749128220\\\"},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20244\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20244\\\"},\\\"tgDailyShowNum_finish_order_group\\\":{\\\"value\\\":1},\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749128248\\\"}}\"}}"}, "cd": {"name": "cd", "data": "{\"cd1\":{\"data\":\"{\\\"lastPopTime\\\":{\\\"value\\\":\\\"1749176394\\\"},\\\"lockedConfig\\\":{\\\"value\\\":\\\"{\\\\\\\"id\\\\\\\":8@\\\\\\\"is_open\\\\\\\":1@\\\\\\\"sLv\\\\\\\":8@\\\\\\\"include\\\\\\\":[\\\\\\\"bundleContent#cd_199\\\\\\\"@\\\\\\\"bundleTrigger#pd_cd\\\\\\\"]@\\\\\\\"bundleContent\\\\\\\":[{\\\\\\\"payID\\\\\\\":\\\\\\\"rush_order_199\\\\\\\"@\\\\\\\"bundleId\\\\\\\":\\\\\\\"cd_199\\\\\\\"@\\\\\\\"price\\\\\\\":1.99@\\\\\\\"discountTag\\\\\\\":\\\\\\\"125%\\\\\\\"@\\\\\\\"content\\\\\\\":[{\\\\\\\"Currency\\\\\\\":\\\\\\\"gem\\\\\\\"@\\\\\\\"Amount\\\\\\\":40}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"additem_1\\\\\\\"@\\\\\\\"Amount\\\\\\\":4}@{\\\\\\\"Currency\\\\\\\":\\\\\\\"energy\\\\\\\"@\\\\\\\"Amount\\\\\\\":50}]}]@\\\\\\\"specialType\\\\\\\":\\\\\\\"cd\\\\\\\"@\\\\\\\"uiCode\\\\\\\":\\\\\\\"cd\\\\\\\"@\\\\\\\"dailyBuyNum\\\\\\\":2@\\\\\\\"bundleTrigger\\\\\\\":[{\\\\\\\"trigger\\\\\\\":\\\\\\\"pd_cd\\\\\\\"@\\\\\\\"popOrder\\\\\\\":[\\\\\\\"cd\\\\\\\"]@\\\\\\\"popNum\\\\\\\":1}]@\\\\\\\"buyCD\\\\\\\":30@\\\\\\\"groupId\\\\\\\":\\\\\\\"cd1\\\\\\\"@\\\\\\\"popCD\\\\\\\":60@\\\\\\\"duration\\\\\\\":360@\\\\\\\"sTime\\\\\\\":1733875200@\\\\\\\"order\\\\\\\":[\\\\\\\"cd_199\\\\\\\"]@\\\\\\\"eTime\\\\\\\":1765411200@\\\\\\\"dailyShowNum\\\\\\\":10}\\\"},\\\"tgLastShowDay\\\":{\\\"value\\\":\\\"20245\\\"},\\\"triggerTime\\\":{\\\"value\\\":\\\"1749176072\\\"},\\\"tgDailyShowNum_pd_cd\\\":{\\\"value\\\":1},\\\"lastShowDay\\\":{\\\"value\\\":\\\"20245\\\"},\\\"dailyShowNum\\\":{\\\"value\\\":\\\"1\\\"}}\"}}"}}, "cdn2": {}, "openFunc": {"bubble": {"state": 1, "id": "bubble"}, "discoveries": {"state": 1, "id": "discoveries"}, "shop": {"state": 1, "id": "shop"}, "inventory": {"state": 1, "id": "inventory"}}, "biSync": {"ConsumedEnergy": {"value": "26535", "key": "ConsumedEnergy"}, "GameDuration": {"value": "117998", "key": "GameDuration"}, "ConsumedFreeGem": {"value": "239", "key": "ConsumedFreeGem"}}, "ItemUnlock": {"ds_juice_1": {"state": 3, "type": "ds_juice_1"}, "it_6_1_6": {"state": 3, "type": "it_6_1_6"}, "it_2_2_6": {"state": 3, "type": "it_2_2_6"}, "eb1_1_4": {"state": 2, "type": "eb1_1_4"}, "it_2_3_2": {"state": 3, "type": "it_2_3_2"}, "ds_fd_3": {"state": 2, "type": "ds_fd_3"}, "it_5_2_7": {"state": 3, "type": "it_5_2_7"}, "pd_1_7": {"state": 3, "type": "pd_1_7"}, "it_1_1_1_4": {"state": 3, "type": "it_1_1_1_4"}, "eq_6_3": {"state": 2, "type": "eq_6_3"}, "ds_fd_8": {"state": 2, "type": "ds_fd_8"}, "pd_2_7": {"state": 3, "type": "pd_2_7"}, "it_1_2_1_3": {"state": 3, "type": "it_1_2_1_3"}, "it_1_2_8": {"state": 3, "type": "it_1_2_8"}, "ds_e1cockt_1": {"state": 2, "type": "ds_e1cockt_1"}, "pd_5_5": {"state": 3, "type": "pd_5_5"}, "pd_1_4": {"state": 3, "type": "pd_1_4"}, "it_5_2_6": {"state": 3, "type": "it_5_2_6"}, "it_1_1_5": {"state": 3, "type": "it_1_1_5"}, "ds_grillmt_6": {"state": 3, "type": "ds_grillmt_6"}, "it_3_2_5": {"state": 3, "type": "it_3_2_5"}, "ds_fd_5": {"state": 2, "type": "ds_fd_5"}, "ds_chopfr_1": {"state": 3, "type": "ds_chopfr_1"}, "ds_mixdrk_5": {"state": 3, "type": "ds_mixdrk_5"}, "it_5_2_4": {"state": 3, "type": "it_5_2_4"}, "it_2_2_4": {"state": 3, "type": "it_2_2_4"}, "eq_3_4": {"state": 3, "type": "eq_3_4"}, "it_4_1_6": {"state": 3, "type": "it_4_1_6"}, "it_3_1_4": {"state": 3, "type": "it_3_1_4"}, "pd_3_1": {"state": 3, "type": "pd_3_1"}, "skipprop_4": {"state": 2, "type": "skipprop_4"}, "ds_e4friedmt_6": {"state": 2, "type": "ds_e4friedmt_6"}, "ds_e1icytre_2": {"state": 2, "type": "ds_e1icytre_2"}, "gem_4": {"state": 3, "type": "gem_4"}, "it_1_1_4": {"state": 3, "type": "it_1_1_4"}, "it_3_1_8": {"state": 3, "type": "it_3_1_8"}, "pd_2_1": {"state": 3, "type": "pd_2_1"}, "eq_3_1": {"state": 3, "type": "eq_3_1"}, "eb1_2_3": {"state": 2, "type": "eb1_2_3"}, "ds_chopve_2": {"state": 3, "type": "ds_chopve_2"}, "pd_5_6": {"state": 3, "type": "pd_5_6"}, "it_2_2_5": {"state": 3, "type": "it_2_2_5"}, "ds_grillve_1": {"state": 3, "type": "ds_grillve_1"}, "ds_fd_19": {"state": 2, "type": "ds_fd_19"}, "it_5_2_5": {"state": 3, "type": "it_5_2_5"}, "it_1_2_5": {"state": 3, "type": "it_1_2_5"}, "ds_juice_3": {"state": 3, "type": "ds_juice_3"}, "ds_grillve_2": {"state": 3, "type": "ds_grillve_2"}, "eq_6_4": {"state": 2, "type": "eq_6_4"}, "eb4_1_8": {"state": 2, "type": "eb4_1_8"}, "skipprop_3": {"state": 2, "type": "skipprop_3"}, "gold_1": {"state": 3, "type": "gold_1"}, "ds_chopve_4": {"state": 3, "type": "ds_chopve_4"}, "greenbox_1": {"state": 2, "type": "greenbox_1"}, "skiptime_1": {"state": 2, "type": "skiptime_1"}, "pd_5_3": {"state": 3, "type": "pd_5_3"}, "it_1_2_6": {"state": 3, "type": "it_1_2_6"}, "eb1_2_1": {"state": 2, "type": "eb1_2_1"}, "additem_1_2": {"state": 2, "type": "additem_1_2"}, "ds_fd_15": {"state": 2, "type": "ds_fd_15"}, "it_1_1_2_5": {"state": 3, "type": "it_1_1_2_5"}, "it_7_2_4": {"state": 2, "type": "it_7_2_4"}, "ds_chopve_1": {"state": 3, "type": "ds_chopve_1"}, "ds_mixdrk_8": {"state": 3, "type": "ds_mixdrk_8"}, "ds_grillmt_1": {"state": 3, "type": "ds_grillmt_1"}, "it_2_1_3": {"state": 3, "type": "it_2_1_3"}, "pd_1_5": {"state": 3, "type": "pd_1_5"}, "it_4_1_9": {"state": 3, "type": "it_4_1_9"}, "pd_4_5": {"state": 3, "type": "pd_4_5"}, "ds_friedve_2": {"state": 3, "type": "ds_friedve_2"}, "ds_e1cockt_11": {"state": 2, "type": "ds_e1cockt_11"}, "eq_1_4": {"state": 3, "type": "eq_1_4"}, "ds_friedve_5": {"state": 3, "type": "ds_friedve_5"}, "it_1_1_9": {"state": 3, "type": "it_1_1_9"}, "it_2_3_4": {"state": 3, "type": "it_2_3_4"}, "it_4_1_4": {"state": 3, "type": "it_4_1_4"}, "pd_6_3": {"state": 3, "type": "pd_6_3"}, "pd_6_4": {"state": 3, "type": "pd_6_4"}, "ds_fd_1": {"state": 2, "type": "ds_fd_1"}, "it_5_2_1": {"state": 3, "type": "it_5_2_1"}, "it_4_2_1": {"state": 3, "type": "it_4_2_1"}, "pd_7_4": {"state": 3, "type": "pd_7_4"}, "it_5_1_8": {"state": 3, "type": "it_5_1_8"}, "additem_2": {"state": 2, "type": "additem_2"}, "eq_1_6": {"state": 3, "type": "eq_1_6"}, "ds_friedmt_5": {"state": 3, "type": "ds_friedmt_5"}, "pd_1_2": {"state": 3, "type": "pd_1_2"}, "pd_2_2": {"state": 3, "type": "pd_2_2"}, "pd_3_2": {"state": 3, "type": "pd_3_2"}, "ds_mixdrk_4": {"state": 3, "type": "ds_mixdrk_4"}, "ds_mixdrk_9": {"state": 3, "type": "ds_mixdrk_9"}, "it_3_2_1": {"state": 3, "type": "it_3_2_1"}, "ds_fd_18": {"state": 2, "type": "ds_fd_18"}, "ds_juice_6": {"state": 3, "type": "ds_juice_6"}, "it_5_1_6": {"state": 3, "type": "it_5_1_6"}, "ds_e1cockt_10": {"state": 2, "type": "ds_e1cockt_10"}, "eq_5_3": {"state": 3, "type": "eq_5_3"}, "ds_chopfs_1": {"state": 2, "type": "ds_chopfs_1"}, "it_3_2_4": {"state": 3, "type": "it_3_2_4"}, "ds_fd_20": {"state": 2, "type": "ds_fd_20"}, "it_6_1_1": {"state": 3, "type": "it_6_1_1"}, "it_4_2_5": {"state": 3, "type": "it_4_2_5"}, "ene_3": {"state": 3, "type": "ene_3"}, "it_1_1_6": {"state": 3, "type": "it_1_1_6"}, "ds_grillmt_5": {"state": 3, "type": "ds_grillmt_5"}, "pd_4_1": {"state": 3, "type": "pd_4_1"}, "ds_grillsf_6": {"state": 3, "type": "ds_grillsf_6"}, "ds_e1cockt_9": {"state": 2, "type": "ds_e1cockt_9"}, "it_2_1_2": {"state": 3, "type": "it_2_1_2"}, "eq_6_2": {"state": 2, "type": "eq_6_2"}, "it_5_1_4": {"state": 3, "type": "it_5_1_4"}, "it_7_2_2": {"state": 2, "type": "it_7_2_2"}, "gem_1": {"state": 3, "type": "gem_1"}, "it_1_2_1_2": {"state": 3, "type": "it_1_2_1_2"}, "it_1_2_2": {"state": 3, "type": "it_1_2_2"}, "freebox_1": {"state": 2, "type": "freebox_1"}, "gold_3": {"state": 3, "type": "gold_3"}, "it_1_1_1_2": {"state": 3, "type": "it_1_1_1_2"}, "it_5_1_1": {"state": 3, "type": "it_5_1_1"}, "ds_friedve_1": {"state": 3, "type": "ds_friedve_1"}, "it_1_1_3": {"state": 3, "type": "it_1_1_3"}, "it_3_1_9": {"state": 3, "type": "it_3_1_9"}, "pd_4_4": {"state": 3, "type": "pd_4_4"}, "gold_2": {"state": 3, "type": "gold_2"}, "pd_7_2": {"state": 3, "type": "pd_7_2"}, "skipprop_5": {"state": 2, "type": "skipprop_5"}, "ds_grillsf_3": {"state": 3, "type": "ds_grillsf_3"}, "eq_3_5": {"state": 3, "type": "eq_3_5"}, "pd_4_2": {"state": 3, "type": "pd_4_2"}, "ds_grillsf_1": {"state": 3, "type": "ds_grillsf_1"}, "eb4_2_2": {"state": 1, "type": "eb4_2_2"}, "ds_mixdrk_2": {"state": 3, "type": "ds_mixdrk_2"}, "pd_4_3": {"state": 3, "type": "pd_4_3"}, "ds_chopfru_1": {"state": 3, "type": "ds_chopfru_1"}, "ds_fd_23": {"state": 2, "type": "ds_fd_23"}, "it_3_1_1": {"state": 3, "type": "it_3_1_1"}, "it_5_1_3": {"state": 3, "type": "it_5_1_3"}, "it_1_1_2": {"state": 3, "type": "it_1_1_2"}, "it_5_1_7": {"state": 3, "type": "it_5_1_7"}, "eb1_2_2": {"state": 2, "type": "eb1_2_2"}, "pd_4_6": {"state": 3, "type": "pd_4_6"}, "ds_fd_2": {"state": 3, "type": "ds_fd_2"}, "ds_grillmt_2": {"state": 3, "type": "ds_grillmt_2"}, "eb4_2_1": {"state": 2, "type": "eb4_2_1"}, "ds_e1cockt_14": {"state": 2, "type": "ds_e1cockt_14"}, "gold_5": {"state": 3, "type": "gold_5"}, "it_2_1_6": {"state": 3, "type": "it_2_1_6"}, "ds_fd_17": {"state": 2, "type": "ds_fd_17"}, "it_6_1_5": {"state": 3, "type": "it_6_1_5"}, "additem_1_3": {"state": 2, "type": "additem_1_3"}, "ds_fd_13": {"state": 2, "type": "ds_fd_13"}, "ds_friedmt_4": {"state": 3, "type": "ds_friedmt_4"}, "ds_grillmt_12": {"state": 2, "type": "ds_grillmt_12"}, "it_3_1_5": {"state": 3, "type": "it_3_1_5"}, "it_4_1_8": {"state": 3, "type": "it_4_1_8"}, "it_4_1_5": {"state": 3, "type": "it_4_1_5"}, "ds_e1cockt_2": {"state": 2, "type": "ds_e1cockt_2"}, "it_6_1_3": {"state": 3, "type": "it_6_1_3"}, "it_2_1_5": {"state": 3, "type": "it_2_1_5"}, "pd_3_3": {"state": 3, "type": "pd_3_3"}, "ds_friedve_4": {"state": 3, "type": "ds_friedve_4"}, "it_6_1_7": {"state": 3, "type": "it_6_1_7"}, "it_3_1_2": {"state": 3, "type": "it_3_1_2"}, "it_4_1_3": {"state": 3, "type": "it_4_1_3"}, "eq_4_4": {"state": 3, "type": "eq_4_4"}, "it_2_1_10": {"state": 3, "type": "it_2_1_10"}, "ds_fd_10": {"state": 2, "type": "ds_fd_10"}, "it_3_1_10": {"state": 3, "type": "it_3_1_10"}, "it_7_1_8": {"state": 1, "type": "it_7_1_8"}, "ds_e6stewmt_1": {"state": 2, "type": "ds_e6stewmt_1"}, "ds_e1cockt_12": {"state": 1, "type": "ds_e1cockt_12"}, "it_1_1_7": {"state": 3, "type": "it_1_1_7"}, "it_4_2_6": {"state": 3, "type": "it_4_2_6"}, "it_4_2_4": {"state": 3, "type": "it_4_2_4"}, "ds_sal_1": {"state": 2, "type": "ds_sal_1"}, "ds_grillve_3": {"state": 3, "type": "ds_grillve_3"}, "it_3_2_3": {"state": 3, "type": "it_3_2_3"}, "it_1_2_7": {"state": 3, "type": "it_1_2_7"}, "pd_2_3": {"state": 3, "type": "pd_2_3"}, "ds_chopve_3": {"state": 3, "type": "ds_chopve_3"}, "eq_1_3": {"state": 3, "type": "eq_1_3"}, "pd_1_6": {"state": 3, "type": "pd_1_6"}, "it_2_1_1": {"state": 3, "type": "it_2_1_1"}, "eb4_1_4": {"state": 2, "type": "eb4_1_4"}, "ds_e1cockt_8": {"state": 1, "type": "ds_e1cockt_8"}, "ds_e1cockt_5": {"state": 2, "type": "ds_e1cockt_5"}, "it_1_2_1": {"state": 3, "type": "it_1_2_1"}, "it_6_1_2": {"state": 3, "type": "it_6_1_2"}, "it_4_1_10": {"state": 3, "type": "it_4_1_10"}, "ds_fd_12": {"state": 2, "type": "ds_fd_12"}, "ds_friedsf_1": {"state": 3, "type": "ds_friedsf_1"}, "eq_2_1": {"state": 3, "type": "eq_2_1"}, "ds_grillmt_3": {"state": 3, "type": "ds_grillmt_3"}, "eq_2_2": {"state": 3, "type": "eq_2_2"}, "it_2_1_9": {"state": 3, "type": "it_2_1_9"}, "ds_friedmt_1": {"state": 3, "type": "ds_friedmt_1"}, "gem_1_1": {"state": 2, "type": "gem_1_1"}, "eb4_1_2": {"state": 2, "type": "eb4_1_2"}, "it_5_1_5": {"state": 3, "type": "it_5_1_5"}, "pd_7_3": {"state": 3, "type": "pd_7_3"}, "it_2_2_3": {"state": 3, "type": "it_2_2_3"}, "pd_1_3": {"state": 3, "type": "pd_1_3"}, "it_4_2_3": {"state": 3, "type": "it_4_2_3"}, "eq_2_3": {"state": 3, "type": "eq_2_3"}, "gem_2": {"state": 3, "type": "gem_2"}, "it_5_2_3": {"state": 3, "type": "it_5_2_3"}, "ds_fd_14": {"state": 2, "type": "ds_fd_14"}, "ene_4": {"state": 3, "type": "ene_4"}, "it_3_2_2": {"state": 3, "type": "it_3_2_2"}, "ds_e4sf_12": {"state": 1, "type": "ds_e4sf_12"}, "it_4_2_7": {"state": 3, "type": "it_4_2_7"}, "ds_juice_8": {"state": 3, "type": "ds_juice_8"}, "pd_5_4": {"state": 3, "type": "pd_5_4"}, "eq_1_5": {"state": 3, "type": "eq_1_5"}, "it_7_1_3": {"state": 3, "type": "it_7_1_3"}, "eq_2_5": {"state": 3, "type": "eq_2_5"}, "ds_grillve_4": {"state": 3, "type": "ds_grillve_4"}, "it_2_1_7": {"state": 3, "type": "it_2_1_7"}, "it_7_1_4": {"state": 2, "type": "it_7_1_4"}, "eb1_1_9": {"state": 2, "type": "eb1_1_9"}, "ds_fd_16": {"state": 2, "type": "ds_fd_16"}, "ds_grillmt_7": {"state": 3, "type": "ds_grillmt_7"}, "ds_juice_2": {"state": 3, "type": "ds_juice_2"}, "eb1_1_2": {"state": 2, "type": "eb1_1_2"}, "ds_grillsf_4": {"state": 3, "type": "ds_grillsf_4"}, "eb1_2_4": {"state": 2, "type": "eb1_2_4"}, "it_1_1_2_1": {"state": 3, "type": "it_1_1_2_1"}, "it_7_2_3": {"state": 2, "type": "it_7_2_3"}, "ds_fd_21": {"state": 2, "type": "ds_fd_21"}, "gem_3": {"state": 3, "type": "gem_3"}, "it_1_1_1_1": {"state": 3, "type": "it_1_1_1_1"}, "cbox1_1": {"state": 2, "type": "cbox1_1"}, "eq_2_4": {"state": 3, "type": "eq_2_4"}, "ds_mixdrk_6": {"state": 3, "type": "ds_mixdrk_6"}, "pd_2_5": {"state": 3, "type": "pd_2_5"}, "eb1_1_5": {"state": 2, "type": "eb1_1_5"}, "pd_3_6": {"state": 3, "type": "pd_3_6"}, "it_7_1_5": {"state": 2, "type": "it_7_1_5"}, "eb2_1_2": {"state": 1, "type": "eb2_1_2"}, "it_4_1_7": {"state": 3, "type": "it_4_1_7"}, "ds_juice_7": {"state": 3, "type": "ds_juice_7"}, "pd_2_4": {"state": 3, "type": "pd_2_4"}, "it_2_2_2": {"state": 3, "type": "it_2_2_2"}, "ds_grillmt_10": {"state": 3, "type": "ds_grillmt_10"}, "pd_3_5": {"state": 3, "type": "pd_3_5"}, "ds_grillmt_8": {"state": 3, "type": "ds_grillmt_8"}, "it_1_2_4": {"state": 3, "type": "it_1_2_4"}, "eb1_1_8": {"state": 2, "type": "eb1_1_8"}, "eq_3_3": {"state": 3, "type": "eq_3_3"}, "ds_e1cockt_4": {"state": 2, "type": "ds_e1cockt_4"}, "ds_friedmt_3": {"state": 3, "type": "ds_friedmt_3"}, "it_7_1_7": {"state": 2, "type": "it_7_1_7"}, "it_2_3_1_2": {"state": 3, "type": "it_2_3_1_2"}, "it_3_2_6": {"state": 3, "type": "it_3_2_6"}, "it_2_3_5": {"state": 3, "type": "it_2_3_5"}, "ds_e1cockt_6": {"state": 2, "type": "ds_e1cockt_6"}, "eq_3_2": {"state": 3, "type": "eq_3_2"}, "eb1_2_5": {"state": 2, "type": "eb1_2_5"}, "eb4_1_1": {"state": 2, "type": "eb4_1_1"}, "ds_juice_9": {"state": 3, "type": "ds_juice_9"}, "it_2_3_3": {"state": 3, "type": "it_2_3_3"}, "eq_5_4": {"state": 3, "type": "eq_5_4"}, "ds_grillmt_4": {"state": 3, "type": "ds_grillmt_4"}, "eb4_1_6": {"state": 2, "type": "eb4_1_6"}, "additem_1": {"state": 2, "type": "additem_1"}, "it_2_3_1": {"state": 3, "type": "it_2_3_1"}, "eq_4_1": {"state": 3, "type": "eq_4_1"}, "it_2_1_4": {"state": 3, "type": "it_2_1_4"}, "it_5_1_2": {"state": 3, "type": "it_5_1_2"}, "ds_juice_4": {"state": 3, "type": "ds_juice_4"}, "eb1_1_6": {"state": 2, "type": "eb1_1_6"}, "eb1_1_7": {"state": 2, "type": "eb1_1_7"}, "it_4_1_1": {"state": 3, "type": "it_4_1_1"}, "it_1_2_3": {"state": 3, "type": "it_1_2_3"}, "it_7_2_1": {"state": 2, "type": "it_7_2_1"}, "ds_friedve_3": {"state": 3, "type": "ds_friedve_3"}, "eq_4_6": {"state": 3, "type": "eq_4_6"}, "pd_1_1": {"state": 3, "type": "pd_1_1"}, "ds_grillsf_2": {"state": 3, "type": "ds_grillsf_2"}, "eq_6_1": {"state": 2, "type": "eq_6_1"}, "it_2_2_1": {"state": 3, "type": "it_2_2_1"}, "ds_fd_6": {"state": 2, "type": "ds_fd_6"}, "it_1_1_2_4": {"state": 3, "type": "it_1_1_2_4"}, "it_1_1_1": {"state": 3, "type": "it_1_1_1"}, "it_2_3_1_1": {"state": 3, "type": "it_2_3_1_1"}, "it_6_1_4": {"state": 3, "type": "it_6_1_4"}, "it_7_1_2": {"state": 2, "type": "it_7_1_2"}, "it_7_2_5": {"state": 2, "type": "it_7_2_5"}, "additem_1_1": {"state": 2, "type": "additem_1_1"}, "it_1_1_2_2": {"state": 3, "type": "it_1_1_2_2"}, "eq_5_5": {"state": 2, "type": "eq_5_5"}, "ds_friedmt_2": {"state": 3, "type": "ds_friedmt_2"}, "it_1_2_1_1": {"state": 3, "type": "it_1_2_1_1"}, "ds_mixdrk_7": {"state": 3, "type": "ds_mixdrk_7"}, "it_5_2_2": {"state": 3, "type": "it_5_2_2"}, "ds_grillmt_9": {"state": 3, "type": "ds_grillmt_9"}, "ds_fd_11": {"state": 2, "type": "ds_fd_11"}, "enebox_1": {"state": 1, "type": "enebox_1"}, "ds_friedsf_3": {"state": 3, "type": "ds_friedsf_3"}, "ene_2": {"state": 3, "type": "ene_2"}, "eq_4_3": {"state": 3, "type": "eq_4_3"}, "ds_e3juice_11": {"state": 2, "type": "ds_e3juice_11"}, "it_7_1_1": {"state": 2, "type": "it_7_1_1"}, "ene_5": {"state": 3, "type": "ene_5"}, "it_2_3_6": {"state": 3, "type": "it_2_3_6"}, "it_3_1_6": {"state": 3, "type": "it_3_1_6"}, "skipprop_2": {"state": 2, "type": "skipprop_2"}, "it_4_1_2": {"state": 3, "type": "it_4_1_2"}, "ds_mixdrk_3": {"state": 3, "type": "ds_mixdrk_3"}, "ds_fd_7": {"state": 2, "type": "ds_fd_7"}, "cbox2_1": {"state": 2, "type": "cbox2_1"}, "skipprop_1": {"state": 2, "type": "skipprop_1"}, "it_3_2_7": {"state": 3, "type": "it_3_2_7"}, "ds_fd_4": {"state": 2, "type": "ds_fd_4"}, "it_3_1_3": {"state": 3, "type": "it_3_1_3"}, "eb1_1_10": {"state": 2, "type": "eb1_1_10"}, "eq_4_2": {"state": 3, "type": "eq_4_2"}, "eq_3_6": {"state": 3, "type": "eq_3_6"}, "it_1_1_8": {"state": 3, "type": "it_1_1_8"}, "ds_mixdrk_1": {"state": 3, "type": "ds_mixdrk_1"}, "ds_fd_9": {"state": 2, "type": "ds_fd_9"}, "ene_1": {"state": 3, "type": "ene_1"}, "pd_7_1": {"state": 3, "type": "pd_7_1"}, "pd_7_5": {"state": 2, "type": "pd_7_5"}, "eb4_1_5": {"state": 2, "type": "eb4_1_5"}, "pd_6_2": {"state": 3, "type": "pd_6_2"}, "pd_3_4": {"state": 3, "type": "pd_3_4"}, "it_4_2_2": {"state": 3, "type": "it_4_2_2"}, "ds_e1hotdrk_1": {"state": 2, "type": "ds_e1hotdrk_1"}, "eb1_1_3": {"state": 2, "type": "eb1_1_3"}, "gold_4": {"state": 3, "type": "gold_4"}, "ds_grillsf_7": {"state": 3, "type": "ds_grillsf_7"}, "pd_6_1": {"state": 3, "type": "pd_6_1"}, "it_1_1_2_3": {"state": 3, "type": "it_1_1_2_3"}, "it_2_3_1_4": {"state": 3, "type": "it_2_3_1_4"}, "pd_2_6": {"state": 3, "type": "pd_2_6"}, "ds_e3juice_10": {"state": 2, "type": "ds_e3juice_10"}, "ds_e4sf_13": {"state": 2, "type": "ds_e4sf_13"}, "ds_grillmt_11": {"state": 3, "type": "ds_grillmt_11"}, "it_2_1_8": {"state": 3, "type": "it_2_1_8"}, "it_3_1_7": {"state": 3, "type": "it_3_1_7"}, "pd_1_8": {"state": 3, "type": "pd_1_8"}, "eq_4_5": {"state": 3, "type": "eq_4_5"}, "ds_dst_1": {"state": 3, "type": "ds_dst_1"}, "pd_6_5": {"state": 3, "type": "pd_6_5"}, "eb4_1_7": {"state": 2, "type": "eb4_1_7"}, "eb4_1_3": {"state": 2, "type": "eb4_1_3"}, "it_5_2_8": {"state": 3, "type": "it_5_2_8"}, "eb1_1_1": {"state": 2, "type": "eb1_1_1"}, "it_7_2_6": {"state": 2, "type": "it_7_2_6"}, "ds_friedsf_2": {"state": 3, "type": "ds_friedsf_2"}, "eq_2_6": {"state": 3, "type": "eq_2_6"}, "ds_friedsf_4": {"state": 3, "type": "ds_friedsf_4"}, "it_1_1_1_3": {"state": 3, "type": "it_1_1_1_3"}, "it_7_1_6": {"state": 2, "type": "it_7_1_6"}, "eb2_1_1": {"state": 1, "type": "eb2_1_1"}, "ds_grillsf_5": {"state": 3, "type": "ds_grillsf_5"}, "pd_3_7": {"state": 3, "type": "pd_3_7"}, "it_1_1_10": {"state": 3, "type": "it_1_1_10"}, "it_2_3_1_3": {"state": 3, "type": "it_2_3_1_3"}}, "slot": {}, "inventory": {"1749175987002": {"storeTime": 9000, "codeStr": "ene_1", "itemId": "1749175987002"}, "1741054354001": {"storeTime": 1000, "codeStr": "pd_4_3", "itemId": "1741054354001"}, "1748593961002": {"storeTime": 5000, "codeStr": "it_1_1_6", "itemId": "1748593961002"}, "1749004167001": {"storeTime": 7000, "codeStr": "eq_4_3", "itemId": "1749004167001"}, "1748059869001": {"storeTime": 4000, "codeStr": "it_2_2_4", "itemId": "1748059869001"}, "1749089970002": {"storeTime": 6000, "codeStr": "it_1_1_7", "itemId": "1749089970002"}, "1740574630001": {"storeTime": 3000, "codeStr": "it_1_1_1_2", "itemId": "1740574630001"}, "1749176127001": {"storeTime": 8000, "codeStr": "ds_e1cockt_2", "itemId": "1749176127001"}, "1744683906001": {"storeTime": 2000, "codeStr": "eq_2_3", "itemId": "1744683906001"}, "1749176336001": {"storeTime": 1749176378, "codeStr": "ene_3", "itemId": "1749176336001"}}, "survey": {}, "rate": {"1": {"activeConfigId": 0, "id": "1", "triggeredConfig": "", "canPopup": 0, "rated": 0}}, "energy": {"1": {"id": "1", "generateTime": 1749176703, "energyValue": 4}, "2": {"energyValue": 100, "id": "2"}}, "shopItem": {"1749175796006": {"id": "1749175796006", "itemCode": "it_7_1_5", "costCount": 12, "startCount": 5, "shopType": "FlashSale", "leftCount": 5, "costType": "gem"}, "1749175796004": {"id": "1749175796004", "itemCode": "it_5_2_3", "costCount": 9, "startCount": 5, "shopType": "FlashSale", "leftCount": 5, "costType": "gem"}, "1749175796003": {"id": "1749175796003", "itemCode": "it_6_1_7", "costCount": 60, "startCount": 5, "shopType": "FlashSale", "leftCount": 5, "costType": "gem"}, "1749175796008": {"id": "1749175796008", "itemCode": "it_4_2_2", "costCount": 8, "startCount": 5, "shopType": "FlashSale", "leftCount": 5, "costType": "gem"}, "1749175796005": {"id": "1749175796005", "itemCode": "it_7_1_8", "costCount": 73, "startCount": 5, "shopType": "FlashSale", "leftCount": 5, "costType": "gem"}, "1749175796001": {"id": "1749175796001", "itemCode": "enebox_1", "costCount": 20, "shopType": "DailySpecial", "leftCount": 1, "costType": "gem"}, "1749175796002": {"id": "1749175796002", "itemCode": "freebox_1", "costCount": 0, "shopType": "DailySpecial", "leftCount": 0, "costType": "gem"}, "1749175796007": {"id": "1749175796007", "itemCode": "it_2_1_7", "costCount": 30, "startCount": 5, "shopType": "FlashSale", "leftCount": 5, "costType": "gem"}}, "bundle": {}, "orderMeta": {"CurFinishedGroupCount": {"value": "59", "key": "CurFinishedGroupCount"}, "OrderGroupConsumeEnergy": {"value": "162", "key": "OrderGroupConsumeEnergy"}, "TotalFinishedGroupCount": {"value": "59", "key": "TotalFinishedGroupCount"}, "OrderGroupCostCurDayEnergy": {"value": "0", "key": "OrderGroupCostCurDayEnergy"}, "FirstOrder": {"value": "1", "key": "FirstOrder"}, "OrderGroupCostPastDayEnergy": {"value": "0", "key": "OrderGroupCostPastDayEnergy"}, "SecondOrder": {"value": "1", "key": "SecondOrder"}, "CurOrderGroupId": {"value": "7", "key": "CurOrderGroupId"}, "CurGroupFinishedOrderIds": {"value": "", "key": "CurGroupFinishedOrderIds"}, "CurChapterId": {"value": "5", "key": "CurChapterId"}}, "bundleMeta": {"energy_199PurchaseBundleId": {"value": "{\"groupId\":\"energy1\"@\"rewards\":[{\"Currency\":\"gem\"@\"Crypt\":\"wH\"@\"Amount\":40}@{\"Currency\":\"energy\"@\"Crypt\":\"rMZ\"@\"Amount\":150}@{\"Currency\":\"skipprop\"@\"Crypt\":\"rH\"@\"Amount\":10}]@\"bundleType\":\"energy\"}", "key": "energy_199PurchaseBundleId"}, "starter_bundle_1PurchaseBundleId": {"value": "{\"rewards\":[{\"Amount\":240@\"Crypt\":\"qLZ\"@\"Currency\":\"gem\"}@{\"Amount\":200@\"Crypt\":\"qHZ\"@\"Currency\":\"energy\"}@{\"Amount\":4@\"Crypt\":\"w\"@\"Currency\":\"additem_1\"}@{\"Amount\":1@\"Crypt\":\"r\"@\"Currency\":\"enebox_1\"}]@\"groupId\":\"starter\"@\"bundleType\":\"starter\"@\"bundleId\":\"starter_499\"}", "key": "starter_bundle_1PurchaseBundleId"}, "energyTriggerRefreshTime": {"value": "1733307400", "key": "energyTriggerRefreshTime"}, "rush_order_199PurchaseBundleId": {"value": "{\"groupId\":\"cd1\"@\"rewards\":[{\"Amount\":40@\"Currency\":\"gem\"@\"Crypt\":\"wH\"}@{\"Amount\":4@\"Currency\":\"additem_1\"@\"Crypt\":\"w\"}@{\"Amount\":50@\"Currency\":\"energy\"@\"Crypt\":\"vH\"}]@\"bundleType\":\"cd\"}", "key": "rush_order_199PurchaseBundleId"}}, "cached_requests2": {}, "mainTask": {}, "local": {"DId": {"value": "fa28c8e34b67dc2df6fec0c182067320", "key": "DId"}, "VId": {"value": "fa28c8e34b67dc2df6fec0c182067320", "key": "VId"}, "DataInconsistent": {"value": "false", "key": "DataInconsistent"}, "Authorization": {"value": "Xi0AAAAAAAArK0VoAAAAAA1hNmU3ZWQFLKi7HwUYFe/DDLyFmSIk", "key": "Authorization"}, "LastSyncTime": {"value": "1749176722", "key": "LastSyncTime"}, "Name": {"value": "father", "key": "Name"}, "RegisterVersion": {"value": "1.1.0", "key": "RegisterVersion"}, "UserId": {"value": "11614", "key": "UserId"}, "Icon": {"value": "head7", "key": "Icon"}}, "iapOrder": {}, "noticeState": {}, "returnUser": {"rewardexpiredTime": {"value": "0", "key": "rewardexpiredTime"}, "rewardLeaveDay": {"value": "0", "key": "rewardLeaveDay"}, "returnReward": {"value": "0", "key": "returnReward"}}, "misc": {"DataBalanceDiff": {"value": "eq_2#-8", "key": "DataBalanceDiff"}, "FlambeTimePDChains": {"value": "", "key": "FlambeTimePDChains"}, "ItemTypeDeleteStateit_1_1_2": {"value": 1, "key": "ItemTypeDeleteStateit_1_1_2"}, "FlambeTimeType": {"value": "mode", "key": "FlambeTimeType"}, "EQPieceRemove": {"value": "1", "key": "EQPieceRemove"}, "DataBalanceVersion": {"value": "1.18", "key": "DataBalanceVersion"}, "PDItemSPIndex": {"value": "pd_1_4-7;greenbox_1-1;pd_3_4-1;pd_2_6-1;pd_1_5-4;pd_1_6-1;pd_2_4-4;pd_2_5-2", "key": "PDItemSPIndex"}, "EnergyBoostUserOn": {"value": "1", "key": "EnergyBoostUserOn"}, "EnergyBoostWindowOpenState": {"value": "0", "key": "EnergyBoostWindowOpenState"}, "InventoryBoughtCap": {"value": "6", "key": "InventoryBoughtCap"}, "CheckItemRecycle": {"value": "0", "key": "CheckItemRecycle"}, "FreeRefillEnergy": {"value": "1", "key": "FreeRefillEnergy"}, "FlambeTimeInstruSpeed": {"value": 1, "key": "FlambeTimeInstruSpeed"}, "FlambeTimeLinkOrder": {"value": "", "key": "FlambeTimeLinkOrder"}, "EnergyBoostTriggerEndTime": {"value": "1748932519", "key": "EnergyBoostTriggerEndTime"}, "FlambeTimeFinishTime": {"value": 0, "key": "FlambeTimeFinishTime"}, "FlambeTimeInstruChains": {"value": "", "key": "FlambeTimeInstruChains"}, "IsFlambeTimeOrderGroup": {"value": 1, "key": "IsFlambeTimeOrderGroup"}}, "skin": {}, "orders": {"50460": {"id": "50460", "avatarId": 4, "requirementStr": "ds_chopfs_1;ds_e1icytre_2", "cleanGoldCount": 0, "rewards": "gold-281", "createTime": 1749128240, "chapterId": 5, "type": 1, "groupId": 7}, "50450": {"id": "50450", "avatarId": 3, "requirementStr": "ds_grillmt_7;ds_e1cockt_2", "cleanGoldCount": 0, "rewards": "gold-121;additem_1-1", "createTime": 1749128240, "chapterId": 5, "type": 1, "groupId": 7}, "50470": {"id": "50470", "avatarId": 5, "requirementStr": "ds_fd_18;it_7_2_6", "cleanGoldCount": 0, "rewards": "gold-265", "createTime": 1749128240, "chapterId": 5, "type": 1, "groupId": 7}, "50430": {"id": "50430", "avatarId": 1, "requirementStr": "ds_juice_6", "cleanGoldCount": 0, "rewards": "gold-62", "createTime": 1749128240, "chapterId": 5, "type": 1, "groupId": 7}, "50490": {"id": "50490", "avatarId": 7, "requirementStr": "ds_friedve_1;ds_e1cockt_8", "cleanGoldCount": 0, "rewards": "gold-262", "createTime": 1749128240, "chapterId": 5, "type": 1, "groupId": 7}, "50480": {"id": "50480", "avatarId": 6, "requirementStr": "ds_grillsf_5;it_2_2_6", "cleanGoldCount": 0, "rewards": "gold-233;additem_1-1", "createTime": 1749128240, "chapterId": 5, "type": 1, "groupId": 7}, "50440": {"id": "50440", "avatarId": 2, "requirementStr": "ds_e4sf_12;ds_e1cockt_12", "cleanGoldCount": 0, "rewards": "gold-316", "createTime": 1749128240, "chapterId": 5, "type": 1, "groupId": 7}}, "user": {"gem_Give": {"value": "26", "key": "gem_Give"}, "taskprgs_Give": {"value": "44", "key": "taskprgs_Give"}, "skipprop_Give": {"value": "90", "key": "skipprop_Give"}, "User_Level": {"value": "23", "key": "User_Level"}, "gold_Give": {"value": "4443", "key": "gold_Give"}, "exp_Buy": {"value": "0", "key": "exp_Buy"}, "skipprop_Buy": {"value": "0", "key": "skipprop_Buy"}, "gem_Buy": {"value": "0", "key": "gem_Buy"}, "gold_Buy": {"value": "0", "key": "gold_Buy"}, "exp_Give": {"value": "185", "key": "exp_Give"}}}