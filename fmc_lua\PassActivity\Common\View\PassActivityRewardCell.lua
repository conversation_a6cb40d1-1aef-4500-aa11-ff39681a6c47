PassActivityRewardCell = {}
PassActivityRewardCell.__index = PassActivityRewardCell

function PassActivityRewardCell:Init(activityType, level, isVip, rewards)
  self.m_activityType = activityType
  self.m_activityDefinition = PassActivityDefinition[activityType]
  self.m_level = level
  self.m_isVip = isVip
  self.m_model = GM.ActivityManager:GetModel(activityType)
  self.m_initRewards = rewards
  self:_UpdateRewards()
end

function PassActivityRewardCell:_UpdateRewards()
  self.m_rewards = self.m_initRewards
  for i = 1, #(self.m_arrAttachedRewardGo or {}) do
    GameObject.Destroy(self.m_arrAttachedRewardGo[i])
  end
  self.m_arrAttachedRewardGo = {}
  for i = 1, 3 do
    UIUtil.SetActive(self["m_item" .. i .. "Image"].gameObject, false)
  end
  if Table.IsEmpty(self.m_rewards) then
    return
  end
  if #self.m_rewards == 1 then
    self:_SetRewardImage(1, 1.1, Vector2.zero)
  elseif #self.m_rewards == 2 then
    self:_SetRewardImage(1, 1.05, Vector2(50, 0))
    self:_SetRewardImage(2, 1.05, Vector2(-50, 0))
  elseif #self.m_rewards == 3 then
    self:_SetRewardImage(1, 0.9, Vector2(0, -35))
    self:_SetRewardImage(2, 0.9, Vector2(-50, 25))
    self:_SetRewardImage(3, 0.9, Vector2(50, 25))
  elseif GameConfig.IsTestMode() then
    Log.Error("BP 最多支持3种奖励")
  end
  if #self.m_rewards == 1 and self.m_rewards[1][PROPERTY_COUNT] ~= 1 then
    self.m_numberText.text = self.m_rewards[1][PROPERTY_COUNT]
  else
    self.m_numberText.gameObject:SetActive(false)
  end
end

function PassActivityRewardCell:OnDestroy()
  if self.m_receiveTween ~= nil then
    self.m_receiveTween:Kill()
  end
  if self.m_fadeTween ~= nil then
    self.m_fadeTween:Kill()
  end
  EventDispatcher.RemoveTarget(self)
end

function PassActivityRewardCell:TryPlayAnimation()
  if self.m_receiveGo.activeSelf and not self.m_model:HasShownRewardEffect(self.m_level, self.m_isVip) then
    if self.m_level == 1 and not self.m_isVip then
      self.m_receiveAnimationNode.localScale = Vector3.one
    else
      self.m_effectGo:SetActive(true)
      DOVirtual.DelayedCall(0.2, function()
        self.m_receiveAnimationNode:DOScale(1, 0.5)
      end)
    end
    self.m_model:SetShownRewardEffect(self.m_level, self.m_isVip)
  end
end

function PassActivityRewardCell:UpdateState(tokenLevel)
  local taken = self.m_model:IsRewardTaken(self.m_level, self.m_isVip)
  self.m_maskGo:SetActive(taken)
  self.m_checkGo:SetActive(taken)
  if taken then
    self:_UpdateRewards()
  end
  if self.m_effectImage ~= nil then
    if taken then
      self.m_effectImage.gameObject:SetActive(false)
    elseif self.m_fadeTween == nil then
      self.m_fadeTween = self.m_effectImage:DOFade(1, 1.5):SetLoops(-1, LoopType.Yoyo)
    end
  end
  local locked = self.m_isVip and not self.m_model:HasTicket()
  if self.m_lockGo ~= nil then
    self.m_lockGo:SetActive(locked)
  end
  if not locked and not taken and tokenLevel >= self.m_level then
    self.m_receiveGo:SetActive(true)
    if self.m_receiveTween == nil then
      local scaleTween = DOTween.Sequence()
      scaleTween:Append(self.m_receiveGo.transform:DOScale(1.1, 0.3))
      scaleTween:Append(self.m_receiveGo.transform:DOScale(1, 0.3))
      scaleTween:SetLoops(2)
      self.m_receiveTween = DOTween.Sequence()
      self.m_receiveTween:Append(scaleTween)
      self.m_receiveTween:AppendInterval(0.8)
      self.m_receiveTween:SetLoops(-1)
      if self.m_model:HasShownRewardEffect(self.m_level, self.m_isVip) then
        self.m_receiveAnimationNode.localScale = Vector3.one
      end
    else
      self.m_receiveTween:Restart()
    end
  else
    self.m_receiveGo:SetActive(false)
  end
end

function PassActivityRewardCell:_SetRewardImage(index, scale, anchoredPosition)
  local rewardImage = self["m_item" .. index .. "Image"]
  rewardImage.transform.localScale = Vector3(scale, scale, 1)
  rewardImage.transform.anchoredPosition = anchoredPosition
  rewardImage.gameObject:SetActive(true)
  local reward = self.m_rewards[index]
  local rewardType = reward[PROPERTY_TYPE]
  if not GM.UserProfileModel:IsAvatarReward(rewardType) then
    rewardImage.enabled = true
    local isPropertyType = GM.PropertyDataManager:IsPropertyType(rewardType)
    local spriteName = isPropertyType and EPropertySpriteBig[rewardType] or GM.ItemDataModel:GetSpriteName(rewardType)
    local limitSize = isPropertyType and 100 or 125
    SpriteUtil.SetImage(rewardImage, spriteName, true, function()
      SpriteUtil.LimitSize(rewardImage, limitSize, limitSize)
    end)
  else
    rewardImage.enabled = false
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(UIPrefabConfigName.UserAvatar), rewardImage.transform, V3Zero, function(go)
      if go ~= nil and not go:IsNull() then
        UIUtil.SetLocalScale(go.transform, 0.6, 0.6, 1)
        go:GetLuaTable():Init(rewardType, EAvatarFrame.Highlight)
        table.insert(self.m_arrAttachedRewardGo, go)
      end
    end)
  end
end

function PassActivityRewardCell:OnClicked()
  self.transform.localScale = Vector3(0.95, 0.95, 1)
  self.transform:DOScale(1, 0.4):SetEase(Ease.OutBack)
  local mainWindow = GM.UIManager:GetOpenedViewByName(self.m_activityDefinition.MainWindowPrefabName)
  if not mainWindow then
    return
  end
  mainWindow:TryShowRewardTip(self.m_level, self.m_isVip, self)
end

function PassActivityRewardCell:OnReceiveButtonClicked()
  self.m_model:TakeReward(self.m_level, self.m_isVip, self.m_rewards)
  GM.UIManager:OpenView(UIPrefabConfigName.RewardWindow, self.m_rewards, "battlepass_get_reward", true, nil, nil, false)
  self:UpdateState(self.m_level)
end
