# 引导系统详细说明

## 概述

浮岛物语的引导系统是一个完整的新手教学和功能提示系统，通过强制引导和弱引导相结合的方式，帮助玩家逐步了解游戏机制和功能。

## 引导系统架构

### 核心组件
- **TutorialModel**: 引导模型，管理引导状态和数据
- **TutorialHelper**: 引导辅助工具，提供各种引导效果
- **TutorialLayer**: 引导层，负责UI显示和交互
- **TutorialExecuter**: 引导执行器，具体实现各种引导逻辑

### 引导类型
- **强制引导**: 阻塞式引导，必须完成才能继续
- **弱引导**: 提示式引导，可以跳过或忽略
- **条件引导**: 满足特定条件后触发的引导

## 引导配置详细列表

### 🎬 开场引导

#### 1. CG动画引导 (tutorial_cg)
- **功能**: 播放开场CG动画
- **触发条件**: 游戏首次启动
- **是否强制**: 是
- **执行器**: CG
- **步骤**: 播放开场动画

#### 2. 开场时间线 (timeline)
- **功能**: 开场时间线演示
- **触发条件**: CG动画完成
- **是否强制**: 是
- **执行器**: Beginning
- **步骤**: 展示游戏世界观和基础设定

### 🎯 基础操作引导

#### 3. 点击棋盘入口 (toboard)
- **功能**: 引导玩家进入主棋盘
- **触发条件**: 开场时间线完成
- **是否强制**: 是
- **执行器**: ToBoard
- **步骤**: 
  1. 高亮棋盘入口按钮
  2. 显示点击提示
  3. 等待玩家点击

#### 4. 弱引导手势 (weakGesture)
- **功能**: 弱引导点击图鉴、去棋盘、去场景等
- **触发条件**: 棋盘入口引导完成
- **是否强制**: 否
- **执行器**: WeakGesture
- **步骤**: 显示箭头提示各功能入口

### 🔄 合成系统引导

#### 5. 基础合成引导 (merge1)
- **功能**: 教学基础合成操作
- **触发条件**: 开场时间线完成
- **是否强制**: 是
- **执行器**: Merge1
- **步骤**:
  1. **Merge1**: 拖拽合成第一次 (3,5) → (4,5)
  2. **Merge2**: 拖拽合成第二次 (4,5) → (5,5)
  3. **Merge3**: 拖拽合成第三次 (5,5) → (5,4)
  4. **Click1**: 点击生成器 (5,4)
  5. **Click2**: 再次点击生成器 (5,4)
  6. **Merge4**: 合成新产出的物品 (4,5) → (5,5)

#### 6. 母棋子合成引导 (merge2)
- **功能**: 教学母棋子（生成器）合成
- **触发条件**: 任务1-1完成
- **是否强制**: 是
- **执行器**: Merge2
- **步骤**: 引导合成高级生成器

#### 7. 剪刀合成引导 (merge_scissors)
- **功能**: 教学剪刀工具合成
- **触发条件**: 获得剪刀物品
- **是否强制**: 是
- **执行器**: MergeScissors
- **步骤**: 引导合成和使用剪刀

### 📋 订单系统引导

#### 8-15. 订单完成引导 (order10010-order10140)
- **功能**: 引导完成特定订单
- **触发条件**: 拥有对应订单
- **是否强制**: 是
- **执行器**: Order
- **订单列表**:
  - order10010: 完成订单10010
  - order10020: 完成订单10020
  - order10030: 完成订单10030
  - order10040: 完成订单10040
  - order10050: 完成订单10050
  - order10070: 完成订单10070
  - order10080: 完成订单10080
  - order10140: 完成订单10140

#### 16. 订单物品信息引导 (order_item_info)
- **功能**: 教学查看订单物品详细信息
- **触发条件**: merge2完成
- **是否强制**: 是
- **执行器**: OrderItemInfo
- **步骤**: 引导点击订单物品查看详情

#### 17. 订单组入口引导 (order_group)
- **功能**: 引导进入订单组界面
- **触发条件**: order10040完成
- **是否强制**: 是
- **执行器**: OrderGroup
- **步骤**: 引导点击订单组入口

### 📝 任务系统引导

#### 18-21. 任务完成引导 (task1_1-task1_4)
- **功能**: 引导完成章节1的前4个任务
- **触发条件**: 顺序完成前一个任务
- **是否强制**: 是
- **执行器**: Task
- **步骤**: 
  - task1_1: 完成任务1
  - task1_2: 完成任务2 (需task1_1完成)
  - task1_3: 完成任务3 (需task1_2完成)
  - task1_4: 完成任务4 (需task1_3完成)

### ⚡ 生成器CD引导

#### 22-23. 母体棋子CD引导 (CD_pd_1_7, CD_pd_2_6)
- **功能**: 教学生成器冷却时间机制
- **触发条件**: merge1完成 + 获得对应生成器
- **是否强制**: 是
- **执行器**: PDCD
- **步骤**: 
  - CD_pd_1_7: 引导pd_1_7生成器CD机制
  - CD_pd_2_6: 引导pd_2_6生成器CD机制

#### 24. 点击转化棋子引导 (clickPD)
- **功能**: 教学点击生成器生产物品
- **触发条件**: 拥有订单10150 + 获得it_1_1_6物品
- **是否强制**: 是
- **执行器**: ClickPD
- **步骤**: 引导点击生成器进行物品转化

### 🍳 厨具系统引导

#### 25-27. 厨具使用引导 (cook1-cook3)
- **功能**: 教学厨具的完整使用流程
- **触发条件**: 主线等级4
- **是否强制**: 是
- **执行器**: Cook1, Cook2, Cook3
- **步骤**:
  - cook1: 合成厨具并打开详情页
  - cook2: 使用厨具做菜 (需cook1完成)
  - cook3: 从厨具中取出菜品 (需cook1完成)

### 💾 缓存系统引导

#### 28. 缓存队列引导 (cache)
- **功能**: 教学缓存队列功能
- **触发条件**: 主线等级2
- **是否强制**: 是
- **执行器**: Cache
- **步骤**: 引导使用缓存队列存储物品

### ⚡ 道具系统引导

#### 29. 体力不足引导 (energy)
- **功能**: 教学体力机制和恢复方法
- **触发条件**: 无特殊条件
- **是否强制**: 是
- **执行器**: Energy
- **步骤**: 引导体力不足时的处理方法

#### 30. CD加速道具引导 (cd_speed)
- **功能**: 教学CD加速道具使用
- **触发条件**: 订单组2-3完成 + 主线等级5
- **是否强制**: 是
- **执行器**: CDSpeed
- **步骤**: 引导使用CD加速道具

#### 31. 泡泡引导 (bubble)
- **功能**: 教学泡泡机制
- **触发条件**: 主线等级5
- **是否强制**: 是
- **执行器**: Bubble
- **步骤**: 引导泡泡的产生和收集

### 🎁 库存系统引导

#### 32-33. 库存+8棋子引导 (additem_+8, additem_old_user)
- **功能**: 教学库存扩展功能
- **触发条件**: 订单组2-1完成
- **是否强制**: 是
- **执行器**: AddItem, AddItemOldUser
- **步骤**:
  - additem_+8: 库存+8棋子功能
  - additem_old_user: 老用户库存+8棋子弹窗

#### 34. 母棋子仓库引导 (tutorial_producer_inventory)
- **功能**: 教学母棋子仓库管理
- **触发条件**: 无特殊条件
- **是否强制**: 是
- **执行器**: ProducerInventory
- **步骤**: 引导母棋子仓库的使用

### 🛒 商城系统引导

#### 35. 商城引导 (shop)
- **功能**: 教学商城功能
- **触发条件**: 商城功能解锁
- **是否强制**: 是
- **执行器**: Shop
- **步骤**: 引导商城的使用和购买

## 🎪 活动系统引导

### 通关活动引导

#### 36-37. 通关活动引导 (tutorial_bakeout_1, tutorial_bakeout_2)
- **功能**: 教学通关活动机制
- **触发条件**: 通关活动开始
- **是否强制**: 是
- **执行器**: BakeOut1, BakeOut2
- **步骤**:
  - tutorial_bakeout_1: 通关活动排行榜介绍
  - tutorial_bakeout_2: 通关活动订单及金币兑换

### 竞赛活动引导

#### 38-41. 五人小组竞赛引导 (tutorial_coin_race_*)
- **功能**: 教学五人小组竞赛完整流程
- **触发条件**: 五人小组竞赛开始
- **是否强制**: 是
- **执行器**: CoinRace, CoinRace2, CoinRace3, CoinRace4
- **步骤**:
  - tutorial_coin_race_entry: 竞赛开始引导
  - tutorial_coin_race_first_main_window: 首次打开主界面引导
  - tutorial_coin_race_second_main_window: 第二轮主界面引导
  - tutorial_coin_race_order: 竞赛订单引导

#### 42. 1v1竞赛引导 (tutorial_pk_race_start)
- **功能**: 教学1v1竞赛机制
- **触发条件**: 1v1竞赛开始
- **是否强制**: 是
- **执行器**: PkRaceStart
- **步骤**: 引导1v1竞赛的参与和规则

### 挖宝活动引导

#### 43-44. 挖宝活动引导 (tutorial_digactivity_*)
- **功能**: 教学挖宝活动机制
- **触发条件**: 挖宝活动开始
- **是否强制**: 是
- **执行器**: DigActivityFirstDig, DigActivitySecondDig
- **步骤**:
  - tutorial_digactivity_firstdig: 首次挖宝引导
  - tutorial_digactivity_seconddig: 第二次挖宝引导

### 通行证活动引导

#### 45-46. 通行证活动引导 (tutorial_battlepass_*)
- **功能**: 教学通行证活动机制
- **触发条件**: 通行证活动开始
- **是否强制**: 是
- **执行器**: PassActivity, PassActivityLoop
- **步骤**:
  - tutorial_battlepass: 通行证活动介绍
  - tutorial_battlepass_loop: 通行证常规引导

#### 47. 进度条活动引导 (tutorial_progress_new)
- **功能**: 教学进度条活动机制
- **触发条件**: 进度条活动开始
- **是否强制**: 是
- **执行器**: ProgressActivity
- **步骤**: 引导进度条活动的参与和奖励获取

### 小棋盘活动引导

#### 48-51. 小棋盘活动引导 (tutorial_extraboard_*)
- **功能**: 教学小棋盘活动完整机制
- **触发条件**: 小棋盘活动开始
- **是否强制**: 是
- **执行器**: ExtraBoardDelete, ExtraBoardStart, ExtraBoardCobwebUnlock, ExtraBoardCobwebMerge
- **步骤**:
  - tutorial_extraboard_item_delete: 小棋盘棋子删除引导
  - tutorial_extraboard_start: 小棋盘开始引导
  - tutorial_extraboard_cobweb_unlock: 蛛网解锁引导
  - tutorial_extraboard_cobweb_merge: 蛛网合成引导

### 其他活动引导

#### 52. 开宝箱活动引导 (tutorial_blind_chest)
- **功能**: 教学开宝箱活动机制
- **触发条件**: 开宝箱活动开始
- **是否强制**: 是
- **执行器**: BlindChest
- **步骤**: 引导开宝箱活动的参与

#### 53. 改名引导 (tutorial_change_name)
- **功能**: 教学改名功能
- **触发条件**: 通关活动开始
- **是否强制**: 是
- **执行器**: ChangeName
- **步骤**: 引导玩家修改昵称

#### 54. 能量助力引导 (tutorial_energy_boost)
- **功能**: 教学能量助力功能
- **触发条件**: 无特殊条件
- **是否强制**: 是
- **执行器**: EnergyBoost
- **步骤**: 引导能量助力的开启和使用

## 🎯 引导触发条件类型

### 基础条件类型
- **TutorialFinished**: 前置引导完成
- **FunctionEnabled**: 功能解锁
- **MainLevel**: 主线等级达到
- **TaskFinished**: 任务完成
- **OrderGroupFinished**: 订单组完成
- **ItemAdded**: 获得特定物品
- **HasOrder**: 拥有特定订单
- **CachedItemPushed**: 缓存队列有物品

### 活动条件类型
- **CashDashStart**: 金币冲刺活动开始
- **BakeOutStart**: 通关活动开始
- **CoinRaceStart**: 五人小组竞赛开始
- **PkRaceStart**: 1v1竞赛开始
- **PassStart**: 通行证活动开始
- **DigActivityStart**: 挖宝活动开始
- **ProgressStart**: 进度条活动开始
- **ExtraBoardStart**: 小棋盘活动开始
- **BlindChestStart**: 开宝箱活动开始

## 🎨 引导效果类型

### 视觉效果
- **强制遮罩**: 全屏遮罩，只允许特定区域交互
- **高亮显示**: 突出显示目标UI元素
- **手势动画**: 点击、拖拽等操作演示
- **箭头指示**: 指向目标的箭头提示
- **对话框**: 显示引导文本和说明

### 交互控制
- **强制引导**: 阻塞其他操作，必须完成
- **弱引导**: 提示性引导，可以跳过
- **位置强制**: 强制特定位置的操作
- **事件监听**: 监听特定事件触发下一步

### 引导状态
- **NotStarted**: 未开始 (0)
- **Ongoing**: 进行中 (1)
- **Finished**: 已完成 (2)

## 🔧 技术实现特点

### 模块化设计
- 每个引导都有独立的执行器
- 支持多步骤引导流程
- 可配置的触发条件
- 灵活的状态管理

### 数据持久化
- 引导状态保存到本地数据库
- 支持引导进度恢复
- 测试模式支持引导重置

### 事件驱动
- 基于事件系统触发引导
- 支持复杂的条件组合
- 实时响应游戏状态变化

### 用户体验
- 平滑的动画过渡
- 清晰的视觉反馈
- 合理的引导节奏
- 可跳过的弱引导

这个引导系统为浮岛物语提供了完整的新手教学体验，确保玩家能够逐步掌握游戏的各种机制和功能。
