DigActivityBoardView = {}
DigActivityBoardView.__index = DigActivityBoardView
local DirRotate = {
  Left = 90,
  Right = 270,
  Up = 0,
  Down = 180
}

function DigActivityBoardView:Init(model, window)
  if model == nil then
    return
  end
  self.m_window = window
  self.m_model = model
  self.m_activityDefinition = model:GetActivityDefinition()
  self.m_levelConfig = model:GetCurLevelConfig()
  self.m_gridAnimNum = 0
  self.m_mapFlySequence = {}
  UIUtil.SetSizeDelta(self.m_boardFrameRootRect, self:GetRealBoardFrameSize())
  self:ClearLastBoardView()
  local targetScale = self.m_levelConfig.mapScale or 1
  UIUtil.SetLocalScale(self.m_scaleRootRect, targetScale, targetScale, 1)
  self:SetGridLayout(self.m_tileGridLayout, self.m_activityDefinition.GridSize, self.m_levelConfig.width, self.m_activityDefinition.SpacingOffsetX, self.m_activityDefinition.SpacingOffsetY)
  self:SetGridLayout(self.m_digGridLayout, self.m_activityDefinition.GridSize, self.m_levelConfig.width, self.m_activityDefinition.SpacingOffsetX, self.m_activityDefinition.SpacingOffsetY)
  self:UpdateContent()
  UIUtil.SetLocalPosition(self.transform, nil, (self.m_activityDefinition.BoardBottomMargin or 0) / 2)
  EventDispatcher.AddListener(self.m_activityDefinition.ScoreChangedEvent, self, self.UpdateScore, true)
end

function DigActivityBoardView:OnDestroy()
  if not Table.IsEmpty(self.m_mapFlySequence) then
    for seq, _ in pairs(self.m_mapFlySequence) do
      seq:Kill()
    end
    self.m_mapFlySequence = {}
  end
  if self.m_eventLockedDigItemIndex ~= nil then
    self.m_eventLockedDigItemIndex = nil
    GM.UIManager:SetEventLock(false)
  end
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
end

function DigActivityBoardView:SetGridLayout(layout, gridSize, constraintCount, spacingX, spacingY)
  layout.constraintCount = constraintCount
  layout.cellSize = Vector2(gridSize, gridSize)
  layout.spacing = Vector2(spacingX or 0, spacingY or 0)
end

function DigActivityBoardView:ClearLastBoardView()
  for _, tileGo in ipairs(self.m_arrTiles or {}) do
    UIUtil.SetActive(tileGo, false)
    GameObject.Destroy(tileGo)
  end
  for _, digGrid in ipairs(self.m_arrDigGrids or {}) do
    UIUtil.SetActive(digGrid.gameObject, false)
    GameObject.Destroy(digGrid.gameObject)
  end
  for _, digItem in ipairs(self.m_arrDigItems or {}) do
    UIUtil.SetActive(digItem.gameObject, false)
    GameObject.Destroy(digItem.gameObject)
  end
  self.m_arrTiles = {}
  self.m_arrDigGrids = {}
  self.m_arrDigItems = {}
end

function DigActivityBoardView:UpdateContent()
  self:UpdateTiles()
  self:UpdateDigGrids()
  self:UpdateItems()
  self:UpdateDigGaps()
  self:UpdateScore({UpdateScore = true})
end

function DigActivityBoardView:UpdateContentbyDiggedGrid(digGrid, digItemIndex)
  local x = digGrid.x
  local y = digGrid.y
  local arrPos
  if digItemIndex then
    UIUtil.SetActive(self.m_arrDigItems[digItemIndex].gameObject, not self.m_model:HasAcquiredItem(digItemIndex))
    local arrDigItemData = self.m_model:GetCurDigItemDataList()
    local digItemData = arrDigItemData[digItemIndex]
    arrPos = digItemData:GetOccupancyGrids()
  else
    arrPos = {
      {x = x, y = y}
    }
  end
  local tileGo, imgKey
  if self.m_activityDefinition.TileValidImageName ~= nil then
    for _, pos in ipairs(arrPos) do
      tileGo = self.m_arrTiles[self:XY2Index(pos.x, pos.y)]
      imgKey = self.m_activityDefinition.TileValidImageName
      if not self.m_model:IsValidGrid(pos.x, pos.y, true) then
        imgKey = (pos.x + pos.y) % 2 == 0 and self.m_activityDefinition.TileDarkImageName or self.m_activityDefinition.TileLightImageName
      end
      SpriteUtil.SetImage(tileGo:GetComponent(typeof(Image)), imgKey)
    end
  end
end

function DigActivityBoardView:UpdateScore(msg)
  if msg and msg.UpdateScore then
    self.m_hudDigButton:Init(self.m_activityDefinition.ActivityTokenPropertyType)
  end
end

function DigActivityBoardView:UpdateTiles()
  local tileImg1 = self.m_activityDefinition.TileDarkImageName
  local tileImg2 = self.m_activityDefinition.TileLightImageName
  local tileImg3 = self.m_activityDefinition.TileValidImageName
  local tileGo, imgKey, index
  for y = 1, self.m_levelConfig.height do
    for x = 1, self.m_levelConfig.width do
      index = self:XY2Index(x, y)
      if self.m_arrTiles[index] == nil then
        self.m_arrTiles[index] = GameObject.Instantiate(self.m_tileGo, self.m_tileGo.transform.parent)
      end
      tileGo = self.m_arrTiles[index]
      if self.m_model:IsValidGrid(x, y, true) and tileImg3 ~= nil then
        imgKey = tileImg3
      else
        imgKey = (x + y) % 2 == 0 and tileImg1 or tileImg2
      end
      SpriteUtil.SetImage(tileGo:GetComponent(typeof(Image)), imgKey)
      UIUtil.SetActive(tileGo, true)
    end
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_tileGridLayout.transform)
end

function DigActivityBoardView:UpdateDigGrids()
  local digGridGo, index
  for y = 1, self.m_levelConfig.height do
    for x = self.m_levelConfig.width, 1, -1 do
      index = self:XY2Index(x, y)
      if self.m_arrDigGrids[index] == nil then
        digGridGo = GameObject.Instantiate(self.m_digGridGo, self.m_digGridLayout.transform)
        UIUtil.SetActive(digGridGo, true)
        self.m_arrDigGrids[index] = digGridGo:GetLuaTable()
      end
      self.m_arrDigGrids[index]:Init(self.m_model, self, x, y)
    end
  end
  LayoutRebuilder.ForceRebuildLayoutImmediate(self.m_digGridLayout.transform)
end

function DigActivityBoardView:UpdateDigGaps()
  for _, go in ipairs(self.m_arrDigGaps or {}) do
    local img = go:GetComponent(typeof(Image))
    img:DOFade(0, 0.4):OnComplete(function()
      GameObject.Destroy(go)
    end)
  end
  self.m_arrDigGaps = {}
  local height = self.m_levelConfig.height
  local width = self.m_levelConfig.width
  for y = 1, height do
    for x = 1, width do
      if (x == 1 or x == width) and height >= y + 1 then
        local dir = x == 1 and DirRotate.Right or DirRotate.Left
        self:TryLoadEdgeGap(x, y, x, y + 1, dir)
      end
      if (y == 1 or y == height) and width >= x + 1 then
        local dir = y == 1 and DirRotate.Down or DirRotate.Up
        self:TryLoadEdgeGap(x, y, x + 1, y, dir)
      end
      if 1 <= x - 1 and 1 <= y - 1 then
        self:TryLoadMiddleGap(x, y)
      end
    end
  end
end

function DigActivityBoardView:TryLoadEdgeGap(x1, y1, x2, y2, dirRot)
  local bShow = not self.m_model:IsGridDig(x1, y1) and not self.m_model:IsGridDig(x2, y2)
  if bShow then
    self:LoadEdgeGap(x1, y1, x2, y2, dirRot)
  end
end

function DigActivityBoardView:LoadEdgeGap(x1, y1, x2, y2, dirRot)
  local prefab = dirRot == DirRotate.Down and self.m_gapEdgeGo or self.m_gapEdgeGo2
  local go = GameObject.Instantiate(prefab, prefab.transform.parent)
  go.transform.localRotation = Quaternion.Euler(Vector3(0, 0, dirRot))
  local anchoredPosition = self:GetEdgeGapAnchoredPos(x1, y1, x2, y2, dirRot)
  if dirRot == DirRotate.Up then
    anchoredPosition.y = anchoredPosition.y - 12
  end
  go.transform.anchoredPosition = anchoredPosition
  table.insert(self.m_arrDigGaps, go)
  UIUtil.SetActive(go, true)
end

function DigActivityBoardView:GetEdgeGapAnchoredPos(x1, y1, x2, y2, dirRot)
  local gridSize = self.m_activityDefinition.GridSize
  local pos1 = self:GetGridAnchoredPos(x1, y1)
  local pos2 = self:GetGridAnchoredPos(x2, y2)
  local targetPos = (pos1 + pos2) / 2
  if x1 == x2 then
    local dir = dirRot == DirRotate.Right and -1 or 1
    return Vector2(targetPos.x + gridSize * dir / 2, targetPos.y)
  else
    local dir = dirRot == DirRotate.Down and 1 or -1
    return Vector2(targetPos.x, targetPos.y + gridSize * dir / 2)
  end
end

function DigActivityBoardView:TryLoadMiddleGap(x, y)
  local bLT = not self.m_model:IsGridDig(x - 1, y - 1)
  local bLB = not self.m_model:IsGridDig(x - 1, y)
  local bRT = not self.m_model:IsGridDig(x, y - 1)
  local bRB = not self.m_model:IsGridDig(x, y)
  local bShowMiddle = bLT and bRB or bLB and bRT
  if bShowMiddle then
    self:LoadMiddleGap(x, y)
  elseif bLT and bRT then
    self:LoadEdgeGap(x - 1, y - 1, x, y - 1, DirRotate.Up)
  elseif bLB and bRB then
    self:LoadEdgeGap(x - 1, y, x, y, DirRotate.Down)
  elseif bLT and bLB then
    self:LoadEdgeGap(x - 1, y - 1, x - 1, y, DirRotate.Left)
  elseif bRT and bRB then
    self:LoadEdgeGap(x, y - 1, x, y, DirRotate.Right)
  end
end

function DigActivityBoardView:LoadMiddleGap(x, y)
  local middleGo = GameObject.Instantiate(self.m_gapMiddleGo, self.m_gapMiddleGo.transform.parent)
  local offsetX = self.m_activityDefinition.SpacingOffsetX or 0
  local offsetY = self.m_activityDefinition.SpacingOffsetY or 0
  local offset = Vector2(math.abs(offsetX / 2), math.abs(offsetY / 2))
  local anchoredPosition = self:GetMiddleGapAnchoredPos(x, y) + offset
  anchoredPosition.x = anchoredPosition.x + 1
  anchoredPosition.y = anchoredPosition.y - 6
  middleGo.transform.anchoredPosition = anchoredPosition
  table.insert(self.m_arrDigGaps, middleGo)
  UIUtil.SetActive(middleGo, true)
end

function DigActivityBoardView:GetMiddleGapAnchoredPos(x, y)
  local posRB = self:GetGridAnchoredPos(x, y)
  local posLB = self:GetGridAnchoredPos(x - 1, y)
  local posLT = self:GetGridAnchoredPos(x - 1, y - 1)
  local posRT = self:GetGridAnchoredPos(x, y - 1)
  return (posRB + posLB + posLT + posRT) / 4
end

function DigActivityBoardView:UpdateItems()
  local arrDigItemData = self.m_model:GetCurDigItemDataList()
  local digItemGo
  for i = 1, #arrDigItemData do
    if self.m_arrDigItems[i] == nil then
      digItemGo = GameObject.Instantiate(self.m_digItemGo, self.m_digItemGo.transform.parent)
      self.m_arrDigItems[i] = digItemGo:GetLuaTable()
    end
    self.m_arrDigItems[i]:Init(arrDigItemData[i], self)
    UIUtil.SetActive(self.m_arrDigItems[i].gameObject, not self.m_model:HasAcquiredItem(i))
  end
end

function DigActivityBoardView:GetActivityDefinition()
  return self.m_activityDefinition
end

function DigActivityBoardView:GetGridRectTrans(x, y)
  local index = self:XY2Index(x, y)
  return self.m_arrTiles[index].transform
end

function DigActivityBoardView:GetGridAnchoredPos(x, y)
  return self:GetGridRectTrans(x, y).anchoredPosition
end

function DigActivityBoardView:GetGridWorldPos(x, y)
  return self:GetGridRectTrans(x, y).position
end

function DigActivityBoardView:GetDigItem(index)
  return self.m_arrDigItems[index]
end

function DigActivityBoardView:OnDigGridClicked(digGrid)
  self.m_window:OnDigGridClicked()
  if self.m_model:GetScore() < 1 then
    if not self.m_model:OnLackDigToken() then
      local promptStr = GM.GameTextModel:GetText(self.m_activityDefinition.LackScoreTextKey, self.m_activityDefinition.TokenImageName, self.m_activityDefinition.TokenImageName)
      if self.m_model:GetState() == ActivityState.Ended then
        promptStr = GM.GameTextModel:GetText("treasure_dig_end")
      end
      GM.UIManager:ShowPrompt(promptStr, nil, nil, true)
    end
    return false
  end
  if self.m_tutorialX ~= nil and self.m_tutorialY ~= nil and (digGrid.x ~= self.m_tutorialX or digGrid.y ~= self.m_tutorialY) then
    return false
  end
  local digItemIndex = self.m_model:DigTargetGrid({
    x = digGrid.x,
    y = digGrid.y
  })
  local gridPos = self:GetGridWorldPos(digGrid.x, digGrid.y)
  local offset = self.m_activityDefinition.DigSpineOffset or Vector3(0, 0, 0)
  if digItemIndex ~= nil and self.m_model:HasFinishedCurRound() then
    GM.UIManager:SetEventLock(true)
    self.m_eventLockedDigItemIndex = digItemIndex
  end
  self.m_gridAnimNum = self.m_gridAnimNum + 1
  local animCallback
  
  function animCallback()
    self.m_gridAnimNum = self.m_gridAnimNum - 1
    if digItemIndex ~= nil then
      self:TryDigItemFlyToGroove(digItemIndex)
      self:UpdateContentbyDiggedGrid(digGrid, digItemIndex)
    else
      self:UpdateContentbyDiggedGrid(digGrid)
      if self.m_window ~= nil and self.m_window.OnDigGridAnimFinished ~= nil then
        self.m_window:OnDigGridAnimFinished()
      end
    end
  end
  
  digGrid:PlayDisappearAnim(animCallback)
  self:UpdateDigGaps()
  return true
end

function DigActivityBoardView:TryDigItemFlyToGroove(index)
  local digItemGroove = self.m_window:GetDigGrooveItem(index)
  local digItem = self:GetDigItem(index)
  local targetRect = digItemGroove:GetIconRect()
  local targetRotate = digItemGroove:GetGrooveRotate()
  local targetScale = digItemGroove:GetGrooveScale() / self.m_levelConfig.mapScale
  local rotation = digItem.transform.eulerAngles.z
  if digItemGroove ~= nil and digItem ~= nil then
    local flyRect = GameObject.Instantiate(digItem.gameObject, self.m_scaleRootRect).transform
    flyRect.gameObject:SetActive(true)
    flyRect.localPosition = self.m_scaleRootRect:InverseTransformPoint(digItem.transform.position)
    local flyTime = 0.6
    local appearScale = 1.5
    local flySequence = DOTween.Sequence()
    flySequence:Append(flyRect:DOScale(Vector3(appearScale, appearScale, 1), 0.2):SetEase(Ease.OutSine))
    flySequence:Append(flyRect:DORotate(Vector3(0, 0, rotation + 10), 0.06))
    flySequence:Append(flyRect:DORotate(Vector3(0, 0, rotation - 8), 0.08))
    flySequence:Append(flyRect:DORotate(Vector3(0, 0, rotation + 6), 0.1))
    flySequence:AppendInterval(0.2)
    local backOffset = self:_GetFlyItemBackOffset(flyRect, targetRect, appearScale, 6, 300)
    flySequence:Append(flyRect:DOMove(flyRect.position + backOffset, 0.2):SetEase(Ease.OutCubic))
    flySequence:Append(flyRect:DOJump(targetRect.position, 1, 1, flyTime):SetEase(Ease.InCubic))
    flySequence:Join(flyRect:DOLocalRotate(Vector3(0, 0, targetRotate), flyTime))
    flySequence:Join(flyRect:DOScale(Vector3(targetScale, targetScale, 1), flyTime))
    flySequence:AppendCallback(function()
      self.m_mapFlySequence[flySequence] = nil
      GameObject.Destroy(flyRect.gameObject)
      if self.m_eventLockedDigItemIndex and self.m_eventLockedDigItemIndex == index then
        GM.UIManager:SetEventLock(false)
        self.m_eventLockedDigItemIndex = nil
      end
      if self.m_window ~= nil and self.m_window.OnDigItemFlyAnimFinished ~= nil then
        self.m_window:OnDigItemFlyAnimFinished(index)
      end
    end)
    self.m_mapFlySequence[flySequence] = true
  end
end

function DigActivityBoardView:_GetFlyItemBackOffset(flyRect, targetRect, appearScale, deltaRotate, offsetCoef)
  local corners = CS.System.Array.CreateInstance(typeof(Vector3), 4)
  UIUtil.SetLocalScale(flyRect, appearScale, appearScale)
  local originRotation = flyRect.rotation
  flyRect.localEulerAngles = Vector3(0, 0, flyRect.eulerAngles.z + deltaRotate)
  flyRect:GetWorldCorners(corners)
  UIUtil.SetLocalScale(flyRect, 1, 1)
  flyRect.rotation = originRotation
  local minY = corners[0].y
  local minX = corners[0].x
  local maxX = corners[0].x
  for i = 0, corners.Length - 1 do
    minY = math.min(minY, corners[i].y)
    minX = math.min(minX, corners[i].x)
    maxX = math.max(maxX, corners[i].x)
  end
  local itemLD = Vector3(minX, minY, 0)
  local itemRD = Vector3(maxX, minY, 0)
  local screenLD = GM.UIManager.camera:ViewportToWorldPoint(Vector3(0, 0, 0))
  local screenRD = GM.UIManager.camera:ViewportToWorldPoint(Vector3(1, 0, 0))
  local dir = (flyRect.position - targetRect.position).normalized
  local maxY = math.abs((screenLD.y - itemLD.y) / dir.y)
  local maxX = math.min(math.abs((screenLD.x - itemLD.x) / dir.x), math.abs((screenRD.x - itemRD.x) / dir.x))
  local coef = math.min(offsetCoef, maxX, maxY)
  return dir * coef
end

function DigActivityBoardView:IsPlayingAnimation()
  return self.m_gridAnimNum > 0 or not Table.IsEmpty(self.m_mapFlySequence)
end

function DigActivityBoardView:XY2Index(x, y)
  return (y - 1) * self.m_levelConfig.width + x
end

function DigActivityBoardView:Index2XY(index)
  local y = (index - 1) // self.m_levelConfig.width + 1
  local x = (index - 1) % self.m_levelConfig.width + 1
  return x, y
end

function DigActivityBoardView:GetRealBoardFrameSize()
  local gridSize = self.m_activityDefinition.GridSize
  local offsetX = self.m_activityDefinition.SpacingOffsetX or 0
  local offsetY = self.m_activityDefinition.SpacingOffsetY or 0
  local resultX = self.m_levelConfig.width * (gridSize + offsetX) * self.m_levelConfig.mapScale
  local resultY = self.m_levelConfig.height * (gridSize + offsetY) * self.m_levelConfig.mapScale
  return resultX, resultY
end

function DigActivityBoardView:SetTargetGridForTutorial(bOpen, pos)
  if not bOpen then
    self.m_tutorialX = nil
    self.m_tutorialY = nil
    return
  end
  if pos ~= nil then
    self.m_tutorialX = pos.x
    self.m_tutorialY = pos.y
  end
end

function DigActivityBoardView:GetTwoValidGridPos()
  local digItemData = self.m_model:GetOneItemForTutorial()
  if digItemData ~= nil then
    local arrPos = digItemData:GetOccupancyGrids()
    if not Table.IsEmpty(arrPos) and #arrPos == 2 then
      return arrPos
    else
      Debug.LogError("DigActivity Tutorial First Step Error")
    end
  end
end

function DigActivityBoardView:GetHudButton()
  return self.m_hudDigButton
end

function DigActivityBoardView:HideAllGrids()
  for _, digGrid in ipairs(self.m_arrDigGrids or {}) do
    digGrid:HideForAnim()
  end
  UIUtil.SetActive(self.m_targetContentGo, false)
  UIUtil.SetActive(self.m_gapContentGo, false)
end

local maxDelayEnterTime = 0.2
local minDelayEnterTime = 0.1
local enterTime = 0.5

function DigActivityBoardView:PlayGridEnterAnim()
  local delayTime = self:GetDelayEnterTime()
  for x = 1, self.m_levelConfig.width do
    for y = 1, self.m_levelConfig.height do
      local index = self:XY2Index(x, y)
      DelayExecuteFuncInView(function()
        self.m_arrDigGrids[index]:PlayEnterAnim()
      end, (x - 1) * delayTime, self)
    end
  end
  DelayExecuteFuncInView(function()
    UIUtil.SetActive(self.m_targetContentGo, true)
    UIUtil.SetActive(self.m_gapContentGo, true)
  end, self:GetEnterAnimTime(), self)
end

function DigActivityBoardView:GetEnterAnimTime()
  return (self.m_levelConfig.width - 1) * self:GetDelayEnterTime() + enterTime
end

function DigActivityBoardView:GetDelayEnterTime()
  local maxCloumn = 7
  local minCloumn = 3
  local curCloumn = self.m_levelConfig.width
  if 7 <= curCloumn then
    return minDelayEnterTime
  elseif curCloumn <= 3 then
    return maxDelayEnterTime
  else
    return (maxCloumn - curCloumn) / (maxCloumn - minCloumn) * (maxDelayEnterTime - minDelayEnterTime) + minDelayEnterTime
  end
end

DigGrid = {}
DigGrid.__index = DigGrid

function DigGrid:Init(model, boardView, x, y)
  self.m_model = model
  self.m_activityDefinition = model:GetActivityDefinition()
  self.m_boardView = boardView
  self.m_clicked = nil
  self.x = x
  self.y = y
  if not self.m_bInit then
    local bEven = (x + y) % 2 == 0
    local skinName = bEven and "02" or "01"
    self.m_spineAnim:SetSkin(skinName)
    self.m_spineAnim:Init()
    self.m_spineAnim:PlayAnimation("init", nil)
    self.m_bInit = true
  end
  UIUtil.SetActive(self.m_lockedRootGo, not model:IsGridDig(x, y))
end

function DigGrid:OnBtnClicked()
  if self.m_boardView ~= nil and not self.m_clicked and self.m_boardView:OnDigGridClicked(self) then
    self.m_clicked = true
  end
end

function DigGrid:PlayDisappearAnim(callback)
  self.m_spineAnim:PlayAnimation("appear", function()
    UIUtil.SetActive(self.m_lockedRootGo, false)
    if callback ~= nil then
      callback()
    end
  end)
end

function DigGrid:OnDestroy()
end

function DigGrid:PlayEnterAnim()
  local animTime = enterTime
  self.m_canvasGroup.alpha = 0
  UIUtil.SetLocalPosition(self.m_gridImg.transform, 0, 80)
  UIUtil.SetLocalScale(self.transform, 1.3, 1.3)
  self.m_canvasGroup:DOFade(1, animTime):SetEase(Ease.OutCubic)
  self.transform:DOScale(V3One, animTime):SetEase(Ease.OutCubic)
  self.m_gridImg.transform:DOLocalMove(V3Zero, animTime):SetEase(Ease.OutCubic)
end

function DigGrid:HideForAnim()
  self.m_canvasGroup.alpha = 0
end

function DigGrid:InitForTest()
  UIUtil.SetActive(self.m_lockedRootGo, false)
end

DigItem = {}
DigItem.__index = DigItem

function DigItem:Init(digItemData, boardView)
  if digItemData == nil or boardView == nil then
    Debug.LogError("DigItemData is nil, please check!")
    return
  end
  SpriteUtil.SetImage(self.m_itemImg, digItemData.imgKey, true)
  local gridSize = boardView:GetActivityDefinition().GridSize
  local totalWidth = gridSize * digItemData.width
  local totalHeight = gridSize * digItemData.height
  self.gameObject.transform.localRotation = Quaternion.Euler(Vector3(0, 0, digItemData.rotate or 0))
  local anchoredPosition = boardView:GetGridAnchoredPos(digItemData.pos.x, digItemData.pos.y)
  local targetPosX = anchoredPosition.x + (totalWidth / 2 - gridSize / 2)
  local targetPosY = anchoredPosition.y - (totalHeight / 2 - gridSize / 2)
  UIUtil.SetAnchoredPosition(self.gameObject.transform, targetPosX, targetPosY, 0)
  if digItemData.modifyScale ~= nil then
    UIUtil.SetLocalScale(self.transform, digItemData.modifyScale, digItemData.modifyScale)
  end
end

DigHudButton = setmetatable({}, HudPropertyButton)
DigHudButton.__index = DigHudButton

function DigHudButton:UpdateValueText()
  if self.m_valueText then
    self.m_valueText.text = "x" .. math.floor(self.m_value + 0.5)
  end
end
