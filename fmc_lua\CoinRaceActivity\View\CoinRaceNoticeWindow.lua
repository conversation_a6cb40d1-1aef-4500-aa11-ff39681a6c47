CoinRaceNoticeWindow = setmetatable({}, BaseWindow)
CoinRaceNoticeWindow.__index = CoinRaceNoticeWindow

function CoinRaceNoticeWindow:Init(activityType, autoOpen, isEntry)
  if isEntry then
    PlayerPrefs.SetInt(EPlayerPrefKey.CoinRaceDailyOpenTime, GM.GameModel:GetServerTime())
  end
  self.m_model = GM.ActivityManager:GetModel(activityType)
  if self.m_model == nil then
    return
  end
  self.m_activityDefinition = self.m_model:GetActivityDefinition()
  self.m_model:SetWindowOpened()
  self:UpdatePerSecond()
  if isEntry == nil then
    isEntry = false
  end
  self.m_bClicked = false
  self.m_bEntry = isEntry
  self.m_closeGo:SetActive(not isEntry)
  self.m_noticeGo:SetActive(not isEntry)
  self.m_noticeGo2:SetActive(not isEntry)
  self.m_entryGo:SetActive(isEntry)
  self.m_entryTimeGo:SetActive(isEntry)
  local curRound = self.m_model:GetCurrentRound()
  local bFirstRound = curRound == 0
  local titleKey = self.m_activityDefinition.TitleTextKey
  if not bFirstRound and isEntry then
    titleKey = self.m_activityDefinition.MainTitleTextKey
  end
  self.canCloseByAndroidBack = not isEntry
  local round = self.m_model:GetNextRoundInRoman()
  self.m_titleText.text = GM.GameTextModel:GetText(titleKey, round)
  local descKey = bFirstRound and "coin_race_desc_start" or "coin_race_desc_moreRound"
  if bFirstRound and self.m_model:CanShowRound() then
    descKey = descKey .. "2"
  end
  local roundCnt = self.m_model:GetRoundCount()
  self.m_entryDescText.text = GM.GameTextModel:GetText(descKey, roundCnt)
  self:_UpdateCountdown()
  local ext = self.m_bEntry and "entry" or "notice"
  if autoOpen then
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.AutoPopup
    }, ext)
  else
    self:LogWindowAction(EBIType.UIActionType.Open, {
      EBIReferType.UserClick
    }, ext)
  end
  AddHandlerAndRecordMap(self.m_model.event, RaceEventType.StateChanged, {
    obj = self,
    method = self.Close
  })
end

function CoinRaceNoticeWindow:OnDestroy()
  BaseWindow.OnDestroy(self)
  if self.m_model ~= nil then
    RemoveAllHandlers(self.m_model.event, self)
  end
end

function CoinRaceNoticeWindow:UpdatePerSecond()
  self:_UpdateCountdown()
end

function CoinRaceNoticeWindow:_UpdateCountdown()
  if self.m_model == nil then
    return
  end
  local nextTime = self.m_model:GetNextStateTime()
  if nextTime ~= nil then
    local delta = self.m_model:GetNextStateTime() - GM.GameModel:GetServerTime()
    self.m_countdownText.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
    self.m_countdownText2.text = TimeUtil.ParseTimeDescription(delta, 2, false, false)
  else
    self:Close()
  end
end

function CoinRaceNoticeWindow:OnBtnClicked()
  self:_OnEntry()
end

function CoinRaceNoticeWindow:_OnEntry()
  if self.m_bClicked then
    return
  end
  self.m_bClicked = true
  local state = self.m_model:GetState()
  if state ~= ActivityState.Started then
    self:Close()
    return
  end
  local r = math.random(1, 2)
  if r == 1 then
    EventDispatcher.DispatchEvent(EEventType.CoinRaceSignUpClicked)
    self.m_model:TryCompetitionEntry()
    self:Close()
    return
  end
  GM.UIManager:SetEventLock(true)
  self.m_iconTransf.gameObject:SetActive(true)
  self.m_btnTextGo:SetActive(false)
  local rate = math.random(900, 1260)
  local time = rate / 720
  local seq = DOTween.Sequence()
  seq:Append(self.m_iconTransf:DORotate(Vector3(0, 0, -rate), time))
  seq:AppendCallback(function()
    EventDispatcher.DispatchEvent(EEventType.CoinRaceSignUpClicked)
    GM.UIManager:SetEventLock(false)
    self.m_model:TryCompetitionEntry()
    self:Close()
  end)
end

function CoinRaceNoticeWindow:GetButtonTransform()
  return self.m_buttonGo.transform
end

function CoinRaceNoticeWindow:IsEntry()
  return self.m_bEntry
end
