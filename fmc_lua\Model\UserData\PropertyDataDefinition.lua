PROPERTY_TYPE = "Currency"
PROPERTY_COUNT = "Amount"
PROPERTY_CRYPT = "Crypt"
SKIPPROP_EFFECT_TIME = 5
DELETE_TAG = "delete"
EPropertySource = {Buy = 1, Give = 2}
EPropertyKeySuffix = {
  [EPropertySource.Buy] = "_Buy",
  [EPropertySource.Give] = "_Give"
}
EPropertyType = {
  Gold = "gold",
  Gem = "gem",
  Experience = "exp",
  Energy = "energy",
  SkipProp = "skipprop",
  TaskProgress = "taskprgs",
  BakeOutToken = "botok",
  CoinRaceToken = "coinRaceTok",
  PkRaceToken = "pkRace",
  BP1ScoreToken = "bp1st",
  BP1Ticket = "bp_vip_1",
  BP2ScoreToken = "bp2st",
  BP2Ticket = "bp_vip_2",
  BP2PlusTicket = "bp_supervip_2",
  BP3ScoreToken = "bp3st",
  BP3Ticket = "bp_vip_3",
  BP3PlusTicket = "bp_supervip_3",
  BP4ScoreToken = "bp4st",
  BP4Ticket = "bp_vip_4",
  BP4PlusTicket = "bp_supervip_4",
  BP5ScoreToken = "bp5st",
  BP5Ticket = "bp_vip_5",
  BP5PlusTicket = "bp_supervip_5",
  BP6ScoreToken = "bp6st",
  BP6Ticket = "bp_vip_6",
  BP6PlusTicket = "bp_supervip_6",
  BP7ScoreToken = "bp7st",
  BP7Ticket = "bp_vip_7",
  BP7PlusTicket = "bp_supervip_7",
  TreasureDigToken = "treasureDig",
  ProgressActivity1 = "pg1_st",
  BlindChest1 = "blindChest1"
}
EPropertySprite = setmetatable({
  [EPropertyType.Gold] = ImageFileConfigName.icon_coin,
  [EPropertyType.Gem] = ImageFileConfigName.icon_gem,
  [EPropertyType.Experience] = ImageFileConfigName.icon_exp,
  [EPropertyType.Energy] = ImageFileConfigName.icon_energy,
  [EPropertyType.SkipProp] = ImageFileConfigName.icon_skipprop,
  [EPropertyType.TaskProgress] = ImageFileConfigName.icon_task_progress,
  [EPropertyType.BakeOutToken] = ImageFileConfigName.icon_bo_token,
  [EPropertyType.CoinRaceToken] = ImageFileConfigName.coinRace_token_icon
}, {
  __index = function(table, key)
    for _, v in pairs(PassActivityDefinition) do
      if key == v.ActivityTokenPropertyType then
        return v.TokenIconName
      elseif key == v.TicketPropertyType then
        return ImageFileConfigName.bp_game_golden
      elseif key == v.PlusTicketPropertyType then
        return ImageFileConfigName.bp_svip_icon
      end
    end
    if StringUtil.StartWith(key, ProducerInventoryRewardPrefix) then
      return string.sub(key, string.len(ProducerInventoryRewardPrefix) + 1)
    end
    if ExtraBoardActivityModel.IsExtraBoardActivityItem(key) then
      return GM.ItemDataModel:GetSpriteName(key)
    end
    if GameConfig.IsTestMode() then
      Log.Error("EPropertySprite 中缺少定义: " .. tostring(key))
      return nil
    end
  end
})
EPropertySpriteBig = setmetatable({
  [EPropertyType.Gold] = ImageFileConfigName.icon_coin_big,
  [EPropertyType.Gem] = ImageFileConfigName.icon_gem_big,
  [EPropertyType.Experience] = ImageFileConfigName.icon_exp_big,
  [EPropertyType.Energy] = ImageFileConfigName.icon_energy_big,
  [EPropertyType.SkipProp] = ImageFileConfigName.icon_skipprop_big,
  [EPropertyType.TaskProgress] = ImageFileConfigName.icon_task_progress_big,
  [EPropertyType.BakeOutToken] = ImageFileConfigName.icon_bo_token
}, {
  __index = EPropertySprite
})
