"""
配置加载模块
负责从Config目录加载各种配置文件
基于需求背景2进行优化，支持更完整的数据结构解析
"""

import os
import re
import json
from typing import Dict, List, Optional, Any, Tuple
from data_models import (
    ItemData, GeneratorData, InstrumentData, RecipeData, OrderData,
    OrderRequirement, OrderReward, ConfigData, ProductionData
)


class ConfigLoader:
    """配置文件加载器"""
    
    def __init__(self, config_path: str):
        """
        初始化配置加载器
        
        Args:
            config_path: 配置文件根目录路径
        """
        self.config_path = config_path
        self.config_data = ConfigData()
    
    def load_all_configs(self) -> ConfigData:
        """
        加载所有配置文件
        
        Returns:
            ConfigData: 包含所有配置数据的对象
        """
        try:
            # 加载物品和生成器配置
            self._load_item_model_config()
            
            # 加载订单配置（所有章节）
            self._load_order_configs()
            
            # 加载器械配置（如果存在）
            self._load_instrument_configs()
            
            # 加载菜品配方配置（如果存在）
            self._load_recipe_configs()
            
            return self.config_data
            
        except Exception as e:
            print(f"加载配置文件时出错: {e}")
            raise
    
    def _load_item_model_config(self):
        """加载物品模型配置"""
        item_config_path = os.path.join(self.config_path, "ItemModelConfig.lua")
        
        if not os.path.exists(item_config_path):
            print(f"物品配置文件不存在: {item_config_path}")
            return
        
        try:
            with open(item_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析Lua表结构
            self._parse_item_config_content(content)
            
        except Exception as e:
            print(f"加载物品配置失败: {e}")
            raise
    
    def _parse_item_config_content(self, content: str):
        """解析物品配置内容"""
        # 移除注释
        content = re.sub(r'--.*?\n', '\n', content)
        
        # 查找所有配置块
        # 匹配形如 {Type = "xxx", ...} 的块
        pattern = r'\{\s*Type\s*=\s*"([^"]+)"([^}]*)\}'
        matches = re.finditer(pattern, content, re.DOTALL)
        
        for match in matches:
            item_type = match.group(1)
            item_content = match.group(2)
            
            if item_type.startswith('pd_'):
                # 生成器配置
                generator = self._parse_generator_config(item_type, item_content)
                self.config_data.generators[item_type] = generator
            elif item_type.startswith('it_'):
                # 物品配置
                item = self._parse_item_config(item_type, item_content)
                self.config_data.items[item_type] = item
            elif item_type.startswith('ds_'):
                # 菜品配置
                recipe = self._parse_recipe_config(item_type, item_content)
                self.config_data.recipes[item_type] = recipe
    
    def _parse_generator_config(self, generator_type: str, content: str) -> GeneratorData:
        """解析生成器配置（增强版，支持需求背景2的完整字段）"""
        generator = GeneratorData(code=generator_type)

        # 基础属性解析
        capacity_match = re.search(r'Capacity\s*=\s*(\d+)', content)
        if capacity_match:
            generator.capacity = int(capacity_match.group(1))

        frequency_match = re.search(r'Frequency\s*=\s*(\d+)', content)
        if frequency_match:
            generator.frequency = int(frequency_match.group(1))

        cd_match = re.search(r'Cd\s*=\s*(\d+)', content)
        if cd_match:
            generator.cd = int(cd_match.group(1))

        use_energy_match = re.search(r'UseEnergy\s*=\s*(\d+)', content)
        if use_energy_match:
            generator.use_energy = int(use_energy_match.group(1))

        # 新增字段解析（基于需求背景2）
        unlock_price_match = re.search(r'UnlockPrice\s*=\s*(\d+)', content)
        if unlock_price_match:
            generator.unlock_price = int(unlock_price_match.group(1))

        speed_up_price_match = re.search(r'SpeedUpPrice\s*=\s*(\d+)', content)
        if speed_up_price_match:
            generator.speed_up_price = int(speed_up_price_match.group(1))

        initial_number_match = re.search(r'InitialNumber\s*=\s*(\d+)', content)
        if initial_number_match:
            generator.initial_number = int(initial_number_match.group(1))

        merged_type_match = re.search(r'MergedType\s*=\s*"([^"]+)"', content)
        if merged_type_match:
            generator.merged_type = merged_type_match.group(1)

        # 解析TapeItems（传送带物品）
        tape_items_match = re.search(r'TapeItems\s*=\s*\{([^}]*)\}', content, re.DOTALL)
        if tape_items_match:
            tape_items_content = tape_items_match.group(1)
            generator.tape_items = self._parse_generated_items(tape_items_content)

        # 解析GeneratedItems（产出物品）
        generated_items_match = re.search(r'GeneratedItems\s*=\s*\{([^}]*)\}', content, re.DOTALL)
        if generated_items_match:
            items_content = generated_items_match.group(1)
            generator.generated_items = self._parse_generated_items(items_content)

            # 转换为ProductionData格式（需求背景2）
            generator.production = []
            for item in generator.generated_items:
                production_data = ProductionData(
                    item_code=item.get('Code', ''),
                    weight=item.get('Weight', 1),
                    total_clicks=20  # 默认值
                )
                generator.production.append(production_data)

        # 解析BookReward（图鉴奖励）
        book_reward_match = re.search(r'BookReward\s*=\s*\{([^}]*)\}', content, re.DOTALL)
        if book_reward_match:
            book_reward_content = book_reward_match.group(1)
            generator.book_reward = self._parse_book_reward(book_reward_content)

        # 从代码中提取等级和系列信息
        parts = generator_type.split('_')
        if len(parts) >= 3:
            try:
                generator.series = int(parts[1])
                generator.level = int(parts[2])
            except ValueError:
                pass

        # 计算效率
        if generator.frequency > 0 and generator.cd > 0:
            generator.efficiency = generator.frequency / (generator.cd / 3600)  # 每小时产出

        return generator
    
    def _parse_item_config(self, item_type: str, content: str) -> ItemData:
        """解析物品配置（增强版，支持需求背景2的完整字段）"""
        item = ItemData(code=item_type)

        # 基础字段解析
        category_match = re.search(r'Category\s*=\s*\{([^}]*)\}', content)
        if category_match:
            categories = re.findall(r'(\d+)', category_match.group(1))
            item.category = [int(cat) for cat in categories]

        score_match = re.search(r'Score\s*=\s*([\d.]+)', content)
        if score_match:
            item.score = float(score_match.group(1))

        generators_match = re.search(r'Generators\s*=\s*\{([^}]*)\}', content, re.DOTALL)
        if generators_match:
            generators_content = generators_match.group(1)
            generators = re.findall(r'"([^"]+)"', generators_content)
            item.generators = generators

        merged_type_match = re.search(r'MergedType\s*=\s*"([^"]+)"', content)
        if merged_type_match:
            item.merged_type = merged_type_match.group(1)

        # 新增字段解析（基于需求背景2）
        use_energy_match = re.search(r'UseEnergy\s*=\s*(\d+)', content)
        if use_energy_match:
            item.use_energy = int(use_energy_match.group(1))

        cd_match = re.search(r'Cd\s*=\s*(\d+)', content)
        if cd_match:
            item.cd = int(cd_match.group(1))

        frequency_match = re.search(r'Frequency\s*=\s*(\d+)', content)
        if frequency_match:
            item.frequency = int(frequency_match.group(1))

        capacity_match = re.search(r'Capacity\s*=\s*(\d+)', content)
        if capacity_match:
            item.capacity = int(capacity_match.group(1))

        drops_total_match = re.search(r'DropsTotal\s*=\s*(\d+)', content)
        if drops_total_match:
            item.drops_total = int(drops_total_match.group(1))

        drop_on_spot_match = re.search(r'DropOnSpot\s*=\s*(\d+)', content)
        if drop_on_spot_match:
            item.drop_on_spot = int(drop_on_spot_match.group(1))

        level_down_match = re.search(r'LevelDown\s*=\s*(\d+)', content)
        if level_down_match:
            item.level_down = int(level_down_match.group(1))

        reward_match = re.search(r'Reward\s*=\s*(\d+)', content)
        if reward_match:
            item.reward = int(reward_match.group(1))

        speed_up_price_match = re.search(r'SpeedUpPrice\s*=\s*(\d+)', content)
        if speed_up_price_match:
            item.speed_up_price = int(speed_up_price_match.group(1))

        initial_number_match = re.search(r'InitialNumber\s*=\s*(\d+)', content)
        if initial_number_match:
            item.initial_number = int(initial_number_match.group(1))

        # 从代码中提取等级和系列信息
        parts = item_type.split('_')
        if len(parts) >= 4:
            try:
                item.series = int(parts[1])
                item.level = int(parts[3])
            except ValueError:
                pass

        # 计算折合1级物品数量
        item.base_equivalent = 2 ** (item.level - 1) if item.level > 0 else 1.0

        return item
    
    def _parse_recipe_config(self, recipe_type: str, content: str) -> RecipeData:
        """解析菜品配方配置"""
        recipe = RecipeData(code=recipe_type)
        
        # 这里可以根据实际的菜品配置格式进行解析
        # 目前简化处理
        recipe.name = recipe_type
        
        return recipe
    
    def _parse_generated_items(self, content: str) -> List[Dict[str, Any]]:
        """解析产出物品列表"""
        items = []
        
        # 匹配形如 {Code = "xxx", Weight = n} 的项
        pattern = r'\{\s*Code\s*=\s*"([^"]+)"\s*,\s*Weight\s*=\s*(\d+)\s*\}'
        matches = re.finditer(pattern, content)
        
        for match in matches:
            items.append({
                'Code': match.group(1),
                'Weight': int(match.group(2))
            })
        
        return items

    def _parse_book_reward(self, content: str) -> List[Dict[str, Any]]:
        """解析图鉴奖励"""
        rewards = []

        # 匹配形如 {Currency = "xxx", Amount = n} 的项
        pattern = r'\{\s*Currency\s*=\s*"([^"]+)"\s*,\s*Amount\s*=\s*(\d+)\s*\}'
        matches = re.finditer(pattern, content)

        for match in matches:
            rewards.append({
                'Currency': match.group(1),
                'Amount': int(match.group(2))
            })

        return rewards

    def _load_order_configs(self):
        """加载所有章节的订单配置"""
        for chapter in range(1, 17):  # 假设有16个章节
            self._load_single_chapter_orders(chapter)
    
    def _load_single_chapter_orders(self, chapter: int):
        """加载单个章节的订单配置"""
        order_file = os.path.join(self.config_path, f"OrderFixedConfig_{chapter}.lua")
        
        if not os.path.exists(order_file):
            return
        
        try:
            with open(order_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            orders = self._parse_order_config_content(content, chapter)
            self.config_data.orders[chapter] = orders
            
        except Exception as e:
            print(f"加载章节{chapter}订单配置失败: {e}")
    
    def _parse_order_config_content(self, content: str, chapter: int) -> List[OrderData]:
        """解析订单配置内容"""
        orders = []
        
        # 移除注释
        content = re.sub(r'--.*?\n', '\n', content)
        
        # 查找所有订单块
        pattern = r'\{\s*Id\s*=\s*"([^"]+)"([^}]*)\}'
        matches = re.finditer(pattern, content, re.DOTALL)
        
        for match in matches:
            order_id = match.group(1)
            order_content = match.group(2)
            
            order = self._parse_single_order(order_id, order_content, chapter)
            orders.append(order)
        
        return orders
    
    def _parse_single_order(self, order_id: str, content: str, chapter: int) -> OrderData:
        """解析单个订单配置"""
        order = OrderData(order_id=order_id, chapter_id=chapter)
        
        # 提取组ID
        group_match = re.search(r'GroupId\s*=\s*(\d+)', content)
        if group_match:
            order.group_id = int(group_match.group(1))
        
        # 提取前置订单ID
        pre_id_match = re.search(r'PreId\s*=\s*\{([^}]*)\}', content)
        if pre_id_match:
            pre_ids = re.findall(r'"([^"]+)"', pre_id_match.group(1))
            order.pre_ids = pre_ids
        
        # 提取需求
        req_pattern = r'Requirement_\d+\s*=\s*\{\s*Type\s*=\s*"([^"]+)"\s*,\s*Count\s*=\s*(\d+)\s*\}'
        req_matches = re.finditer(req_pattern, content)
        
        for req_match in req_matches:
            requirement = OrderRequirement(
                item_type=req_match.group(1),
                count=int(req_match.group(2))
            )
            order.requirements.append(requirement)
        
        # 提取奖励
        rewards_match = re.search(r'Rewards\s*=\s*\{([^}]*)\}', content, re.DOTALL)
        if rewards_match:
            rewards_content = rewards_match.group(1)
            reward_pattern = r'\{\s*Currency\s*=\s*"([^"]+)"\s*,\s*Amount\s*=\s*(\d+)\s*\}'
            reward_matches = re.finditer(reward_pattern, rewards_content)
            
            for reward_match in reward_matches:
                reward = OrderReward(
                    currency=reward_match.group(1),
                    amount=int(reward_match.group(2))
                )
                order.rewards.append(reward)
        
        return order
    
    def _load_instrument_configs(self):
        """加载器械配置（如果存在）"""
        # 这里可以根据实际的器械配置文件进行实现
        pass
    
    def _load_recipe_configs(self):
        """加载菜品配方配置（如果存在）"""
        # 这里可以根据实际的菜品配方配置文件进行实现
        pass
    
    def get_items_by_category(self, category: int) -> List[ItemData]:
        """根据分类获取物品列表"""
        return [item for item in self.config_data.items.values() 
                if category in item.category]
    
    def get_generators_by_series(self, series: int) -> List[GeneratorData]:
        """根据系列获取生成器列表"""
        return [gen for gen in self.config_data.generators.values() 
                if gen.series == series]
    
    def get_items_by_generator(self, generator_code: str) -> List[ItemData]:
        """获取指定生成器可以产出的物品"""
        return [item for item in self.config_data.items.values() 
                if generator_code in item.generators]
