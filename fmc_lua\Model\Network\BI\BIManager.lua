BIManager = {
  SendingPeriod = 10.0,
  MaxPending = 20,
  LocalFile = "dot_message.dat"
}
BIManager.__index = BIManager
local JsonArrayMetatable = {__jsontype = "array"}
local ArrayCreate = function()
  local tb = setmetatable({}, JsonArrayMetatable)
  return tb
end

function BIManager:Init()
  self.m_strUrl = NetworkConfig.GetOneSDKBIUrl()
  self.m_fTickCount = 0.0
  self.m_fLastSendTime = 0.0
  self.m_method = ""
  self.m_stringDict = SimpleStringDict.Create(BIManager.LocalFile)
  self.m_totalPendings = 0
  if not self.m_stringDict:IsEmpty() then
    local tbContent = self.m_stringDict:GetAllInTable()
    local msgId = 0
    for k, v in pairs(tbContent) do
      msgId = tonumber(k)
      if msgId ~= nil and msgId > self.m_totalPendings then
        self.m_totalPendings = msgId
      end
    end
  end
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.BISync)
  ModelHelper.DefineProperties(BIManager, EBISyncKey)
  self.m_bInited = true
end

function BIManager:GetData()
  return self.m_dbTable
end

function BIManager:FromSyncData(dataArr)
  self.m_dbTable:FromArr(dataArr)
end

function BIManager:Get(key)
  return self.m_dbTable:GetValue(key, DB_VALUE_KEY)
end

function BIManager:Set(key, value)
  self.m_dbTable:Set(key, DB_VALUE_KEY, value)
end

function BIManager:GetInNumber(key)
  return tonumber(self:Get(key)) or 0
end

function BIManager:ChangeNumber(key, delta)
  if delta == 0 then
    return
  end
  local curNum = self:GetInNumber(key)
  local newNum = curNum + delta
  self:Set(key, tostring(newNum))
end

function BIManager:Update(dt)
  self.m_fTickCount = (self.m_fTickCount or 0) + dt
  if self.m_fTickCount - self.m_fLastSendTime >= BIManager.SendingPeriod or self.m_totalPendings >= BIManager.MaxPending then
    self:FlushMessages()
  end
end

function BIManager:SetCompressMethod(strMethod)
  self.m_method = strMethod or ""
end

function BIManager:QueueMessage(mapData)
  self:QueueData(json.encode(mapData))
  local biid = PlayerPrefs.GetInt(EPlayerPrefKey.BIMsgId, 0)
  if biid < 30 then
    self:FlushMessages()
  elseif self.m_totalPendings >= BIManager.MaxPending then
    self:FlushMessages()
  end
end

function BIManager:QueueWithoutFlush(mapData)
  self:QueueData(json.encode(mapData))
end

function BIManager:QueueData(strData)
  self.m_totalPendings = self.m_totalPendings or 0
  self.m_totalPendings = self.m_totalPendings + 1
  self.m_stringDict:Set(self.m_totalPendings, strData)
  self.m_stringDict:Serialize()
end

function BIManager:FlushMessages()
  self.m_fLastSendTime = self.m_fTickCount
  if self.m_totalPendings > 0 then
    local reqCtx = CSNetLibManager:CreateGeneralHttpRequest(self.m_strUrl, "POST", 8000, 0)
    reqCtx:SetHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader())
    reqCtx:SetRetryHeader(NetworkConfig.ClientHeaderKey, NetworkConfig.GetClientHeader(2))
    reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
    local reqBody = self:_JoinMessages(reqCtx.MessageId)
    reqCtx:AppendBody(reqBody)
    if reqCtx.ContentLength > 1024 and not StringUtil.IsNilOrEmpty(self.m_method) then
      reqCtx:CompressBody(self.m_method)
    end
    reqCtx:SetCallback(function()
      if GM == nil then
        return
      end
      if reqCtx.Rcode ~= ResultCode.Succeeded and OperManager.TryFallbackRequest(reqCtx) then
        return
      end
      GM.HttpManager:OnFinishSavedHttpRequest(reqCtx.MessageId, reqCtx.Rcode)
    end)
    GM.HttpManager:SaveHttpRequest(reqCtx)
    reqCtx:Send()
    self.m_stringDict:Clear()
    self.m_totalPendings = 0
  end
end

function BIManager:_JoinMessages(msgId)
  local tbContent = self.m_stringDict:GetAllInTable()
  local body = "["
  local first = true
  for key, data in pairs(tbContent) do
    if first then
      body = body .. data
      first = false
    else
      body = body .. "," .. data
    end
  end
  body = body .. "]"
  local reqBody = "{\"data\":" .. body .. ",\"msg_id\":" .. msgId .. ",\"c_time\":" .. TimeUtil.GetTimeInSecond() .. ",\"token\":\"" .. GameConfig.GetGameToken() .. "\"}"
  return reqBody
end

function BIManager:TableToString(tb)
  local str = ""
  local first = true
  for key, value in pairs(tb) do
    if first then
      first = false
    else
      str = str .. ";"
    end
    str = str .. key .. ":" .. tostring(value)
  end
  return str
end

function BIManager:GetGameDuration()
  GM.SceneManager:StoreGameDuration()
  return self:GetInNumber(EBISyncKey.GameDuration)
end

function BIManager:LogWithInfo(dict, withBagInfo, withDeviceInfo)
  if not self.m_bInited then
    return
  end
  if GameConfig.IsTestMode() and GM.TestModel and GM.TestModel.isSkipping then
    return
  end
  if IsAutoRun() then
    return
  end
  local withBagInfo = withBagInfo == nil and true or withBagInfo
  local withDeviceInfo = withDeviceInfo ~= nil and withDeviceInfo or false
  if withBagInfo then
    local chapterId = GM.TaskManager:GetOngoingChapterId()
    local orderModel = GM.MainBoardModel:GetOrderModel()
    local orderCId, orderGId = orderModel:GetOrderGroupInfo()
    local pass = orderModel:IsCurChapterCanUnlockGroupFinished(false)
    local op
    if pass ~= nil then
      op = pass and 1 or 0
    end
    local bagDict = {
      rm = chapterId,
      t = GM.TaskManager:GetTaskProgress(),
      lv = GM.LevelModel:GetCurrentLevel(),
      gd = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gold),
      cgd = GM.TaskManager:GetCleanGoldCount(),
      gem = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem),
      exp = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Experience),
      ene = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Energy),
      spu = GM.PropertyDataManager:GetPropertyNum(EPropertyType.SkipProp),
      cfgem = self:GetInNumber(EBISyncKey.ConsumedFreeGem),
      cpgem = self:GetInNumber(EBISyncKey.ConsumedPaidGem),
      cene = self:GetInNumber(EBISyncKey.ConsumedEnergy),
      ct = self:GetGameDuration(),
      cht = orderCId,
      og = orderGId,
      op = op
    }
    dict.info = bagDict
  end
  self:_AppendBIInfo(dict, withDeviceInfo)
  self:QueueMessage(dict)
end

function BIManager:LogUseItem(eType, eNum, eScene, eConsumer)
  local dict = {
    event = "useitem",
    map = GM.SceneManager:GetGameMode(),
    type = eType,
    num = eNum,
    scene = eScene,
    consumer = eConsumer
  }
  self:LogWithInfo(dict)
end

function BIManager:LogAcquire(eType, eNum, eScene, eFree, eGameMode)
  local dict = {
    event = "acquire",
    map = eGameMode or GM.SceneManager:GetGameMode(),
    type = eType,
    num = eNum,
    scene = eScene,
    free = eFree
  }
  self:LogWithInfo(dict)
end

function BIManager:LogAction(eScene, eAction, map)
  local dict = {
    event = "action",
    map = map or GM.SceneManager:GetGameMode(),
    scene = eScene,
    action = eAction
  }
  if eAction == "init" then
    self:LogWithInfo(dict, true, true)
  else
    self:LogWithInfo(dict)
  end
end

function BIManager:LogPay(productId, orderId, eScene)
  local dict = {
    event = "pay",
    map = GM.SceneManager:GetGameMode(),
    productid = productId,
    orderid = orderId,
    scene = eScene
  }
  self:LogWithInfo(dict)
end

function BIManager:LogPayAction(stage, eIAPType, orderId, info, interval)
  local dict = {
    event = "payact",
    map = GM.SceneManager:GetGameMode(),
    stage = stage,
    type = eIAPType,
    orderid = orderId,
    pinfo = info,
    gt = interval and interval.gtInMS or nil,
    tt = interval and interval.totalInMS or nil
  }
  self:LogWithInfo(dict, true)
  local log = "payact: " .. table.concat({
    stage or "",
    eIAPType or "",
    orderId or "",
    info or ""
  }, ", ")
  CSFirebaseManager:CrashlyticsLog(log)
end

function BIManager:LogSurvey(action, timeOnPage, data)
  local dict = {
    event = "survey_online",
    action = action,
    timeOnPage = timeOnPage,
    data = data
  }
  self:LogWithInfo(dict)
end

function BIManager:LogUI(eScene, eAction, eRefer, ext)
  local dict = {
    event = "ui",
    map = GM.SceneManager:GetGameMode(),
    scene = eScene,
    action = eAction,
    refer = eRefer,
    ext = ext
  }
  self:LogWithInfo(dict)
end

function BIManager:LogString(strstr, eScene)
  local dict = {
    event = "string",
    str = strstr,
    scene = eScene
  }
  self:LogWithInfo(dict)
end

function BIManager:LogMerge(fromName, toName, ext, map)
  ext = ext or {}
  local dict = {
    event = "merge",
    map = map or GM.SceneManager:GetGameMode(),
    from = fromName,
    to = toName,
    ext = ext
  }
  self:LogWithInfo(dict)
end

function BIManager:LogSplit(fromName, toName, ext)
  local dict = {
    event = "split",
    map = GM.SceneManager:GetGameMode(),
    from = fromName,
    to = toName,
    ext = ext
  }
  self:LogWithInfo(dict)
end

function BIManager:LogSpread(eSource, element, ext, map)
  ext = ext or {}
  local dict = {
    event = "spread",
    map = map or GM.SceneManager:GetGameMode(),
    src = eSource,
    ele = element,
    ext = ext
  }
  self:LogWithInfo(dict)
end

function BIManager:LogReplace(from, to, map)
  local dict = {
    event = "replace",
    map = map or GM.SceneManager:GetGameMode(),
    from = from,
    to = to
  }
  self:LogWithInfo(dict)
end

function BIManager:LogTask(chapterName, chapterId, taskId, cost, rewards, type, sub_type)
  local dict = {
    event = "task",
    char_id = chapterName,
    char_index = tonumber(chapterId),
    id = taskId,
    cost = cost,
    reward = rewards,
    type = type,
    sub_type = sub_type,
    map = GM.SceneManager:GetGameMode()
  }
  self:LogWithInfo(dict)
end

function BIManager:LogPassActivityTask(taskId, task)
  local dict = {
    event = "task",
    map = GM.SceneManager:GetGameMode(),
    id = taskId,
    reward = {
      bptoken = task.Reward
    },
    type = "battlepass",
    type_sub = task.Type
  }
  self:LogWithInfo(dict)
end

function BIManager:LogOrder(orderId, type, content, action, score, isTimelimit, startTime, endTime, reward, cost, ext, readyinfo, other)
  local biRewards = {}
  for _, rawReward in ipairs(reward) do
    biRewards[rawReward[PROPERTY_TYPE]] = rawReward[PROPERTY_COUNT]
  end
  local dict = {
    event = "order",
    map = GM.SceneManager:GetGameMode(),
    id = orderId,
    type = type,
    order = content,
    action = action,
    score = score,
    time_limite = isTimelimit,
    start_time = startTime,
    end_time = endTime,
    reward = self:TableToString(biRewards),
    cost = cost,
    ext = ext,
    ready = readyinfo ~= nil and self:TableToString(readyinfo) or nil,
    other = other
  }
  self:LogWithInfo(dict)
end

function BIManager:LogStore(costType, costNum, getType, getNum, storeType, map)
  local dict = {
    event = "store",
    map = map or GM.SceneManager:GetGameMode(),
    ct = costType,
    cn = costNum,
    gt = getType,
    gn = getNum,
    st = storeType
  }
  self:LogWithInfo(dict)
end

function BIManager:LogTutorialStepFinish(tutorialId, finishStep)
  local dict = {
    event = "tutorial",
    id = tutorialId,
    st = finishStep
  }
  self:LogWithInfo(dict)
end

function BIManager:LogActivity(activityType, id, scene, action)
  local dict = {
    event = "act",
    t = activityType,
    id = tostring(id),
    s = scene,
    a = tostring(action)
  }
  self:LogWithInfo(dict)
end

function BIManager:LogProfiling(gameTimes)
  local result, frames, frameTime, mainThread, maxFrameTime, maxMainThread, hiccupFrames, hiccupMainThread = MicrofunProfiler.Instance:GetFrameTime("Default")
  local dict = {
    total_mem = MicrofunProfiler.Instance:GetMemorySize("Total"),
    mem = MicrofunProfiler.Instance:GetMemorySize("MonoHeap Used"),
    nativemem = MicrofunProfiler.Instance:GetMemorySize("NativeHeap Used"),
    luamem = MicrofunProfiler.Instance:GetMemorySize("Lua"),
    texturemem = MicrofunProfiler.Instance:GetMemorySize("Textures"),
    battery = CS.InGameProfiler.batteryLevel,
    startuptime = CS.InGameProfiler.gameTime,
    ab = MicrofunProfiler.Instance:GetLoadedAssetbundleCount()
  }
  if result then
    local fps = 0
    if 0 < frameTime then
      fps = 1000 / frameTime
    end
    dict.fps = fps
    dict.frames = frames
    dict.frametime = frameTime
    dict.mainthread = mainThread
    dict.maxftime = maxFrameTime
    dict.maxmttime = maxMainThread
    dict.hiccups = hiccupFrames
    dict.mthiccups = hiccupMainThread
  end
  for mode, time in pairs(gameTimes) do
    dict[mode] = time
  end
  dict.event = "profiling"
  self:LogWithInfo(dict, false)
end

function BIManager:LogProfilingSession(sessionName)
  if sessionName == nil then
    return
  end
  local result, totalMem, monoReserved, monoUsed, nativeReserved, nativeUsed, lua, textures, assetbundles, frames, frametime, maxFrametime, hiccups = MicrofunProfiler.Instance:EndSession(sessionName)
  if result then
    local dict = {
      mem = totalMem,
      monoreserved = monoReserved,
      monoused = monoUsed,
      nativereserved = nativeReserved,
      nativeused = nativeUsed,
      lua = lua,
      tex = textures,
      ab = assetbundles,
      frames = frames,
      frametime = frametime,
      maxframetime = maxFrametime,
      hiccups = hiccups,
      event = "profilingsession",
      scene = sessionName,
      startuptime = CS.InGameProfiler.gameTime
    }
    self:LogWithInfo(dict, false)
  end
end

function BIManager:LogProfilingHiccup(hiccups, maxFrametime)
  local dict = {
    hiccups = hiccups,
    maxframetime = maxFrametime,
    startuptime = CS.InGameProfiler.gameTime,
    event = "profilinghiccup"
  }
  self:LogWithInfo(dict, false)
end

function BIManager:LogDeviceSpec()
  local dict = {
    chips = CS.InGameProfiler.processorType,
    chipcnt = CS.InGameProfiler.processorCount,
    frequency = CS.InGameProfiler.processorFrequency,
    gpu = CS.InGameProfiler.gpu,
    total_ds = PlatformInterface.GetTotalDiskSpaceInBytes(),
    remain_ds = PlatformInterface.GetRemainingDiskSpaceInBytes(),
    app_ds = PlatformInterface.GetAppDataSize(),
    sysmem = CS.InGameProfiler.systemMem,
    graphics = CS.InGameProfiler.gpuType,
    texture = CS.InGameProfiler.maxTextureSize,
    astc = CS.InGameProfiler.astcSupport
  }
  local resolution = CS.InGameProfiler.resolution
  dict.resolution = {
    resolution.x,
    resolution.y
  }
  dict.event = "device"
  self:LogWithInfo(dict, false)
end

function BIManager:LogFileStorage()
  local dict = {
    remain_ds = PlatformInterface.GetRemainingDiskSpaceInBytes(),
    app_ds = PlatformInterface.GetAppDataSize(),
    cache = AddressableManager.Instance:GetCachedAssetBundlesSize(),
    bi = self.m_stringDict:GetFileSize()
  }
  dict.event = "storage"
  self:LogWithInfo(dict, false)
end

function BIManager:LogNet(eAction, msg, reqCtx, interval, isLoading)
  reqCtx = reqCtx or {}
  local url = reqCtx.RealUrl
  local host = reqCtx.Host
  local reqid = reqCtx.MessageId
  local error = reqCtx.ErrorMsg
  local dict = {
    event = "net",
    action = eAction,
    msg = msg,
    url = url,
    host = host,
    reqid = reqid,
    error = error,
    interval = interval and interval.cpuInS or nil,
    intervalMS = interval and interval.gtInMS or nil,
    is_loading = isLoading and 1 or nil
  }
  self:LogWithInfo(dict, false, true)
end

function BIManager:LogSession(action)
  local dict = {
    event = action,
    network_name = CSAppsFlyerManager:GetMediaSource(),
    tracker_name = CSAppsFlyerManager:GetTrackerName(),
    adid = "::" .. CSAppsFlyerManager:GetAppsFlyerId()
  }
  self:LogWithInfo(dict, true, true)
end

function BIManager:LogSDKLogin(success, rawResult, log, eSocialType, accessToken, socialID, name, email, picture)
  local dict = {
    event = "facebook",
    sc = success,
    rr = rawResult,
    l = log,
    st = eSocialType,
    at = accessToken,
    si = socialID,
    na = name,
    em = email,
    pic = picture
  }
  self:LogWithInfo(dict)
end

function BIManager:LogErrorInfo(scene, msg)
  local trackInfo = debug.traceback(msg, 2)
  if GameConfig.IsTestMode() then
    Log.Error("BIManager:LogErrorInfo " .. scene .. ":" .. trackInfo)
  end
  local dict = {
    event = "error",
    scene = scene,
    msg = trackInfo
  }
  self:LogWithInfo(dict, false)
end

function BIManager:LogPushToken(tokenKey, token)
  local dict = {
    event = "push_token",
    [tokenKey] = token
  }
  self:LogWithInfo(dict, false)
end

function BIManager:LogProject(eScene, eAction, ext)
  local dict = {
    event = "project",
    scene = eScene,
    action = eAction,
    ext = ext
  }
  self:LogWithInfo(dict)
end

function BIManager:LogAd(eScene, eStage, ext)
  local dict = {
    event = "ad",
    scene = eScene,
    stage = eStage,
    map = GM.SceneManager:GetGameMode(),
    ext = ext
  }
  self:LogWithInfo(dict)
end

function BIManager:LogCook(action, dish, instru, materials, time)
  local dict = {
    event = "cook",
    action = action,
    ds = dish,
    eq = instru,
    mt = materials,
    time = time,
    map = GM.SceneManager:GetGameMode()
  }
  self:LogWithInfo(dict, true)
end

function BIManager:LogDayStart(orderChapterId, orderGroupId)
  local cost, items = GM.MainBoardModel:GetItemsEnergyCost()
  local dict = {
    event = "day_start",
    cht = orderChapterId,
    og = orderGroupId,
    p = cost,
    pitem = self:TableToString(items)
  }
  Log.Info("订单组打点：订单组开始 " .. self:TableToString(dict))
  self:LogWithInfo(dict, true)
end

function BIManager:LogDayEnd(orderChapterId, orderGroupId, consumedEnergy, costEnergyCurDay, costEnergyPastDay)
  local cost, items = GM.MainBoardModel:GetItemsEnergyCost()
  local dict = {
    event = "day_end",
    cht = orderChapterId,
    og = orderGroupId,
    t = consumedEnergy,
    td = costEnergyCurDay,
    pd = costEnergyPastDay,
    q = cost
  }
  Log.Info("订单组打点：订单组结束 " .. self:TableToString(dict))
  self:LogWithInfo(dict, true)
end

function BIManager:LogABTest(groupName)
  local dict = {event = "ab", gname = groupName}
  self:LogWithInfo(dict)
end

function BIManager:_InitSystemDeviceInfo()
  self.m_appVersion = GameConfig.GetCurrentVersion()
  self.m_token = GameConfig.GetGameToken()
  self.m_channelId = string.format("%u", GameConfig.CURRENT_PLATFORM)
  self.m_env = GameConfig.IsTestMode() and "0" or "1"
  self.m_strOSName = DeviceInfo.GetOsName()
  self.m_strOSVersion = DeviceInfo.GetOsVersion()
  self.m_strOSModel = DeviceInfo.GetDeviceModel()
  self.m_strLanguage = DeviceInfo.GetLocale()
  self.m_strMCC = DeviceInfo.GetMCC()
  self.m_strMNC = DeviceInfo.GetMNC()
  self.m_idfv = DeviceInfo.GetVendorID()
  self.m_imei = DeviceInfo.GetIMEI()
  self.m_device_id = DeviceInfo.GetDeviceID()
  self.m_mac = DeviceInfo.GetMacAddress()
  self.m_country = DeviceInfo.GetCountry()
  self:ResetInstallUuidCache(GM.UserModel:GetInstallUuid())
  self.m_deviceInfoInitialized = true
end

function BIManager:GetNetworkReachability()
  if Application.internetReachability == NetworkReachability.NotReachable then
    return 0
  elseif Application.internetReachability == NetworkReachability.ReachableViaCarrierDataNetwork then
    return 1
  elseif Application.internetReachability == NetworkReachability.ReachableViaLocalAreaNetwork then
    return 2
  end
  return nil
end

function BIManager:_AppendBIInfo(dict, withDeviceInfo)
  if not self.m_deviceInfoInitialized then
    self:_InitSystemDeviceInfo()
  end
  dict.app_version = self.m_appVersion
  dict.token = self.m_token
  dict.channelid = self.m_channelId
  dict.env = self.m_env
  dict.client_time = TimeUtil.GetTimeInSecond()
  dict.cs_time = GM.GameModel:HasServerTime() and GM.GameModel:GetServerTime() or 0
  dict.network = self:GetNetworkReachability()
  dict.os_name = self.m_strOSName
  dict.country = self.m_country
  if withDeviceInfo then
    dict.os_version = self.m_strOSVersion
    dict.os_model = self.m_strOSModel
    dict.language = self.m_strLanguage
    dict.mcc = self.m_strMCC
    dict.mnc = self.m_strMNC
    dict.idfv = self.m_idfv
    dict.imei = self.m_imei
    dict.device_id = self.m_device_id
    dict.mac = self.m_mac
    if StringUtil.IsNilOrEmpty(self.m_idfa) then
      self.m_idfa = DeviceInfo.GetAdvertisingIdentifier()
    end
    dict.idfa = self.m_idfa
  end
  dict.uuid = self.m_strUuid
  local biid = PlayerPrefs.GetInt(EPlayerPrefKey.BIMsgId, 0) + 1
  dict.bi_id = biid
  PlayerPrefs.SetInt(EPlayerPrefKey.BIMsgId, biid)
  local uid = GM.UserModel:GetUserId()
  if uid and uid ~= 0 then
    dict.userid = GM.UserModel:GetUserId()
  end
end

function BIManager:ResetInstallUuidCache(strUuid)
  self.m_strUuid = strUuid
  Log.Assert(not StringUtil.IsNilOrEmpty(self.m_strUuid), "uuid 不应为空")
end

function BIManager:SetNetworkCheckEnd()
  self.m_bNetworkCheckEnd = true
end

function BIManager:IsNetworkCheckEnd()
  return self.m_bNetworkCheckEnd == true
end

local appInfo = {
  EverMerge = {
    ios = "com.bigfishgames.mergetalesios://",
    android = "com.bigfishgames.mergetalesgoog"
  },
  MergeDragons = {
    ios = "mergedragons://",
    android = "com.gramgames.mergedragons"
  },
  MergeMansion = {
    ios = "mergemansion://",
    android = "com.everywear.game5"
  },
  MergeFables = {
    ios = "mergemyths://",
    android = "com.mergegames.myth"
  },
  MergeCounty = {
    ios = "com.mf.town://",
    android = "com.mf.town"
  },
  LoveAndPies = {
    ios = "fb234554248063824://",
    android = "com.Trailmix.LoveAndPiesMerge"
  },
  FamilyIsland = {
    ios = "com.MelsoftGames.FamilyIsland://",
    android = "com.MelsoftGames.FamilyIslandFarm"
  },
  FamilyFarmAdventure = {
    ios = "com.farmadventure.global://",
    android = "com.farmadventure.global"
  },
  ChefMerge = {
    ios = "fb569050894424411://",
    android = "com.chefmerge.room.decor"
  },
  TravelTown = {
    ios = "traveltown://",
    android = "io.randomco.travel"
  },
  SeasideEscape = {
    ios = "com.mf.cupcake://",
    android = "com.gamedots.seasideescape"
  },
  GossipHarbor = {
    ios = "com.mergegame.icookie://",
    android = "com.mergegames.gossipharbor"
  },
  MergeHotel = {
    ios = "fb756433048855816://",
    android = "puzzle.merge.hotel.empire"
  },
  MergeCooking = {
    ios = "fb622121112850283://",
    android = "com.merge.cooking.theme.restaurant.food"
  }
}

function BIManager:LogAppInstallInfo()
  local info = ""
  local appName
  for k, v in pairs(appInfo) do
    appName = DeviceInfo.IsSystemIOS() and v.ios or v.android
    if info ~= "" then
      info = info .. ";"
    end
    info = info .. k .. ":" .. (PlatformInterface.IsAppInstalled(appName) and "1" or "0")
  end
  self:LogAction(EBIType.AppInstalledInfo, info)
end
