import re
import pandas as pd
import os
import glob

def split_entries(content):
    entries = []
    entry = []
    brace_count = 0
    in_entry = False
    for char in content:
        if char == '{':
            brace_count += 1
            if brace_count == 1:
                in_entry = True
                entry = []
            else:
                entry.append(char)
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                in_entry = False
                entries.append(''.join(entry))
                entry = []
            else:
                entry.append(char)
        elif in_entry:
            entry.append(char)
    return entries

def process_lua_file(lua_file_path):
    # 提取文件名（不含扩展名）作为Excel输出文件名
    file_name = os.path.basename(lua_file_path)
    excel_name = os.path.splitext(file_name)[0] + '.xlsx'
    
    # 读取Lua文件
    with open(lua_file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 提取主数据部分
    start = content.find('{')
    end = content.rfind('}')
    entries_content = content[start+1:end].strip()

    # 分割条目
    entries = split_entries(entries_content)

    # 准备数据结构
    sheet1_data = []
    sheet2_data = []

    for entry_str in entries:
        # 解析基础字段
        group_id = re.search(r'GroupId\s*=\s*(\d+)', entry_str)
        chapter_id = re.search(r'ChapterId\s*=\s*(\d+)', entry_str)
        day = re.search(r'Day\s*=\s*(\d+)', entry_str)
        
        if group_id and chapter_id and day:
            group_id = int(group_id.group(1))
            chapter_id = int(chapter_id.group(1))
            day = int(day.group(1))
            
            sheet1_data.append({
                'GroupId': group_id,
                'ChapterId': chapter_id,
                'Day': day
            })
            
            # 解析奖励数据
            rewards_match = re.search(r'Rewards\s*=\s*{(.*)}', entry_str, re.DOTALL)
            if rewards_match:
                rewards_str = rewards_match.group(1)
                rewards = re.findall(
                    r'{Currency\s*=\s*"([^"]+)"\s*,\s*Amount\s*=\s*(\d+)}', 
                    rewards_str
                )
                for currency, amount in rewards:
                    sheet2_data.append({
                        'Day': day,
                        'Currency': currency,
                        'Amount': amount
                    })

    # 创建DataFrame
    df_sheet1 = pd.DataFrame(sheet1_data)
    df_sheet2 = pd.DataFrame(sheet2_data)

    # 导出到Excel
    output_path = os.path.join(os.path.dirname(lua_file_path), excel_name)
    with pd.ExcelWriter(output_path) as writer:
        df_sheet1.to_excel(writer, sheet_name='Sheet1', index=False)
        df_sheet2.to_excel(writer, sheet_name='Sheet2', index=False)
    
    return excel_name

# 获取目录路径
base_dir = 'd:/MCWork/GHabout/FMC/fmc_lua_1.18.5/fmc_lua/Data/Config'

# 查找所有匹配的文件
pattern = os.path.join(base_dir, 'OrderGroupConfig_*.lua')
matching_files = glob.glob(pattern)

# 处理每个文件
processed_files = []
for lua_file in matching_files:
    excel_file = process_lua_file(lua_file)
    processed_files.append(excel_file)

# 输出处理结果
print(f"导出成功！已处理以下文件：")
for file in processed_files:
    print(f"- {file}")