"""
订单编辑器主程序入口
基于需求背景2进行优化
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox, ttk
from tkinter import *

class EnhancedOrderDesigner:
    """增强的订单设计器"""

    def __init__(self, root):
        """初始化订单设计器"""
        self.root = root
        self.root.title("浮岛物语订单设计器 - 增强版")
        self.root.geometry("1200x800")

        # 初始化配置
        self.config_path = os.path.join(os.path.dirname(__file__), "..", "Data", "Config")
        self.config_data = None
        self.order_generator = None

        # 创建界面
        self._create_ui()

        # 加载配置
        self._load_configurations()

    def _create_ui(self):
        """创建用户界面"""
        # 创建主菜单
        menubar = Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="加载配置", command=self._load_configurations)
        file_menu.add_command(label="保存方案", command=self._save_scheme)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        menubar.add_cascade(label="文件", menu=file_menu)

        # 生成菜单
        generate_menu = Menu(menubar, tearoff=0)
        generate_menu.add_command(label="精确算法生成", command=self._precise_generation)
        generate_menu.add_command(label="批量生成对比", command=self._batch_generation)
        generate_menu.add_command(label="算法验证", command=self._algorithm_validation)
        menubar.add_cascade(label="生成", menu=generate_menu)

        # 分析菜单
        analysis_menu = Menu(menubar, tearoff=0)
        analysis_menu.add_command(label="配置分析", command=self._config_analysis)
        analysis_menu.add_command(label="性能分析", command=self._performance_analysis)
        analysis_menu.add_command(label="数据导出", command=self._data_export)
        menubar.add_cascade(label="分析", menu=analysis_menu)

        # 创建主框架
        main_frame = ttk.PanedWindow(self.root, orient=HORIZONTAL)
        main_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)

        # 左侧配置面板
        config_frame = ttk.LabelFrame(main_frame, text="配置面板", padding="10")
        main_frame.add(config_frame, weight=1)

        # 右侧结果面板
        result_frame = ttk.LabelFrame(main_frame, text="结果面板", padding="10")
        main_frame.add(result_frame, weight=2)

        # 配置面板内容
        self._create_config_panel(config_frame)

        # 结果面板内容
        self._create_result_panel(result_frame)

        # 状态栏
        self.status_var = StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=SUNKEN)
        status_bar.pack(side=BOTTOM, fill=X)

    def _create_config_panel(self, parent):
        """创建配置面板"""
        # 基础配置
        basic_frame = ttk.LabelFrame(parent, text="基础配置", padding="5")
        basic_frame.pack(fill=X, pady=5)

        # 特色菜品配置
        ttk.Label(basic_frame, text="特色菜品:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        self.special_dishes_var = StringVar(value="ds_chopve_1,ds_chopve_2,ds_flb_1,ds_chopve_3")
        ttk.Entry(basic_frame, textvariable=self.special_dishes_var, width=40).grid(
            row=0, column=1, sticky=W, padx=5, pady=2)

        # 体力范围配置
        ttk.Label(basic_frame, text="体力范围:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        energy_frame = ttk.Frame(basic_frame)
        energy_frame.grid(row=1, column=1, sticky=W, padx=5, pady=2)

        self.energy_min_var = StringVar(value="100")
        self.energy_max_var = StringVar(value="200")
        ttk.Entry(energy_frame, textvariable=self.energy_min_var, width=10).pack(side=LEFT)
        ttk.Label(energy_frame, text=" - ").pack(side=LEFT)
        ttk.Entry(energy_frame, textvariable=self.energy_max_var, width=10).pack(side=LEFT)

        # 算法配置
        algo_frame = ttk.LabelFrame(parent, text="算法配置", padding="5")
        algo_frame.pack(fill=X, pady=5)

        # 体力模式
        ttk.Label(algo_frame, text="体力模式:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        self.bet_mode_var = StringVar(value="2bet")
        bet_combo = ttk.Combobox(algo_frame, textvariable=self.bet_mode_var,
                                values=["2bet", "4bet", "8bet"], state="readonly", width=10)
        bet_combo.grid(row=0, column=1, sticky=W, padx=5, pady=2)

        # 效率模式
        ttk.Label(algo_frame, text="效率模式:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        self.efficiency_mode_var = StringVar(value="平均效率")
        efficiency_combo = ttk.Combobox(algo_frame, textvariable=self.efficiency_mode_var,
                                      values=["平均效率", "动态效率", "top3平均效率"],
                                      state="readonly", width=15)
        efficiency_combo.grid(row=1, column=1, sticky=W, padx=5, pady=2)

        # 操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=X, pady=10)

        ttk.Button(button_frame, text="精确生成", command=self._precise_generation).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="批量生成", command=self._batch_generation).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="清空结果", command=self._clear_results).pack(side=LEFT, padx=5)

    def _create_result_panel(self, parent):
        """创建结果面板"""
        # 创建Notebook用于多标签页
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=BOTH, expand=True)

        # 生成结果标签页
        result_tab = ttk.Frame(notebook)
        notebook.add(result_tab, text="生成结果")

        # 结果文本框
        self.result_text = Text(result_tab, wrap=WORD, font=("Consolas", 10))
        result_scrollbar = ttk.Scrollbar(result_tab, orient=VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=result_scrollbar.set)

        self.result_text.pack(side=LEFT, fill=BOTH, expand=True)
        result_scrollbar.pack(side=RIGHT, fill=Y)

        # 配置分析标签页
        analysis_tab = ttk.Frame(notebook)
        notebook.add(analysis_tab, text="配置分析")

        self.analysis_text = Text(analysis_tab, wrap=WORD, font=("Consolas", 10))
        analysis_scrollbar = ttk.Scrollbar(analysis_tab, orient=VERTICAL, command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=analysis_scrollbar.set)

        self.analysis_text.pack(side=LEFT, fill=BOTH, expand=True)
        analysis_scrollbar.pack(side=RIGHT, fill=Y)

    def _load_configurations(self):
        """加载配置文件"""
        try:
            self.status_var.set("正在加载配置...")

            from config_loader import ConfigLoader
            from order_generator import OrderGenerator

            config_loader = ConfigLoader(self.config_path)
            self.config_data = config_loader.load_all_configs()
            self.order_generator = OrderGenerator(self.config_data)

            self.status_var.set(f"配置加载完成 - 物品:{len(self.config_data.items)} 生成器:{len(self.config_data.generators)}")

            # 显示配置分析
            self._show_config_analysis()

        except Exception as e:
            self.status_var.set(f"配置加载失败: {e}")
            messagebox.showerror("错误", f"配置加载失败: {e}")

    def _precise_generation(self):
        """精确算法生成"""
        if not self.order_generator:
            messagebox.showwarning("警告", "请先加载配置文件")
            return

        try:
            self.status_var.set("正在进行精确生成...")

            # 构建配置
            config = self._build_generation_config()

            # 使用精确算法生成
            result = self.order_generator.generate_precise_order_scheme(config)

            if result:
                self._display_generation_result(result, "精确算法生成")
                self.status_var.set("精确生成完成")
            else:
                self.status_var.set("精确生成失败")

        except Exception as e:
            self.status_var.set(f"精确生成失败: {e}")
            messagebox.showerror("错误", f"精确生成失败: {e}")

    def _batch_generation(self):
        """批量生成对比"""
        if not self.order_generator:
            messagebox.showwarning("警告", "请先加载配置文件")
            return

        try:
            self.status_var.set("正在进行批量生成...")

            config = self._build_generation_config()

            # 生成多套方案
            schemes = self.order_generator.generate_order_schemes(config, 10)

            self._display_batch_results(schemes)
            self.status_var.set(f"批量生成完成 - 共{len(schemes)}套方案")

        except Exception as e:
            self.status_var.set(f"批量生成失败: {e}")
            messagebox.showerror("错误", f"批量生成失败: {e}")

    def _algorithm_validation(self):
        """算法验证"""
        messagebox.showinfo("算法验证", "算法验证功能开发中...")

    def _config_analysis(self):
        """配置分析"""
        self._show_config_analysis()

    def _performance_analysis(self):
        """性能分析"""
        messagebox.showinfo("性能分析", "性能分析功能开发中...")

    def _data_export(self):
        """数据导出"""
        messagebox.showinfo("数据导出", "数据导出功能开发中...")

    def _save_scheme(self):
        """保存方案"""
        messagebox.showinfo("保存方案", "保存方案功能开发中...")

    def _clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, END)
        self.analysis_text.delete(1.0, END)

    def _build_generation_config(self):
        """构建生成配置"""
        from data_models import OrderGenerationConfig, BetMode, EfficiencyMode, PlayerGeneratorInfo

        config = OrderGenerationConfig()

        # 解析特色菜品
        config.special_dishes = [dish.strip() for dish in self.special_dishes_var.get().split(',') if dish.strip()]

        # 设置体力范围
        config.total_energy_range = (float(self.energy_min_var.get()), float(self.energy_max_var.get()))

        # 设置体力消耗模式
        bet_mode_map = {"2bet": BetMode.BET_2, "4bet": BetMode.BET_4, "8bet": BetMode.BET_8}
        config.bet_mode = bet_mode_map.get(self.bet_mode_var.get(), BetMode.BET_2)

        # 设置效率模式
        efficiency_mode_map = {
            "平均效率": EfficiencyMode.AVERAGE,
            "动态效率": EfficiencyMode.DYNAMIC,
            "top3平均效率": EfficiencyMode.TOP3_AVERAGE
        }
        config.efficiency_mode = efficiency_mode_map.get(self.efficiency_mode_var.get(), EfficiencyMode.AVERAGE)

        # 添加示例玩家生成器信息
        config.player_generators = [
            PlayerGeneratorInfo(generator_type="pd_1_4", level=4, count=2),
            PlayerGeneratorInfo(generator_type="pd_1_5", level=5, count=1),
            PlayerGeneratorInfo(generator_type="pd_2_4", level=4, count=2),
        ]

        return config

    def _display_generation_result(self, result, title):
        """显示生成结果"""
        self.result_text.insert(END, f"\n=== {title} ===\n")
        self.result_text.insert(END, f"总体力消耗: {result.total_energy:.2f}\n")
        self.result_text.insert(END, f"方案有效性: {'有效' if result.is_valid else '无效'}\n\n")

        for i, order in enumerate(result.orders):
            self.result_text.insert(END, f"订单{i+1}: {order.order_id}\n")
            for req in order.requirements:
                self.result_text.insert(END, f"  需求: {req.item_type} x{req.count}\n")
            if hasattr(order, 'energy_cost'):
                self.result_text.insert(END, f"  体力消耗: {order.energy_cost:.2f}\n")
            self.result_text.insert(END, "\n")

        self.result_text.see(END)

    def _display_batch_results(self, schemes):
        """显示批量结果"""
        self.result_text.insert(END, f"\n=== 批量生成结果 ===\n")
        self.result_text.insert(END, f"共生成 {len(schemes)} 套方案\n\n")

        for i, scheme in enumerate(schemes):
            self.result_text.insert(END, f"方案{i+1}: 体力消耗 {scheme.total_energy:.2f} ({'有效' if scheme.is_valid else '无效'})\n")

        self.result_text.see(END)

    def _show_config_analysis(self):
        """显示配置分析"""
        if not self.config_data:
            return

        self.analysis_text.delete(1.0, END)
        self.analysis_text.insert(END, "=== 配置文件分析 ===\n\n")

        # 物品统计
        self.analysis_text.insert(END, f"物品总数: {len(self.config_data.items)}\n")

        # 按系列统计物品
        series_count = {}
        for item in self.config_data.items.values():
            series_count[item.series] = series_count.get(item.series, 0) + 1

        self.analysis_text.insert(END, "物品系列分布:\n")
        for series, count in sorted(series_count.items()):
            self.analysis_text.insert(END, f"  系列{series}: {count}个\n")

        # 生成器统计
        self.analysis_text.insert(END, f"\n生成器总数: {len(self.config_data.generators)}\n")

        # 按系列统计生成器
        gen_series_count = {}
        for gen in self.config_data.generators.values():
            gen_series_count[gen.series] = gen_series_count.get(gen.series, 0) + 1

        self.analysis_text.insert(END, "生成器系列分布:\n")
        for series, count in sorted(gen_series_count.items()):
            self.analysis_text.insert(END, f"  系列{series}: {count}个\n")

        # 订单统计
        total_orders = sum(len(orders) for orders in self.config_data.orders.values())
        self.analysis_text.insert(END, f"\n订单总数: {total_orders}\n")
        self.analysis_text.insert(END, f"章节数: {len(self.config_data.orders)}\n")


def main():
    """主程序入口"""
    try:
        # 设置工作目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(current_dir)

        # 创建主窗口
        root = tk.Tk()

        # 创建增强的订单设计器
        app = EnhancedOrderDesigner(root)

        # 启动主循环
        root.mainloop()

    except ImportError as e:
        messagebox.showerror("导入错误", f"无法导入必要的模块: {e}")
        sys.exit(1)
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
