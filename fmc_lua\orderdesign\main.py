"""
订单编辑器主程序入口
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox

def main():
    """主程序入口"""
    try:
        # 设置工作目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(current_dir)
        
        # 导入主程序
        from orderCreaterzuluwindcontinuezoo import OrderEditor
        
        # 创建主窗口
        root = tk.Tk()
        
        # 创建订单编辑器
        app = OrderEditor(root)
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        messagebox.showerror("导入错误", f"无法导入必要的模块: {e}")
        sys.exit(1)
    except Exception as e:
        messagebox.showerror("启动错误", f"程序启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
