return {
  {
    id = "tutorial_cg",
    desc = "cg动画",
    condition = "",
    executer = "CG"
  },
  {
    id = "timeline",
    desc = "开场时间线",
    condition = {
      {
        type = "TutorialFinished",
        args = {
          "tutorial_cg"
        }
      }
    },
    executer = "Beginning"
  },
  {
    id = "toboard",
    desc = "点击棋盘入口",
    condition = {
      {
        type = "TutorialFinished",
        args = {"timeline"}
      }
    },
    executer = "ToBoard"
  },
  {
    id = "weakGesture",
    desc = "弱引导点击图鉴、去棋盘、去场景等",
    condition = {
      {
        type = "TutorialFinished",
        args = {"toboard"}
      }
    },
    executer = "WeakGesture"
  },
  {
    id = "merge1",
    desc = "合成引导",
    condition = {
      {
        type = "TutorialFinished",
        args = {"timeline"}
      }
    },
    executer = "Merge1"
  },
  {
    id = "order10010",
    desc = "完成订单",
    condition = {
      {
        type = "HasOrder",
        args = {"10010"}
      }
    },
    executer = "Order",
    args = {"10010"}
  },
  {
    id = "order10020",
    desc = "完成订单",
    condition = {
      {
        type = "HasOrder",
        args = {"10020"}
      }
    },
    executer = "Order",
    args = {"10020"}
  },
  {
    id = "order10030",
    desc = "完成订单",
    condition = {
      {
        type = "HasOrder",
        args = {"10030"}
      }
    },
    executer = "Order",
    args = {"10030"}
  },
  {
    id = "order10040",
    desc = "完成订单",
    condition = {
      {
        type = "HasOrder",
        args = {"10040"}
      }
    },
    executer = "Order",
    args = {"10040"}
  },
  {
    id = "order10050",
    desc = "完成订单",
    condition = {
      {
        type = "HasOrder",
        args = {"10050"}
      }
    },
    executer = "Order",
    args = {"10050"}
  },
  {
    id = "order10070",
    desc = "完成订单",
    condition = {
      {
        type = "HasOrder",
        args = {"10070"}
      }
    },
    executer = "Order",
    args = {"10070"}
  },
  {
    id = "order10080",
    desc = "完成订单",
    condition = {
      {
        type = "HasOrder",
        args = {"10080"}
      }
    },
    executer = "Order",
    args = {"10080"}
  },
  {
    id = "order10140",
    desc = "完成订单",
    condition = {
      {
        type = "HasOrder",
        args = {"10140"}
      }
    },
    executer = "Order",
    args = {"10140"}
  },
  {
    id = "task1_1",
    desc = "完成任务",
    condition = {},
    executer = "Task",
    args = {"1"}
  },
  {
    id = "task1_2",
    desc = "完成任务",
    condition = {
      {
        type = "TutorialFinished",
        args = {"task1_1"}
      }
    },
    executer = "Task",
    args = {"2"}
  },
  {
    id = "task1_3",
    desc = "完成任务",
    condition = {
      {
        type = "TutorialFinished",
        args = {"task1_2"}
      }
    },
    executer = "Task",
    args = {"3"}
  },
  {
    id = "task1_4",
    desc = "完成任务",
    condition = {
      {
        type = "TutorialFinished",
        args = {"task1_3"}
      }
    },
    executer = "Task",
    args = {"4"}
  },
  {
    id = "CD_pd_1_7",
    desc = "母体棋子CD",
    condition = {
      {
        type = "TutorialFinished",
        args = {"merge1"}
      },
      {
        type = "ItemAdded",
        args = {"pd_1_7"}
      }
    },
    executer = "PDCD",
    args = {"pd_1_7"}
  },
  {
    id = "CD_pd_2_6",
    desc = "母体棋子CD",
    condition = {
      {
        type = "TutorialFinished",
        args = {"merge1"}
      },
      {
        type = "ItemAdded",
        args = {"pd_2_6"}
      }
    },
    executer = "PDCD",
    args = {"pd_2_6"}
  },
  {
    id = "merge2",
    desc = "合成母棋子",
    condition = {
      {
        type = "TaskFinished",
        args = {
          {chapterId = 1, taskId = 1}
        }
      }
    },
    executer = "Merge2"
  },
  {
    id = "order_item_info",
    desc = "订单棋子信息",
    condition = {
      {
        type = "TutorialFinished",
        args = {"merge2"}
      }
    },
    executer = "OrderItemInfo"
  },
  {
    id = "cache",
    desc = "缓存队列",
    condition = {
      {
        type = "MainLevel",
        args = {2}
      }
    },
    executer = "Cache"
  },
  {
    id = "clickPD",
    desc = "点击转化棋子",
    condition = {
      {
        type = "HasOrder",
        args = {"10150"}
      },
      {
        type = "ItemAdded",
        args = {"it_1_1_6"}
      }
    },
    executer = "ClickPD"
  },
  {
    id = "cook1",
    desc = "厨具使用-合成厨具打开详情页",
    condition = {
      {
        type = "MainLevel",
        args = {4}
      }
    },
    executer = "Cook1"
  },
  {
    id = "cook2",
    desc = "厨具使用-做菜",
    condition = {
      {
        type = "TutorialFinished",
        args = {"cook1"}
      }
    },
    executer = "Cook2"
  },
  {
    id = "cook3",
    desc = "厨具使用-菜品取出",
    condition = {
      {
        type = "TutorialFinished",
        args = {"cook1"}
      }
    },
    executer = "Cook3"
  },
  {
    id = "merge_scissors",
    desc = "合成剪刀",
    condition = {
      {
        type = "ItemAdded",
        args = {"scissors_1"}
      }
    },
    executer = "MergeScissors"
  },
  {
    id = "energy",
    desc = "体力不足",
    condition = {},
    executer = "Energy"
  },
  {
    id = "cd_speed",
    desc = "CD道具",
    condition = {
      {
        type = "OrderGroupFinished",
        args = {2, 3}
      },
      {
        type = "MainLevel",
        args = {5}
      }
    },
    executer = "CDSpeed"
  },
  {
    id = "bubble",
    desc = "泡泡",
    condition = {
      {
        type = "MainLevel",
        args = {5}
      }
    },
    executer = "Bubble"
  },
  {
    id = "order_group",
    desc = "订单组入口",
    condition = {
      {
        type = "TutorialFinished",
        args = {"order10040"}
      }
    },
    executer = "OrderGroup"
  },
  {
    id = "additem_+8",
    desc = "库存+8棋子",
    condition = {
      {
        type = "OrderGroupFinished",
        args = {2, 1}
      }
    },
    executer = "AddItem"
  },
  {
    id = "additem_old_user",
    desc = "老用户库存+8棋子弹窗",
    condition = {
      {
        type = "TutorialFinished",
        args = {"additem_+8"}
      }
    },
    executer = "AddItemOldUser"
  },
  {
    id = "tutorial_producer_inventory",
    desc = "母棋子仓库引导",
    condition = {},
    executer = "ProducerInventory"
  },
  {
    id = "tutorial_bakeout_1",
    desc = "通关活动排行榜",
    condition = {
      {
        type = "BakeOutStart"
      }
    },
    executer = "BakeOut1"
  },
  {
    id = "tutorial_bakeout_2",
    desc = "通关活动订单及金币兑换",
    condition = {
      {
        type = "BakeOutStart"
      }
    },
    executer = "BakeOut2"
  },
  {
    id = "shop",
    desc = "商城",
    condition = {
      {
        type = "FunctionEnabled",
        args = {"shop"}
      }
    },
    executer = "Shop"
  },
  {
    id = "tutorial_coin_race_entry",
    desc = "五人小组竞赛开始引导",
    condition = {
      {
        type = "CoinRaceStart"
      }
    },
    executer = "CoinRace"
  },
  {
    id = "tutorial_coin_race_first_main_window",
    desc = "五人小组竞赛首次打开主界面引导",
    condition = {
      {
        type = "CoinRaceStart"
      }
    },
    executer = "CoinRace2"
  },
  {
    id = "tutorial_coin_race_second_main_window",
    desc = "五人小组竞赛首次第二轮主界面引导",
    condition = {
      {
        type = "CoinRaceStart"
      }
    },
    executer = "CoinRace3"
  },
  {
    id = "tutorial_coin_race_order",
    desc = "五人小组竞赛订单引导",
    condition = {
      {
        type = "CoinRaceStart"
      }
    },
    executer = "CoinRace4"
  },
  {
    id = "tutorial_pk_race_start",
    desc = "1v1竞赛开始引导",
    condition = {
      {
        type = "PkRaceStart"
      }
    },
    executer = "PkRaceStart"
  },
  {
    id = "tutorial_digactivity_firstdig",
    desc = "挖宝活动首次挖宝",
    condition = {
      {
        type = "DigActivityStart"
      }
    },
    executer = "DigActivityFirstDig"
  },
  {
    id = "tutorial_digactivity_seconddig",
    desc = "挖宝活动第二次引导挖宝",
    condition = {
      {
        type = "DigActivityStart"
      },
      {
        type = "TutorialFinished",
        args = {
          "tutorial_digactivity_firstdig"
        }
      }
    },
    executer = "DigActivitySecondDig"
  },
  {
    id = "tutorial_battlepass",
    desc = "PassActivity活动",
    condition = {
      {type = "PassStart"}
    },
    executer = "PassActivity"
  },
  {
    id = "tutorial_battlepass_loop",
    desc = "PassActivity活动常规引导",
    condition = {
      {
        type = "TutorialFinished",
        args = {
          "tutorial_battlepass"
        }
      }
    },
    executer = "PassActivityLoop"
  },
  {
    id = "tutorial_progress_new",
    desc = "进度条活动",
    condition = {
      {
        type = "ProgressStart"
      }
    },
    executer = "ProgressActivity"
  },
  {
    id = "tutorial_extraboard_item_delete",
    desc = "小棋盘棋子删除引导",
    condition = {
      {
        type = "ExtraBoardStart"
      }
    },
    executer = "ExtraBoardDelete"
  },
  {
    id = "tutorial_extraboard_start",
    desc = "小棋盘开始引导",
    condition = {
      {
        type = "ExtraBoardStart"
      }
    },
    executer = "ExtraBoardStart"
  },
  {
    id = "tutorial_extraboard_cobweb_unlock",
    desc = "小棋盘蛛网解锁引导",
    condition = {
      {
        type = "ExtraBoardStart"
      }
    },
    executer = "ExtraBoardCobwebUnlock"
  },
  {
    id = "tutorial_extraboard_cobweb_merge",
    desc = "小棋盘蛛网合成引导",
    condition = {
      {
        type = "ExtraBoardStart"
      }
    },
    executer = "ExtraBoardCobwebMerge"
  },
  {
    id = "tutorial_blind_chest",
    desc = "开宝箱活动引导",
    condition = {
      type = "BlindChestStart"
    },
    executer = "BlindChest"
  },
  {
    id = "tutorial_change_name",
    desc = "改名引导",
    condition = {
      {
        type = "BakeOutStart"
      }
    },
    executer = "ChangeName"
  },
  {
    id = "tutorial_energy_boost",
    desc = "能量助力开启引导",
    condition = "",
    executer = "EnergyBoost"
  }
}
