EStandardColor = {
  MainText = CSColor(0.*****************, 0.*****************, 0.*****************, 1),
  RedText = CSColor(0.****************, 0.*****************, 0.*****************, 1),
  GreenText = CSColor(0.*****************, 0.****************, 0.****************, 1)
}
ESpecialViewSortingOrder = {
  MaskLayer = 28000,
  EventLock = 28000,
  HudHighlight = 25500,
  Prompt = 32000,
  FlyElement = 32000,
  TutorialMask = 26000,
  TutorialHighlight = 26500,
  Transition = 27000,
  DataConflictWindow = 27500,
  UpdateHintWindow = 27600,
  AccountNoticeWindow = 27700,
  SystemGeneralWindow = 28400,
  ForceRestartWindow = 28500,
  TestWindow = 29000
}
EHudAnchorType = {
  All = 0,
  TopLeft = 1,
  TopRight = 2,
  CenterLeft = 3,
  Center = 4,
  CenterRight = 5,
  BottomCenter = 6,
  TopCenter = 7,
  BottomLeft = 8,
  BottomRight = 9
}
EBIReferType = {
  UserClick = "click",
  ClickEle = "click_ele",
  AutoPopup = "auto"
}
EWindowMaskAlpha = {Default = 0.****************, Dark = 0.85}
EEntryRootKey = {
  BakeOut = "bakeOut",
  CoinRace = "coinRace",
  PkRace = "pkRace",
  PassActivity = "passActivity",
  DigActivity = "digActivity",
  ExtraBoardActivity = "extraBoardActivity",
  BlindChest = "blindChest",
  ProgressActivity = "progressActivity",
  Bundle = "bundle"
}

function GetEntryRootName(eEntryRootKey)
  return "m_" .. tostring(eEntryRootKey) .. "EntryNode"
end

function GetEntryCountName(eEntryRootKey)
  return "m_" .. tostring(eEntryRootKey) .. "EntryCount"
end
