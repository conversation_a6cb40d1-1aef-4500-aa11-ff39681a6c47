OrderCell = setmetatable({}, BaseOrderCell)
OrderCell.__index = OrderCell
local OneItemSize = Vector2(310, 216)
local TwoItemsSize = Vector2(438, 216)
local TwoGoSize = Vector2(242, 87)
local BottomRewardsMaxCount = 4
local RewardsMinSpace = 3
local SurpriseChestWidth = 100

function OrderCell:Init(order, orderArea)
  self.m_order = order
  self.m_orderArea = orderArea
  self.m_orderViewState = OrderState.Init
  self:_UpdateAvatar()
  self.m_icons = self.m_icons or {}
  for _, icon in ipairs(self.m_icons) do
    icon.gameObject:SetActive(false)
  end
  local countMap, arrOrder = order:GetUniqueRequirements()
  self.m_uniqueOrderCount = #arrOrder
  for i = 1, self.m_uniqueOrderCount do
    if self.m_icons[i] == nil then
      local iconObject = Object.Instantiate(self.m_iconPrefab, self.m_iconArea.transform)
      local orderIcon = iconObject:GetLuaTable()
      table.insert(self.m_icons, orderIcon)
    end
    local type = arrOrder[i]
    self.m_icons[i].gameObject:SetActive(true)
    self.m_icons[i]:Init(order, type, countMap[type], self)
  end
  if self.m_uniqueOrderCount == 2 then
    self.m_iconArea.transform:SetAnchoredPosX(68)
    self.m_buttonTrans:SetLocalPosX(68)
    self.m_handEffectGo.transform:SetLocalPosX(68)
    self.m_buttonTrans.sizeDelta = TwoGoSize
  else
    self.m_iconArea.transform:SetAnchoredPosX(73)
    self.m_buttonTrans:SetLocalPosX(73)
    self.m_handEffectGo.transform:SetLocalPosX(73)
  end
  self:UpdateRewardDisplay()
  if order:IsTimelimitOrder() then
    DOVirtual.DelayedCall(0.01, function()
      orderArea:ScrollToRectTransformVisible(self.transform, true)
    end)
  end
  if not self.m_inited then
    if not self.m_orderArea:IsShowNewOrders() then
      self:PlayEnterAnimation()
    end
    EventDispatcher.AddListener(EEventType.ShowItemTestInfoChanged, self, self._OnShowItemTestInfoChanged)
    EventDispatcher.AddListener(EEventType.FlambeTimeChanged, self, self._OnFlambeTimeChanged)
    self:_OnFlambeTimeChanged()
    for _, activityDefinition in pairs(SurpriseChestActivityDefinition) do
      if activityDefinition.ChestDataStateChanged ~= nil then
        EventDispatcher.AddListener(activityDefinition.ChestDataStateChanged, self, self.OnSurpriseChestDataChanged)
      end
      if activityDefinition.StateChangedEvent ~= nil then
        EventDispatcher.AddListener(activityDefinition.StateChangedEvent, self, self.OnSurpriseChestDataChanged)
      end
    end
  end
  self.m_buttonTrans.localScale = V3Zero
  self.m_deliverButtonVisible = false
  self:_OnShowItemTestInfoChanged()
  self.m_inited = true
  self:UpdateSurpriseChestState()
end

function OrderCell.GetCellSizeDelta(order)
  local countMap, arrOrder = order:GetUniqueRequirements()
  return #arrOrder == 1 and OneItemSize or TwoItemsSize
end

function OrderCell:UpdateRewardDisplay()
  if self.m_uniqueOrderCount == 2 then
    self.transform.sizeDelta = TwoItemsSize
  else
    self.transform.sizeDelta = OneItemSize
  end
  self.m_rewardCells = self.m_rewardCells or {}
  for _, reward in ipairs(self.m_rewardCells) do
    reward.gameObject:SetActive(false)
  end
  local displayRewards = self.m_order:GetRewards()
  local bottomRewardPrefab = self.m_bottomRewardPrefab
  local bottomRewardsWidth = 0
  local topRewardsWidth = 0
  for i, reward in ipairs(displayRewards) do
    if self.m_rewardCells[i] == nil then
      local orderRewardGo
      if i <= BottomRewardsMaxCount then
        orderRewardGo = Object.Instantiate(bottomRewardPrefab, self.m_bottomRewards)
      else
        orderRewardGo = Object.Instantiate(self.m_topRewardPrefab, self.m_topRewards)
      end
      table.insert(self.m_rewardCells, orderRewardGo:GetLuaTable())
    elseif i <= BottomRewardsMaxCount then
      self.m_rewardCells[i].transform:SetParent(self.m_bottomRewards)
    else
      self.m_rewardCells[i].transform:SetParent(self.m_topRewards)
    end
    self.m_rewardCells[i].gameObject:SetActive(true)
    if i <= BottomRewardsMaxCount then
      bottomRewardsWidth = bottomRewardsWidth + self.m_rewardCells[i]:Init(reward)
    else
      topRewardsWidth = topRewardsWidth + self.m_rewardCells[i]:Init(reward)
    end
  end
  self:_UpdateRewardDisplay(displayRewards, bottomRewardsWidth, topRewardsWidth)
  self:_UpdateFlambeFireWidth()
end

function OrderCell:_UpdateRewardDisplay(displayRewards, bottomRewardsWidth, topRewardsWidth)
  local maxBottomRewardsSpacing = 16
  self.m_bottomRewardsLayoutGroup.spacing = maxBottomRewardsSpacing
  local bottomSpaceNum = math.min(#displayRewards, BottomRewardsMaxCount) - 1
  local bottomRewardsTotalWidth = bottomRewardsWidth + bottomSpaceNum * maxBottomRewardsSpacing
  local orderWidth = self.transform.sizeDelta.x - 20
  if bottomRewardsTotalWidth > orderWidth then
    local diff = (bottomRewardsTotalWidth - orderWidth) / bottomSpaceNum
    local targetSpacing = self.m_bottomRewardsLayoutGroup.spacing - diff
    self.m_bottomRewardsLayoutGroup.spacing = math.max(RewardsMinSpace, targetSpacing)
    if targetSpacing < RewardsMinSpace then
      UIUtil.AddSizeDelta(self.transform, (RewardsMinSpace - targetSpacing) * bottomSpaceNum)
    end
  else
    local space = (orderWidth - bottomRewardsTotalWidth) / bottomSpaceNum
    space = space < 0 and 0 or space
    space = 5 < space and 5 or space
    for i = 1, bottomSpaceNum + 1 do
      if self.m_rewardCells[i] ~= nil then
        self.m_rewardCells[i]:AddSpace(space)
      end
    end
  end
  if #displayRewards <= BottomRewardsMaxCount then
    self.m_topRewardsGo:SetActive(false)
    return
  end
  self.m_topRewardsGo:SetActive(true)
  local maxTopRewardsSpacing = 10
  local topRewardNum = #displayRewards - BottomRewardsMaxCount
  local topSpaceNum = topRewardNum - 1
  local topRewardMaxWidth = self.transform.sizeDelta.x - 144
  local extra = topRewardMaxWidth - topRewardsWidth
  local space = extra / topSpaceNum
  space = space < 0 and 0 or space
  space = maxTopRewardsSpacing < space and maxTopRewardsSpacing or space
  self.m_topRewardsLayoutGroup.spacing = space
  local topRewardsTotalWidth = topRewardsWidth + space * topSpaceNum
  if topRewardMaxWidth > topRewardsTotalWidth then
    topRewardsTotalWidth = math.min(topRewardsTotalWidth + 30, topRewardMaxWidth)
  end
  if topRewardMaxWidth > topRewardsTotalWidth then
    local space = (topRewardMaxWidth - topRewardsTotalWidth) / topSpaceNum
    space = space < 0 and 0 or space
    space = 5 < space and 5 or space
    for i = BottomRewardsMaxCount + 1, BottomRewardsMaxCount + topRewardNum do
      if self.m_rewardCells[i] ~= nil then
        local added = self.m_rewardCells[i]:AddSpace(space)
        if added then
          topRewardsTotalWidth = topRewardsTotalWidth + space
        end
      end
    end
  end
  self.m_topRewardsGo.transform.sizeDelta = Vector2(topRewardsTotalWidth, self.m_topRewardsGo.transform.sizeDelta.y)
  local selfWidth = self.transform.sizeDelta.x
  local topLeftSpace = 150
  if self.m_surpriseChest ~= nil then
    topLeftSpace = topLeftSpace + 80
  end
  if topLeftSpace > selfWidth - topRewardsTotalWidth then
    UIUtil.AddSizeDelta(self.transform, topRewardsTotalWidth + topLeftSpace - selfWidth)
  end
end

function OrderCell:GetCellWidth()
  return self.transform.sizeDelta.x
end

function OrderCell:_UpdateAvatar()
  local avatarName = "Order" .. self.m_order:GetAvatarName()
  if avatarName ~= self.m_avatarName then
    if self.m_avatarTrans ~= nil then
      AddressableLoader.Destroy(self.m_avatarTrans.gameObject)
      self.m_avatarTrans = nil
      self.m_avatar = nil
    end
    self.m_avatarName = avatarName
    GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(avatarName), self.m_avatarRoot, Vector3.zero, function(gameObject)
      self.m_avatarTrans = gameObject.transform
      self.m_avatar = gameObject:GetLuaTable()
      if self.m_avatar then
        self.m_avatar:Init(self.m_order)
        self.m_avatar:SetTransparent(false)
        if not self.m_orderArea:IsShowNewOrders() then
          self.m_avatar:PlayEnterAnimation()
        end
        self:ToggleAvatarVisible()
      end
    end)
  end
end

function OrderCell:_OnShowItemTestInfoChanged()
  if not GM.UIManager:CanShowTestUI() then
    self.m_testText.gameObject:SetActive(false)
    return
  end
  self.m_testText.gameObject:SetActive(true)
  local id = self.m_order:GetId()
  local requirementStr = ""
  for _, icon in ipairs(self.m_icons) do
    requirementStr = requirementStr .. icon:GetType() .. ", s:" .. GM.ItemDataModel:GetItemScore(icon:GetType()) .. "\n"
  end
  requirementStr = requirementStr .. "orderScore: " .. self.m_order:GetOrderScore() .. "\n"
  self.m_testText.text = "Tp:" .. self.m_order:GetType() .. " Id:" .. (id or "nil") .. "\n" .. requirementStr
end

function OrderCell:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  Scheduler.UnscheduleTarget(self)
  if self.m_buttonLoopTween ~= nil then
    self.m_buttonLoopTween:Kill()
    self.m_buttonLoopTween = nil
  end
  if self.m_buttonShowTween ~= nil then
    self.m_buttonShowTween:Kill()
    self.m_buttonShowTween = nil
  end
  if self.m_buttonHideTween ~= nil then
    self.m_buttonHideTween:Kill()
    self.m_buttonHideTween = nil
  end
end

function OrderCell:GetOrder()
  return self.m_order
end

function OrderCell:GetIconArea()
  return self.m_iconArea
end

function OrderCell:GetIcon(index)
  return self.m_icons[index]
end

function OrderCell:GetGoButton()
  return self.m_buttonTrans
end

function OrderCell:OnGoButtonClicked()
  if self.m_bGoButtonEnabled == false or self.m_orderViewState ~= OrderState.CanDeliver then
    return
  end
  self:SetGoButtonEnabled(false)
  EventDispatcher.DispatchEvent(EEventType.HideTapCacheWeakTutorial)
  GM.AudioModel:PlayEffect(AudioFileConfigName.sfxOrderServe)
  local boardModel = self.m_orderArea:GetBoardView():GetModel()
  boardModel:FinishOrder(self.m_order)
end

function OrderCell:SetGoButtonEnabled(enable)
  self.m_bGoButtonEnabled = enable
end

function OrderCell:_UpdateOrderViewState(codeCountMap)
  if self.m_order:GetState() == OrderState.Finished then
    self.m_orderViewState = OrderState.Finished
    return
  end
  local fillCount = 0
  local boardModel = self.m_orderArea:GetBoardView():GetModel()
  local requirements = self.m_order:GetRequirements()
  for i, requirement in ipairs(requirements) do
    local count = 1
    for k = i - 1, 1, -1 do
      if requirement == requirements[k] then
        count = count + 1
      end
    end
    if codeCountMap[requirement] ~= nil and count <= codeCountMap[requirement] and OrderStateHelper.TryFillOrderRequirement(boardModel, requirement, count) then
      fillCount = fillCount + 1
    end
  end
  if fillCount == #requirements then
    self.m_orderViewState = OrderState.CanDeliver
  elseif 0 < fillCount then
    self.m_orderViewState = OrderState.PartiallyFinished
  else
    self.m_orderViewState = OrderState.Init
  end
end

function OrderCell:GetOrderViewState()
  return self.m_orderViewState
end

function OrderCell:OnOrderStateChanged(codeCountMap, directDishLack)
  if self:IsPlayingAnimation() then
    return
  end
  self:_UpdateOrderViewState(codeCountMap)
  if self.m_order:GetState() == OrderState.Finished then
    self:_ChangeDeliverButton(false)
    return true
  end
  local canDeliverOrder = self.m_orderViewState == OrderState.CanDeliver
  if canDeliverOrder and not self.m_showDeliver then
    if not self.m_orderArea:IsShowNewOrders() then
      self.m_bCenterOrder = true
    end
    if GM.SceneManager:GetGameMode() == EGameMode.Board then
      GM.AudioModel:PlayEffect(AudioFileConfigName.sfxOrderComplete)
    end
  elseif not canDeliverOrder then
    self.m_bCenterOrder = nil
  end
  self:_ChangeDeliverButton(canDeliverOrder)
  self.m_starEffectGo:SetActive(canDeliverOrder)
  self.m_radianceGo:SetActive(canDeliverOrder)
  local oldValue = self.m_showDeliver
  self.m_showDeliver = canDeliverOrder
  if self.m_avatar ~= nil then
    self.m_avatar:OnOrderStateChanged()
  end
  local boardModel = self.m_orderArea:GetBoardView():GetModel()
  for _, icon in ipairs(self.m_icons) do
    icon:OnOrderStateChanged(boardModel, codeCountMap, canDeliverOrder, directDishLack)
  end
  if self.m_showDeliver ~= oldValue then
    return true
  end
end

function OrderCell:_ChangeDeliverButton(visible)
  if self.m_deliverButtonVisible == visible then
    return
  end
  self.m_deliverButtonVisible = visible
  if self.m_buttonLoopTween == nil then
    local seq = DOTween.Sequence():SetLoops(-1)
    seq:Append(self.m_buttonTrans:DOScale(0.9, 0.4):SetLoops(2, LoopType.Yoyo))
    seq:Join(self.m_buttonImage:DOColor(CSColor(0.64, 0.83, 0.72, 1), 0.4):SetLoops(2, LoopType.Yoyo))
    seq:AppendInterval(0.2)
    seq:SetAutoKill(false)
    self.m_buttonLoopTween = seq
    local seq2 = DOTween.Sequence()
    seq2:Append(self.m_buttonTrans:DOScale(1, 0.2))
    seq2:AppendInterval(0.2)
    seq2:OnComplete(function()
      self.m_buttonLoopTween:Restart()
    end)
    seq2:SetAutoKill(false)
    self.m_buttonShowTween = seq2
    local seq3 = DOTween.Sequence()
    seq3:Append(self.m_buttonTrans:DOScale(0, 0.2))
    seq3:OnComplete(function()
      self.m_buttonTrans.gameObject:SetActive(false)
    end)
    seq3:SetAutoKill(false)
    self.m_buttonHideTween = seq3
  end
  self.m_buttonLoopTween:Pause()
  self.m_buttonShowTween:Pause()
  self.m_buttonHideTween:Pause()
  self.m_buttonTrans.gameObject:SetActive(true)
  if visible then
    self.m_buttonShowTween:Restart()
  else
    self.m_buttonHideTween:Restart()
  end
end

function OrderCell:IsPlayingAnimation()
  return self.m_playingEnterAnimation or self.m_playingLeaveAnimation
end

function OrderCell:PlayEnterAnimation(withPosMove)
  self.m_playingEnterAnimation = true
  local transform = self.gameObject.transform
  transform.localScale = Vector3.zero
  local originalPos = transform.anchoredPosition
  if withPosMove then
    transform.anchoredPosition = Vector2(originalPos.x, originalPos.y * 2)
  end
  local sequence = DOTween.Sequence()
  sequence:Append(transform:DOScale(1.1, 0.2))
  if withPosMove then
    sequence:Join(transform:DOAnchorPosY(originalPos.y, 0.2))
  end
  sequence:Append(transform:DOScale(1.0, 0.1))
  sequence:AppendCallback(function()
    self.m_playingEnterAnimation = nil
    if not self.m_orderArea:IsShowNewOrders() then
      self.m_orderArea:OnOrderEnterFinish()
    end
  end)
end

function OrderCell:PlayCoinEffect()
  GM.AudioModel:PlayEffect(AudioFileConfigName.SfxMergeCollectCoins)
  Object.Instantiate(self.m_coinEffectPrefab, self.m_iconArea.transform)
end

function OrderCell:PlayLeaveAnimation(onLeaveFinish, dt)
  if self.m_bHasLeaved then
    self:_ChangeToTransparent()
  end
  self.m_bHasLeaved = true
  self.m_playingLeaveAnimation = true
  local transform = self.gameObject.transform
  if dt and dt <= 0 then
    transform.localScale = V3Zero
    self.m_playingLeaveAnimation = nil
    if onLeaveFinish then
      onLeaveFinish()
    end
    return nil
  end
  local sequence = DOTween.Sequence()
  sequence:Append(transform:DOScale(0, dt or 0.2):SetEase(Ease.OutCubic))
  sequence:OnComplete(function()
    self.m_playingLeaveAnimation = nil
    if onLeaveFinish then
      onLeaveFinish()
    end
  end)
  return sequence
end

function OrderCell:_ChangeToTransparent()
  self.transform.localScale = V3One
  self.canvasGroup.alpha = 0
  self:HideEffects()
end

function OrderCell:SetHandEffectActive(active)
  if self.m_handEffectGo:IsNull() then
    return
  end
  self.m_handEffectGo:SetActive(active)
end

function OrderCell:Update()
  if self:IsPlayingAnimation() then
    self.m_orderArea:ForceRebuildLayout()
  end
end

function OrderCell:GetCanCenterOrderAndReset()
  local value = self.m_bCenterOrder
  self.m_bCenterOrder = nil
  return value
end

function OrderCell:ToggleAvatarVisible(showAvatar)
  if showAvatar == nil then
    showAvatar = self.m_bShowAvatar
  end
  self.m_bShowAvatar = showAvatar
  if self.m_avatar == nil or self.m_bShowAvatar == nil then
    return
  end
  if self.m_avatar.gameObject.activeSelf ~= self.m_bShowAvatar then
    self.m_avatar.gameObject:SetActive(self.m_bShowAvatar)
    if self.m_bShowAvatar and self.m_orderArea:IsShowNewOrders() then
      self.m_avatar:PlayEnterAnimation()
    end
  end
end

function OrderCell:_OnFlambeTimeChanged()
  if self.m_order:GetState() == OrderState.Finished then
    return
  end
  local showFire = false
  if GM.FlambeTimeModel:IsOrderShowFire(self.m_order:GetId()) and GM.FlambeTimeModel:GetFlambeTimeType() == EFlambeTimeType.link then
    showFire = true
  end
  if self.m_flambeFire.activeSelf and not showFire then
    self.m_flambeFire:SetActive(false)
  elseif not self.m_flambeFire.activeSelf and showFire then
    self.m_flambeFire:SetActive(true)
    self.m_flambeFire.transform.localScale = V3Zero
    self.m_flambeFire.transform:DOScale(1, 0.2)
  end
end

function OrderCell:_UpdateFlambeFireWidth()
  local cellWidth = self.transform.sizeDelta.x
  local fireWidth = self.m_innerFireTrans.sizeDelta.x
  UIUtil.SetLocalScale(self.m_innerFireTrans, cellWidth / fireWidth)
end

function OrderCell:HideFire()
  self.m_flambeFire:SetActive(false)
  for _, icon in ipairs(self.m_icons) do
    icon:HideFire()
  end
end

function OrderCell:TryShowFire()
  self:_OnFlambeTimeChanged()
  for _, icon in ipairs(self.m_icons) do
    icon:TryShowFire()
  end
  self.m_orderArea:ToggleLayoutGroup(false)
  local transform = self.transform
  local seq = DOTween.Sequence()
  seq:Append(transform:DOScale(0.85, 0.08))
  seq:Append(transform:DOScale(1.12, 0.16))
  seq:Append(transform:DOScale(1, 0.08))
  seq:OnComplete(function()
    self.m_orderArea:ToggleLayoutGroup(true)
  end)
end

function OrderCell:HideEffects()
  self:HideFire()
  self.m_starEffectGo:SetActive(false)
  self.m_radianceGo:SetActive(false)
end

function OrderCell:OnSurpriseChestDataChanged(bTriggered)
  self:UpdateSurpriseChestState(bTriggered)
end

function OrderCell:CanPlaySurpriseChestRewardAnim()
  return self.m_surpriseChest and self.m_surpriseChest:CanPlayRewardAnim()
end

function OrderCell:GetSurpriseChestRewardAnimTime()
  return self.m_surpriseChest and self.m_surpriseChest:GetRewardAnimTime() or 0
end

function OrderCell:PlaySurpriseChestRewardAnim()
  if self.m_surpriseChest ~= nil then
    self.m_surpriseChest:PlayRewardAnim()
  end
end

function OrderCell:GetSurpriseChest()
  return self.m_surpriseChest
end

function OrderCell:UpdateSurpriseChestState(bTriggered)
  if self.m_order == nil or self.m_surpriseChestNode == nil then
    return
  end
  local model, activityDefinition, chestData
  for actType, actDefinition in pairs(SurpriseChestActivityDefinition) do
    model = GM.ActivityManager:GetModel(actType)
    if model ~= nil and model.IsInOpeningPeriod and model:IsInOpeningPeriod() then
      activityDefinition = actDefinition
      chestData = model.GetChestDataByOrderId and model:GetChestDataByOrderId(self.m_order:GetId())
      break
    end
  end
  if chestData ~= nil then
    if self.m_surpriseChest == nil then
      GM.ResourceLoader:LoadPrefab(GM.DataResource.UIPrefabConfig:GetConfig(activityDefinition.SurpriseChestViewPrefabName), self.m_surpriseChestNode, Vector3.zero, function(go)
        if go ~= nil and not go:IsNull() then
          UIUtil.SetActive(self.m_surpriseChestNode.gameObject, true)
          self.m_surpriseChest = go:GetLuaTable()
          self.m_surpriseChest:Init(model, chestData, self, self.m_orderArea)
          if bTriggered and GM.UIManager:GetOpenedViewByName(activityDefinition.MainWindowPrefabName) ~= nil then
            UIUtil.SetActive(self.m_surpriseChest.gameObject, false)
          end
          self:UpdateRewardDisplay()
          self.m_orderArea:ForceRebuildLayout(true)
        end
      end)
    else
      self.m_surpriseChest:Init(model, chestData, self, self.m_orderArea)
    end
  elseif chestData == nil and self.m_surpriseChest ~= nil and not self.m_order:IsFinished() then
    UIUtil.SetActive(self.m_surpriseChestNode.gameObject, false)
    GameObject.Destroy(self.m_surpriseChest.gameObject)
    self.m_surpriseChest = nil
    self:UpdateRewardDisplay()
    self.m_orderArea:ForceRebuildLayout(true)
  end
end
