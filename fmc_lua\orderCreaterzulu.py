import os
import json
import re
from tkinter import *
from tkinter import ttk, messagebox, filedialog
from collections import defaultdict
import math

class OrderEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("订单编辑器 - 浮岛物语")
        self.root.geometry("1400x900")
        
        # 修改状态标记
        self.modified = False
        
        # 初始化核心数据结构
        self.initialize_data_structures()
        
    def initialize_data_structures(self):
        """确保所有核心数据结构正确初始化"""
        self.orders = defaultdict(list)  # 使用defaultdict确保每个章节都有空列表
        self.all_orders = []
        self.items = []
        self.current_order = None
        self.loaded_chapters = set()
        
        # 添加快捷键
        self.root.bind("<Control-s>", lambda e: self.save_order())
        self.root.bind("<Control-n>", lambda e: self.add_order())
        self.root.bind("<Control-d>", lambda e: self.delete_order())
        self.root.bind("<Control-q>", lambda e: self.root.quit())
        
        # 配置路径
        self.config_path = "d:/MCWork/GHabout/FMC/fmc_lua_1.18.5/fmc_lua/Data/Config"
        self.item_config_path = os.path.join(self.config_path, "ItemModelConfig.lua")
        self.max_chapters = 16  # 最大章节数
        
        # 数据存储
        self.orders = {}  # 按章节存储订单
        self.all_orders = []  # 当前显示的订单列表
        self.items = []  # 物品类型列表
        self.current_group = 1
        self.current_chapter = 1
        self.loaded_chapters = set()  # 已加载的章节
        self.pd_types = {}  # PD类型配置
        
        # 状态变量
        self.status_var = StringVar()  # 状态栏变量
        self.energy_var = StringVar()  # 能量消耗显示
        
        # 初始化UI元素
        self.req_tree = None
        self.rew_tree = None
        self.order_tree = None
        
        # 初始化UI
        self.setup_ui()
        
        # 初始化方法定义
        self._load_pd_types = self._create_pd_loader()
        self.load_item_config = self._create_item_loader()
        self.load_order_config = self._create_order_loader()
        
        # 加载数据
        self.load_item_config()
        # 加载所有章节 (1-16)
        for chapter in range(1, 17):
            self.load_order_config(chapter)
        
        # 加载PD类型
        self.pd_types = self._load_pd_types()
        if hasattr(self, 'pd_type_combo') and self.pd_types:
            pd_values = sorted(self.pd_types.keys())
            self.pd_type_combo['values'] = pd_values
            if pd_values:
                self.pd_type_combo.set(pd_values[0])
        
    def setup_ui(self):
        # 添加快捷键提示和帮助按钮
        shortcut_frame = ttk.Frame(self.root, padding="5")
        shortcut_frame.pack(fill=X)
        ttk.Label(shortcut_frame, 
                 text="快捷键: Ctrl+S保存 | Ctrl+N新建 | Ctrl+D删除 | Ctrl+Q退出",
                 font=('Arial', 8)).pack(side=LEFT)
        
        # 添加帮助按钮
        ttk.Button(shortcut_frame, 
                  text="帮助", 
                  command=self.show_help,
                  width=8).pack(side=RIGHT, padx=5)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=BOTH, expand=True)
        
        # 左侧 - 章节和订单列表
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=LEFT, fill=Y, padx=5, pady=5)
        
        # 章节选择
        chapter_frame = ttk.LabelFrame(left_frame, text="章节选择", padding="5")
        chapter_frame.pack(fill=X, pady=5)
        
        self.chapter_var = IntVar(value=self.current_chapter)
        
        # 创建两行按钮，每行8个
        for row in range(2):
            btn_frame = ttk.Frame(chapter_frame)
            btn_frame.pack(fill=X, pady=2)
            for col in range(8):
                chapter = row * 8 + col + 1
                if chapter > self.max_chapters:
                    break
                ttk.Radiobutton(
                    btn_frame, 
                    text=f"第{chapter}章", 
                    variable=self.chapter_var, 
                    value=chapter,
                    command=self.on_chapter_change_radio
                ).pack(side=LEFT, padx=2, expand=True, fill=X)
        
        # 订单列表
        list_frame = ttk.LabelFrame(left_frame, text="订单列表", padding="5")
        list_frame.pack(fill=BOTH, expand=True, pady=5)
        
        # 添加搜索框
        search_frame = ttk.Frame(list_frame)
        search_frame.pack(fill=X, pady=(0, 5))
        
        ttk.Label(search_frame, text="搜索:").pack(side=LEFT, padx=2)
        self.search_var = StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side=LEFT, fill=X, expand=True, padx=2)
        search_entry.bind("<KeyRelease>", self.on_search)
        
        # 订单表格
        self.order_tree = ttk.Treeview(
            list_frame, 
            columns=("ID", "组", "需求", "奖励"), 
            show="headings",
            selectmode="browse"
        )
        self.order_tree.heading("ID", text="订单ID")
        self.order_tree.heading("组", text="组ID")
        self.order_tree.heading("需求", text="需求物品")
        self.order_tree.heading("奖励", text="奖励")
        
        self.order_tree.column("ID", width=80)
        self.order_tree.column("组", width=50)
        self.order_tree.column("需求", width=250)
        self.order_tree.column("奖励", width=200)
        
        scrollbar = ttk.Scrollbar(list_frame, orient=VERTICAL, command=self.order_tree.yview)
        self.order_tree.configure(yscrollcommand=scrollbar.set)
        
        self.order_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)
        
        # 添加右键菜单
        self.order_menu = Menu(self.root, tearoff=0)
        self.order_menu.add_command(label="复制订单ID", command=self.copy_order_id)
        self.order_menu.add_separator()
        self.order_menu.add_command(label="删除订单", command=self.delete_order)
        
        self.order_tree.bind("<Button-3>", self.show_order_context_menu)
        self.order_tree.bind("<<TreeviewSelect>>", self.on_order_select)
        
        # 右侧 - 订单详情
        detail_frame = ttk.LabelFrame(main_frame, text="订单详情", padding="10")
        detail_frame.pack(side=RIGHT, fill=BOTH, expand=True, padx=5, pady=5)
        
        # 基本信息
        info_frame = ttk.Frame(detail_frame)
        info_frame.pack(fill=X, pady=5)
        
        ttk.Label(info_frame, text="订单ID:").grid(row=0, column=0, sticky=W)
        self.order_id_var = StringVar()
        ttk.Entry(info_frame, textvariable=self.order_id_var, state='readonly').grid(row=0, column=1, sticky=W)
        
        ttk.Label(info_frame, text="组ID:").grid(row=0, column=2, sticky=W, padx=10)
        self.group_id_var = StringVar()
        ttk.Spinbox(info_frame, from_=1, to=20, textvariable=self.group_id_var, width=5).grid(row=0, column=3, sticky=W)
        
        ttk.Label(info_frame, text="章节:").grid(row=0, column=4, sticky=W, padx=10)
        self.chapter_var = StringVar(value=str(self.current_chapter))
        chapter_spinbox = ttk.Spinbox(
            info_frame, 
            from_=1, 
            to=self.max_chapters, 
            textvariable=self.chapter_var, 
            width=5,
            command=lambda: self.on_chapter_change(int(self.chapter_var.get()))
        )
        chapter_spinbox.grid(row=0, column=5, sticky=W)
        
        # 初始化需求和奖励UI
        self.setup_ui_requirements(detail_frame)
        self.setup_ui_rewards(detail_frame)
        
        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=X, side=BOTTOM, padx=5, pady=5)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=LEFT)
        
        # 初始化状态
        self.status_var.set(f"准备就绪 - 第{self.current_chapter}章")
        
    def setup_ui_requirements(self, detail_frame):
        # 需求物品
        req_frame = ttk.LabelFrame(detail_frame, text="需求物品", padding="5")
        req_frame.pack(fill=BOTH, expand=True, pady=5)
        
        # 需求物品树形视图
        self.req_tree = ttk.Treeview(req_frame, columns=("类型", "数量", "1级等效", "能量"), show="headings")
        self.req_tree.heading("类型", text="物品类型")
        self.req_tree.heading("数量", text="数量")
        self.req_tree.heading("1级等效", text="1级等效")
        self.req_tree.heading("能量", text="能量")
        self.req_tree.column("类型", width=150)
        self.req_tree.column("数量", width=50)
        self.req_tree.column("1级等效", width=80)
        self.req_tree.column("能量", width=80)
        self.req_tree.pack(fill=BOTH, expand=True)
        
        # 需求物品操作按钮
        req_btn_frame = ttk.Frame(req_frame)
        req_btn_frame.pack(fill=X, pady=5)
        
        self.req_type_var = StringVar()
        self.req_count_var = StringVar(value="1")
        
        ttk.Label(req_btn_frame, text="类型:").pack(side=LEFT, padx=2)
        self.req_type_combo = ttk.Combobox(req_btn_frame, textvariable=self.req_type_var, width=30)
        if hasattr(self, 'items'):
            self.req_type_combo['values'] = sorted(self.items)
        self.req_type_combo.pack(side=LEFT, padx=2, fill=X, expand=True)
        
        ttk.Label(req_btn_frame, text="数量:").pack(side=LEFT, padx=2)
        ttk.Entry(req_btn_frame, textvariable=self.req_count_var, width=5).pack(side=LEFT, padx=2)
        ttk.Button(req_btn_frame, text="添加", command=self.add_requirement).pack(side=LEFT, padx=2)
        ttk.Button(req_btn_frame, text="删除", command=self.delete_requirement).pack(side=LEFT, padx=2)
    
    def on_chapter_change_radio(self):
        """Handle chapter change from radio buttons"""
        try:
            new_chapter = self.chapter_var.get()
            self.on_chapter_change(new_chapter)
        except Exception as e:
            print(f"Error in on_chapter_change_radio: {e}")
    
    def on_chapter_change(self, new_chapter):
        """Handle chapter change event with improved order display"""
        try:
            # 验证章节号
            new_chapter = int(new_chapter)
            if not (1 <= new_chapter <= self.max_chapters):
                raise ValueError(f"无效的章节号: {new_chapter}")
                
            if new_chapter == self.current_chapter:
                return
                
            # 保存当前选中的订单ID（如果有）
            selected_id = None
            if hasattr(self, 'order_tree') and self.order_tree.selection():
                selected_id = self.order_tree.item(self.order_tree.selection()[0])['values'][0]
                
            # 更新当前章节
            self.current_chapter = new_chapter
            self.chapter_var.set(str(new_chapter))
            
            # 确保章节已加载
            if new_chapter not in self.loaded_chapters:
                success = self.load_order_config(new_chapter)
                if not success:
                    raise RuntimeError(f"加载第{new_chapter}章订单失败")
                self.loaded_chapters.add(new_chapter)
                
            # 获取当前章节订单，确保数据结构正确
            chapter_orders = self.orders.get(new_chapter, [])
            if not isinstance(chapter_orders, list):
                print(f"警告: 第{new_chapter}章订单不是列表类型，已重置")
                self.orders[new_chapter] = []
                chapter_orders = []
                
            # 更新当前显示的订单列表
            self.all_orders = chapter_orders
            
            # 更新订单列表UI
            self.update_order_list()
            
            # 尝试恢复之前选中的订单
            if selected_id:
                for item in self.order_tree.get_children():
                    if self.order_tree.item(item)['values'][0] == selected_id:
                        self.order_tree.selection_set(item)
                        self.order_tree.focus(item)
                        self.on_order_select(None)
                        break
            
            # 更新状态栏
            order_count = len(chapter_orders)
            status_text = f"第{new_chapter}章 - 共 {order_count} 个订单"
            if order_count == 0:
                status_text += " (空章节)"
            self.status_var.set(status_text)
            
            # 强制UI更新
            self.root.update_idletasks()
            
        except ValueError as ve:
            error_msg = f"无效的章节号: {ve}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
        except Exception as e:
            error_msg = f"切换章节失败: {str(e)}"
            print(f"章节切换错误: {error_msg}")
            import traceback
            traceback.print_exc()
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
            messagebox.showerror("错误", error_msg)
        except Exception as e:
            error_msg = f"切换章节失败: {str(e)}"
            print(f"Error in on_chapter_change: {error_msg}")
            import traceback
            traceback.print_exc()
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def clear_order_details(self):
        """Clear order details section"""
        if hasattr(self, 'order_id_var'):
            self.order_id_var.set("")
            self.group_id_var.set("1")
            self.energy_var.set("")
        if hasattr(self, 'req_tree') and self.req_tree:
            for item in self.req_tree.get_children():
                self.req_tree.delete(item)
        if hasattr(self, 'rew_tree') and self.rew_tree:
            for item in self.rew_tree.get_children():
                self.rew_tree.delete(item)
                
    def setup_ui_rewards(self, detail_frame):
        """Setup the rewards section of the UI"""
        # 奖励框架
        reward_frame = ttk.LabelFrame(detail_frame, text="奖励", padding="5")
        reward_frame.pack(fill=BOTH, expand=True, pady=5)
        
        # 奖励树形视图
        self.rew_tree = ttk.Treeview(reward_frame, columns=("类型", "数量"), show="headings")
        self.rew_tree.heading("类型", text="奖励类型")
        self.rew_tree.heading("数量", text="数量")
        self.rew_tree.column("类型", width=150)
        self.rew_tree.column("数量", width=80)
        self.rew_tree.pack(fill=BOTH, expand=True)
        
        # 奖励操作按钮
        rew_btn_frame = ttk.Frame(reward_frame)
        rew_btn_frame.pack(fill=X, pady=5)
        
        self.rew_type_var = StringVar()
        self.rew_count_var = StringVar(value="1")
        
        ttk.Label(rew_btn_frame, text="类型:").pack(side=LEFT, padx=2)
        self.rew_type_combo = ttk.Combobox(rew_btn_frame, textvariable=self.rew_type_var, width=30)
        if hasattr(self, 'items'):
            self.rew_type_combo['values'] = sorted(self.items)
        self.rew_type_combo.pack(side=LEFT, padx=2, fill=X, expand=True)
        
        ttk.Label(rew_btn_frame, text="数量:").pack(side=LEFT, padx=2)
        ttk.Entry(rew_btn_frame, textvariable=self.rew_count_var, width=5).pack(side=LEFT, padx=2)
        ttk.Button(rew_btn_frame, text="添加", command=self.add_reward).pack(side=LEFT, padx=2)
        ttk.Button(rew_btn_frame, text="删除", command=self.delete_reward).pack(side=LEFT, padx=2)
        
        # 能量消耗显示
        energy_frame = ttk.Frame(detail_frame)
        energy_frame.pack(fill=X, pady=5)
        ttk.Label(energy_frame, text="总能量消耗:").pack(side=LEFT, padx=5)
        ttk.Label(energy_frame, textvariable=self.energy_var, foreground="blue").pack(side=LEFT)
        
        # 操作按钮
        btn_frame = ttk.Frame(detail_frame)
        btn_frame.pack(fill=X, pady=5)
        ttk.Button(btn_frame, text="保存订单", command=self.save_order).pack(side=LEFT, padx=5)
        ttk.Button(btn_frame, text="新增订单", command=self.add_order).pack(side=LEFT, padx=5)
        
        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=X, side=BOTTOM, padx=5, pady=5)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=LEFT)
        
        # 定义可能的奖励类型
        self.reward_types = [
            "Gold", "Gem", "Exp", "Energy", "Stamina",
            "Diamond", "Star", "Coin", "Key", "Ticket"
        ]
        
        ttk.Label(rew_btn_frame, text="类型:").pack(side=LEFT, padx=2)
        self.rew_type_combo = ttk.Combobox(
            rew_btn_frame, 
            textvariable=self.rew_type_var, 
            values=self.reward_types,
            width=15
        )
        self.rew_type_combo.pack(side=LEFT, padx=2)
        if self.reward_types:
            self.rew_type_combo.set(self.reward_types[0])
        
        ttk.Label(rew_btn_frame, text="数量:").pack(side=LEFT, padx=2)
        ttk.Spinbox(rew_btn_frame, from_=1, to=9999, width=5, textvariable=self.rew_count_var).pack(side=LEFT, padx=2)
        
        ttk.Button(rew_btn_frame, text="添加奖励", command=self.add_reward).pack(side=LEFT, padx=5)
        ttk.Button(rew_btn_frame, text="删除奖励", command=self.delete_reward).pack(side=LEFT, padx=5)
        
        # PD配置区域
        pd_frame = ttk.LabelFrame(detail_frame, text="生产者配置")
        pd_frame.pack(fill=X, padx=5, pady=5)
        
        # 加载PD类型
        self.pd_types = self._load_pd_types()
        
        ttk.Label(pd_frame, text="PD类型:").grid(row=0, column=0, padx=2, pady=2, sticky="e")
        self.pd_type_var = StringVar()
        self.pd_type_combo = ttk.Combobox(pd_frame, textvariable=self.pd_type_var, state="readonly", width=20)
        self.pd_type_combo['values'] = sorted(self.pd_types.keys())
        self.pd_type_combo.grid(row=0, column=1, padx=2, pady=2, sticky="w")
        
        ttk.Label(pd_frame, text="PD等级:").grid(row=0, column=2, padx=2, pady=2, sticky="e")
        self.pd_level_var = IntVar(value=1)
        self.pd_level_spin = ttk.Spinbox(pd_frame, from_=1, to=20, textvariable=self.pd_level_var, width=5)
        self.pd_level_spin.grid(row=0, column=3, padx=2, pady=2, sticky="w")
        
        ttk.Label(pd_frame, text="数量:").grid(row=0, column=4, padx=2, pady=2, sticky="e")
        self.pd_count_var = IntVar(value=1)
        self.pd_count_spin = ttk.Spinbox(pd_frame, from_=1, to=100, textvariable=self.pd_count_var, width=5)
        self.pd_count_spin.grid(row=0, column=5, padx=2, pady=2, sticky="w")
        
        ttk.Button(pd_frame, text="计算能量", command=self.calculate_pd_energy).grid(row=0, column=6, padx=5, pady=2)
        
        # 能量显示
        self.energy_frame = ttk.LabelFrame(detail_frame, text="能量消耗")
        self.energy_frame.pack(fill=X, padx=5, pady=5)
        
        self.energy_var = StringVar(value="总能量消耗: 0")
        ttk.Label(self.energy_frame, textvariable=self.energy_var, font=('Arial', 10, 'bold')).pack(pady=5)
        
        # PD配置区域
        pd_frame = ttk.LabelFrame(detail_frame, text="生产者配置")
        pd_frame.pack(fill=X, padx=5, pady=5)
        
        # 加载PD类型
        self.pd_types = self._load_pd_types()
        
        ttk.Label(pd_frame, text="PD类型:").grid(row=0, column=0, padx=2, pady=2, sticky="e")
        self.pd_type_var = StringVar()
        self.pd_type_combo = ttk.Combobox(pd_frame, textvariable=self.pd_type_var, state="readonly", width=20)
        self.pd_type_combo['values'] = sorted(self.pd_types.keys())
        self.pd_type_combo.grid(row=0, column=1, padx=2, pady=2, sticky="w")
        
        ttk.Label(pd_frame, text="PD等级:").grid(row=0, column=2, padx=2, pady=2, sticky="e")
        self.pd_level_var = IntVar(value=1)
        self.pd_level_spin = ttk.Spinbox(pd_frame, from_=1, to=20, textvariable=self.pd_level_var, width=5)
        self.pd_level_spin.grid(row=0, column=3, padx=2, pady=2, sticky="w")
        
        ttk.Label(pd_frame, text="数量:").grid(row=0, column=4, padx=2, pady=2, sticky="e")
        self.pd_count_var = IntVar(value=1)
        self.pd_count_spin = ttk.Spinbox(pd_frame, from_=1, to=100, textvariable=self.pd_count_var, width=5)
        self.pd_count_spin.grid(row=0, column=5, padx=2, pady=2, sticky="w")
        
        ttk.Button(pd_frame, text="计算能量", command=self.calculate_pd_energy).grid(row=0, column=6, padx=5, pady=2)
        
        # 能量显示
        self.energy_frame = ttk.LabelFrame(detail_frame, text="能量消耗")
        self.energy_frame.pack(fill=X, padx=5, pady=5)
        
        self.energy_var = StringVar(value="总能量消耗: 0")
        ttk.Label(self.energy_frame, textvariable=self.energy_var, font=('Arial', 10, 'bold')).pack(pady=5)
        
        # 底部状态栏
        self.status_var = StringVar()
        self.status_var.set("就绪")
        ttk.Label(self.root, textvariable=self.status_var, relief=SUNKEN, anchor=W).pack(side=BOTTOM, fill=X)
    
    def on_chapter_change(self, chapter=None):
        """Handle chapter change event"""
        try:
            # 获取章节号
            if chapter is None:
                chapter = int(self.chapter_var.get())
            else:
                chapter = int(chapter)
                
            # 验证章节号
            if not (1 <= chapter <= self.max_chapters):
                raise ValueError(f"无效的章节号: {chapter}")
                
            if chapter != self.current_chapter:
                # 更新当前章节
                self.current_chapter = chapter
                self.chapter_var.set(str(chapter))
                
                # 清空当前选择和详情
                self.clear_order_details()
                
                # 确保章节已加载
                if chapter not in self.loaded_chapters:
                    self.load_order_config(chapter)
                    self.loaded_chapters.add(chapter)
                
                # 获取当前章节订单，确保是列表
                chapter_orders = self.orders.get(chapter, [])
                if not isinstance(chapter_orders, list):
                    self.orders[chapter] = []
                    chapter_orders = []
                
                # 更新当前显示的订单列表
                self.all_orders = chapter_orders
                
                # 清空当前订单
                self.current_order = None
                
                # 更新订单列表UI
                self.update_order_list()
                
                # 更新状态
                self.status_var.set(f"已加载第 {chapter} 章订单 (共 {len(self.all_orders)} 个)")
                
                # 强制UI更新
                self.root.update()
                
        except ValueError as ve:
            error_msg = f"无效的章节号: {ve}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
        except Exception as e:
            error_msg = f"切换章节失败: {str(e)}"
            print(f"Error in on_chapter_change: {error_msg}")
            import traceback
            traceback.print_exc()
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def on_search(self, event=None):
        """Search orders based on the search query"""
        query = self.search_var.get().lower()
        
        # Clear current selection
        self.order_tree.selection_remove(self.order_tree.selection())
        
        # If search is empty, show all orders
        if not query:
            for item in self.order_tree.get_children():
                self.order_tree.item(item, tags=())
            return
        
        # Search through orders
        found = False
        for item in self.order_tree.get_children():
            values = self.order_tree.item(item, 'values')
            if any(query in str(value).lower() for value in values):
                self.order_tree.item(item, tags=('match',))
                found = True
            else:
                self.order_tree.item(item, tags=())
        
        if not found:
            self.status_var.set("未找到匹配的订单")
    
    def show_order_context_menu(self, event):
        """Show context menu for order list"""
        item = self.order_tree.identify_row(event.y)
        if item:
            self.order_tree.selection_set(item)
            self.order_menu.post(event.x_root, event.y_root)
    
    def copy_order_id(self):
        """Copy selected order ID to clipboard"""
        selection = self.order_tree.selection()
        if selection:
            order_id = self.order_tree.item(selection[0], 'values')[0]
            self.root.clipboard_clear()
            self.root.clipboard_append(order_id)
            self.status_var.set(f"已复制订单ID: {order_id}")
    
    def on_order_select(self, event):
        """Handle order selection"""
        selection = self.order_tree.selection()
        if not selection:
            return
            
        order_id = self.order_tree.item(selection[0], 'values')[0]
        chapter = self.chapter_var.get()
        
        # Find the selected order
        self.current_order = None
        for order in self.orders.get(chapter, []):
            if order["Id"] == order_id:
                self.current_order = order
                break
                
        if not self.current_order:
            return
            
        # Update UI with order details
        self.order_id_var.set(order_id)
        self.group_id_var.set(self.current_order.get("GroupId", 1))
        
        # Update requirements
        self.update_requirements_list()
        
        # Update rewards
        self.update_rewards_list()
    
    def update_requirements_list(self, requirements=None):
        """Update requirements treeview with current order's requirements"""
        if not hasattr(self, 'req_tree') or self.req_tree is None:
            return
            
        # Clear existing items
        for item in self.req_tree.get_children():
            self.req_tree.delete(item)
            
        if not self.current_order:
            return
            
        # Use provided requirements or get from current order
        reqs = requirements if requirements is not None else self.current_order.get("Requirements", [])
        
        for req in reqs:
            if not isinstance(req, dict):
                continue
                
            item_type = req.get("Type", "")
            count = req.get("Count", 0)
            
            # Calculate level 1 equivalent and energy if needed
            level1_equiv = self.calculate_level1_equivalent(item_type, count) if hasattr(self, 'calculate_level1_equivalent') else ""
            energy = self.calculate_energy(item_type, count) if hasattr(self, 'calculate_energy') else ""
            
            self.req_tree.insert("", "end", values=(
                item_type,
                count,
                level1_equiv,
                f"{energy:.1f} 能量" if isinstance(energy, (int, float)) else energy
            ))
    
    def update_rewards_list(self):
        """Update rewards treeview with current order's rewards"""
        # Clear existing items
        for item in self.reward_tree.get_children():
            self.reward_tree.delete(item)
            
        if not self.current_order or "Rewards" not in self.current_order:
            return
            
        for reward in self.current_order["Rewards"]:
            self.reward_tree.insert("", "end", values=(
                reward.get("Currency", ""),
                reward.get("Amount", 1)
            ))
    
    def add_requirement(self):
        """添加需求物品"""
        try:
            # 获取选中的订单
            selected = self.order_tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择一个订单")
                return
                
            # 验证当前章节
            if not hasattr(self, 'current_chapter'):
                raise ValueError("当前章节未设置")
                
            # 获取物品类型和数量
            item_type = self.req_type_var.get().strip()
            if not item_type:
                messagebox.showwarning("警告", "请选择物品类型")
                return
                
            # 验证数量
            try:
                count = int(self.req_count_var.get())
                if count <= 0:
                    raise ValueError("数量必须大于0")
            except ValueError as ve:
                messagebox.showerror("错误", f"无效的数量: {ve}")
                return
            
            # 获取当前选中的订单
            order_id = self.order_tree.item(selected[0])['values'][0]
            
            # 确保当前章节订单存在且是列表
            if self.current_chapter not in self.orders:
                self.orders[self.current_chapter] = []
            current_chapter_orders = self.orders[self.current_chapter]
            
            if not isinstance(current_chapter_orders, list):
                raise TypeError("当前章节订单不是列表类型")
                
            # 查找订单
            order = None
            for o in current_chapter_orders:
                if not isinstance(o, dict):
                    continue
                if "Id" not in o:
                    continue
                if str(o["Id"]) == str(order_id):
                    order = o
                    break
                    
            if not order:
                messagebox.showerror("错误", f"未找到ID为{order_id}的订单")
                return
                
            # 初始化需求列表（如果不存在）
            if "Requirements" not in order:
                order["Requirements"] = []
            elif not isinstance(order["Requirements"], list):
                order["Requirements"] = []
            
            # 检查是否已存在相同类型的物品
            requirement_added = False
            for req in order["Requirements"]:
                if not isinstance(req, dict):
                    continue
                    
                if req.get("Type") == item_type:
                    req["Count"] = req.get("Count", 0) + count
                    requirement_added = True
                    break
            
            # 如果不存在相同类型，则添加新需求
            if not requirement_added:
                order["Requirements"].append({
                    "Type": item_type,
                    "Count": count
                })
            
            # 更新UI
            self.current_order = order
            self.update_requirements_list()
            self.status_var.set(f"已添加需求: {item_type} x{count}")
            
            # 标记为已修改
            self.modified = True
            self.root.title("订单编辑器 - 浮岛物语 (*)")
            
        except Exception as e:
            messagebox.showerror("错误", f"添加需求失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def delete_requirement(self):
        """Delete selected requirement from current order"""
        if not hasattr(self, 'current_order') or "Requirements" not in self.current_order:
            return
            
        selection = self.req_tree.selection()
        if not selection:
            return
            
        # Get the index of the selected requirement
        selected_item = selection[0]
        index = self.req_tree.index(selected_item)
        
        # Remove the requirement
        if 0 <= index < len(self.current_order["Requirements"]):
            req = self.current_order["Requirements"][index]
            if messagebox.askyesno("确认删除", f"确定要删除需求 {req['Type']} x{req['Count']} 吗？"):
                del self.current_order["Requirements"][index]
                self.update_requirements_list()
                self.status_var.set("需求已删除")
    
    def add_reward(self):
        """Add a new reward to the current order"""
        if not hasattr(self, 'current_order'):
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        reward_type = self.reward_type_var.get()
        try:
            amount = int(self.reward_amount_var.get())
            if amount <= 0:
                raise ValueError("数量必须大于0")
        except ValueError as e:
            messagebox.showerror("错误", f"无效的数量: {e}")
            return
            
        if not reward_type:
            messagebox.showwarning("警告", "请选择奖励类型")
            return
            
        # Initialize rewards list if it doesn't exist
        if "Rewards" not in self.current_order:
            self.current_order["Rewards"] = []
            
        # Add new reward
        self.current_order["Rewards"].append({
            "Currency": reward_type,
            "Amount": amount
        })
        
        # Update UI
        self.update_rewards_list()
        self.status_var.set(f"已添加奖励: {reward_type} x{amount}")
    
    def delete_reward(self):
        """Delete selected reward from current order"""
        if not hasattr(self, 'current_order') or "Rewards" not in self.current_order:
            return
            
        selection = self.reward_tree.selection()
        if not selection:
            return
            
        # Get the index of the selected reward
        selected_item = selection[0]
        index = self.reward_tree.index(selected_item)
        
        # Remove the reward
        if 0 <= index < len(self.current_order["Rewards"]):
            reward = self.current_order["Rewards"][index]
            if messagebox.askyesno("确认删除", f"确定要删除奖励 {reward['Currency']} x{reward['Amount']} 吗？"):
                del self.current_order["Rewards"][index]
                self.update_rewards_list()
                self.status_var.set("奖励已删除")
    
    def save_order(self):
        """Save the current order"""
        if not hasattr(self, 'current_order'):
            messagebox.showwarning("警告", "没有可保存的订单")
            return
            
        try:
            # Update order details
            self.current_order["GroupId"] = int(self.group_id_var.get())
            
            # Save to the appropriate chapter
            chapter = self.chapter_var.get()
            if chapter not in self.orders:
                self.orders[chapter] = []
                
            # Check if this is a new order or existing one
            order_id = self.order_id_var.get()
            order_exists = False
            
            for i, order in enumerate(self.orders[chapter]):
                if order["Id"] == order_id:
                    # Update existing order
                    self.orders[chapter][i] = self.current_order
                    order_exists = True
                    break
                    
            if not order_exists:
                # Add new order
                self.orders[chapter].append(self.current_order)
            
            # Update the order list
            self.update_order_list()
            self.status_var.set(f"订单已保存: {order_id}")
            
        except Exception as e:
            messagebox.showerror("保存错误", f"保存订单时出错: {str(e)}")
    
    def add_order(self):
        """Add a new order with complete validation"""
        try:
            # 获取并验证章节号
            chapter = int(self.chapter_var.get())
            if not (1 <= chapter <= self.max_chapters):
                raise ValueError(f"无效的章节号: {chapter}")
                
            # 确保章节订单列表存在且是列表
            if chapter not in self.orders or not isinstance(self.orders[chapter], list):
                self.orders[chapter] = []
                
            # 查找下一个可用订单ID
            used_ids = set()
            for order in self.orders[chapter]:
                try:
                    if isinstance(order, dict) and "Id" in order:
                        used_ids.add(int(order["Id"]))
                except (ValueError, TypeError):
                    continue
                    
            new_id = 1
            while new_id in used_ids:
                new_id += 1
                
            # 创建新订单，确保所有必需字段
            self.current_order = {
                "Id": str(new_id),
                "ChapterId": chapter,
                "GroupId": 1,
                "Requirements": [],
                "Rewards": [],
                "CreatedAt": datetime.datetime.now().isoformat()
            }
            
            # 更新UI
            self.order_id_var.set(str(new_id))
            self.group_id_var.set(1)
            self.update_requirements_list([])
            self.update_rewards_list([])
            
            # 添加到订单列表
            self.orders[chapter].append(self.current_order)
            self.all_orders = self.orders[chapter]
            
            # 更新UI并滚动到新订单
            self.update_order_list()
            for child in self.order_tree.get_children():
                if self.order_tree.item(child, 'values')[0] == str(new_id):
                    self.order_tree.selection_set(child)
                    self.order_tree.focus(child)
                    self.order_tree.see(child)
                    break
                    
            # 更新状态
            self.status_var.set(f"已创建新订单: {new_id}")
            self.modified = True
            self.root.title("订单编辑器 - 浮岛物语 (*)")
            
            # 记录操作日志
            print(f"新增订单: 章节{chapter} ID{new_id}")
            
        except ValueError as ve:
            error_msg = f"创建订单失败: {str(ve)}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
        except Exception as e:
            error_msg = f"创建订单失败: {str(e)}"
            self.status_var.set(error_msg)
            messagebox.showerror("错误", error_msg)
            import traceback
            traceback.print_exc()
    
    def delete_order(self):
        """Delete the current order"""
        if not hasattr(self, 'current_order'):
            return
            
        order_id = self.order_id_var.get()
        if not messagebox.askyesno("确认删除", f"确定要删除订单 {order_id} 吗？此操作不可恢复！"):
            return
            
        chapter = self.chapter_var.get()
        if chapter in self.orders:
            self.orders[chapter] = [o for o in self.orders[chapter] if o["Id"] != order_id]
            
        # Clear current order
        if hasattr(self, 'current_order'):
            del self.current_order
            
        # Update UI
        self.order_id_var.set("")
        self.group_id_var.set(1)
        self.update_requirements_list()
        self.update_rewards_list()
        self.update_order_list()
        self.status_var.set(f"已删除订单: {order_id}")
    
    def update_order_list(self):
        """Update the order list in the UI"""
        if not hasattr(self, 'order_tree') or self.order_tree is None:
            return
            
        try:
            # 保存当前选中的订单ID
            selected = self.order_tree.selection()
            selected_id = self.order_tree.item(selected[0])['values'][0] if selected else None
            
            # 清空当前列表
            for item in self.order_tree.get_children():
                self.order_tree.delete(item)
                
            # 确保all_orders是一个列表
            if not isinstance(self.all_orders, list):
                self.all_orders = []
                
            # 按ID排序订单
            sorted_orders = sorted(
                (o for o in self.all_orders if isinstance(o, dict)),
                key=lambda x: int(x.get('Id', 0))
            )
            
            # 添加订单到列表
            for order in sorted_orders:
                if not isinstance(order, dict):
                    continue
                    
                # 格式化需求
                reqs = order.get('Requirements', [])
                req_text = ", ".join(
                    f"{req['Type']}x{req['Count']}" 
                    for req in reqs 
                    if isinstance(req, dict) and 'Type' in req and 'Count' in req
                )
                
                # 格式化奖励
                rews = order.get('Rewards', [])
                rew_text = ", ".join(
                    f"{rew['Currency']}x{rew['Amount']}" 
                    for rew in rews 
                    if isinstance(rew, dict) and 'Currency' in rew and 'Amount' in rew
                )
                
                # 添加订单到树形视图
                item_id = self.order_tree.insert('', 'end', values=(
                    str(order.get('Id', '')),
                    str(order.get('GroupId', '')),
                    req_text,
                    rew_text
                ))
                
                # 如果这是之前选中的订单，重新选择它
                if selected_id is not None and str(order.get('Id', '')) == str(selected_id):
                    self.order_tree.selection_set(item_id)
                    
                    # 更新当前订单
                    self.current_order = order
                    
                    # 更新需求列表
                    self.update_requirements_list(order.get('Requirements', []))
                    
                    # 更新奖励列表
                    self.update_rewards_list()
            
            # 更新状态栏
            if hasattr(self, 'status_var'):
                self.status_var.set(f"已加载 {len(sorted_orders)} 个订单 (第{self.current_chapter}章)")
                
            # 如果没有选择任何订单，清空详情区域
            if not selected_id and hasattr(self, 'order_id_var'):
                self.clear_order_details()
                
        except Exception as e:
            error_msg = f"更新订单列表失败: {str(e)}"
            print(f"Error in update_order_list: {error_msg}")
            import traceback
            traceback.print_exc()
            if hasattr(self, 'status_var'):
                self.status_var.set(error_msg)
    
    def _load_pd_types(self):
        """加载PD类型配置"""
        pd_types = {}
        try:
            with open(self.item_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 匹配所有PD配置
            pd_blocks = re.findall(r'\{\s*Type\s*=\s*"(pd_\d+_\d+)"(.+?)(?=\}\s*\n\s*\})', content, re.DOTALL)
            
            for pd_type, pd_data in pd_blocks:
                # 提取PD名称
                name_match = re.search(r'Name\s*=\s*"([^"]+)"', pd_data)
                name = name_match.group(1) if name_match else pd_type
                pd_types[pd_type] = name
                
            return pd_types
            
        except Exception as e:
            messagebox.showerror("错误", f"加载PD类型配置失败: {str(e)}")
            return {}

    def calculate_pd_energy(self):
        """计算PD能量消耗"""
        try:
            pd_type = self.pd_type_var.get()
            pd_level = self.pd_level_var.get()
            pd_count = self.pd_count_var.get()
            
            if not pd_type:
                messagebox.showwarning("警告", "请选择PD类型")
                return
                
            # 检查PD数量有效性
            try:
                pd_count = int(pd_count)
                if pd_count <= 0:
                    raise ValueError("PD数量必须大于0")
            except ValueError as e:
                messagebox.showerror("错误", f"无效的PD数量: {str(e)}")
                return
                
            # 获取当前订单的需求物品
            requirements = []
            for item in self.req_tree.get_children():
                values = self.req_tree.item(item, 'values')
                if len(values) >= 2:
                    item_type = values[0]
                    count = int(values[1])
                    requirements.append((item_type, count))
            
            if not requirements:
                messagebox.showinfo("提示", "当前订单没有需求物品")
                return
                
            # 计算总能量消耗
            total_energy = 0
            for item_type, count in requirements:
                energy = self.calculate_energy(item_type, count, pd_type, pd_level)
                total_energy += energy
            
            # 考虑PD数量
            total_energy = total_energy / pd_count if pd_count > 0 else 0
            
            # 更新UI
            self.energy_var.set(f"总能量消耗: {total_energy:.2f} (PD: {pd_type} Lv{pd_level} x{pd_count})")
            
        except Exception as e:
            messagebox.showerror("错误", f"计算能量消耗失败: {str(e)}")
            
    def calculate_energy(self, item_type, count, pd_type=None, pd_level=1):
        """
        计算物品的能量消耗
        
        Args:
            item_type: 物品类型
            count: 物品数量
            pd_type: PD类型
            pd_level: PD等级
            
        Returns:
            float: 能量消耗
        """
        # 能量计算配置
        ENERGY_CONFIG = {
            # 基础能量值
            'base_energy': 10,
            # 物品等级系数
            'item_level_factor': 1.2,
            # PD类型系数
            'pd_type_factors': {
                'pd_1_': 1.0,  # 基础PD
                'pd_2_': 1.2,  # 高级PD
                'pd_3_': 1.5   # 稀有PD
            },
            # 等级效率曲线
            'level_efficiency': lambda lvl: 1.0 / (1 + math.log(lvl))
        }
        
        try:
            count = int(count)
            pd_level = int(pd_level) if pd_level else 1
        except (ValueError, TypeError):
            return 0.0
        
        # 计算物品等级
        level_match = re.search(r'_(\d+)_(\d+)_(\d+)', item_type)
        item_level = int(level_match.group(3)) if level_match else 1
        
        # 计算物品基础能量
        item_energy = (ENERGY_CONFIG['base_energy'] * 
                      (ENERGY_CONFIG['item_level_factor'] ** (item_level - 1)))
        
        # 计算PD效率
        if pd_type:
            # 获取PD类型系数
            pd_type_factor = 1.0
            for prefix, factor in ENERGY_CONFIG['pd_type_factors'].items():
                if pd_type.startswith(prefix):
                    pd_type_factor = factor
                    break
                    
            # 计算等级效率
            level_efficiency = ENERGY_CONFIG['level_efficiency'](pd_level)
            
            # 计算总能量
            total_energy = (item_energy * count * 
                          pd_type_factor * level_efficiency)
        else:
            total_energy = item_energy * count
            
        return round(total_energy, 2)
        
    def _create_item_loader(self):
        """Create item config loader"""
        def loader():
            try:
                with open(self.item_config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Extract item patterns like it_ or ds_
                patterns = [
                    r'it_\w+',  # it_ items
                    r'ds_\w+',  # ds_ items
                    r'eq_\w+',  # eq_ items
                    r'pd_\w+',  # pd_ items
                ]
                
                items = set()
                for pattern in patterns:
                    items.update(re.findall(pattern, content))
                
                self.items = sorted(list(items))
                
                # Update the item combo box if it exists
                if hasattr(self, 'req_type_combo'):
                    self.req_type_combo['values'] = self.items
                    if self.items:
                        self.req_type_combo.set(self.items[0])
                
                self.status_var.set(f"已加载 {len(self.items)} 种物品类型")
                return True
                
            except Exception as e:
                error_msg = f"加载物品配置失败: {str(e)}\n文件: {self.item_config_path}"
                print(error_msg)
                messagebox.showerror("错误", error_msg)
                return False
        return loader
        
    def _create_pd_loader(self):
        """创建PD类型加载器"""
        def loader():
            """加载PD类型配置"""
            try:
                with open(self.item_config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 匹配所有PD配置
                pd_blocks = re.findall(r'\{\s*Type\s*=\s*"(pd_\d+_\d+)"(.+?)(?=\}\s*\n\s*\})', content, re.DOTALL)
                pd_types = {}
                
                for pd_type, pd_data in pd_blocks:
                    # 提取PD名称
                    name_match = re.search(r'Name\s*=\s*"([^"]+)"', pd_data)
                    name = name_match.group(1) if name_match else pd_type
                    pd_types[pd_type] = name
                    
                return pd_types
                
            except Exception as e:
                messagebox.showerror("错误", f"加载PD类型配置失败: {str(e)}")
                return {}
        return loader
        
    def _create_order_loader(self):
        """创建订单加载器"""
        def loader(chapter=None, force_reload=False):
            """加载订单配置
            
            Args:
                chapter: 要加载的章节号，如果为None则加载所有章节
                force_reload: 是否强制重新加载已加载的章节
            """
            try:
                loaded_count = 0
                if chapter is not None:
                    # 确保chapter是整数类型
                    chapter = int(chapter)
                    # 加载指定章节
                    if force_reload or chapter not in self.loaded_chapters:
                        self._load_single_chapter(chapter)
                        loaded_count = len(self.orders.get(chapter, []))
                    # 更新当前章节
                    self.current_chapter = chapter
                    self.all_orders = self.orders.get(chapter, [])
                else:
                    # 加载所有章节
                    for ch in range(1, self.max_chapters + 1):
                        if force_reload or ch not in self.loaded_chapters:
                            self._load_single_chapter(ch)
                            loaded_count += len(self.orders.get(ch, []))
                    # 更新当前章节为第一个章节
                    if self.orders:
                        self.current_chapter = next(iter(self.orders.keys()))
                        self.all_orders = self.orders[self.current_chapter]
                
                # 更新UI
                self.update_order_list()
                if chapter is not None:
                    self.chapter_var.set(chapter)  # 更新UI中的章节选择
                    self.status_var.set(f"成功加载 {len(self.orders.get(chapter, []))} 个订单 (第{chapter}章)")
                else:
                    total_orders = sum(len(orders) for orders in self.orders.values())
                    self.status_var.set(f"成功加载 {total_orders} 个订单 (全部章节)")
                
                return loaded_count
                
            except ValueError as ve:
                error_msg = f"无效的章节号: {chapter}"
                print(f"{error_msg} - {str(ve)}")
                messagebox.showerror("错误", error_msg)
                self.status_var.set(error_msg)
                return 0
            except Exception as e:
                error_msg = f"加载订单配置失败: {str(e)}"
                print(f"{error_msg}\n{str(e)}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("错误", error_msg)
                self.status_var.set(error_msg)
                return 0
        return loader
    
    def _load_single_chapter(self, chapter):
        """加载单个章节的订单配置"""
        order_file = os.path.join(self.config_path, f"OrderFixedConfig_{chapter}.lua")
        print(f"Loading order file: {order_file}")
        
        if not os.path.exists(order_file):
            print(f"订单文件不存在: {order_file}")
            self.orders[chapter] = []
            self.loaded_chapters.add(chapter)
            return
            
        try:
            with open(order_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取所有订单块
            order_blocks = re.findall(r'\{\s*Id\s*=\s*"([^"]+)"(.*?)(?=\s*\},?\s*\n\s*\})', content, re.DOTALL)
            
            chapter_orders = []
            for order_id, order_data in order_blocks:
                try:
                    order = {
                        "Id": str(order_id),
                        "ChapterId": int(chapter),
                        "GroupId": 1,
                        "Requirements": [],
                        "Rewards": []
                    }
                    
                    # 提取需求
                    reqs = re.finditer(
                        r'Requirement_\d+\s*=\s*\{\s*Type\s*=\s*"([^"]+)"\s*,\s*Count\s*=\s*(\d+)\s*\}',
                        order_data, re.DOTALL
                    )
                    
                    for req in reqs:
                        if req and len(req.groups()) >= 2:
                            order["Requirements"].append({
                                "Type": str(req.group(1)),
                                "Count": int(req.group(2))
                            })
                    
                    # 提取奖励
                    rewards = re.finditer(
                        r'Reward_\d+\s*=\s*\{\s*Currency\s*=\s*"([^"]+)"\s*,\s*Amount\s*=\s*(\d+)\s*\}',
                        order_data, re.DOTALL
                    )
                    
                    for reward in rewards:
                        if reward and len(reward.groups()) >= 2:
                            order["Rewards"].append({
                                "Currency": str(reward.group(1)),
                                "Amount": int(reward.group(2))
                            })
                    
                    # 提取组ID
                    group_match = re.search(r'GroupId\s*=\s*(\d+)', order_data)
                    if group_match:
                        order["GroupId"] = int(group_match.group(1))
                    
                    # 提取前置订单ID
                    pre_id_match = re.search(r'PreId\s*=\s*\{\s*([^}]*)\s*\}', order_data)
                    if pre_id_match and pre_id_match.group(1):
                        pre_ids = re.findall(r'"([^"]+)"', pre_id_match.group(1))
                        order["PreId"] = [pid.strip() for pid in pre_ids if pid.strip()]
                    
                    chapter_orders.append(order)
                    
                except Exception as e:
                    print(f"解析订单 {order_id} 时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    continue
            
            self.orders[chapter] = chapter_orders
            self.loaded_chapters.add(chapter)
            print(f"成功加载 {len(chapter_orders)} 个订单 (第{chapter}章)")
            
        except Exception as e:
            print(f"加载订单文件 {order_file} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            self.orders[chapter] = []
            self.loaded_chapters.add(chapter)
    
    def update_order_list(self):
        """更新订单列表"""
        # 清空当前列表
        for item in self.order_tree.get_children():
            self.order_tree.delete(item)
        
        # 获取当前章节的订单
        current_chapter_orders = self.orders.get(self.current_chapter, [])
        
        # 按订单ID排序并添加到树状图
        for order in sorted(current_chapter_orders, key=lambda x: int(x.get('Id', '0'))):
            # 构建需求文本
            req_text = ", ".join([f"{req['Type']}×{req['Count']}" for req in order.get("Requirements", [])])
            
            # 构建奖励文本
            rew_text = ", ".join([f"{rew['Currency']}×{rew['Amount']}" for rew in order.get("Rewards", [])])
            
            self.order_tree.insert("", "end", 
                                 values=(
                                     order.get("Id", "N/A"),
                                     f"组 {order.get('GroupId', 1)}",
                                     req_text,
                                     rew_text
                                 ),
                                 tags=('uncompleted',))
        
        # 设置标签样式
        self.order_tree.tag_configure('uncompleted', foreground='black')
        self.order_tree.tag_configure('completed', foreground='green')
    
    def on_order_select(self, event):
        """Handle order selection"""
        if not hasattr(self, 'order_tree') or not hasattr(self, 'energy_var'):
            return
            
        selected = self.order_tree.selection()
        if not selected:
            self.energy_var.set("")
            return
            
        try:
            order_id = self.order_tree.item(selected[0])['values'][0]
            
            # 在当前章节的订单中查找
            current_chapter_orders = self.orders.get(self.current_chapter, [])
            order = next((o for o in current_chapter_orders if str(o.get("Id")) == str(order_id)), None)
            
            if not order:
                self.energy_var.set("")
                return
                
            # 更新基本信息
            if hasattr(self, 'order_id_var'):
                self.order_id_var.set(order.get("Id", ""))
            if hasattr(self, 'group_id_var'):
                self.group_id_var.set(order.get("GroupId", 1))
            if hasattr(self, 'chapter_var'):
                self.chapter_var.set(order.get("ChapterId", self.current_chapter))
            
            # 更新需求列表和奖励列表
            self.update_requirements_list(order.get("Requirements", []))
            self.update_rewards_list(order.get("Rewards", []))
            
            # 计算并显示能量消耗
            total_energy = 0
            for req in order.get("Requirements", []):
                energy = self.calculate_energy(req.get("Type", ""), req.get("Count", 0))
                total_energy += energy
            
            self.energy_var.set(f"{total_energy:.1f}")
        except Exception as e:
            print(f"选择订单时出错: {e}")
            self.energy_var.set("计算错误")
    
    def update_requirements_list(self, requirements):
        """更新需求列表"""
        if not hasattr(self, 'req_tree') or self.req_tree is None:
            return
            
        try:
            # 清空当前列表
            for item in self.req_tree.get_children():
                self.req_tree.delete(item)
            
            # 添加需求
            for req in requirements:
                item_type = req.get("Type", "")
                count = req.get("Count", 0)
                level1_count = self.calculate_level1_equivalent(item_type, count)
                energy = self.calculate_energy(item_type, count)
                
                self.req_tree.insert("", "end", values=(
                    item_type,
                    count,
                    level1_count,
                    f"{energy:.1f} 能量"
                ))
        except Exception as e:
            print(f"更新需求列表时出错: {e}")
    
    def update_rewards_list(self, rewards):
        """更新奖励列表"""
        if not hasattr(self, 'rew_tree') or self.rew_tree is None:
            return
            
        try:
            # 清空当前列表
            for item in self.rew_tree.get_children():
                self.rew_tree.delete(item)
            
            # 添加奖励
            for rew in rewards:
                self.rew_tree.insert("", "end", values=(
                    rew.get("Currency", ""),
                    rew.get("Amount", 0)
                ))
        except Exception as e:
            print(f"更新奖励列表时出错: {e}")
    
    def calculate_level1_equivalent(self, item_type, count):
        """计算1级等效数量"""
        # 这里简化处理，实际应根据物品等级计算
        # 例如：it_1_2_3 表示1级链2级物品，需要2^(2-1)=2个1级物品
        match = re.match(r'it_(\d+)_(\d+)_', item_type)
        if match:
            level = int(match.group(2))
            return count * (2 ** (level - 1))
        return count
    
    # calculate_energy 方法已在上方定义，支持更多参数
    
    def save_order(self):
        """保存订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            # 数据验证
            order_id = self.order_id_var.get().strip()
            if not order_id:
                raise ValueError("订单ID不能为空")
                
            # 验证订单ID唯一性
            current_chapter = self.current_chapter
            for order in self.orders.get(current_chapter, []):
                if order["Id"] == order_id and order is not self.current_order:
                    raise ValueError(f"订单ID {order_id} 已存在")
            
            # 验证需求物品
            requirements = []
            for item in self.req_tree.get_children():
                values = self.req_tree.item(item)['values']
                if len(values) < 2:
                    continue
                    
                item_type = values[0]
                try:
                    count = int(values[1])
                    if count <= 0:
                        raise ValueError("物品数量必须大于0")
                except ValueError:
                    raise ValueError(f"无效的物品数量: {values[1]}")
                    
                # 验证物品类型是否存在
                if item_type not in self.items:
                    raise ValueError(f"无效的物品类型: {item_type}")
                    
                requirements.append({
                    "Type": item_type,
                    "Count": count
                })
                
            if not requirements:
                raise ValueError("订单必须至少有一个需求物品")
                
            # 验证奖励
            rewards = []
            for item in self.rew_tree.get_children():
                values = self.rew_tree.item(item)['values']
                if len(values) < 2:
                    continue
                    
                currency = values[0]
                try:
                    amount = int(values[1])
                    if amount <= 0:
                        raise ValueError("奖励数量必须大于0")
                except ValueError:
                    raise ValueError(f"无效的奖励数量: {values[1]}")
                    
                rewards.append({
                    "Currency": currency,
                    "Amount": amount
                })
                
            if not rewards:
                raise ValueError("订单必须至少有一个奖励")
                
            # 标记为已修改
            self.modified = True
            self.root.title("订单编辑器 - 浮岛物语 (*)")
            
            order_id = self.order_id_var.get().strip()
            if not order_id:
                messagebox.showwarning("警告", "订单ID不能为空")
                return
                
            # 获取当前订单数据
            current_chapter = self.current_chapter
            current_orders = self.orders.get(current_chapter, [])
            
            # 查找现有订单或创建新订单
            order = next((o for o in current_orders if str(o.get("Id")) == str(order_id)), None)
            is_new = order is None
            
            if is_new:
                order = {
                    "Id": order_id,
                    "ChapterId": current_chapter,
                    "GroupId": 1,
                    "Requirements": [],
                    "Rewards": []
                }
                current_orders.append(order)
            
            # 更新订单信息
            order["GroupId"] = int(self.group_id_var.get())
            
            # 收集需求
            requirements = []
            for item in self.req_tree.get_children():
                values = self.req_tree.item(item)['values']
                if len(values) >= 2:  # 确保有足够的元素
                    requirements.append({
                        "Type": values[0],
                        "Count": int(values[1])
                    })
            order["Requirements"] = requirements
            
            # 收集奖励
            rewards = []
            for item in self.rew_tree.get_children():
                values = self.rew_tree.item(item)['values']
                if len(values) >= 2:  # 确保有足够的元素
                    rewards.append({
                        "Currency": values[0],
                        "Amount": int(values[1])
                    })
            order["Rewards"] = rewards
            
            # 更新订单列表
            self.orders[current_chapter] = current_orders
            self.update_order_list()
            
            # 保存到文件
            self._save_chapter_to_file(current_chapter)
            
            messagebox.showinfo("成功", f"订单 {order_id} 已保存")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存订单时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def _save_chapter_to_file(self, chapter):
        """将章节订单保存到文件"""
        if chapter not in self.orders:
            return
            
        order_file = os.path.join(self.config_path, f"OrderFixedConfig_{chapter}.lua")
        orders = self.orders.get(chapter, [])
        
        # 创建备份
        backup_dir = os.path.join(self.config_path, "backup")
        os.makedirs(backup_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(backup_dir, f"OrderFixedConfig_{chapter}_backup_{timestamp}.lua")
        
        try:
            # 先备份原文件
            if os.path.exists(order_file):
                shutil.copy2(order_file, backup_file)
                
            # 写入新文件
            with open(order_file, 'w', encoding='utf-8') as f:
                f.write("return {")
                
                for i, order in enumerate(orders):
                    if i > 0:
                        f.write(",\n    ")
                    else:
                        f.write("\n    ")
                        
                    f.write(f'{{Id = "{order["Id"]}",\n')
                    f.write(f'     GroupId = {order.get("GroupId", 1)},\n')
                    
                    # 写入需求
                    for j, req in enumerate(order.get("Requirements", []), 1):
                        f.write(f'     Requirement_{j} = {{Type = "{req["Type"]}", Count = {req["Count"]}}},\n')
                    
                    # 写入奖励
                    for j, rew in enumerate(order.get("Rewards", []), 1):
                        f.write(f'     Reward_{j} = {{Currency = "{rew["Currency"]}", Amount = {rew["Amount"]}}},\n')
                    
                    # 写入前置订单ID
                    if "PreId" in order and order["PreId"]:
                        pre_ids = '", "'.join(order["PreId"])
                        f.write(f'     PreId = {{"{pre_ids}"}}')
                    
                    f.write('}')
                
                f.write("\n}")
                
            print(f"成功保存订单到文件: {order_file}")
            
        except Exception as e:
            print(f"保存订单文件 {order_file} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def add_order(self):
        """添加新订单"""
        try:
            # 验证当前章节
            if not hasattr(self, 'current_chapter'):
                raise ValueError("当前章节未设置")
                
            # 确保章节已加载
            if self.current_chapter not in self.loaded_chapters:
                self.load_order_config(self.current_chapter)
                
            # 确保当前章节订单列表存在
            if self.current_chapter not in self.orders:
                self.orders[self.current_chapter] = []
                
            # 生成唯一订单ID
            used_ids = set()
            for order in self.orders[self.current_chapter]:
                try:
                    if isinstance(order, dict) and "Id" in order:
                        used_ids.add(int(order["Id"]))
                except (ValueError, TypeError):
                    continue
                    
            new_id = 1
            while new_id in used_ids:
                new_id += 1
                
            # 创建新订单
            self.current_order = {
                "Id": str(new_id),
                "ChapterId": self.current_chapter,
                "GroupId": 1,
                "Requirements": [],
                "Rewards": [],
                "CreatedAt": datetime.datetime.now().isoformat()
            }
            
            # 更新UI
            self.order_id_var.set(str(new_id))
            self.group_id_var.set(1)
            self.chapter_var.set(str(self.current_chapter))
            
            # 清空需求和奖励列表
            self.update_requirements_list([])
            self.update_rewards_list([])
            
            # 添加到当前章节的订单列表
            if self.current_chapter not in self.orders:
                self.orders[self.current_chapter] = []
            self.orders[self.current_chapter].append(self.current_order)
            
            # 更新订单列表显示
            self.all_orders = self.orders.get(self.current_chapter, [])
            self.update_order_list()
            
            # 设置状态
            self.status_var.set(f"已创建新订单: {new_id}")
            
            # 滚动到新添加的订单
            self.order_tree.see(self.order_tree.get_children()[-1] if self.order_tree.get_children() else '')
            
        except Exception as e:
            error_msg = f"创建订单失败: {str(e)}"
            print(f"Error in add_order: {error_msg}")
            import traceback
            traceback.print_exc()
            self.status_var.set(error_msg)
    
    def delete_order(self):
        """删除当前选中的订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        order_id = self.order_tree.item(selected[0])['values'][0]
        
        if not messagebox.askyesno("确认删除", f"确定要删除订单 {order_id} 吗？此操作不可恢复！"):
            return
            
        try:
            # 从内存中删除订单
            current_chapter = self.current_chapter
            if current_chapter in self.orders:
                self.orders[current_chapter] = [
                    o for o in self.orders[current_chapter] 
                    if str(o.get("Id")) != str(order_id)
                ]
                
                # 保存到文件
                self._save_chapter_to_file(current_chapter)
                
                # 更新UI
                self.update_order_list()
                
                # 清空编辑区域
                self.order_id_var.set("")
                self.group_id_var.set(1)
                for item in self.req_tree.get_children():
                    self.req_tree.delete(item)
                for item in self.rew_tree.get_children():
                    self.rew_tree.delete(item)
                    
                messagebox.showinfo("成功", f"订单 {order_id} 已删除")
            
        except Exception as e:
            messagebox.showerror("错误", f"删除订单时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def add_requirement(self):
        """添加需求物品"""
        # 这里可以打开一个对话框让用户选择物品和数量
        # 简化处理，直接添加一个空的需求
        self.req_tree.insert("", "end", values=("it_1_1_1", 1, 1, "5 能量"))
    
    def delete_requirement(self):
        """删除选中的需求物品"""
        selected = self.req_tree.selection()
        if selected:
            self.req_tree.delete(selected[0])
    
    def add_reward(self):
        """添加奖励"""
        # 这里可以打开一个对话框让用户选择货币类型和数量
        # 简化处理，直接添加一个空的奖励
        self.rew_tree.insert("", "end", values=("coin", 100))
    
    def delete_reward(self):
        """删除选中的奖励"""
        selected = self.rew_tree.selection()
        if selected:
            self.rew_tree.delete(selected[0])
            
    def add_requirement(self):
        """添加需求物品"""
        try:
            # 验证当前订单选择
            selected = self.order_tree.selection()
            if not selected:
                messagebox.showwarning("警告", "请先选择一个订单")
                return
                
            # 验证物品类型
            item_type = self.req_type_var.get().strip()
            if not item_type:
                messagebox.showwarning("警告", "请选择物品类型")
                return
                
            # 验证物品类型是否有效
            if not hasattr(self, 'items') or item_type not in self.items:
                messagebox.showerror("错误", f"无效的物品类型: {item_type}")
                return
                
            # 验证数量
            try:
                count = int(self.req_count_var.get())
                if count <= 0:
                    raise ValueError("数量必须大于0")
            except ValueError as ve:
                messagebox.showerror("错误", f"无效的数量: {str(ve)}")
                return
                
            # 获取当前订单
            order_id = self.order_tree.item(selected[0])['values'][0]
            current_chapter = self.current_chapter
            
            # 确保当前章节订单存在
            if current_chapter not in self.orders:
                self.orders[current_chapter] = []
                
            # 查找订单
            order = None
            for o in self.orders[current_chapter]:
                if isinstance(o, dict) and str(o.get("Id")) == str(order_id):
                    order = o
                    break
                    
            if not order:
                messagebox.showerror("错误", f"未找到订单ID: {order_id}")
                return
                
            # 初始化需求列表
            if "Requirements" not in order or not isinstance(order["Requirements"], list):
                order["Requirements"] = []
                
            # 检查是否已存在相同类型的物品
            requirement_added = False
            for req in order["Requirements"]:
                if isinstance(req, dict) and req.get("Type") == item_type:
                    req["Count"] = req.get("Count", 0) + count
                    requirement_added = True
                    break
                    
            # 如果不存在相同类型，则添加新需求
            if not requirement_added:
                order["Requirements"].append({
                    "Type": item_type,
                    "Count": count
                })
                
            # 更新UI
            self.current_order = order
            self.update_requirements_list(order.get("Requirements", []))
            self.status_var.set(f"已添加需求: {item_type} x{count}")
            
            # 标记为已修改
            self.modified = True
            self.root.title("订单编辑器 - 浮岛物语 (*)")
            
        except Exception as e:
            messagebox.showerror("错误", f"添加需求失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def delete_requirement(self):
        """删除选中的需求物品"""
        selected = self.req_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个需求物品")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = next((o for o in self.orders if o["Id"] == order_id), None)
            
            if order and "Requirements" in order:
                item = self.req_tree.item(selected[0])
                item_type = item['values'][0]
                
                # 删除第一个匹配的物品类型
                for i, req in enumerate(order["Requirements"]):
                    if req["Type"] == item_type:
                        order["Requirements"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已删除需求: {item_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除需求失败: {str(e)}")
    
    def add_reward(self):
        """添加奖励"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            reward_type = self.reward_type_var.get().strip()
            if not reward_type:
                messagebox.showwarning("警告", "请选择奖励类型")
                return
                
            amount = int(self.reward_amount_var.get())
            if amount <= 0:
                raise ValueError("数量必须大于0")
                
            order_id = self.order_tree.item(selected[0])['values'][0]
            order = next((o for o in self.orders if o["Id"] == order_id), None)
            
            if order:
                if "Rewards" not in order:
                    order["Rewards"] = []
                
                # 检查是否已存在相同类型的奖励
                for reward in order["Rewards"]:
                    if reward["Currency"] == reward_type:
                        reward["Amount"] += amount
                        break
                else:
                    order["Rewards"].append({
                        "Currency": reward_type,
                        "Amount": amount
                    })
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已添加奖励: {reward_type} x{amount}")
                
        except ValueError as ve:
            messagebox.showerror("错误", f"无效的数量: {str(ve)}")
        except Exception as e:
            messagebox.showerror("错误", f"添加奖励失败: {str(e)}")
    
    def delete_reward(self):
        """删除选中的奖励"""
        selected = self.reward_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个奖励")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = next((o for o in self.orders if o["Id"] == order_id), None)
            
            if order and "Rewards" in order:
                item = self.reward_tree.item(selected[0])
                reward_type = item['values'][0]
                
                # 删除第一个匹配的奖励类型
                for i, reward in enumerate(order["Rewards"]):
                    if reward["Currency"] == reward_type:
                        order["Rewards"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已删除奖励: {reward_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除奖励失败: {str(e)}")
            
    def show_help(self):
        """显示帮助文档"""
        help_text = """订单编辑器使用指南

1. 基本操作
- 左键点击章节按钮切换不同章节
- 双击订单可查看/编辑详情
- 右键点击订单可弹出操作菜单

2. 订单编辑
- 需求物品: 添加订单需要的物品及数量
- 奖励: 设置完成订单后可获得的奖励
- PD配置: 设置生产者类型和等级来计算能量消耗

3. 快捷键
- Ctrl+S: 保存当前订单
- Ctrl+N: 新建订单
- Ctrl+D: 删除订单
- Ctrl+Q: 退出程序

4. 数据验证
- 订单ID必须唯一
- 物品数量必须大于0
- 物品类型必须有效
- 订单必须至少有一个需求和奖励

5. 其他功能
- 自动备份: 每次保存都会创建备份
- 修改标记: 标题栏显示*表示有未保存的修改
"""
        messagebox.showinfo("订单编辑器帮助", help_text)
    
    def delete_order(self):
        """删除订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        order_id = self.order_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("确认", f"确定要删除订单 {order_id} 吗？"):
            # 标记为已修改
            self.modified = True
            self.root.title("订单编辑器 - 浮岛物语 (*)")
            self.orders = [o for o in self.orders if o["Id"] != order_id]
            self.update_order_list()
            self.status_var.set(f"订单 {order_id} 已删除")

if __name__ == "__main__":
    root = Tk()
    app = OrderEditor(root)
    root.mainloop()