EPopupScene = {
  Main = EGameMode.Main,
  Board = EGameMode.Board,
  Spree = "spree"
}
BasePopupHelper = {
  canPopScene = {
    [EPopupScene.Main] = true
  },
  canIgnorePopup = true
}
BasePopupHelper.__index = BasePopupHelper

function BasePopupHelper:Init()
  self.m_needCheckPopup = true
end

function BasePopupHelper:OnDestroy()
  EventDispatcher.RemoveTarget(self)
end

function BasePopupHelper:SetNeedCheckPopup(needCheck)
  self.m_needCheckPopup = needCheck
end

function BasePopupHelper:NeedCheckPopup()
  return self.m_needCheckPopup
end

function BasePopupHelper:CheckPopup()
  return nil
end

PopupChain = {}
PopupChain.__index = PopupChain
local nStart = 0
local autoIncrease = function()
  nStart = nStart + 1
  return nStart
end
EPopupHelper = {
  DataBalance = autoIncrease(),
  SkipTimeAutoEffect = autoIncrease(),
  ProgressActivityReward = autoIncrease(),
  ScrollBackToOrder = autoIncrease(),
  FlambeTime = autoIncrease(),
  Level = autoIncrease(),
  Task = autoIncrease(),
  OrderGroup = autoIncrease(),
  OrderDay = autoIncrease(),
  ItemTypeDelete = autoIncrease(),
  ItemRecycle = autoIncrease(),
  Tutorial = autoIncrease(),
  Notice = autoIncrease(),
  Rate = autoIncrease(),
  Bundle = autoIncrease(),
  EnergyBoost = autoIncrease(),
  BP = autoIncrease(),
  SurpriseChestActivity = autoIncrease(),
  BakeOut = autoIncrease(),
  CoinRace = autoIncrease(),
  PkRace = autoIncrease(),
  ProgressActivity = autoIncrease(),
  Dig = autoIncrease(),
  BlindChest = autoIncrease(),
  ExtraBoard = autoIncrease()
}

function PopupChain:Awake()
  self.m_helpers = {}
  self.m_levelUpHelper = LevelUpPopupHelper.Create()
  self.m_helpers[EPopupHelper.DataBalance] = DataBalancePopupHelper.Create()
  self.m_helpers[EPopupHelper.SkipTimeAutoEffect] = SkipTimeAutoEffectPopupHelper.Create()
  self.m_helpers[EPopupHelper.FlambeTime] = FlambeTimePopupHelper.Create()
  self.m_helpers[EPopupHelper.Level] = self.m_levelUpHelper
  self.m_helpers[EPopupHelper.BakeOut] = BakeOutPopupHelper.Create()
  self.m_helpers[EPopupHelper.Bundle] = BundlePopupHelper.Create()
  self.m_helpers[EPopupHelper.Notice] = NoticePopupHelper.Create()
  self.m_helpers[EPopupHelper.Rate] = RatePopupHelper.Create()
  self.m_helpers[EPopupHelper.Tutorial] = TutorialPopupHelper.Create()
  self.m_helpers[EPopupHelper.OrderGroup] = OrderGroupPopupHelper.Create()
  self.m_helpers[EPopupHelper.OrderDay] = OrderDayPopupHelper.Create()
  self.m_helpers[EPopupHelper.Task] = TaskPopupHelper.Create()
  self.m_helpers[EPopupHelper.CoinRace] = CoinRacePopupHelper.Create()
  self.m_helpers[EPopupHelper.PkRace] = PkRacePopupHelper.Create()
  self.m_helpers[EPopupHelper.BP] = PassActivityPopupHelper.Create()
  self.m_helpers[EPopupHelper.Dig] = DigActivityPopupHelper.Create()
  self.m_helpers[EPopupHelper.ProgressActivity] = ProgressActivityPopupHelper.Create()
  self.m_helpers[EPopupHelper.ProgressActivityReward] = ProgressActivityRewardPopupHelper.Create()
  self.m_helpers[EPopupHelper.BlindChest] = BlindChestPopupHelper.Create()
  self.m_helpers[EPopupHelper.ExtraBoard] = ExtraBoardActivityPopupHelper.Create()
  self.m_helpers[EPopupHelper.SurpriseChestActivity] = SurpriseChestActivityPopupHelper.Create()
  self.m_helpers[EPopupHelper.EnergyBoost] = EnergyBoostPopupHelper.Create()
  self.m_helpers[EPopupHelper.ScrollBackToOrder] = ScrollBackToOrderPopupHelper.Create()
  self.m_helpers[EPopupHelper.ItemRecycle] = ItemRecyclePopupHelper.Create()
  self.m_helpers[EPopupHelper.ItemTypeDelete] = ItemTypeDeletePopupHelper.Create()
  EventDispatcher.AddListener(EEventType.LoginFinished, self, self._OnLoginFinished)
  EventDispatcher.AddListener(EEventType.ChangeGameModeFinished, self, self._OnGameModeChanged)
end

function PopupChain:OnDestroy()
  EventDispatcher.RemoveTarget(self)
  for _, helper in ipairs(self.m_helpers) do
    helper:OnDestroy()
  end
  self.m_levelUpHelper = nil
end

function PopupChain:OnSceneViewLoaded()
  DelayExecuteFunc(function()
    self.m_bInited = true
  end, SceneViewHud.Duration)
end

function PopupChain:Update()
  if not self.m_bInited then
    return
  end
  self:_StartPopup()
end

function PopupChain:_OnLoginFinished(message)
  if not message.bSuccess then
    return
  end
  self:SetNeedCheckPopup(true)
end

function PopupChain:_OnGameModeChanged()
  if GM.SceneManager:GetGameMode() == EGameMode.Main then
    self:SetNeedCheckPopup(true)
  end
end

function PopupChain:SetNeedCheckPopup(needCheck)
  for _, helper in ipairs(self.m_helpers) do
    helper:SetNeedCheckPopup(needCheck)
  end
end

function PopupChain.GetCurPopScene()
  local curGameMode = GM.SceneManager:GetGameMode()
  if curGameMode == EGameMode.Board then
    return EPopupScene.Board
  elseif curGameMode == EGameMode.Main then
    return EPopupScene.Main
  elseif GM.SceneManager:InSpreeGameMode() then
    return EPopupScene.Spree
  else
    Log.Assert(false, "找不到当前GameMode对应的PopupScene:" .. (curGameMode or "nil"))
  end
end

function PopupChain:_StartPopup()
  if not GM.UIManager.allWindowClosed or GM.UIManager:IsEventLock(true) or GM.UIManager:IsMaskVisible() or GM.TimelineManager:IsPlayingTimeline() or GM.TutorialModel:HasAnyStrongTutorialOngoing() or GM.ChapterManager.curActiveChapterName ~= GM.TaskManager:GetOngoingChapterName() or GM.SceneManager.isChanging then
    return
  end
  local arrHelpers = self.m_helpers
  if IsAutoRun() then
    arrHelpers = {
      self.m_levelUpHelper,
      self.m_helpers[EPopupHelper.OrderGroup],
      self.m_helpers[EPopupHelper.FlambeTime],
      self.m_helpers[EPopupHelper.Task],
      self.m_helpers[EPopupHelper.ProgressActivityReward]
    }
  end
  for _, helper in ipairs(arrHelpers) do
    if helper.canPopScene[PopupChain.GetCurPopScene()] and (helper.allowPopWhenHasFlying or PropertyAnimationManager.uiLockFlyingCount <= 0) and (not (GameConfig.IsTestMode() and helper.canIgnorePopup) or PlayerPrefs.GetInt(EPlayerPrefKey.TestIgnorePopupChain, 0) ~= 1) and helper:NeedCheckPopup() then
      helper:SetNeedCheckPopup(false)
      local windowName, parameters = helper:CheckPopup()
      if windowName == true then
        GM.UIManager:OnChainPopuped()
        return
      elseif windowName ~= nil then
        GM.UIManager:OpenView(windowName, table.unpack(parameters or Table.Empty))
        GM.UIManager:OnChainPopuped()
        return
      end
    end
  end
  if not GM.BIManager:IsNetworkCheckEnd() then
    GM.BIManager:LogNet(EBIType.NetworkCheckAction.CanInteract, nil, nil, nil, true)
    GM.BIManager:SetNetworkCheckEnd()
  end
  if IsAutoRun() then
    GM.TestAutoRunModel:AutoRun()
  end
end

function PopupChain:GetHelper(ePopupHelper)
  return self.m_helpers[ePopupHelper]
end
