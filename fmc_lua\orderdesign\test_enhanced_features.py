"""
测试增强功能
验证基于需求背景2的优化是否正常工作
"""

import os
import sys
import traceback

def test_enhanced_data_models():
    """测试增强的数据模型"""
    print("=== 测试增强的数据模型 ===")
    
    try:
        from data_models import (
            GeneratorData, ItemData, InstrumentData, RecipeData, 
            ProductionData, BetMode, EfficiencyMode
        )
        
        # 测试ProductionData
        production = ProductionData(item_code="100020", weight=15, total_clicks=20)
        print(f"ProductionData创建成功: {production.item_code}, 权重: {production.weight}")
        
        # 测试增强的GeneratorData
        generator = GeneratorData(code="pd_1_4")
        generator.production = [production]
        generator.tape_items = [{"Code": "it_1_1_1", "Weight": 1}]
        generator.unlock_price = 412
        generator.speed_up_price = 4
        print(f"GeneratorData增强字段测试成功: 解锁价格: {generator.unlock_price}")
        
        # 测试增强的ItemData
        item = ItemData(code="it_1_1_1")
        item.use_energy = 1
        item.drops_total = 1
        item.reward = 32
        print(f"ItemData增强字段测试成功: 奖励: {item.reward}")
        
        print("✓ 数据模型测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据模型测试失败: {e}")
        traceback.print_exc()
        return False

def test_enhanced_config_loader():
    """测试增强的配置加载器"""
    print("\n=== 测试增强的配置加载器 ===")
    
    try:
        from config_loader import ConfigLoader
        
        config_path = os.path.join(os.path.dirname(__file__), "..", "Data", "Config")
        if not os.path.exists(config_path):
            print(f"配置路径不存在: {config_path}")
            return False
        
        config_loader = ConfigLoader(config_path)
        config_data = config_loader.load_all_configs()
        
        print(f"配置加载成功:")
        print(f"  物品数量: {len(config_data.items)}")
        print(f"  生成器数量: {len(config_data.generators)}")
        print(f"  订单章节数: {len(config_data.orders)}")
        
        # 测试增强字段解析
        if config_data.generators:
            sample_gen = list(config_data.generators.values())[0]
            print(f"  示例生成器增强字段:")
            print(f"    解锁价格: {sample_gen.unlock_price}")
            print(f"    加速价格: {sample_gen.speed_up_price}")
            print(f"    传送带物品数: {len(sample_gen.tape_items)}")
            print(f"    产出数据数: {len(sample_gen.production)}")
        
        if config_data.items:
            sample_item = list(config_data.items.values())[0]
            print(f"  示例物品增强字段:")
            print(f"    消耗体力: {sample_item.use_energy}")
            print(f"    奖励值: {sample_item.reward}")
        
        print("✓ 配置加载器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置加载器测试失败: {e}")
        traceback.print_exc()
        return False

def test_precise_order_generation():
    """测试精确订单生成"""
    print("\n=== 测试精确订单生成 ===")
    
    try:
        from config_loader import ConfigLoader
        from order_generator import OrderGenerator
        from data_models import OrderGenerationConfig, BetMode, EfficiencyMode, PlayerGeneratorInfo
        
        # 加载配置
        config_path = os.path.join(os.path.dirname(__file__), "..", "Data", "Config")
        config_loader = ConfigLoader(config_path)
        config_data = config_loader.load_all_configs()
        
        # 创建订单生成器
        order_generator = OrderGenerator(config_data)
        
        # 创建生成配置
        generation_config = OrderGenerationConfig()
        generation_config.special_dishes = ["ds_chopve_1", "ds_chopve_2", "ds_flb_1", "ds_chopve_3"]
        generation_config.total_energy_range = (100.0, 200.0)
        generation_config.bet_mode = BetMode.BET_2
        generation_config.efficiency_mode = EfficiencyMode.AVERAGE
        generation_config.player_generators = [
            PlayerGeneratorInfo(generator_type="pd_1_4", level=4, count=2),
            PlayerGeneratorInfo(generator_type="pd_1_5", level=5, count=1),
        ]
        
        # 测试精确生成
        result = order_generator.generate_precise_order_scheme(generation_config)
        
        if result:
            print(f"精确生成成功:")
            print(f"  订单数量: {len(result.orders)}")
            print(f"  总体力消耗: {result.total_energy:.2f}")
            print(f"  方案有效性: {'有效' if result.is_valid else '无效'}")
            
            for i, order in enumerate(result.orders):
                print(f"  订单{i+1}: {len(order.requirements)}个需求")
        else:
            print("精确生成返回空结果")
            return False
        
        print("✓ 精确订单生成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 精确订单生成测试失败: {e}")
        traceback.print_exc()
        return False

def test_algorithm_validator():
    """测试算法验证器"""
    print("\n=== 测试算法验证器 ===")
    
    try:
        from config_loader import ConfigLoader
        from algorithm_validator import AlgorithmValidator
        from data_models import ProductionData, BetMode, OrderRequirement
        
        # 加载配置
        config_path = os.path.join(os.path.dirname(__file__), "..", "Data", "Config")
        config_loader = ConfigLoader(config_path)
        config_data = config_loader.load_all_configs()
        
        # 创建验证器
        validator = AlgorithmValidator(config_data)
        
        # 测试数据完整性验证
        data_ok, data_issues = validator.validate_data_integrity()
        print(f"数据完整性验证: {'通过' if data_ok else '失败'}")
        if data_issues:
            print(f"  发现 {len(data_issues)} 个问题")
            for issue in data_issues[:3]:  # 只显示前3个问题
                print(f"    - {issue}")
        
        # 测试产出计算验证
        if config_data.generators:
            gen_code = list(config_data.generators.keys())[0]
            generator = config_data.generators[gen_code]
            
            if generator.production:
                production_ok = validator.validate_production_calculation(gen_code, generator.production)
                print(f"产出计算验证: {'通过' if production_ok else '失败'}")
        
        # 测试体力计算验证
        test_requirements = [
            OrderRequirement(item_type="it_1_1_1", count=1),
            OrderRequirement(item_type="it_1_1_2", count=2)
        ]
        energy_ok = validator.validate_energy_calculation(test_requirements, BetMode.BET_2, 6.0)
        print(f"体力计算验证: {'通过' if energy_ok else '失败'}")
        
        # 测试用例生成
        test_cases = validator.generate_test_cases()
        print(f"生成测试用例: {len(test_cases)} 个")
        for case in test_cases:
            print(f"  - {case['name']}: {len(case['special_dishes'])} 个特色菜品")
        
        print("✓ 算法验证器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 算法验证器测试失败: {e}")
        traceback.print_exc()
        return False

def test_production_data_extraction():
    """测试产出数据提取"""
    print("\n=== 测试产出数据提取 ===")
    
    try:
        from config_loader import ConfigLoader
        from order_generator import OrderGenerator
        
        # 加载配置
        config_path = os.path.join(os.path.dirname(__file__), "..", "Data", "Config")
        config_loader = ConfigLoader(config_path)
        config_data = config_loader.load_all_configs()
        
        # 创建订单生成器
        order_generator = OrderGenerator(config_data)
        
        # 提取产出数据
        production_data = order_generator._extract_production_data()
        
        print(f"产出数据提取成功:")
        print(f"  生成器数量: {len(production_data)}")
        
        # 显示示例数据
        for i, (gen_code, productions) in enumerate(production_data.items()):
            if i >= 3:  # 只显示前3个
                break
            print(f"  {gen_code}: {len(productions)} 个产出项")
            for j, prod in enumerate(productions):
                if j >= 2:  # 每个生成器只显示前2个产出
                    break
                print(f"    - {prod.item_code}: 权重{prod.weight}")
        
        print("✓ 产出数据提取测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 产出数据提取测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试增强功能...")
    
    tests = [
        test_enhanced_data_models,
        test_enhanced_config_loader,
        test_precise_order_generation,
        test_algorithm_validator,
        test_production_data_extraction
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！增强功能工作正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
