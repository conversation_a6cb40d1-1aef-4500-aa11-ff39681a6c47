BuyEnergyWindow = setmetatable({
  disableEffectWhenCloseView = true,
  Type = EnergyType.Main
}, BaseWindow)
BuyEnergyWindow.__index = BuyEnergyWindow

function BuyEnergyWindow.EnergyType2WindowName(energyType)
  if energyType == EnergyType.Event then
    return UIPrefabConfigName.BuyEventEnergyWindow
  else
    return UIPrefabConfigName.BuyEnergyWindow
  end
end

function BuyEnergyWindow:Init(popupForLackingEnergy, inShopWindow)
  self.m_energyModel = GM.EnergyModel
  self.m_shopModel = GM.ShopModel
  self.m_energyCount = GM.ShopDataModel:GetBuyEnergyCount()
  self.m_inShopWindow = inShopWindow
  self.m_popupForLackingEnergy = popupForLackingEnergy
  local energyStr = "+" .. tostring(self.m_energyCount)
  self.m_numText.text = energyStr
  self.m_numText1.text = energyStr
  self:UpdateContent()
  EventDispatcher.AddListener(EEventType.ShopRefreshed, self, self.UpdateContent)
  EventDispatcher.AddListener(EEventType.OnAdEnd, self, self.OnAdEnd)
  if popupForLackingEnergy then
    local isAutoRun = IsAutoRun()
    local disableTouchDuration = isAutoRun and 0.2 or GM.ConfigModel:GetAntiMisTouchDuration()
    if disableTouchDuration ~= nil and 0 < disableTouchDuration then
      self.m_bDisableTouch = true
      DelayExecuteFuncInView(function()
        self.m_bDisableTouch = nil
        if isAutoRun then
          self.m_propertyButton:OnBtnClicked()
        end
      end, disableTouchDuration, self)
    end
  end
  DelayExecuteFuncInView(function()
    self.m_effectGo:SetActive(true)
  end, 0.2, self)
  self:LogWindowAction(EBIType.UIActionType.Open, popupForLackingEnergy and EBIReferType.ClickEle or EBIReferType.UserClick)
end

function BuyEnergyWindow:OnDestroy()
  Scheduler.UnscheduleTarget(self)
  EventDispatcher.RemoveTarget(self)
end

function BuyEnergyWindow:GetBtnRectTrans()
  return self.m_propertyButton.gameObject.transform
end

function BuyEnergyWindow:UpdateContent()
  if self.m_callback == nil then
    function self.m_callback()
      if self.m_bDisableTouch then
        return
      end
      self.m_bBuySuccess = self.m_shopModel:BuyEnergy(self.Type)
      self:Close()
    end
  end
  self.m_free2Refill = self.m_shopModel:IsEnergyFreeRefill(self.Type)
  self.m_adEnergy = GM.AdModel:GetEnergyNum(EAdType.Energy)
  if not self.m_free2Refill and self.m_adEnergy and self.m_adEnergy > 0 then
    self:UpdateNoAdContent()
    self:UpdateAdContent()
  else
    self:UpdateNoAdContent()
  end
end

function BuyEnergyWindow:UpdateNoAdContent()
  self.m_AdContent:SetActive(false)
  self.m_NoAdContent:SetActive(true)
  local energyData = self.m_shopModel:GetEnergyDatas(self.Type)
  self.m_freeTextGo:SetActive(self.m_free2Refill)
  self.m_priceTextGo:SetActive(not self.m_free2Refill)
  self.m_propertyButton:Init(EPropertyType.Gem, energyData[1].costNum, self.m_callback)
end

function BuyEnergyWindow:UpdateAdContent()
  self.m_AdContent:SetActive(true)
  self.m_NoAdContent:SetActive(false)
  local energyData = self.m_shopModel:GetEnergyDatas(self.Type)
  self.m_adEnergyText.text = "+" .. self.m_adEnergy
  self.m_propertyButton1:Init(EPropertyType.Gem, energyData[1].costNum, self.m_callback)
end

function BuyEnergyWindow:OnAdButtonClicked()
  if self.m_bDisableTouch then
    return
  end
  GM.AdModel:ShowAd(EAdType.Energy, self.m_adEnergy)
end

function BuyEnergyWindow:OnAdEnd(msg)
  if msg.eAdType ~= EAdType.Energy then
    return
  end
  if msg.bSuccess then
    self.m_bBuySuccess = true
    self.m_energyCount = self.m_adEnergy
    local formattedRewards = {
      {
        [PROPERTY_TYPE] = EnergyModel.EnergyType2PropertyType(self.Type),
        [PROPERTY_COUNT] = self.m_energyCount
      }
    }
    RewardApi.AcquireRewardsLogic(formattedRewards, EPropertySource.Give, EBIType.AdSuccess, EGameMode.Board, CacheItemType.Stack)
    self:Close()
  else
    self:UpdateContent()
  end
end

function BuyEnergyWindow:OnCloseFinish()
  if self.m_bBuySuccess then
    RewardApi.AcquireRewardsInView({
      {
        [PROPERTY_TYPE] = EnergyModel.EnergyType2PropertyType(self.Type),
        [PROPERTY_COUNT] = self.m_energyCount or 0
      }
    }, {eventLock = false, noDelayTime = true})
  elseif self.m_bBuySuccess == false then
    GM.ShopModel:OnLackOfGem(self.m_shopModel:GetEnergyDatas(self.Type)[1].costNum - GM.PropertyDataManager:GetPropertyNum(EPropertyType.Gem))
  elseif self.m_inShopWindow then
    GM.UIManager:OpenView(UIPrefabConfigName.ShopWindow)
  elseif self.Type == EnergyType.Main then
    GM.NotificationModel:TryOpenNotificationWindow(ENotiSceneDescKey.EnergyEmpty)
    if self.m_popupForLackingEnergy and not self.m_free2Refill then
      GM.BundleManager:TryStartBundlePopupChain(EBundleTriggerType.LackEnergy)
    end
  end
  BaseWindow.OnCloseFinish(self)
end

BuyEventEnergyWindow = setmetatable({
  Type = EnergyType.Event
}, BuyEnergyWindow)
BuyEventEnergyWindow.__index = BuyEventEnergyWindow
