import re
import csv

# 读取 Lua 文件
def read_lua_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        return file.read()

# 解析 Lua 文件，提取所需字段
def parse_lua_data(lua_content):
    # 正则表达式匹配每个物品的完整块
    item_pattern = re.compile(r'\{[^{}]*GeneratedItems\s*=\s*\{[^{}]*\}.*?\}', re.DOTALL)
    items = item_pattern.findall(lua_content)

    print(f"匹配到的物品块数量: {len(items)}")
    for i, item in enumerate(items):
        print(f"物品块 {i + 1}:\n{item}\n")

    # 提取所需字段的正则表达式
    type_pattern = re.compile(r'Type\s*=\s*"(.*?)"')
    merged_type_pattern = re.compile(r'MergedType\s*=\s*"(.*?)"')
    unlock_price_pattern = re.compile(r'UnlockPrice\s*=\s*(\d+)')
    use_energy_pattern = re.compile(r'UseEnergy\s*=\s*(\d+)')
    generated_items_pattern = re.compile(r'GeneratedItems\s*=\s*\{(.*?)\}', re.DOTALL)
    code_weight_pattern = re.compile(r'Code\s*=\s*"(.*?)",\s*Weight\s*=\s*(\d+)')
    cd_pattern = re.compile(r'Cd\s*=\s*(\d+)')
    initial_number_pattern = re.compile(r'InitialNumber\s*=\s*(\d+)')
    frequency_pattern = re.compile(r'Frequency\s*=\s*(\d+)')
    capacity_pattern = re.compile(r'Capacity\s*=\s*(\d+)')
    speed_up_price_pattern = re.compile(r'SpeedUpPrice\s*=\s*(\d+)')

    results = []

    for item in items:
        data = {
            'Type': type_pattern.search(item).group(1) if type_pattern.search(item) else '',
            'MergedType': merged_type_pattern.search(item).group(1) if merged_type_pattern.search(item) else '',
            'UnlockPrice': unlock_price_pattern.search(item).group(1) if unlock_price_pattern.search(item) else '',
            'UseEnergy': use_energy_pattern.search(item).group(1) if use_energy_pattern.search(item) else '',
            'GeneratedItems': [],
            'Cd': cd_pattern.search(item).group(1) if cd_pattern.search(item) else '',
            'InitialNumber': initial_number_pattern.search(item).group(1) if initial_number_pattern.search(item) else '',
            'Frequency': frequency_pattern.search(item).group(1) if frequency_pattern.search(item) else '',
            'Capacity': capacity_pattern.search(item).group(1) if capacity_pattern.search(item) else '',
            'SpeedUpPrice': speed_up_price_pattern.search(item).group(1) if speed_up_price_pattern.search(item) else '',
        }

        # 提取 GeneratedItems 中的 Code 和 Weight
        generated_items_match = generated_items_pattern.search(item)
        if generated_items_match:
            generated_items = generated_items_match.group(1)
            code_weight_matches = code_weight_pattern.findall(generated_items)
            for code, weight in code_weight_matches:
                data['GeneratedItems'].append({'Code': code, 'Weight': weight})

        results.append(data)

    return results

# 导出为 CSV 文件
def export_to_csv(data, output_file):
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Type', 'MergedType', 'UnlockPrice', 'UseEnergy', 'GeneratedItems', 'Cd', 'InitialNumber', 'Frequency', 'Capacity', 'SpeedUpPrice']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for item in data:
            # 将 GeneratedItems 转换为字符串格式
            generated_items_str = ', '.join([f"{i['Code']}:{i['Weight']}" for i in item['GeneratedItems']])
            writer.writerow({
                'Type': item['Type'],
                'MergedType': item['MergedType'],
                'UnlockPrice': item['UnlockPrice'],
                'UseEnergy': item['UseEnergy'],
                'GeneratedItems': generated_items_str,
                'Cd': item['Cd'],
                'InitialNumber': item['InitialNumber'],
                'Frequency': item['Frequency'],
                'Capacity': item['Capacity'],
                'SpeedUpPrice': item['SpeedUpPrice'],
            })

    print(f"导出的数据行数: {len(data)}")
    for i, item in enumerate(data):
        print(f"第 {i + 1} 行数据: {item}")

# 主函数
def main():
    lua_file_path = r'fmc_lua\Data\Config\ItemModelConfig copy.lua'  # 替换为你的 Lua 文件路径
    output_csv_path = 'out1put.csv'  # 输出 CSV 文件路径

    lua_content = read_lua_file(lua_file_path)
    parsed_data = parse_lua_data(lua_content)
    export_to_csv(parsed_data, output_csv_path)

    print(f"数据已导出到 {output_csv_path}")

if __name__ == '__main__':
    main()