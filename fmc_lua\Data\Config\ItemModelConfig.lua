-- Decompiled using luadec 2.2 rev: 895d923 for Lua 5.3 from https://github.com/viruscamp/luadec
-- Command line: fmc_lua_encry/Data/Config/ItemModelConfig 

-- params : ...
-- function num : 0 , upvalues : _ENV
return {
{Type = "box_1"}
, 
{Type = "cobweb_1"}
, 
{Type = "bubble_1"}
, 
{Type = "pd_1_1", MergedType = "pd_1_2", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "pd_1_2", MergedType = "pd_1_3", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "pd_1_3", MergedType = "pd_1_4", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "pd_1_4", MergedType = "pd_1_5", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, UseEnergy = 1, 
TapeItems = {
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_1_1_1", Weight = 15}
, 
{Code = "it_1_2_1", Weight = 5}
}
, Cd = 7200, InitialNumber = 8, Frequency = 8, Capacity = 24, SpeedUpPrice = 4}
, 
{Type = "pd_1_5", MergedType = "pd_1_6", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, UseEnergy = 1, 
TapeItems = {
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_1_1_1", Weight = 9}
, 
{Code = "it_1_1_2", Weight = 6}
, 
{Code = "it_1_2_1", Weight = 5}
}
, Cd = 6000, InitialNumber = 18, Frequency = 10, Capacity = 30, SpeedUpPrice = 6}
, 
{Type = "pd_1_6", MergedType = "pd_1_7", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, UseEnergy = 1, 
TapeItems = {
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_1_1_1", Weight = 10}
, 
{Code = "it_1_1_2", Weight = 4}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 5}
}
, Cd = 4200, InitialNumber = 12, Frequency = 12, Capacity = 36, SpeedUpPrice = 8}
, 
{Type = "pd_1_7", MergedType = "pd_1_8", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, UseEnergy = 1, 
TapeItems = {
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_1_1_1", Weight = 10}
, 
{Code = "it_1_1_2", Weight = 3}
, 
{Code = "it_1_1_3", Weight = 2}
, 
{Code = "it_1_2_1", Weight = 5}
}
, Cd = 3300, InitialNumber = 14, Frequency = 14, Capacity = 42, SpeedUpPrice = 9}
, 
{Type = "pd_1_8", MergedType = "pd_1_9", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, UseEnergy = 1, 
TapeItems = {
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_1_1_1", Weight = 8}
, 
{Code = "it_1_1_2", Weight = 4}
, 
{Code = "it_1_1_3", Weight = 3}
, 
{Code = "it_1_2_1", Weight = 5}
}
, Cd = 2700, InitialNumber = 16, Frequency = 16, Capacity = 48, SpeedUpPrice = 11}
, 
{Type = "pd_1_9", MergedType = "pd_1_10", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 8000, UseEnergy = 1, 
TapeItems = {
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_1_1_1", Weight = 6}
, 
{Code = "it_1_1_2", Weight = 5}
, 
{Code = "it_1_1_3", Weight = 4}
, 
{Code = "it_1_2_1", Weight = 5}
}
, Cd = 1800, InitialNumber = 18, Frequency = 18, Capacity = 54, SpeedUpPrice = 13}
, 
{Type = "pd_1_10", 
Category = {2}
, BookOrder = 1001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, UseEnergy = 1, 
TapeItems = {
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_1_1", Weight = 1}
, 
{Code = "it_1_1_3", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
, 
{Code = "it_1_2_1", Weight = 1}
, 
{Code = "it_1_1_2", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_1_1_1", Weight = 4}
, 
{Code = "it_1_1_2", Weight = 6}
, 
{Code = "it_1_1_3", Weight = 5}
, 
{Code = "it_1_2_1", Weight = 5}
}
, Cd = 1500, InitialNumber = 20, Frequency = 20, Capacity = 60, SpeedUpPrice = 15}
, 
{Type = "it_1_1_1", MergedType = "it_1_1_2", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 0.42, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 3}
, 
{Type = "it_1_1_2", MergedType = "it_1_1_3", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 0.83, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 5}
, 
{Type = "it_1_1_3", MergedType = "it_1_1_4", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1.67, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 8}
, 
{Type = "it_1_1_4", MergedType = "it_1_1_5", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 3.33, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 11}
, 
{Type = "it_1_1_5", MergedType = "it_1_1_6", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 6.67, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 16}
, 
{Type = "it_1_1_6", MergedType = "it_1_1_7", 
Category = {1, 3}
, BookOrder = 1002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 13.33, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_1_1_1_1", Weight = 1}
}
, Cd = 3, InitialNumber = 0, Frequency = 1, Capacity = 1, SpeedUpPrice = 1, 
Transform = {
{Currency = "it_1_1_2_1", Amount = 1}
}
, DropsTotal = 1, DropOnSpot = 1, Reward = 32}
, 
{Type = "it_1_1_7", MergedType = "it_1_1_8", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 26.67, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 59}
, 
{Type = "it_1_1_8", MergedType = "it_1_1_9", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 53.33, BubbleChance = 25, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 113}
, 
{Type = "it_1_1_9", MergedType = "it_1_1_10", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 106.67, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 216}
, 
{Type = "it_1_1_10", MergedType = "it_1_1_11", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 213.33, BubbleChance = 30, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 408}
, 
{Type = "it_1_1_11", MergedType = "it_1_1_12", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 426.67, BubbleChance = 30, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 11}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 764}
, 
{Type = "it_1_1_12", 
Category = {1}
, BookOrder = 1002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 853.33, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 20}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 1423}
, 
{Type = "it_1_1_1_1", MergedType = "it_1_1_1_2", 
Category = {1}
, BookOrder = 1004, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 7.17, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"it_1_1_6"}
, Reward = 19}
, 
{Type = "it_1_1_1_2", MergedType = "it_1_1_1_3", 
Category = {1}
, BookOrder = 1004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 14.33, BubbleChance = 5, UnlockPrice = 135, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"it_1_1_6"}
, Reward = 32}
, 
{Type = "it_1_1_1_3", MergedType = "it_1_1_1_4", 
Category = {1}
, BookOrder = 1004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 28.67, BubbleChance = 10, UnlockPrice = 268, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"it_1_1_6"}
, Reward = 59}
, 
{Type = "it_1_1_1_4", MergedType = "it_1_1_1_5", 
Category = {1}
, BookOrder = 1004, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 57.33, BubbleChance = 15, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"it_1_1_6"}
, Reward = 111}
, 
{Type = "it_1_1_1_5", MergedType = "it_1_1_1_6", 
Category = {1}
, BookOrder = 1004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 114.67, BubbleChance = 20, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"it_1_1_6"}
, Reward = 205}
, 
{Type = "it_1_1_1_6", MergedType = "it_1_1_1_7", 
Category = {1}
, BookOrder = 1004, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 229.33, BubbleChance = 25, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Generators = {"it_1_1_6"}
, Reward = 383}
, 
{Type = "it_1_1_1_7", 
Category = {1}
, BookOrder = 1004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 458.67, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 11}
, 
Generators = {"it_1_1_6"}
, Reward = 713}
, 
{Type = "it_1_1_2_1", MergedType = "it_1_1_2_2", 
Category = {1}
, BookOrder = 1005, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 7.17, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"it_1_1_6"}
, Reward = 19}
, 
{Type = "it_1_1_2_2", MergedType = "it_1_1_2_3", 
Category = {1}
, BookOrder = 1005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 14.33, BubbleChance = 5, UnlockPrice = 135, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"it_1_1_6"}
, Reward = 32}
, 
{Type = "it_1_1_2_3", MergedType = "it_1_1_2_4", 
Category = {1}
, BookOrder = 1005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 28.67, BubbleChance = 10, UnlockPrice = 268, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"it_1_1_6"}
, Reward = 59}
, 
{Type = "it_1_1_2_4", MergedType = "it_1_1_2_5", 
Category = {1}
, BookOrder = 1005, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 57.33, BubbleChance = 15, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"it_1_1_6"}
, Reward = 111}
, 
{Type = "it_1_1_2_5", MergedType = "it_1_1_2_6", 
Category = {1}
, BookOrder = 1005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 114.67, BubbleChance = 20, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"it_1_1_6"}
, Reward = 205}
, 
{Type = "it_1_1_2_6", MergedType = "it_1_1_2_7", 
Category = {1}
, BookOrder = 1005, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 229.33, BubbleChance = 25, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Generators = {"it_1_1_6"}
, Reward = 383}
, 
{Type = "it_1_1_2_7", 
Category = {1}
, BookOrder = 1005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 458.67, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 11}
, 
Generators = {"it_1_1_6"}
, Reward = 713}
, 
{Type = "it_1_2_1", MergedType = "it_1_2_2", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 2, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 5}
, 
{Type = "it_1_2_2", MergedType = "it_1_2_3", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4, BubbleChance = 5, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 11}
, 
{Type = "it_1_2_3", MergedType = "it_1_2_4", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 8, BubbleChance = 10, UnlockPrice = 268, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 19}
, 
{Type = "it_1_2_4", MergedType = "it_1_2_5", 
Category = {1, 3}
, BookOrder = 1003, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 16, BubbleChance = 15, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_1_2_1_1", Weight = 1}
}
, Cd = 3, InitialNumber = 0, Frequency = 1, Capacity = 1, SpeedUpPrice = 1, DropsTotal = 1, DropOnSpot = 1, Reward = 38}
, 
{Type = "it_1_2_5", MergedType = "it_1_2_6", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 32, BubbleChance = 20, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 70}
, 
{Type = "it_1_2_6", MergedType = "it_1_2_7", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 64, BubbleChance = 25, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 135}
, 
{Type = "it_1_2_7", MergedType = "it_1_2_8", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 128, BubbleChance = 30, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 257}
, 
{Type = "it_1_2_8", MergedType = "it_1_2_9", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 256, BubbleChance = 30, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 7}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 483}
, 
{Type = "it_1_2_9", MergedType = "it_1_2_10", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 512, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 13}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 913}
, 
{Type = "it_1_2_10", 
Category = {1}
, BookOrder = 1003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1024, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 24}
, 
Generators = {"pd_1_4", "pd_1_5", "pd_1_6", "pd_1_7", "pd_1_8", "pd_1_9", "pd_1_10"}
, Reward = 1715}
, 
{Type = "it_1_2_1_1", MergedType = "it_1_2_1_2", 
Category = {1}
, BookOrder = 1006, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 17, UnlockPrice = 110, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"it_1_2_4"}
, Reward = 41}
, 
{Type = "it_1_2_1_2", MergedType = "it_1_2_1_3", 
Category = {1}
, BookOrder = 1006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 34, BubbleChance = 20, UnlockPrice = 135, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"it_1_2_4"}
, Reward = 73}
, 
{Type = "it_1_2_1_3", 
Category = {1}
, BookOrder = 1006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 68, BubbleChance = 25, UnlockPrice = 268, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"it_1_2_4"}
, Reward = 138}
, 
{Type = "pd_2_1", MergedType = "pd_2_2", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "pd_2_2", MergedType = "pd_2_3", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "pd_2_3", MergedType = "pd_2_4", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "pd_2_4", MergedType = "pd_2_5", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, UseEnergy = 1, 
TapeItems = {
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_2_1_1", Weight = 14}
, 
{Code = "it_2_2_1", Weight = 3}
, 
{Code = "it_2_3_1", Weight = 3}
}
, Cd = 4800, InitialNumber = 8, Frequency = 8, Capacity = 24, SpeedUpPrice = 4}
, 
{Type = "pd_2_5", MergedType = "pd_2_6", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, UseEnergy = 1, 
TapeItems = {
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_2_1_1", Weight = 7}
, 
{Code = "it_2_1_2", Weight = 7}
, 
{Code = "it_2_2_1", Weight = 3}
, 
{Code = "it_2_3_1", Weight = 3}
}
, Cd = 3000, InitialNumber = 18, Frequency = 10, Capacity = 30, SpeedUpPrice = 6}
, 
{Type = "pd_2_6", MergedType = "pd_2_7", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, UseEnergy = 1, 
TapeItems = {
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_2_1_1", Weight = 8}
, 
{Code = "it_2_1_2", Weight = 5}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 3}
, 
{Code = "it_2_3_1", Weight = 3}
}
, Cd = 1800, InitialNumber = 12, Frequency = 12, Capacity = 36, SpeedUpPrice = 8}
, 
{Type = "pd_2_7", MergedType = "pd_2_8", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, UseEnergy = 1, 
TapeItems = {
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_2_1_1", Weight = 8}
, 
{Code = "it_2_1_2", Weight = 4}
, 
{Code = "it_2_1_3", Weight = 2}
, 
{Code = "it_2_2_1", Weight = 3}
, 
{Code = "it_2_3_1", Weight = 3}
}
, Cd = 1200, InitialNumber = 14, Frequency = 14, Capacity = 42, SpeedUpPrice = 9}
, 
{Type = "pd_2_8", MergedType = "pd_2_9", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, UseEnergy = 1, 
TapeItems = {
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_2_1_1", Weight = 7}
, 
{Code = "it_2_1_2", Weight = 4}
, 
{Code = "it_2_1_3", Weight = 3}
, 
{Code = "it_2_2_1", Weight = 3}
, 
{Code = "it_2_3_1", Weight = 3}
}
, Cd = 900, InitialNumber = 16, Frequency = 16, Capacity = 48, SpeedUpPrice = 11}
, 
{Type = "pd_2_9", MergedType = "pd_2_10", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, UseEnergy = 1, 
TapeItems = {
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_2_1_1", Weight = 5}
, 
{Code = "it_2_1_2", Weight = 5}
, 
{Code = "it_2_1_3", Weight = 4}
, 
{Code = "it_2_2_1", Weight = 3}
, 
{Code = "it_2_3_1", Weight = 3}
}
, Cd = 600, InitialNumber = 18, Frequency = 18, Capacity = 54, SpeedUpPrice = 13}
, 
{Type = "pd_2_10", 
Category = {2}
, BookOrder = 1010, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 12000, UseEnergy = 1, 
TapeItems = {
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_1_2", Weight = 1}
, 
{Code = "it_2_3_1", Weight = 1}
, 
{Code = "it_2_1_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
, 
{Code = "it_2_2_1", Weight = 1}
, 
{Code = "it_2_1_3", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_2_1_1", Weight = 5}
, 
{Code = "it_2_1_2", Weight = 3}
, 
{Code = "it_2_1_3", Weight = 6}
, 
{Code = "it_2_2_1", Weight = 3}
, 
{Code = "it_2_3_1", Weight = 3}
}
, Cd = 480, InitialNumber = 20, Frequency = 20, Capacity = 60, SpeedUpPrice = 15}
, 
{Type = "it_2_1_1", MergedType = "it_2_1_2", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 0.28, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 3}
, 
{Type = "it_2_1_2", MergedType = "it_2_1_3", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 0.56, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 5}
, 
{Type = "it_2_1_3", MergedType = "it_2_1_4", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1.11, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 8}
, 
{Type = "it_2_1_4", MergedType = "it_2_1_5", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 2.22, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 11}
, 
{Type = "it_2_1_5", MergedType = "it_2_1_6", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4.44, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 16}
, 
{Type = "it_2_1_6", MergedType = "it_2_1_7", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 8.89, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 22}
, 
{Type = "it_2_1_7", MergedType = "it_2_1_8", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 17.78, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 41}
, 
{Type = "it_2_1_8", MergedType = "it_2_1_9", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 35.56, BubbleChance = 25, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 76}
, 
{Type = "it_2_1_9", MergedType = "it_2_1_10", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 71.12, BubbleChance = 25, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 143}
, 
{Type = "it_2_1_10", MergedType = "it_2_1_11", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 142.22, BubbleChance = 30, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 270}
, 
{Type = "it_2_1_11", MergedType = "it_2_1_12", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 284.44, BubbleChance = 30, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 7}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 510}
, 
{Type = "it_2_1_12", 
Category = {1}
, BookOrder = 1011, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 568.89, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 13}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 964}
, 
{Type = "it_2_2_1", MergedType = "it_2_2_2", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 2.22, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 5}
, 
{Type = "it_2_2_2", MergedType = "it_2_2_3", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4.44, BubbleChance = 5, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 11}
, 
{Type = "it_2_2_3", MergedType = "it_2_2_4", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 8.89, BubbleChance = 10, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 22}
, 
{Type = "it_2_2_4", MergedType = "it_2_2_5", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 17.78, BubbleChance = 15, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 41}
, 
{Type = "it_2_2_5", MergedType = "it_2_2_6", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 35.56, BubbleChance = 20, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 78}
, 
{Type = "it_2_2_6", MergedType = "it_2_2_7", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 71.11, BubbleChance = 25, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 149}
, 
{Type = "it_2_2_7", MergedType = "it_2_2_8", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 142.22, BubbleChance = 25, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 284}
, 
{Type = "it_2_2_8", MergedType = "it_2_2_9", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 284.44, BubbleChance = 30, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 8}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 535}
, 
{Type = "it_2_2_9", MergedType = "it_2_2_10", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 568.89, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 14}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 1007}
, 
{Type = "it_2_2_10", 
Category = {1}
, BookOrder = 1012, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 1137.78, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 26}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 1893}
, 
{Type = "it_2_3_1", MergedType = "it_2_3_2", 
Category = {1}
, BookOrder = 1013, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 2.22, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 5}
, 
{Type = "it_2_3_2", MergedType = "it_2_3_3", 
Category = {1, 3}
, BookOrder = 1013, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4.44, BubbleChance = 5, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_2_3_1_1", Weight = 1}
}
, Cd = 3, InitialNumber = 0, Frequency = 1, Capacity = 1, SpeedUpPrice = 1, DropsTotal = 1, DropOnSpot = 1, Reward = 11}
, 
{Type = "it_2_3_3", MergedType = "it_2_3_4", 
Category = {1}
, BookOrder = 1013, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 8.89, BubbleChance = 10, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 22}
, 
{Type = "it_2_3_4", MergedType = "it_2_3_5", 
Category = {1}
, BookOrder = 1013, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 17.78, BubbleChance = 15, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 41}
, 
{Type = "it_2_3_5", MergedType = "it_2_3_6", 
Category = {1}
, BookOrder = 1013, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 35.56, BubbleChance = 20, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 78}
, 
{Type = "it_2_3_6", MergedType = "it_2_3_7", 
Category = {1}
, BookOrder = 1013, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 71.11, BubbleChance = 25, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 149}
, 
{Type = "it_2_3_7", MergedType = "it_2_3_8", 
Category = {1}
, BookOrder = 1013, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 142.22, BubbleChance = 30, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 270}
, 
{Type = "it_2_3_8", 
Category = {1}
, BookOrder = 1013, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 284.44, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 7}
, 
Generators = {"pd_2_4", "pd_2_5", "pd_2_6", "pd_2_7", "pd_2_8", "pd_2_9", "pd_2_10"}
, Reward = 535}
, 
{Type = "it_2_3_1_1", MergedType = "it_2_3_1_2", 
Category = {1}
, BookOrder = 1014, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 5.44, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"it_2_3_2"}
, Reward = 8}
, 
{Type = "it_2_3_1_2", MergedType = "it_2_3_1_3", 
Category = {1, 4}
, BookOrder = 1014, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 10.89, BubbleChance = 10, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"it_2_3_2"}
, Reward = 14, 
Recipes = {
{Recipe = "ds_fd_1", Dur = 60}
, 
{Recipe = "ds_fd_2", Dur = 60}
, 
{Recipe = "ds_fd_3", Dur = 60}
, 
{Recipe = "ds_fd_4", Dur = 60}
}
}
, 
{Type = "it_2_3_1_3", MergedType = "it_2_3_1_4", 
Category = {1}
, BookOrder = 1014, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 21.78, BubbleChance = 15, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"it_2_3_2"}
, Reward = 22}
, 
{Type = "it_2_3_1_4", MergedType = "it_2_3_1_5", 
Category = {1}
, BookOrder = 1014, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 43.56, BubbleChance = 20, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"it_2_3_2"}
, Reward = 41}
, 
{Type = "it_2_3_1_5", MergedType = "it_2_3_1_6", 
Category = {1}
, BookOrder = 1014, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 87.11, BubbleChance = 25, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"it_2_3_2"}
, Reward = 51}
, 
{Type = "it_2_3_1_6", 
Category = {1}
, BookOrder = 1014, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 174.22, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"it_2_3_2"}
, Reward = 149}
, 
{Type = "pd_3_1", MergedType = "pd_3_2", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "pd_3_2", MergedType = "pd_3_3", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "pd_3_3", MergedType = "pd_3_4", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "pd_3_4", MergedType = "pd_3_5", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, UseEnergy = 1, 
TapeItems = {
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_3_1_1", Weight = 15}
, 
{Code = "it_3_2_1", Weight = 5}
}
, Cd = 4200, InitialNumber = 8, Frequency = 8, Capacity = 24, SpeedUpPrice = 4}
, 
{Type = "pd_3_5", MergedType = "pd_3_6", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, UseEnergy = 1, 
TapeItems = {
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_3_1_1", Weight = 9}
, 
{Code = "it_3_1_2", Weight = 6}
, 
{Code = "it_3_2_1", Weight = 5}
}
, Cd = 3600, InitialNumber = 10, Frequency = 10, Capacity = 30, SpeedUpPrice = 6}
, 
{Type = "pd_3_6", MergedType = "pd_3_7", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, UseEnergy = 1, 
TapeItems = {
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_3_1_1", Weight = 10}
, 
{Code = "it_3_1_2", Weight = 4}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 5}
}
, Cd = 2400, InitialNumber = 12, Frequency = 12, Capacity = 36, SpeedUpPrice = 8}
, 
{Type = "pd_3_7", MergedType = "pd_3_8", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, UseEnergy = 1, 
TapeItems = {
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_3_1_1", Weight = 10}
, 
{Code = "it_3_1_2", Weight = 3}
, 
{Code = "it_3_1_3", Weight = 2}
, 
{Code = "it_3_2_1", Weight = 5}
}
, Cd = 2100, InitialNumber = 14, Frequency = 14, Capacity = 42, SpeedUpPrice = 9}
, 
{Type = "pd_3_8", MergedType = "pd_3_9", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, UseEnergy = 1, 
TapeItems = {
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_3_1_1", Weight = 8}
, 
{Code = "it_3_1_2", Weight = 4}
, 
{Code = "it_3_1_3", Weight = 3}
, 
{Code = "it_3_2_1", Weight = 5}
}
, Cd = 1500, InitialNumber = 16, Frequency = 16, Capacity = 48, SpeedUpPrice = 11}
, 
{Type = "pd_3_9", MergedType = "pd_3_10", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, UseEnergy = 1, 
TapeItems = {
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_3_1_1", Weight = 6}
, 
{Code = "it_3_1_2", Weight = 5}
, 
{Code = "it_3_1_3", Weight = 4}
, 
{Code = "it_3_2_1", Weight = 5}
}
, Cd = 900, InitialNumber = 18, Frequency = 18, Capacity = 54, SpeedUpPrice = 13}
, 
{Type = "pd_3_10", 
Category = {2}
, BookOrder = 1020, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 12000, UseEnergy = 1, 
TapeItems = {
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_1_1", Weight = 1}
, 
{Code = "it_3_1_3", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
, 
{Code = "it_3_2_1", Weight = 1}
, 
{Code = "it_3_1_2", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_3_1_1", Weight = 4}
, 
{Code = "it_3_1_2", Weight = 6}
, 
{Code = "it_3_1_3", Weight = 5}
, 
{Code = "it_3_2_1", Weight = 5}
}
, Cd = 780, InitialNumber = 20, Frequency = 20, Capacity = 60, SpeedUpPrice = 15}
, 
{Type = "it_3_1_1", MergedType = "it_3_1_2", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 0.42, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 3}
, 
{Type = "it_3_1_2", MergedType = "it_3_1_3", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 0.83, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 5}
, 
{Type = "it_3_1_3", MergedType = "it_3_1_4", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1.67, BubbleChance = 5, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 8}
, 
{Type = "it_3_1_4", MergedType = "it_3_1_5", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 3.33, BubbleChance = 10, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 11}
, 
{Type = "it_3_1_5", MergedType = "it_3_1_6", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 6.67, BubbleChance = 15, UnlockPrice = 824, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 16}
, 
{Type = "it_3_1_6", MergedType = "it_3_1_7", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 13.33, BubbleChance = 20, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 30}
, 
{Type = "it_3_1_7", MergedType = "it_3_1_8", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 26.67, BubbleChance = 25, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 57}
, 
{Type = "it_3_1_8", MergedType = "it_3_1_9", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 53.33, BubbleChance = 30, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 108}
, 
{Type = "it_3_1_9", MergedType = "it_3_1_10", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 106.67, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 203}
, 
{Type = "it_3_1_10", MergedType = "it_3_1_11", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 213.33, BubbleChance = 30, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 383}
, 
{Type = "it_3_1_11", 
Category = {1}
, BookOrder = 1021, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 426.67, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 10}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 724}
, 
{Type = "it_3_2_1", MergedType = "it_3_2_2", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 2, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 5}
, 
{Type = "it_3_2_2", MergedType = "it_3_2_3", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4, BubbleChance = 5, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 11}
, 
{Type = "it_3_2_3", MergedType = "it_3_2_4", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 8, BubbleChance = 10, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 19}
, 
{Type = "it_3_2_4", MergedType = "it_3_2_5", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 16, BubbleChance = 15, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 38}
, 
{Type = "it_3_2_5", MergedType = "it_3_2_6", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 32, BubbleChance = 20, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 70}
, 
{Type = "it_3_2_6", MergedType = "it_3_2_7", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 64, BubbleChance = 25, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 132}
, 
{Type = "it_3_2_7", MergedType = "it_3_2_8", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 128, BubbleChance = 30, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 248}
, 
{Type = "it_3_2_8", MergedType = "it_3_2_9", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 256, BubbleChance = 30, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 7}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 470}
, 
{Type = "it_3_2_9", MergedType = "it_3_2_10", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 512, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 13}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 886}
, 
{Type = "it_3_2_10", 
Category = {1}
, BookOrder = 1022, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1024, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 24}
, 
Generators = {"pd_3_4", "pd_3_5", "pd_3_6", "pd_3_7", "pd_3_8", "pd_3_9", "pd_3_10"}
, Reward = 1671}
, 
{Type = "eq_1_1", MergedType = "eq_1_2", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "eq_1_2", MergedType = "eq_1_3", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "eq_1_3", MergedType = "eq_1_4", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "eq_1_4", MergedType = "eq_1_5", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, 
Recipes = {
{Recipe = "ds_chopve_1", Dur = 10}
, 
{Recipe = "ds_chopve_2", Dur = 20}
, 
{Recipe = "ds_chopve_3", Dur = 40}
, 
{Recipe = "ds_chopve_4", Dur = 80}
, 
{Recipe = "ds_chopfr_1", Dur = 320}
, 
{Recipe = "ds_grillmt_1", Dur = 20}
, 
{Recipe = "ds_mixdrk_1", Dur = 120}
, 
{Recipe = "ds_mixdrk_2", Dur = 70}
, 
{Recipe = "ds_mixdrk_3", Dur = 70}
, 
{Recipe = "ds_mixdrk_4", Dur = 150}
, 
{Recipe = "ds_mixdrk_5", Dur = 80}
, 
{Recipe = "ds_mixdrk_6", Dur = 70}
, 
{Recipe = "ds_mixdrk_7", Dur = 70}
, 
{Recipe = "ds_mixdrk_8", Dur = 480}
, 
{Recipe = "ds_mixdrk_9", Dur = 1280}
, 
{Recipe = "ds_dst_1", Dur = 160}
, 
{Recipe = "ds_chopfs_1", Dur = 320}
, 
{Recipe = "ds_fd_8", Dur = 10}
, 
{Recipe = "ds_fd_9", Dur = 10}
, 
{Recipe = "ds_fd_10", Dur = 50}
, 
{Recipe = "ds_fd_11", Dur = 10}
, 
{Recipe = "ds_fd_12", Dur = 10}
, 
{Recipe = "ds_fd_13", Dur = 40}
, 
{Recipe = "ds_sal_1", Dur = 1280}
, 
{Recipe = "ds_chopfru_1", Dur = 640}
, 
{Recipe = "ds_grillsf_7", Dur = 1280}
, 
{Recipe = "ds_flb_1", Dur = 40}
, 
{Recipe = "ds_flb_2", Dur = 80}
, 
{Recipe = "ds_e1hotdrk_1", Dur = 340}
, 
{Recipe = "ds_e1cockt_1", Dur = 20}
, 
{Recipe = "ds_e1cockt_2", Dur = 40}
, 
{Recipe = "ds_e1cockt_3", Dur = 40}
, 
{Recipe = "ds_e1cockt_4", Dur = 40}
, 
{Recipe = "ds_e1cockt_5", Dur = 40}
, 
{Recipe = "ds_e1cockt_6", Dur = 40}
, 
{Recipe = "ds_e1cockt_8", Dur = 70}
, 
{Recipe = "ds_e1cockt_9", Dur = 40}
, 
{Recipe = "ds_e1cockt_10", Dur = 70}
, 
{Recipe = "ds_e1cockt_11", Dur = 80}
, 
{Recipe = "ds_e1cockt_12", Dur = 80}
, 
{Recipe = "ds_e1cockt_13", Dur = 80}
, 
{Recipe = "ds_e1cockt_14", Dur = 80}
, 
{Recipe = "ds_e1icytre_1", Dur = 80}
, 
{Recipe = "ds_e1icytre_2", Dur = 120}
, 
{Recipe = "ds_e1cockt_15", Dur = 120}
, 
{Recipe = "ds_e1cockt_16", Dur = 120}
, 
{Recipe = "ds_e1cockt_17", Dur = 640}
, 
{Recipe = "ds_e1cockt_18", Dur = 640}
, 
{Recipe = "ds_e1cockt_19", Dur = 720}
, 
{Recipe = "ds_e1hotdrk_2", Dur = 1280}
, 
{Recipe = "ds_e1cockt_20", Dur = 2560}
, 
{Recipe = "ds_6e1preingre_1", Dur = 50}
, 
{Recipe = "ds_6e1sala_2", Dur = 640}
, 
{Recipe = "ds_6e1icytre_3", Dur = 1280}
, 
{Recipe = "ds_6e1icytre_4", Dur = 1280}
, 
{Recipe = "ds_6e1dst_4", Dur = 1280}
, 
{Recipe = "ds_6e1icytre_5", Dur = 720}
, 
{Recipe = "ds_6e1assort_2", Dur = 640}
}
}
, 
{Type = "eq_1_5", MergedType = "eq_1_6", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, 
Recipes = {
{Recipe = "ds_chopve_1", Dur = 9}
, 
{Recipe = "ds_chopve_2", Dur = 18}
, 
{Recipe = "ds_chopve_3", Dur = 36}
, 
{Recipe = "ds_chopve_4", Dur = 72}
, 
{Recipe = "ds_chopfr_1", Dur = 288}
, 
{Recipe = "ds_grillmt_1", Dur = 18}
, 
{Recipe = "ds_mixdrk_1", Dur = 108}
, 
{Recipe = "ds_mixdrk_2", Dur = 63}
, 
{Recipe = "ds_mixdrk_3", Dur = 63}
, 
{Recipe = "ds_mixdrk_4", Dur = 135}
, 
{Recipe = "ds_mixdrk_5", Dur = 72}
, 
{Recipe = "ds_mixdrk_6", Dur = 63}
, 
{Recipe = "ds_mixdrk_7", Dur = 63}
, 
{Recipe = "ds_mixdrk_8", Dur = 432}
, 
{Recipe = "ds_mixdrk_9", Dur = 1152}
, 
{Recipe = "ds_dst_1", Dur = 144}
, 
{Recipe = "ds_chopfs_1", Dur = 288}
, 
{Recipe = "ds_fd_8", Dur = 9}
, 
{Recipe = "ds_fd_9", Dur = 9}
, 
{Recipe = "ds_fd_10", Dur = 45}
, 
{Recipe = "ds_fd_11", Dur = 9}
, 
{Recipe = "ds_fd_12", Dur = 9}
, 
{Recipe = "ds_fd_13", Dur = 36}
, 
{Recipe = "ds_sal_1", Dur = 1152}
, 
{Recipe = "ds_chopfru_1", Dur = 576}
, 
{Recipe = "ds_grillsf_7", Dur = 1152}
, 
{Recipe = "ds_flb_1", Dur = 36}
, 
{Recipe = "ds_flb_2", Dur = 72}
, 
{Recipe = "ds_e1hotdrk_1", Dur = 306}
, 
{Recipe = "ds_e1cockt_1", Dur = 18}
, 
{Recipe = "ds_e1cockt_2", Dur = 36}
, 
{Recipe = "ds_e1cockt_3", Dur = 36}
, 
{Recipe = "ds_e1cockt_4", Dur = 36}
, 
{Recipe = "ds_e1cockt_5", Dur = 36}
, 
{Recipe = "ds_e1cockt_6", Dur = 36}
, 
{Recipe = "ds_e1cockt_8", Dur = 63}
, 
{Recipe = "ds_e1cockt_9", Dur = 36}
, 
{Recipe = "ds_e1cockt_10", Dur = 63}
, 
{Recipe = "ds_e1cockt_11", Dur = 72}
, 
{Recipe = "ds_e1cockt_12", Dur = 72}
, 
{Recipe = "ds_e1cockt_13", Dur = 72}
, 
{Recipe = "ds_e1cockt_14", Dur = 72}
, 
{Recipe = "ds_e1icytre_1", Dur = 72}
, 
{Recipe = "ds_e1icytre_2", Dur = 108}
, 
{Recipe = "ds_e1cockt_15", Dur = 108}
, 
{Recipe = "ds_e1cockt_16", Dur = 108}
, 
{Recipe = "ds_e1cockt_17", Dur = 576}
, 
{Recipe = "ds_e1cockt_18", Dur = 576}
, 
{Recipe = "ds_e1cockt_19", Dur = 648}
, 
{Recipe = "ds_e1hotdrk_2", Dur = 1152}
, 
{Recipe = "ds_e1cockt_20", Dur = 2304}
, 
{Recipe = "ds_6e1preingre_1", Dur = 45}
, 
{Recipe = "ds_6e1sala_2", Dur = 576}
, 
{Recipe = "ds_6e1icytre_3", Dur = 1152}
, 
{Recipe = "ds_6e1icytre_4", Dur = 1152}
, 
{Recipe = "ds_6e1dst_4", Dur = 1152}
, 
{Recipe = "ds_6e1icytre_5", Dur = 648}
, 
{Recipe = "ds_6e1assort_2", Dur = 576}
}
}
, 
{Type = "eq_1_6", MergedType = "eq_1_7", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, 
Recipes = {
{Recipe = "ds_chopve_1", Dur = 8}
, 
{Recipe = "ds_chopve_2", Dur = 16}
, 
{Recipe = "ds_chopve_3", Dur = 32}
, 
{Recipe = "ds_chopve_4", Dur = 64}
, 
{Recipe = "ds_chopfr_1", Dur = 256}
, 
{Recipe = "ds_grillmt_1", Dur = 16}
, 
{Recipe = "ds_mixdrk_1", Dur = 96}
, 
{Recipe = "ds_mixdrk_2", Dur = 56}
, 
{Recipe = "ds_mixdrk_3", Dur = 56}
, 
{Recipe = "ds_mixdrk_4", Dur = 120}
, 
{Recipe = "ds_mixdrk_5", Dur = 64}
, 
{Recipe = "ds_mixdrk_6", Dur = 56}
, 
{Recipe = "ds_mixdrk_7", Dur = 56}
, 
{Recipe = "ds_mixdrk_8", Dur = 384}
, 
{Recipe = "ds_mixdrk_9", Dur = 1024}
, 
{Recipe = "ds_dst_1", Dur = 128}
, 
{Recipe = "ds_chopfs_1", Dur = 256}
, 
{Recipe = "ds_fd_8", Dur = 8}
, 
{Recipe = "ds_fd_9", Dur = 8}
, 
{Recipe = "ds_fd_10", Dur = 40}
, 
{Recipe = "ds_fd_11", Dur = 8}
, 
{Recipe = "ds_fd_12", Dur = 8}
, 
{Recipe = "ds_fd_13", Dur = 32}
, 
{Recipe = "ds_sal_1", Dur = 1024}
, 
{Recipe = "ds_chopfru_1", Dur = 512}
, 
{Recipe = "ds_grillsf_7", Dur = 1024}
, 
{Recipe = "ds_flb_1", Dur = 32}
, 
{Recipe = "ds_flb_2", Dur = 64}
, 
{Recipe = "ds_e1hotdrk_1", Dur = 272}
, 
{Recipe = "ds_e1cockt_1", Dur = 16}
, 
{Recipe = "ds_e1cockt_2", Dur = 32}
, 
{Recipe = "ds_e1cockt_3", Dur = 32}
, 
{Recipe = "ds_e1cockt_4", Dur = 32}
, 
{Recipe = "ds_e1cockt_5", Dur = 32}
, 
{Recipe = "ds_e1cockt_6", Dur = 32}
, 
{Recipe = "ds_e1cockt_8", Dur = 56}
, 
{Recipe = "ds_e1cockt_9", Dur = 32}
, 
{Recipe = "ds_e1cockt_10", Dur = 56}
, 
{Recipe = "ds_e1cockt_11", Dur = 64}
, 
{Recipe = "ds_e1cockt_12", Dur = 64}
, 
{Recipe = "ds_e1cockt_13", Dur = 64}
, 
{Recipe = "ds_e1cockt_14", Dur = 64}
, 
{Recipe = "ds_e1icytre_1", Dur = 64}
, 
{Recipe = "ds_e1icytre_2", Dur = 96}
, 
{Recipe = "ds_e1cockt_15", Dur = 96}
, 
{Recipe = "ds_e1cockt_16", Dur = 96}
, 
{Recipe = "ds_e1cockt_17", Dur = 512}
, 
{Recipe = "ds_e1cockt_18", Dur = 512}
, 
{Recipe = "ds_e1cockt_19", Dur = 576}
, 
{Recipe = "ds_e1hotdrk_2", Dur = 1024}
, 
{Recipe = "ds_e1cockt_20", Dur = 2048}
, 
{Recipe = "ds_6e1preingre_1", Dur = 40}
, 
{Recipe = "ds_6e1sala_2", Dur = 512}
, 
{Recipe = "ds_6e1icytre_3", Dur = 1024}
, 
{Recipe = "ds_6e1icytre_4", Dur = 1024}
, 
{Recipe = "ds_6e1dst_4", Dur = 1024}
, 
{Recipe = "ds_6e1icytre_5", Dur = 576}
, 
{Recipe = "ds_6e1assort_2", Dur = 512}
}
}
, 
{Type = "eq_1_7", MergedType = "eq_1_8", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, 
Recipes = {
{Recipe = "ds_chopve_1", Dur = 7}
, 
{Recipe = "ds_chopve_2", Dur = 14}
, 
{Recipe = "ds_chopve_3", Dur = 28}
, 
{Recipe = "ds_chopve_4", Dur = 56}
, 
{Recipe = "ds_chopfr_1", Dur = 224}
, 
{Recipe = "ds_grillmt_1", Dur = 14}
, 
{Recipe = "ds_mixdrk_1", Dur = 84}
, 
{Recipe = "ds_mixdrk_2", Dur = 49}
, 
{Recipe = "ds_mixdrk_3", Dur = 49}
, 
{Recipe = "ds_mixdrk_4", Dur = 105}
, 
{Recipe = "ds_mixdrk_5", Dur = 56}
, 
{Recipe = "ds_mixdrk_6", Dur = 49}
, 
{Recipe = "ds_mixdrk_7", Dur = 49}
, 
{Recipe = "ds_mixdrk_8", Dur = 336}
, 
{Recipe = "ds_mixdrk_9", Dur = 896}
, 
{Recipe = "ds_dst_1", Dur = 112}
, 
{Recipe = "ds_chopfs_1", Dur = 224}
, 
{Recipe = "ds_fd_8", Dur = 7}
, 
{Recipe = "ds_fd_9", Dur = 7}
, 
{Recipe = "ds_fd_10", Dur = 35}
, 
{Recipe = "ds_fd_11", Dur = 7}
, 
{Recipe = "ds_fd_12", Dur = 7}
, 
{Recipe = "ds_fd_13", Dur = 28}
, 
{Recipe = "ds_sal_1", Dur = 896}
, 
{Recipe = "ds_chopfru_1", Dur = 448}
, 
{Recipe = "ds_grillsf_7", Dur = 896}
, 
{Recipe = "ds_flb_1", Dur = 28}
, 
{Recipe = "ds_flb_2", Dur = 56}
, 
{Recipe = "ds_e1hotdrk_1", Dur = 238}
, 
{Recipe = "ds_e1cockt_1", Dur = 14}
, 
{Recipe = "ds_e1cockt_2", Dur = 28}
, 
{Recipe = "ds_e1cockt_3", Dur = 28}
, 
{Recipe = "ds_e1cockt_4", Dur = 28}
, 
{Recipe = "ds_e1cockt_5", Dur = 28}
, 
{Recipe = "ds_e1cockt_6", Dur = 28}
, 
{Recipe = "ds_e1cockt_8", Dur = 49}
, 
{Recipe = "ds_e1cockt_9", Dur = 28}
, 
{Recipe = "ds_e1cockt_10", Dur = 49}
, 
{Recipe = "ds_e1cockt_11", Dur = 56}
, 
{Recipe = "ds_e1cockt_12", Dur = 56}
, 
{Recipe = "ds_e1cockt_13", Dur = 56}
, 
{Recipe = "ds_e1cockt_14", Dur = 56}
, 
{Recipe = "ds_e1icytre_1", Dur = 56}
, 
{Recipe = "ds_e1icytre_2", Dur = 84}
, 
{Recipe = "ds_e1cockt_15", Dur = 84}
, 
{Recipe = "ds_e1cockt_16", Dur = 84}
, 
{Recipe = "ds_e1cockt_17", Dur = 448}
, 
{Recipe = "ds_e1cockt_18", Dur = 448}
, 
{Recipe = "ds_e1cockt_19", Dur = 504}
, 
{Recipe = "ds_e1hotdrk_2", Dur = 896}
, 
{Recipe = "ds_e1cockt_20", Dur = 1792}
, 
{Recipe = "ds_6e1preingre_1", Dur = 35}
, 
{Recipe = "ds_6e1sala_2", Dur = 448}
, 
{Recipe = "ds_6e1icytre_3", Dur = 896}
, 
{Recipe = "ds_6e1icytre_4", Dur = 896}
, 
{Recipe = "ds_6e1dst_4", Dur = 896}
, 
{Recipe = "ds_6e1icytre_5", Dur = 504}
, 
{Recipe = "ds_6e1assort_2", Dur = 448}
}
}
, 
{Type = "eq_1_8", MergedType = "eq_1_9", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, 
Recipes = {
{Recipe = "ds_chopve_1", Dur = 6}
, 
{Recipe = "ds_chopve_2", Dur = 12}
, 
{Recipe = "ds_chopve_3", Dur = 24}
, 
{Recipe = "ds_chopve_4", Dur = 48}
, 
{Recipe = "ds_chopfr_1", Dur = 192}
, 
{Recipe = "ds_grillmt_1", Dur = 12}
, 
{Recipe = "ds_mixdrk_1", Dur = 72}
, 
{Recipe = "ds_mixdrk_2", Dur = 42}
, 
{Recipe = "ds_mixdrk_3", Dur = 42}
, 
{Recipe = "ds_mixdrk_4", Dur = 90}
, 
{Recipe = "ds_mixdrk_5", Dur = 48}
, 
{Recipe = "ds_mixdrk_6", Dur = 42}
, 
{Recipe = "ds_mixdrk_7", Dur = 42}
, 
{Recipe = "ds_mixdrk_8", Dur = 288}
, 
{Recipe = "ds_mixdrk_9", Dur = 768}
, 
{Recipe = "ds_dst_1", Dur = 96}
, 
{Recipe = "ds_chopfs_1", Dur = 192}
, 
{Recipe = "ds_fd_8", Dur = 6}
, 
{Recipe = "ds_fd_9", Dur = 6}
, 
{Recipe = "ds_fd_10", Dur = 30}
, 
{Recipe = "ds_fd_11", Dur = 6}
, 
{Recipe = "ds_fd_12", Dur = 6}
, 
{Recipe = "ds_fd_13", Dur = 24}
, 
{Recipe = "ds_sal_1", Dur = 768}
, 
{Recipe = "ds_chopfru_1", Dur = 384}
, 
{Recipe = "ds_grillsf_7", Dur = 768}
, 
{Recipe = "ds_flb_1", Dur = 24}
, 
{Recipe = "ds_flb_2", Dur = 48}
, 
{Recipe = "ds_e1hotdrk_1", Dur = 204}
, 
{Recipe = "ds_e1cockt_1", Dur = 12}
, 
{Recipe = "ds_e1cockt_2", Dur = 24}
, 
{Recipe = "ds_e1cockt_3", Dur = 24}
, 
{Recipe = "ds_e1cockt_4", Dur = 24}
, 
{Recipe = "ds_e1cockt_5", Dur = 24}
, 
{Recipe = "ds_e1cockt_6", Dur = 24}
, 
{Recipe = "ds_e1cockt_8", Dur = 42}
, 
{Recipe = "ds_e1cockt_9", Dur = 24}
, 
{Recipe = "ds_e1cockt_10", Dur = 42}
, 
{Recipe = "ds_e1cockt_11", Dur = 48}
, 
{Recipe = "ds_e1cockt_12", Dur = 48}
, 
{Recipe = "ds_e1cockt_13", Dur = 48}
, 
{Recipe = "ds_e1cockt_14", Dur = 48}
, 
{Recipe = "ds_e1icytre_1", Dur = 48}
, 
{Recipe = "ds_e1icytre_2", Dur = 72}
, 
{Recipe = "ds_e1cockt_15", Dur = 72}
, 
{Recipe = "ds_e1cockt_16", Dur = 72}
, 
{Recipe = "ds_e1cockt_17", Dur = 384}
, 
{Recipe = "ds_e1cockt_18", Dur = 384}
, 
{Recipe = "ds_e1cockt_19", Dur = 432}
, 
{Recipe = "ds_e1hotdrk_2", Dur = 768}
, 
{Recipe = "ds_e1cockt_20", Dur = 1536}
, 
{Recipe = "ds_6e1preingre_1", Dur = 30}
, 
{Recipe = "ds_6e1sala_2", Dur = 384}
, 
{Recipe = "ds_6e1icytre_3", Dur = 768}
, 
{Recipe = "ds_6e1icytre_4", Dur = 768}
, 
{Recipe = "ds_6e1dst_4", Dur = 768}
, 
{Recipe = "ds_6e1icytre_5", Dur = 432}
, 
{Recipe = "ds_6e1assort_2", Dur = 384}
}
}
, 
{Type = "eq_1_9", MergedType = "eq_1_10", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, 
Recipes = {
{Recipe = "ds_chopve_1", Dur = 5}
, 
{Recipe = "ds_chopve_2", Dur = 10}
, 
{Recipe = "ds_chopve_3", Dur = 20}
, 
{Recipe = "ds_chopve_4", Dur = 40}
, 
{Recipe = "ds_chopfr_1", Dur = 160}
, 
{Recipe = "ds_grillmt_1", Dur = 10}
, 
{Recipe = "ds_mixdrk_1", Dur = 60}
, 
{Recipe = "ds_mixdrk_2", Dur = 35}
, 
{Recipe = "ds_mixdrk_3", Dur = 35}
, 
{Recipe = "ds_mixdrk_4", Dur = 75}
, 
{Recipe = "ds_mixdrk_5", Dur = 40}
, 
{Recipe = "ds_mixdrk_6", Dur = 35}
, 
{Recipe = "ds_mixdrk_7", Dur = 35}
, 
{Recipe = "ds_mixdrk_8", Dur = 240}
, 
{Recipe = "ds_mixdrk_9", Dur = 640}
, 
{Recipe = "ds_dst_1", Dur = 80}
, 
{Recipe = "ds_chopfs_1", Dur = 160}
, 
{Recipe = "ds_fd_8", Dur = 5}
, 
{Recipe = "ds_fd_9", Dur = 5}
, 
{Recipe = "ds_fd_10", Dur = 25}
, 
{Recipe = "ds_fd_11", Dur = 5}
, 
{Recipe = "ds_fd_12", Dur = 5}
, 
{Recipe = "ds_fd_13", Dur = 20}
, 
{Recipe = "ds_sal_1", Dur = 640}
, 
{Recipe = "ds_chopfru_1", Dur = 320}
, 
{Recipe = "ds_grillsf_7", Dur = 640}
, 
{Recipe = "ds_flb_1", Dur = 20}
, 
{Recipe = "ds_flb_2", Dur = 40}
, 
{Recipe = "ds_e1hotdrk_1", Dur = 170}
, 
{Recipe = "ds_e1cockt_1", Dur = 10}
, 
{Recipe = "ds_e1cockt_2", Dur = 20}
, 
{Recipe = "ds_e1cockt_3", Dur = 20}
, 
{Recipe = "ds_e1cockt_4", Dur = 20}
, 
{Recipe = "ds_e1cockt_5", Dur = 20}
, 
{Recipe = "ds_e1cockt_6", Dur = 20}
, 
{Recipe = "ds_e1cockt_8", Dur = 35}
, 
{Recipe = "ds_e1cockt_9", Dur = 20}
, 
{Recipe = "ds_e1cockt_10", Dur = 35}
, 
{Recipe = "ds_e1cockt_11", Dur = 40}
, 
{Recipe = "ds_e1cockt_12", Dur = 40}
, 
{Recipe = "ds_e1cockt_13", Dur = 40}
, 
{Recipe = "ds_e1cockt_14", Dur = 40}
, 
{Recipe = "ds_e1icytre_1", Dur = 40}
, 
{Recipe = "ds_e1icytre_2", Dur = 60}
, 
{Recipe = "ds_e1cockt_15", Dur = 60}
, 
{Recipe = "ds_e1cockt_16", Dur = 60}
, 
{Recipe = "ds_e1cockt_17", Dur = 320}
, 
{Recipe = "ds_e1cockt_18", Dur = 320}
, 
{Recipe = "ds_e1cockt_19", Dur = 360}
, 
{Recipe = "ds_e1hotdrk_2", Dur = 640}
, 
{Recipe = "ds_e1cockt_20", Dur = 1280}
, 
{Recipe = "ds_6e1preingre_1", Dur = 25}
, 
{Recipe = "ds_6e1sala_2", Dur = 320}
, 
{Recipe = "ds_6e1icytre_3", Dur = 640}
, 
{Recipe = "ds_6e1icytre_4", Dur = 640}
, 
{Recipe = "ds_6e1dst_4", Dur = 640}
, 
{Recipe = "ds_6e1icytre_5", Dur = 360}
, 
{Recipe = "ds_6e1assort_2", Dur = 320}
}
}
, 
{Type = "eq_1_10", 
Category = {4}
, BookOrder = 2001, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, 
Recipes = {
{Recipe = "ds_chopve_1", Dur = 5}
, 
{Recipe = "ds_chopve_2", Dur = 8}
, 
{Recipe = "ds_chopve_3", Dur = 16}
, 
{Recipe = "ds_chopve_4", Dur = 32}
, 
{Recipe = "ds_chopfr_1", Dur = 128}
, 
{Recipe = "ds_grillmt_1", Dur = 8}
, 
{Recipe = "ds_mixdrk_1", Dur = 48}
, 
{Recipe = "ds_mixdrk_2", Dur = 28}
, 
{Recipe = "ds_mixdrk_3", Dur = 28}
, 
{Recipe = "ds_mixdrk_4", Dur = 60}
, 
{Recipe = "ds_mixdrk_5", Dur = 32}
, 
{Recipe = "ds_mixdrk_6", Dur = 28}
, 
{Recipe = "ds_mixdrk_7", Dur = 28}
, 
{Recipe = "ds_mixdrk_8", Dur = 192}
, 
{Recipe = "ds_mixdrk_9", Dur = 512}
, 
{Recipe = "ds_dst_1", Dur = 64}
, 
{Recipe = "ds_chopfs_1", Dur = 128}
, 
{Recipe = "ds_fd_8", Dur = 4}
, 
{Recipe = "ds_fd_9", Dur = 4}
, 
{Recipe = "ds_fd_10", Dur = 20}
, 
{Recipe = "ds_fd_11", Dur = 4}
, 
{Recipe = "ds_fd_12", Dur = 4}
, 
{Recipe = "ds_fd_13", Dur = 16}
, 
{Recipe = "ds_sal_1", Dur = 512}
, 
{Recipe = "ds_chopfru_1", Dur = 256}
, 
{Recipe = "ds_grillsf_7", Dur = 512}
, 
{Recipe = "ds_flb_1", Dur = 16}
, 
{Recipe = "ds_flb_2", Dur = 32}
, 
{Recipe = "ds_e1hotdrk_1", Dur = 136}
, 
{Recipe = "ds_e1cockt_1", Dur = 8}
, 
{Recipe = "ds_e1cockt_2", Dur = 16}
, 
{Recipe = "ds_e1cockt_3", Dur = 16}
, 
{Recipe = "ds_e1cockt_4", Dur = 16}
, 
{Recipe = "ds_e1cockt_5", Dur = 16}
, 
{Recipe = "ds_e1cockt_6", Dur = 16}
, 
{Recipe = "ds_e1cockt_8", Dur = 28}
, 
{Recipe = "ds_e1cockt_9", Dur = 16}
, 
{Recipe = "ds_e1cockt_10", Dur = 28}
, 
{Recipe = "ds_e1cockt_11", Dur = 32}
, 
{Recipe = "ds_e1cockt_12", Dur = 32}
, 
{Recipe = "ds_e1cockt_13", Dur = 32}
, 
{Recipe = "ds_e1cockt_14", Dur = 32}
, 
{Recipe = "ds_e1icytre_1", Dur = 32}
, 
{Recipe = "ds_e1icytre_2", Dur = 48}
, 
{Recipe = "ds_e1cockt_15", Dur = 48}
, 
{Recipe = "ds_e1cockt_16", Dur = 48}
, 
{Recipe = "ds_e1cockt_17", Dur = 256}
, 
{Recipe = "ds_e1cockt_18", Dur = 256}
, 
{Recipe = "ds_e1cockt_19", Dur = 288}
, 
{Recipe = "ds_e1hotdrk_2", Dur = 512}
, 
{Recipe = "ds_e1cockt_20", Dur = 1024}
, 
{Recipe = "ds_6e1preingre_1", Dur = 20}
, 
{Recipe = "ds_6e1sala_2", Dur = 256}
, 
{Recipe = "ds_6e1icytre_3", Dur = 512}
, 
{Recipe = "ds_6e1icytre_4", Dur = 512}
, 
{Recipe = "ds_6e1dst_4", Dur = 512}
, 
{Recipe = "ds_6e1icytre_5", Dur = 288}
, 
{Recipe = "ds_6e1assort_2", Dur = 256}
}
}
, 
{Type = "eq_2_1", MergedType = "eq_2_2", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "eq_2_2", MergedType = "eq_2_3", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "eq_2_3", MergedType = "eq_2_4", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "eq_2_4", MergedType = "eq_2_5", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, 
Recipes = {
{Recipe = "ds_grillve_1", Dur = 640}
, 
{Recipe = "ds_grillve_2", Dur = 200}
, 
{Recipe = "ds_grillve_3", Dur = 160}
, 
{Recipe = "ds_grillve_4", Dur = 180}
, 
{Recipe = "ds_friedmt_2", Dur = 10}
, 
{Recipe = "ds_grillmt_2", Dur = 80}
, 
{Recipe = "ds_grillmt_3", Dur = 40}
, 
{Recipe = "ds_grillmt_4", Dur = 160}
, 
{Recipe = "ds_grillmt_5", Dur = 320}
, 
{Recipe = "ds_grillmt_6", Dur = 80}
, 
{Recipe = "ds_grillmt_7", Dur = 640}
, 
{Recipe = "ds_grillmt_8", Dur = 1280}
, 
{Recipe = "ds_grillmt_9", Dur = 160}
, 
{Recipe = "ds_grillmt_10", Dur = 640}
, 
{Recipe = "ds_grillmt_11", Dur = 2560}
, 
{Recipe = "ds_grillsf_1", Dur = 40}
, 
{Recipe = "ds_grillsf_2", Dur = 40}
, 
{Recipe = "ds_grillsf_3", Dur = 80}
, 
{Recipe = "ds_grillsf_4", Dur = 160}
, 
{Recipe = "ds_grillsf_5", Dur = 640}
, 
{Recipe = "ds_grillsf_6", Dur = 320}
, 
{Recipe = "ds_grillmt_12", Dur = 1280}
, 
{Recipe = "ds_6e2assort_1", Dur = 640}
, 
{Recipe = "ds_6e2mt_14", Dur = 1280}
}
}
, 
{Type = "eq_2_5", MergedType = "eq_2_6", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, 
Recipes = {
{Recipe = "ds_grillve_1", Dur = 576}
, 
{Recipe = "ds_grillve_2", Dur = 180}
, 
{Recipe = "ds_grillve_3", Dur = 144}
, 
{Recipe = "ds_grillve_4", Dur = 162}
, 
{Recipe = "ds_friedmt_2", Dur = 9}
, 
{Recipe = "ds_grillmt_2", Dur = 72}
, 
{Recipe = "ds_grillmt_3", Dur = 36}
, 
{Recipe = "ds_grillmt_4", Dur = 144}
, 
{Recipe = "ds_grillmt_5", Dur = 288}
, 
{Recipe = "ds_grillmt_6", Dur = 72}
, 
{Recipe = "ds_grillmt_7", Dur = 576}
, 
{Recipe = "ds_grillmt_8", Dur = 1152}
, 
{Recipe = "ds_grillmt_9", Dur = 144}
, 
{Recipe = "ds_grillmt_10", Dur = 576}
, 
{Recipe = "ds_grillmt_11", Dur = 2304}
, 
{Recipe = "ds_grillsf_1", Dur = 36}
, 
{Recipe = "ds_grillsf_2", Dur = 36}
, 
{Recipe = "ds_grillsf_3", Dur = 72}
, 
{Recipe = "ds_grillsf_4", Dur = 144}
, 
{Recipe = "ds_grillsf_5", Dur = 576}
, 
{Recipe = "ds_grillsf_6", Dur = 288}
, 
{Recipe = "ds_grillmt_12", Dur = 1152}
, 
{Recipe = "ds_6e2assort_1", Dur = 576}
, 
{Recipe = "ds_6e2mt_14", Dur = 1152}
}
}
, 
{Type = "eq_2_6", MergedType = "eq_2_7", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, 
Recipes = {
{Recipe = "ds_grillve_1", Dur = 512}
, 
{Recipe = "ds_grillve_2", Dur = 160}
, 
{Recipe = "ds_grillve_3", Dur = 128}
, 
{Recipe = "ds_grillve_4", Dur = 144}
, 
{Recipe = "ds_friedmt_2", Dur = 8}
, 
{Recipe = "ds_grillmt_2", Dur = 64}
, 
{Recipe = "ds_grillmt_3", Dur = 32}
, 
{Recipe = "ds_grillmt_4", Dur = 128}
, 
{Recipe = "ds_grillmt_5", Dur = 256}
, 
{Recipe = "ds_grillmt_6", Dur = 64}
, 
{Recipe = "ds_grillmt_7", Dur = 512}
, 
{Recipe = "ds_grillmt_8", Dur = 1024}
, 
{Recipe = "ds_grillmt_9", Dur = 128}
, 
{Recipe = "ds_grillmt_10", Dur = 512}
, 
{Recipe = "ds_grillmt_11", Dur = 2048}
, 
{Recipe = "ds_grillsf_1", Dur = 32}
, 
{Recipe = "ds_grillsf_2", Dur = 32}
, 
{Recipe = "ds_grillsf_3", Dur = 64}
, 
{Recipe = "ds_grillsf_4", Dur = 128}
, 
{Recipe = "ds_grillsf_5", Dur = 512}
, 
{Recipe = "ds_grillsf_6", Dur = 256}
, 
{Recipe = "ds_grillmt_12", Dur = 1024}
, 
{Recipe = "ds_6e2assort_1", Dur = 512}
, 
{Recipe = "ds_6e2mt_14", Dur = 1024}
}
}
, 
{Type = "eq_2_7", MergedType = "eq_2_8", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, 
Recipes = {
{Recipe = "ds_grillve_1", Dur = 448}
, 
{Recipe = "ds_grillve_2", Dur = 140}
, 
{Recipe = "ds_grillve_3", Dur = 112}
, 
{Recipe = "ds_grillve_4", Dur = 126}
, 
{Recipe = "ds_friedmt_2", Dur = 7}
, 
{Recipe = "ds_grillmt_2", Dur = 56}
, 
{Recipe = "ds_grillmt_3", Dur = 28}
, 
{Recipe = "ds_grillmt_4", Dur = 112}
, 
{Recipe = "ds_grillmt_5", Dur = 224}
, 
{Recipe = "ds_grillmt_6", Dur = 56}
, 
{Recipe = "ds_grillmt_7", Dur = 448}
, 
{Recipe = "ds_grillmt_8", Dur = 896}
, 
{Recipe = "ds_grillmt_9", Dur = 112}
, 
{Recipe = "ds_grillmt_10", Dur = 448}
, 
{Recipe = "ds_grillmt_11", Dur = 1792}
, 
{Recipe = "ds_grillsf_1", Dur = 28}
, 
{Recipe = "ds_grillsf_2", Dur = 28}
, 
{Recipe = "ds_grillsf_3", Dur = 56}
, 
{Recipe = "ds_grillsf_4", Dur = 112}
, 
{Recipe = "ds_grillsf_5", Dur = 448}
, 
{Recipe = "ds_grillsf_6", Dur = 224}
, 
{Recipe = "ds_grillmt_12", Dur = 896}
, 
{Recipe = "ds_6e2assort_1", Dur = 448}
, 
{Recipe = "ds_6e2mt_14", Dur = 896}
}
}
, 
{Type = "eq_2_8", MergedType = "eq_2_9", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, 
Recipes = {
{Recipe = "ds_grillve_1", Dur = 384}
, 
{Recipe = "ds_grillve_2", Dur = 120}
, 
{Recipe = "ds_grillve_3", Dur = 96}
, 
{Recipe = "ds_grillve_4", Dur = 108}
, 
{Recipe = "ds_friedmt_2", Dur = 6}
, 
{Recipe = "ds_grillmt_2", Dur = 48}
, 
{Recipe = "ds_grillmt_3", Dur = 24}
, 
{Recipe = "ds_grillmt_4", Dur = 96}
, 
{Recipe = "ds_grillmt_5", Dur = 192}
, 
{Recipe = "ds_grillmt_6", Dur = 48}
, 
{Recipe = "ds_grillmt_7", Dur = 384}
, 
{Recipe = "ds_grillmt_8", Dur = 768}
, 
{Recipe = "ds_grillmt_9", Dur = 96}
, 
{Recipe = "ds_grillmt_10", Dur = 384}
, 
{Recipe = "ds_grillmt_11", Dur = 1536}
, 
{Recipe = "ds_grillsf_1", Dur = 24}
, 
{Recipe = "ds_grillsf_2", Dur = 24}
, 
{Recipe = "ds_grillsf_3", Dur = 48}
, 
{Recipe = "ds_grillsf_4", Dur = 96}
, 
{Recipe = "ds_grillsf_5", Dur = 384}
, 
{Recipe = "ds_grillsf_6", Dur = 192}
, 
{Recipe = "ds_grillmt_12", Dur = 768}
, 
{Recipe = "ds_6e2assort_1", Dur = 384}
, 
{Recipe = "ds_6e2mt_14", Dur = 768}
}
}
, 
{Type = "eq_2_9", MergedType = "eq_2_10", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, 
Recipes = {
{Recipe = "ds_grillve_1", Dur = 320}
, 
{Recipe = "ds_grillve_2", Dur = 100}
, 
{Recipe = "ds_grillve_3", Dur = 80}
, 
{Recipe = "ds_grillve_4", Dur = 90}
, 
{Recipe = "ds_friedmt_2", Dur = 5}
, 
{Recipe = "ds_grillmt_2", Dur = 40}
, 
{Recipe = "ds_grillmt_3", Dur = 20}
, 
{Recipe = "ds_grillmt_4", Dur = 80}
, 
{Recipe = "ds_grillmt_5", Dur = 160}
, 
{Recipe = "ds_grillmt_6", Dur = 40}
, 
{Recipe = "ds_grillmt_7", Dur = 320}
, 
{Recipe = "ds_grillmt_8", Dur = 640}
, 
{Recipe = "ds_grillmt_9", Dur = 80}
, 
{Recipe = "ds_grillmt_10", Dur = 320}
, 
{Recipe = "ds_grillmt_11", Dur = 1280}
, 
{Recipe = "ds_grillsf_1", Dur = 20}
, 
{Recipe = "ds_grillsf_2", Dur = 20}
, 
{Recipe = "ds_grillsf_3", Dur = 40}
, 
{Recipe = "ds_grillsf_4", Dur = 80}
, 
{Recipe = "ds_grillsf_5", Dur = 320}
, 
{Recipe = "ds_grillsf_6", Dur = 160}
, 
{Recipe = "ds_grillmt_12", Dur = 640}
, 
{Recipe = "ds_6e2assort_1", Dur = 320}
, 
{Recipe = "ds_6e2mt_14", Dur = 640}
}
}
, 
{Type = "eq_2_10", 
Category = {4}
, BookOrder = 2002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, 
Recipes = {
{Recipe = "ds_grillve_1", Dur = 256}
, 
{Recipe = "ds_grillve_2", Dur = 80}
, 
{Recipe = "ds_grillve_3", Dur = 64}
, 
{Recipe = "ds_grillve_4", Dur = 72}
, 
{Recipe = "ds_friedmt_2", Dur = 5}
, 
{Recipe = "ds_grillmt_2", Dur = 32}
, 
{Recipe = "ds_grillmt_3", Dur = 16}
, 
{Recipe = "ds_grillmt_4", Dur = 64}
, 
{Recipe = "ds_grillmt_5", Dur = 128}
, 
{Recipe = "ds_grillmt_6", Dur = 32}
, 
{Recipe = "ds_grillmt_7", Dur = 256}
, 
{Recipe = "ds_grillmt_8", Dur = 512}
, 
{Recipe = "ds_grillmt_9", Dur = 64}
, 
{Recipe = "ds_grillmt_10", Dur = 256}
, 
{Recipe = "ds_grillmt_11", Dur = 1024}
, 
{Recipe = "ds_grillsf_1", Dur = 16}
, 
{Recipe = "ds_grillsf_2", Dur = 16}
, 
{Recipe = "ds_grillsf_3", Dur = 32}
, 
{Recipe = "ds_grillsf_4", Dur = 64}
, 
{Recipe = "ds_grillsf_5", Dur = 256}
, 
{Recipe = "ds_grillsf_6", Dur = 128}
, 
{Recipe = "ds_grillmt_12", Dur = 512}
, 
{Recipe = "ds_6e2assort_1", Dur = 256}
, 
{Recipe = "ds_6e2mt_14", Dur = 512}
}
}
, 
{Type = "pd_4_1", MergedType = "pd_4_2", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "pd_4_2", MergedType = "pd_4_3", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "pd_4_3", MergedType = "pd_4_4", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "pd_4_4", MergedType = "pd_4_5", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, UseEnergy = 1, 
TapeItems = {
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_4_1_1", Weight = 15}
, 
{Code = "it_4_2_1", Weight = 5}
}
, Cd = 7200, InitialNumber = 8, Frequency = 8, Capacity = 24, SpeedUpPrice = 4}
, 
{Type = "pd_4_5", MergedType = "pd_4_6", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, UseEnergy = 1, 
TapeItems = {
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_4_1_1", Weight = 9}
, 
{Code = "it_4_1_2", Weight = 6}
, 
{Code = "it_4_2_1", Weight = 5}
}
, Cd = 6000, InitialNumber = 10, Frequency = 10, Capacity = 30, SpeedUpPrice = 6}
, 
{Type = "pd_4_6", MergedType = "pd_4_7", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, UseEnergy = 1, 
TapeItems = {
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_4_1_1", Weight = 10}
, 
{Code = "it_4_1_2", Weight = 4}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 5}
}
, Cd = 4200, InitialNumber = 12, Frequency = 12, Capacity = 36, SpeedUpPrice = 8}
, 
{Type = "pd_4_7", MergedType = "pd_4_8", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, UseEnergy = 1, 
TapeItems = {
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_4_1_1", Weight = 10}
, 
{Code = "it_4_1_2", Weight = 3}
, 
{Code = "it_4_1_3", Weight = 2}
, 
{Code = "it_4_2_1", Weight = 5}
}
, Cd = 3300, InitialNumber = 14, Frequency = 14, Capacity = 42, SpeedUpPrice = 9}
, 
{Type = "pd_4_8", MergedType = "pd_4_9", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, UseEnergy = 1, 
TapeItems = {
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_4_1_1", Weight = 8}
, 
{Code = "it_4_1_2", Weight = 4}
, 
{Code = "it_4_1_3", Weight = 3}
, 
{Code = "it_4_2_1", Weight = 5}
}
, Cd = 2700, InitialNumber = 16, Frequency = 16, Capacity = 48, SpeedUpPrice = 11}
, 
{Type = "pd_4_9", MergedType = "pd_4_10", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, UseEnergy = 1, 
TapeItems = {
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_4_1_1", Weight = 6}
, 
{Code = "it_4_1_2", Weight = 5}
, 
{Code = "it_4_1_3", Weight = 4}
, 
{Code = "it_4_2_1", Weight = 5}
}
, Cd = 1800, InitialNumber = 18, Frequency = 18, Capacity = 54, SpeedUpPrice = 13}
, 
{Type = "pd_4_10", 
Category = {2}
, BookOrder = 1030, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, UseEnergy = 1, 
TapeItems = {
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_1_1", Weight = 1}
, 
{Code = "it_4_1_3", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
, 
{Code = "it_4_2_1", Weight = 1}
, 
{Code = "it_4_1_2", Weight = 1}
}
, 
GeneratedItems = {
{Code = "it_4_1_1", Weight = 4}
, 
{Code = "it_4_1_2", Weight = 6}
, 
{Code = "it_4_1_3", Weight = 5}
, 
{Code = "it_4_2_1", Weight = 5}
}
, Cd = 1500, InitialNumber = 20, Frequency = 20, Capacity = 60, SpeedUpPrice = 15}
, 
{Type = "it_4_1_1", MergedType = "it_4_1_2", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 0.42, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 3}
, 
{Type = "it_4_1_2", MergedType = "it_4_1_3", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 0.83, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 5}
, 
{Type = "it_4_1_3", MergedType = "it_4_1_4", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1.67, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 8}
, 
{Type = "it_4_1_4", MergedType = "it_4_1_5", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 3.33, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 11}
, 
{Type = "it_4_1_5", MergedType = "it_4_1_6", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 6.67, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 16}
, 
{Type = "it_4_1_6", MergedType = "it_4_1_7", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 13.33, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 30}
, 
{Type = "it_4_1_7", MergedType = "it_4_1_8", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 26.67, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 57}
, 
{Type = "it_4_1_8", MergedType = "it_4_1_9", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 53.33, BubbleChance = 25, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 108}
, 
{Type = "it_4_1_9", MergedType = "it_4_1_10", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 106.67, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 203}
, 
{Type = "it_4_1_10", MergedType = "it_4_1_11", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 213.33, BubbleChance = 30, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 383}
, 
{Type = "it_4_1_11", MergedType = "it_4_1_12", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 426.67, BubbleChance = 30, UnlockPrice = 20000, 
SellCurrency = {Currency = "energy", Amount = 11}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 724}
, 
{Type = "it_4_1_12", 
Category = {1}
, BookOrder = 1031, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 853.33, UnlockPrice = 30000, 
SellCurrency = {Currency = "energy", Amount = 20}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 1366}
, 
{Type = "it_4_2_1", MergedType = "it_4_2_2", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 2, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 5}
, 
{Type = "it_4_2_2", MergedType = "it_4_2_3", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4, BubbleChance = 5, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 11}
, 
{Type = "it_4_2_3", MergedType = "it_4_2_4", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 8, BubbleChance = 10, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 19}
, 
{Type = "it_4_2_4", MergedType = "it_4_2_5", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 16, BubbleChance = 15, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 38}
, 
{Type = "it_4_2_5", MergedType = "it_4_2_6", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 32, BubbleChance = 20, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 73}
, 
{Type = "it_4_2_6", MergedType = "it_4_2_7", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 64, BubbleChance = 25, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 140}
, 
{Type = "it_4_2_7", MergedType = "it_4_2_8", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 128, BubbleChance = 30, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 265}
, 
{Type = "it_4_2_8", MergedType = "it_4_2_9", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 256, BubbleChance = 30, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 7}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 513}
, 
{Type = "it_4_2_9", MergedType = "it_4_2_10", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 512, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 13}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 967}
, 
{Type = "it_4_2_10", 
Category = {1}
, BookOrder = 1032, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1024, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 24}
, 
Generators = {"pd_4_4", "pd_4_5", "pd_4_6", "pd_4_7", "pd_4_8", "pd_4_9", "pd_4_10"}
, Reward = 1823}
, 
{Type = "eq_3_1", MergedType = "eq_3_2", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "eq_3_2", MergedType = "eq_3_3", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "eq_3_3", MergedType = "eq_3_4", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "eq_3_4", MergedType = "eq_3_5", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, 
Recipes = {
{Recipe = "ds_juice_1", Dur = 80}
, 
{Recipe = "ds_juice_2", Dur = 160}
, 
{Recipe = "ds_juice_3", Dur = 20}
, 
{Recipe = "ds_juice_4", Dur = 10}
, 
{Recipe = "ds_juice_6", Dur = 640}
, 
{Recipe = "ds_juice_7", Dur = 40}
, 
{Recipe = "ds_juice_8", Dur = 1280}
, 
{Recipe = "ds_juice_9", Dur = 1280}
, 
{Recipe = "ds_e3juice_10", Dur = 10}
, 
{Recipe = "ds_e3juice_11", Dur = 640}
, 
{Recipe = "ds_6e3preingre_2", Dur = 640}
}
}
, 
{Type = "eq_3_5", MergedType = "eq_3_6", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, 
Recipes = {
{Recipe = "ds_juice_1", Dur = 72}
, 
{Recipe = "ds_juice_2", Dur = 144}
, 
{Recipe = "ds_juice_3", Dur = 18}
, 
{Recipe = "ds_juice_4", Dur = 9}
, 
{Recipe = "ds_juice_6", Dur = 576}
, 
{Recipe = "ds_juice_7", Dur = 36}
, 
{Recipe = "ds_juice_8", Dur = 1152}
, 
{Recipe = "ds_juice_9", Dur = 1152}
, 
{Recipe = "ds_e3juice_10", Dur = 9}
, 
{Recipe = "ds_e3juice_11", Dur = 576}
, 
{Recipe = "ds_6e3preingre_2", Dur = 576}
}
}
, 
{Type = "eq_3_6", MergedType = "eq_3_7", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, 
Recipes = {
{Recipe = "ds_juice_1", Dur = 64}
, 
{Recipe = "ds_juice_2", Dur = 128}
, 
{Recipe = "ds_juice_3", Dur = 16}
, 
{Recipe = "ds_juice_4", Dur = 8}
, 
{Recipe = "ds_juice_6", Dur = 512}
, 
{Recipe = "ds_juice_7", Dur = 32}
, 
{Recipe = "ds_juice_8", Dur = 1024}
, 
{Recipe = "ds_juice_9", Dur = 1024}
, 
{Recipe = "ds_e3juice_10", Dur = 8}
, 
{Recipe = "ds_e3juice_11", Dur = 512}
, 
{Recipe = "ds_6e3preingre_2", Dur = 512}
}
}
, 
{Type = "eq_3_7", MergedType = "eq_3_8", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 3000, 
Recipes = {
{Recipe = "ds_juice_1", Dur = 56}
, 
{Recipe = "ds_juice_2", Dur = 112}
, 
{Recipe = "ds_juice_3", Dur = 14}
, 
{Recipe = "ds_juice_4", Dur = 7}
, 
{Recipe = "ds_juice_6", Dur = 448}
, 
{Recipe = "ds_juice_7", Dur = 28}
, 
{Recipe = "ds_juice_8", Dur = 896}
, 
{Recipe = "ds_juice_9", Dur = 896}
, 
{Recipe = "ds_e3juice_10", Dur = 7}
, 
{Recipe = "ds_e3juice_11", Dur = 448}
, 
{Recipe = "ds_6e3preingre_2", Dur = 448}
}
}
, 
{Type = "eq_3_8", MergedType = "eq_3_9", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, 
Recipes = {
{Recipe = "ds_juice_1", Dur = 48}
, 
{Recipe = "ds_juice_2", Dur = 96}
, 
{Recipe = "ds_juice_3", Dur = 12}
, 
{Recipe = "ds_juice_4", Dur = 6}
, 
{Recipe = "ds_juice_6", Dur = 384}
, 
{Recipe = "ds_juice_7", Dur = 24}
, 
{Recipe = "ds_juice_8", Dur = 768}
, 
{Recipe = "ds_juice_9", Dur = 768}
, 
{Recipe = "ds_e3juice_10", Dur = 6}
, 
{Recipe = "ds_e3juice_11", Dur = 384}
, 
{Recipe = "ds_6e3preingre_2", Dur = 384}
}
}
, 
{Type = "eq_3_9", MergedType = "eq_3_10", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, 
Recipes = {
{Recipe = "ds_juice_1", Dur = 40}
, 
{Recipe = "ds_juice_2", Dur = 80}
, 
{Recipe = "ds_juice_3", Dur = 10}
, 
{Recipe = "ds_juice_4", Dur = 5}
, 
{Recipe = "ds_juice_6", Dur = 320}
, 
{Recipe = "ds_juice_7", Dur = 20}
, 
{Recipe = "ds_juice_8", Dur = 640}
, 
{Recipe = "ds_juice_9", Dur = 640}
, 
{Recipe = "ds_e3juice_10", Dur = 5}
, 
{Recipe = "ds_e3juice_11", Dur = 320}
, 
{Recipe = "ds_6e3preingre_2", Dur = 320}
}
}
, 
{Type = "eq_3_10", 
Category = {4}
, BookOrder = 2003, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 12000, 
Recipes = {
{Recipe = "ds_juice_1", Dur = 32}
, 
{Recipe = "ds_juice_2", Dur = 64}
, 
{Recipe = "ds_juice_3", Dur = 8}
, 
{Recipe = "ds_juice_4", Dur = 5}
, 
{Recipe = "ds_juice_6", Dur = 256}
, 
{Recipe = "ds_juice_7", Dur = 16}
, 
{Recipe = "ds_juice_8", Dur = 512}
, 
{Recipe = "ds_juice_9", Dur = 512}
, 
{Recipe = "ds_e3juice_10", Dur = 4}
, 
{Recipe = "ds_e3juice_11", Dur = 256}
, 
{Recipe = "ds_6e3preingre_2", Dur = 256}
}
}
, 
{Type = "eq_4_1", MergedType = "eq_4_2", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "eq_4_2", MergedType = "eq_4_3", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "eq_4_3", MergedType = "eq_4_4", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "eq_4_4", MergedType = "eq_4_5", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, 
Recipes = {
{Recipe = "ds_friedve_1", Dur = 1280}
, 
{Recipe = "ds_friedve_2", Dur = 720}
, 
{Recipe = "ds_friedve_3", Dur = 320}
, 
{Recipe = "ds_friedve_4", Dur = 2560}
, 
{Recipe = "ds_friedmt_1", Dur = 10}
, 
{Recipe = "ds_friedmt_3", Dur = 20}
, 
{Recipe = "ds_friedmt_4", Dur = 40}
, 
{Recipe = "ds_friedmt_5", Dur = 480}
, 
{Recipe = "ds_friedsf_1", Dur = 20}
, 
{Recipe = "ds_friedsf_2", Dur = 20}
, 
{Recipe = "ds_friedsf_3", Dur = 80}
, 
{Recipe = "ds_fd_21", Dur = 340}
, 
{Recipe = "ds_fd_23", Dur = 640}
, 
{Recipe = "ds_friedsf_4", Dur = 1280}
, 
{Recipe = "ds_friedve_5", Dur = 1280}
, 
{Recipe = "ds_e4friedmt_6", Dur = 70}
, 
{Recipe = "ds_e4sf_12", Dur = 70}
, 
{Recipe = "ds_e4friedmt_7", Dur = 80}
, 
{Recipe = "ds_e4sf_13", Dur = 120}
, 
{Recipe = "ds_e4sf_14", Dur = 380}
, 
{Recipe = "ds_e4sf_15", Dur = 720}
, 
{Recipe = "ds_6e4pasta_1", Dur = 480}
, 
{Recipe = "ds_6e4pasta_2", Dur = 480}
, 
{Recipe = "ds_6e4pasta_3", Dur = 480}
, 
{Recipe = "ds_6e4pasta_4", Dur = 960}
, 
{Recipe = "ds_6e4pasta_5", Dur = 640}
, 
{Recipe = "ds_6e4pasta_6", Dur = 480}
, 
{Recipe = "ds_6e4pasta_7", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_8", Dur = 720}
, 
{Recipe = "ds_6e4pasta_9", Dur = 480}
, 
{Recipe = "ds_6e4pasta_10", Dur = 480}
, 
{Recipe = "ds_6e4pasta_11", Dur = 240}
, 
{Recipe = "ds_6e4pasta_12", Dur = 480}
, 
{Recipe = "ds_6e4pasta_13", Dur = 720}
, 
{Recipe = "ds_6e4pasta_14", Dur = 240}
, 
{Recipe = "ds_6e4friedmt_8", Dur = 720}
, 
{Recipe = "ds_6e4pasta_15", Dur = 960}
, 
{Recipe = "ds_6e4pasta_16", Dur = 960}
, 
{Recipe = "ds_6e4pasta_17", Dur = 1280}
, 
{Recipe = "ds_6e4friedmt_9", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_18", Dur = 960}
, 
{Recipe = "ds_6e4pasta_19", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_20", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_21", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_22", Dur = 960}
, 
{Recipe = "ds_6e4stewmt_2", Dur = 960}
, 
{Recipe = "ds_6e4flb_3", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_23", Dur = 720}
, 
{Recipe = "ds_6e4pasta_24", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_25", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_26", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_27", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_28", Dur = 1280}
, 
{Recipe = "ds_6e4pasta_29", Dur = 2560}
, 
{Recipe = "ds_6e4assort_3", Dur = 960}
}
}
, 
{Type = "eq_4_5", MergedType = "eq_4_6", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, 
Recipes = {
{Recipe = "ds_friedve_1", Dur = 1152}
, 
{Recipe = "ds_friedve_2", Dur = 648}
, 
{Recipe = "ds_friedve_3", Dur = 288}
, 
{Recipe = "ds_friedve_4", Dur = 2304}
, 
{Recipe = "ds_friedmt_1", Dur = 9}
, 
{Recipe = "ds_friedmt_3", Dur = 18}
, 
{Recipe = "ds_friedmt_4", Dur = 36}
, 
{Recipe = "ds_friedmt_5", Dur = 432}
, 
{Recipe = "ds_friedsf_1", Dur = 18}
, 
{Recipe = "ds_friedsf_2", Dur = 18}
, 
{Recipe = "ds_friedsf_3", Dur = 72}
, 
{Recipe = "ds_fd_21", Dur = 306}
, 
{Recipe = "ds_fd_23", Dur = 576}
, 
{Recipe = "ds_friedsf_4", Dur = 1152}
, 
{Recipe = "ds_friedve_5", Dur = 1152}
, 
{Recipe = "ds_e4friedmt_6", Dur = 63}
, 
{Recipe = "ds_e4sf_12", Dur = 63}
, 
{Recipe = "ds_e4friedmt_7", Dur = 72}
, 
{Recipe = "ds_e4sf_13", Dur = 108}
, 
{Recipe = "ds_e4sf_14", Dur = 342}
, 
{Recipe = "ds_e4sf_15", Dur = 648}
, 
{Recipe = "ds_6e4pasta_1", Dur = 432}
, 
{Recipe = "ds_6e4pasta_2", Dur = 432}
, 
{Recipe = "ds_6e4pasta_3", Dur = 432}
, 
{Recipe = "ds_6e4pasta_4", Dur = 864}
, 
{Recipe = "ds_6e4pasta_5", Dur = 576}
, 
{Recipe = "ds_6e4pasta_6", Dur = 432}
, 
{Recipe = "ds_6e4pasta_7", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_8", Dur = 648}
, 
{Recipe = "ds_6e4pasta_9", Dur = 432}
, 
{Recipe = "ds_6e4pasta_10", Dur = 432}
, 
{Recipe = "ds_6e4pasta_11", Dur = 216}
, 
{Recipe = "ds_6e4pasta_12", Dur = 432}
, 
{Recipe = "ds_6e4pasta_13", Dur = 648}
, 
{Recipe = "ds_6e4pasta_14", Dur = 216}
, 
{Recipe = "ds_6e4friedmt_8", Dur = 648}
, 
{Recipe = "ds_6e4pasta_15", Dur = 864}
, 
{Recipe = "ds_6e4pasta_16", Dur = 864}
, 
{Recipe = "ds_6e4pasta_17", Dur = 1152}
, 
{Recipe = "ds_6e4friedmt_9", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_18", Dur = 864}
, 
{Recipe = "ds_6e4pasta_19", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_20", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_21", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_22", Dur = 864}
, 
{Recipe = "ds_6e4stewmt_2", Dur = 864}
, 
{Recipe = "ds_6e4flb_3", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_23", Dur = 648}
, 
{Recipe = "ds_6e4pasta_24", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_25", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_26", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_27", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_28", Dur = 1152}
, 
{Recipe = "ds_6e4pasta_29", Dur = 2304}
, 
{Recipe = "ds_6e4assort_3", Dur = 864}
}
}
, 
{Type = "eq_4_6", MergedType = "eq_4_7", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, 
Recipes = {
{Recipe = "ds_friedve_1", Dur = 1024}
, 
{Recipe = "ds_friedve_2", Dur = 576}
, 
{Recipe = "ds_friedve_3", Dur = 256}
, 
{Recipe = "ds_friedve_4", Dur = 2048}
, 
{Recipe = "ds_friedmt_1", Dur = 8}
, 
{Recipe = "ds_friedmt_3", Dur = 16}
, 
{Recipe = "ds_friedmt_4", Dur = 32}
, 
{Recipe = "ds_friedmt_5", Dur = 384}
, 
{Recipe = "ds_friedsf_1", Dur = 16}
, 
{Recipe = "ds_friedsf_2", Dur = 16}
, 
{Recipe = "ds_friedsf_3", Dur = 64}
, 
{Recipe = "ds_fd_21", Dur = 272}
, 
{Recipe = "ds_fd_23", Dur = 512}
, 
{Recipe = "ds_friedsf_4", Dur = 1024}
, 
{Recipe = "ds_friedve_5", Dur = 1024}
, 
{Recipe = "ds_e4friedmt_6", Dur = 56}
, 
{Recipe = "ds_e4sf_12", Dur = 56}
, 
{Recipe = "ds_e4friedmt_7", Dur = 64}
, 
{Recipe = "ds_e4sf_13", Dur = 96}
, 
{Recipe = "ds_e4sf_14", Dur = 304}
, 
{Recipe = "ds_e4sf_15", Dur = 576}
, 
{Recipe = "ds_6e4pasta_1", Dur = 384}
, 
{Recipe = "ds_6e4pasta_2", Dur = 384}
, 
{Recipe = "ds_6e4pasta_3", Dur = 384}
, 
{Recipe = "ds_6e4pasta_4", Dur = 768}
, 
{Recipe = "ds_6e4pasta_5", Dur = 512}
, 
{Recipe = "ds_6e4pasta_6", Dur = 384}
, 
{Recipe = "ds_6e4pasta_7", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_8", Dur = 576}
, 
{Recipe = "ds_6e4pasta_9", Dur = 384}
, 
{Recipe = "ds_6e4pasta_10", Dur = 384}
, 
{Recipe = "ds_6e4pasta_11", Dur = 192}
, 
{Recipe = "ds_6e4pasta_12", Dur = 384}
, 
{Recipe = "ds_6e4pasta_13", Dur = 576}
, 
{Recipe = "ds_6e4pasta_14", Dur = 192}
, 
{Recipe = "ds_6e4friedmt_8", Dur = 576}
, 
{Recipe = "ds_6e4pasta_15", Dur = 768}
, 
{Recipe = "ds_6e4pasta_16", Dur = 768}
, 
{Recipe = "ds_6e4pasta_17", Dur = 1024}
, 
{Recipe = "ds_6e4friedmt_9", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_18", Dur = 768}
, 
{Recipe = "ds_6e4pasta_19", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_20", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_21", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_22", Dur = 768}
, 
{Recipe = "ds_6e4stewmt_2", Dur = 768}
, 
{Recipe = "ds_6e4flb_3", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_23", Dur = 576}
, 
{Recipe = "ds_6e4pasta_24", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_25", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_26", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_27", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_28", Dur = 1024}
, 
{Recipe = "ds_6e4pasta_29", Dur = 2048}
, 
{Recipe = "ds_6e4assort_3", Dur = 768}
}
}
, 
{Type = "eq_4_7", MergedType = "eq_4_8", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, 
Recipes = {
{Recipe = "ds_friedve_1", Dur = 896}
, 
{Recipe = "ds_friedve_2", Dur = 504}
, 
{Recipe = "ds_friedve_3", Dur = 224}
, 
{Recipe = "ds_friedve_4", Dur = 1792}
, 
{Recipe = "ds_friedmt_1", Dur = 7}
, 
{Recipe = "ds_friedmt_3", Dur = 14}
, 
{Recipe = "ds_friedmt_4", Dur = 28}
, 
{Recipe = "ds_friedmt_5", Dur = 336}
, 
{Recipe = "ds_friedsf_1", Dur = 14}
, 
{Recipe = "ds_friedsf_2", Dur = 14}
, 
{Recipe = "ds_friedsf_3", Dur = 56}
, 
{Recipe = "ds_fd_21", Dur = 238}
, 
{Recipe = "ds_fd_23", Dur = 448}
, 
{Recipe = "ds_friedsf_4", Dur = 896}
, 
{Recipe = "ds_friedve_5", Dur = 896}
, 
{Recipe = "ds_e4friedmt_6", Dur = 49}
, 
{Recipe = "ds_e4sf_12", Dur = 49}
, 
{Recipe = "ds_e4friedmt_7", Dur = 56}
, 
{Recipe = "ds_e4sf_13", Dur = 84}
, 
{Recipe = "ds_e4sf_14", Dur = 266}
, 
{Recipe = "ds_e4sf_15", Dur = 504}
, 
{Recipe = "ds_6e4pasta_1", Dur = 336}
, 
{Recipe = "ds_6e4pasta_2", Dur = 336}
, 
{Recipe = "ds_6e4pasta_3", Dur = 336}
, 
{Recipe = "ds_6e4pasta_4", Dur = 672}
, 
{Recipe = "ds_6e4pasta_5", Dur = 448}
, 
{Recipe = "ds_6e4pasta_6", Dur = 336}
, 
{Recipe = "ds_6e4pasta_7", Dur = 896}
, 
{Recipe = "ds_6e4pasta_8", Dur = 504}
, 
{Recipe = "ds_6e4pasta_9", Dur = 336}
, 
{Recipe = "ds_6e4pasta_10", Dur = 336}
, 
{Recipe = "ds_6e4pasta_11", Dur = 168}
, 
{Recipe = "ds_6e4pasta_12", Dur = 336}
, 
{Recipe = "ds_6e4pasta_13", Dur = 504}
, 
{Recipe = "ds_6e4pasta_14", Dur = 168}
, 
{Recipe = "ds_6e4friedmt_8", Dur = 504}
, 
{Recipe = "ds_6e4pasta_15", Dur = 672}
, 
{Recipe = "ds_6e4pasta_16", Dur = 672}
, 
{Recipe = "ds_6e4pasta_17", Dur = 896}
, 
{Recipe = "ds_6e4friedmt_9", Dur = 896}
, 
{Recipe = "ds_6e4pasta_18", Dur = 672}
, 
{Recipe = "ds_6e4pasta_19", Dur = 896}
, 
{Recipe = "ds_6e4pasta_20", Dur = 896}
, 
{Recipe = "ds_6e4pasta_21", Dur = 896}
, 
{Recipe = "ds_6e4pasta_22", Dur = 672}
, 
{Recipe = "ds_6e4stewmt_2", Dur = 672}
, 
{Recipe = "ds_6e4flb_3", Dur = 896}
, 
{Recipe = "ds_6e4pasta_23", Dur = 504}
, 
{Recipe = "ds_6e4pasta_24", Dur = 896}
, 
{Recipe = "ds_6e4pasta_25", Dur = 896}
, 
{Recipe = "ds_6e4pasta_26", Dur = 896}
, 
{Recipe = "ds_6e4pasta_27", Dur = 896}
, 
{Recipe = "ds_6e4pasta_28", Dur = 896}
, 
{Recipe = "ds_6e4pasta_29", Dur = 1792}
, 
{Recipe = "ds_6e4assort_3", Dur = 672}
}
}
, 
{Type = "eq_4_8", MergedType = "eq_4_9", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, 
Recipes = {
{Recipe = "ds_friedve_1", Dur = 768}
, 
{Recipe = "ds_friedve_2", Dur = 432}
, 
{Recipe = "ds_friedve_3", Dur = 192}
, 
{Recipe = "ds_friedve_4", Dur = 1536}
, 
{Recipe = "ds_friedmt_1", Dur = 6}
, 
{Recipe = "ds_friedmt_3", Dur = 12}
, 
{Recipe = "ds_friedmt_4", Dur = 24}
, 
{Recipe = "ds_friedmt_5", Dur = 288}
, 
{Recipe = "ds_friedsf_1", Dur = 12}
, 
{Recipe = "ds_friedsf_2", Dur = 12}
, 
{Recipe = "ds_friedsf_3", Dur = 48}
, 
{Recipe = "ds_fd_21", Dur = 204}
, 
{Recipe = "ds_fd_23", Dur = 384}
, 
{Recipe = "ds_friedsf_4", Dur = 768}
, 
{Recipe = "ds_friedve_5", Dur = 768}
, 
{Recipe = "ds_e4friedmt_6", Dur = 42}
, 
{Recipe = "ds_e4sf_12", Dur = 42}
, 
{Recipe = "ds_e4friedmt_7", Dur = 48}
, 
{Recipe = "ds_e4sf_13", Dur = 72}
, 
{Recipe = "ds_e4sf_14", Dur = 228}
, 
{Recipe = "ds_e4sf_15", Dur = 432}
, 
{Recipe = "ds_6e4pasta_1", Dur = 288}
, 
{Recipe = "ds_6e4pasta_2", Dur = 288}
, 
{Recipe = "ds_6e4pasta_3", Dur = 288}
, 
{Recipe = "ds_6e4pasta_4", Dur = 576}
, 
{Recipe = "ds_6e4pasta_5", Dur = 384}
, 
{Recipe = "ds_6e4pasta_6", Dur = 288}
, 
{Recipe = "ds_6e4pasta_7", Dur = 768}
, 
{Recipe = "ds_6e4pasta_8", Dur = 432}
, 
{Recipe = "ds_6e4pasta_9", Dur = 288}
, 
{Recipe = "ds_6e4pasta_10", Dur = 288}
, 
{Recipe = "ds_6e4pasta_11", Dur = 144}
, 
{Recipe = "ds_6e4pasta_12", Dur = 288}
, 
{Recipe = "ds_6e4pasta_13", Dur = 432}
, 
{Recipe = "ds_6e4pasta_14", Dur = 144}
, 
{Recipe = "ds_6e4friedmt_8", Dur = 432}
, 
{Recipe = "ds_6e4pasta_15", Dur = 576}
, 
{Recipe = "ds_6e4pasta_16", Dur = 576}
, 
{Recipe = "ds_6e4pasta_17", Dur = 768}
, 
{Recipe = "ds_6e4friedmt_9", Dur = 768}
, 
{Recipe = "ds_6e4pasta_18", Dur = 576}
, 
{Recipe = "ds_6e4pasta_19", Dur = 768}
, 
{Recipe = "ds_6e4pasta_20", Dur = 768}
, 
{Recipe = "ds_6e4pasta_21", Dur = 768}
, 
{Recipe = "ds_6e4pasta_22", Dur = 576}
, 
{Recipe = "ds_6e4stewmt_2", Dur = 576}
, 
{Recipe = "ds_6e4flb_3", Dur = 768}
, 
{Recipe = "ds_6e4pasta_23", Dur = 432}
, 
{Recipe = "ds_6e4pasta_24", Dur = 768}
, 
{Recipe = "ds_6e4pasta_25", Dur = 768}
, 
{Recipe = "ds_6e4pasta_26", Dur = 768}
, 
{Recipe = "ds_6e4pasta_27", Dur = 768}
, 
{Recipe = "ds_6e4pasta_28", Dur = 768}
, 
{Recipe = "ds_6e4pasta_29", Dur = 1536}
, 
{Recipe = "ds_6e4assort_3", Dur = 576}
}
}
, 
{Type = "eq_4_9", MergedType = "eq_4_10", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, 
Recipes = {
{Recipe = "ds_friedve_1", Dur = 640}
, 
{Recipe = "ds_friedve_2", Dur = 360}
, 
{Recipe = "ds_friedve_3", Dur = 160}
, 
{Recipe = "ds_friedve_4", Dur = 1280}
, 
{Recipe = "ds_friedmt_1", Dur = 5}
, 
{Recipe = "ds_friedmt_3", Dur = 10}
, 
{Recipe = "ds_friedmt_4", Dur = 20}
, 
{Recipe = "ds_friedmt_5", Dur = 240}
, 
{Recipe = "ds_friedsf_1", Dur = 10}
, 
{Recipe = "ds_friedsf_2", Dur = 10}
, 
{Recipe = "ds_friedsf_3", Dur = 40}
, 
{Recipe = "ds_fd_21", Dur = 170}
, 
{Recipe = "ds_fd_23", Dur = 320}
, 
{Recipe = "ds_friedsf_4", Dur = 640}
, 
{Recipe = "ds_friedve_5", Dur = 640}
, 
{Recipe = "ds_e4friedmt_6", Dur = 35}
, 
{Recipe = "ds_e4sf_12", Dur = 35}
, 
{Recipe = "ds_e4friedmt_7", Dur = 40}
, 
{Recipe = "ds_e4sf_13", Dur = 60}
, 
{Recipe = "ds_e4sf_14", Dur = 190}
, 
{Recipe = "ds_e4sf_15", Dur = 360}
, 
{Recipe = "ds_6e4pasta_1", Dur = 240}
, 
{Recipe = "ds_6e4pasta_2", Dur = 240}
, 
{Recipe = "ds_6e4pasta_3", Dur = 240}
, 
{Recipe = "ds_6e4pasta_4", Dur = 480}
, 
{Recipe = "ds_6e4pasta_5", Dur = 320}
, 
{Recipe = "ds_6e4pasta_6", Dur = 240}
, 
{Recipe = "ds_6e4pasta_7", Dur = 640}
, 
{Recipe = "ds_6e4pasta_8", Dur = 360}
, 
{Recipe = "ds_6e4pasta_9", Dur = 240}
, 
{Recipe = "ds_6e4pasta_10", Dur = 240}
, 
{Recipe = "ds_6e4pasta_11", Dur = 120}
, 
{Recipe = "ds_6e4pasta_12", Dur = 240}
, 
{Recipe = "ds_6e4pasta_13", Dur = 360}
, 
{Recipe = "ds_6e4pasta_14", Dur = 120}
, 
{Recipe = "ds_6e4friedmt_8", Dur = 360}
, 
{Recipe = "ds_6e4pasta_15", Dur = 480}
, 
{Recipe = "ds_6e4pasta_16", Dur = 480}
, 
{Recipe = "ds_6e4pasta_17", Dur = 640}
, 
{Recipe = "ds_6e4friedmt_9", Dur = 640}
, 
{Recipe = "ds_6e4pasta_18", Dur = 480}
, 
{Recipe = "ds_6e4pasta_19", Dur = 640}
, 
{Recipe = "ds_6e4pasta_20", Dur = 640}
, 
{Recipe = "ds_6e4pasta_21", Dur = 640}
, 
{Recipe = "ds_6e4pasta_22", Dur = 480}
, 
{Recipe = "ds_6e4stewmt_2", Dur = 480}
, 
{Recipe = "ds_6e4flb_3", Dur = 640}
, 
{Recipe = "ds_6e4pasta_23", Dur = 360}
, 
{Recipe = "ds_6e4pasta_24", Dur = 640}
, 
{Recipe = "ds_6e4pasta_25", Dur = 640}
, 
{Recipe = "ds_6e4pasta_26", Dur = 640}
, 
{Recipe = "ds_6e4pasta_27", Dur = 640}
, 
{Recipe = "ds_6e4pasta_28", Dur = 640}
, 
{Recipe = "ds_6e4pasta_29", Dur = 1280}
, 
{Recipe = "ds_6e4assort_3", Dur = 480}
}
}
, 
{Type = "eq_4_10", 
Category = {4}
, BookOrder = 2004, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, 
Recipes = {
{Recipe = "ds_friedve_1", Dur = 512}
, 
{Recipe = "ds_friedve_2", Dur = 288}
, 
{Recipe = "ds_friedve_3", Dur = 128}
, 
{Recipe = "ds_friedve_4", Dur = 1024}
, 
{Recipe = "ds_friedmt_1", Dur = 5}
, 
{Recipe = "ds_friedmt_3", Dur = 8}
, 
{Recipe = "ds_friedmt_4", Dur = 16}
, 
{Recipe = "ds_friedmt_5", Dur = 192}
, 
{Recipe = "ds_friedsf_1", Dur = 8}
, 
{Recipe = "ds_friedsf_2", Dur = 8}
, 
{Recipe = "ds_friedsf_3", Dur = 32}
, 
{Recipe = "ds_fd_21", Dur = 136}
, 
{Recipe = "ds_fd_23", Dur = 256}
, 
{Recipe = "ds_friedsf_4", Dur = 512}
, 
{Recipe = "ds_friedve_5", Dur = 512}
, 
{Recipe = "ds_e4friedmt_6", Dur = 28}
, 
{Recipe = "ds_e4sf_12", Dur = 28}
, 
{Recipe = "ds_e4friedmt_7", Dur = 32}
, 
{Recipe = "ds_e4sf_13", Dur = 48}
, 
{Recipe = "ds_e4sf_14", Dur = 152}
, 
{Recipe = "ds_e4sf_15", Dur = 288}
, 
{Recipe = "ds_6e4pasta_1", Dur = 192}
, 
{Recipe = "ds_6e4pasta_2", Dur = 192}
, 
{Recipe = "ds_6e4pasta_3", Dur = 192}
, 
{Recipe = "ds_6e4pasta_4", Dur = 384}
, 
{Recipe = "ds_6e4pasta_5", Dur = 256}
, 
{Recipe = "ds_6e4pasta_6", Dur = 192}
, 
{Recipe = "ds_6e4pasta_7", Dur = 512}
, 
{Recipe = "ds_6e4pasta_8", Dur = 288}
, 
{Recipe = "ds_6e4pasta_9", Dur = 192}
, 
{Recipe = "ds_6e4pasta_10", Dur = 192}
, 
{Recipe = "ds_6e4pasta_11", Dur = 96}
, 
{Recipe = "ds_6e4pasta_12", Dur = 192}
, 
{Recipe = "ds_6e4pasta_13", Dur = 288}
, 
{Recipe = "ds_6e4pasta_14", Dur = 96}
, 
{Recipe = "ds_6e4friedmt_8", Dur = 288}
, 
{Recipe = "ds_6e4pasta_15", Dur = 384}
, 
{Recipe = "ds_6e4pasta_16", Dur = 384}
, 
{Recipe = "ds_6e4pasta_17", Dur = 512}
, 
{Recipe = "ds_6e4friedmt_9", Dur = 512}
, 
{Recipe = "ds_6e4pasta_18", Dur = 384}
, 
{Recipe = "ds_6e4pasta_19", Dur = 512}
, 
{Recipe = "ds_6e4pasta_20", Dur = 512}
, 
{Recipe = "ds_6e4pasta_21", Dur = 512}
, 
{Recipe = "ds_6e4pasta_22", Dur = 384}
, 
{Recipe = "ds_6e4stewmt_2", Dur = 384}
, 
{Recipe = "ds_6e4flb_3", Dur = 512}
, 
{Recipe = "ds_6e4pasta_23", Dur = 288}
, 
{Recipe = "ds_6e4pasta_24", Dur = 512}
, 
{Recipe = "ds_6e4pasta_25", Dur = 512}
, 
{Recipe = "ds_6e4pasta_26", Dur = 512}
, 
{Recipe = "ds_6e4pasta_27", Dur = 512}
, 
{Recipe = "ds_6e4pasta_28", Dur = 512}
, 
{Recipe = "ds_6e4pasta_29", Dur = 1024}
, 
{Recipe = "ds_6e4assort_3", Dur = 384}
}
}
, 
{Type = "pd_5_1", MergedType = "pd_5_2", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "pd_5_2", MergedType = "pd_5_3", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "pd_5_3", MergedType = "pd_5_4", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "pd_5_4", MergedType = "pd_5_5", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_5_1_1", Weight = 11}
, 
{Code = "it_5_2_1", Weight = 9}
}
, Cd = 5400, InitialNumber = 6, Frequency = 6, Capacity = 18, SpeedUpPrice = 3}
, 
{Type = "pd_5_5", MergedType = "pd_5_6", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_5_1_1", Weight = 9}
, 
{Code = "it_5_1_2", Weight = 2}
, 
{Code = "it_5_2_1", Weight = 9}
}
, Cd = 4800, InitialNumber = 8, Frequency = 8, Capacity = 24, SpeedUpPrice = 4}
, 
{Type = "pd_5_6", MergedType = "pd_5_7", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_5_1_1", Weight = 8}
, 
{Code = "it_5_1_2", Weight = 2}
, 
{Code = "it_5_1_3", Weight = 1}
, 
{Code = "it_5_2_1", Weight = 9}
}
, Cd = 3600, InitialNumber = 10, Frequency = 10, Capacity = 30, SpeedUpPrice = 6}
, 
{Type = "pd_5_7", MergedType = "pd_5_8", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_5_1_1", Weight = 7}
, 
{Code = "it_5_1_2", Weight = 3}
, 
{Code = "it_5_1_3", Weight = 1}
, 
{Code = "it_5_2_1", Weight = 9}
}
, Cd = 3000, InitialNumber = 12, Frequency = 12, Capacity = 36, SpeedUpPrice = 8}
, 
{Type = "pd_5_8", MergedType = "pd_5_9", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_5_1_1", Weight = 6}
, 
{Code = "it_5_1_2", Weight = 4}
, 
{Code = "it_5_1_3", Weight = 1}
, 
{Code = "it_5_2_1", Weight = 9}
}
, Cd = 2100, InitialNumber = 14, Frequency = 14, Capacity = 42, SpeedUpPrice = 9}
, 
{Type = "pd_5_9", MergedType = "pd_5_10", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_5_1_1", Weight = 5}
, 
{Code = "it_5_1_2", Weight = 5}
, 
{Code = "it_5_1_3", Weight = 1}
, 
{Code = "it_5_2_1", Weight = 9}
}
, Cd = 1500, InitialNumber = 16, Frequency = 16, Capacity = 48, SpeedUpPrice = 11}
, 
{Type = "pd_5_10", 
Category = {2}
, BookOrder = 1040, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_5_1_1", Weight = 4}
, 
{Code = "it_5_1_2", Weight = 5}
, 
{Code = "it_5_1_3", Weight = 2}
, 
{Code = "it_5_2_1", Weight = 9}
}
, Cd = 900, InitialNumber = 18, Frequency = 18, Capacity = 54, SpeedUpPrice = 13}
, 
{Type = "it_5_1_1", MergedType = "it_5_1_2", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 0.59, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 3}
, 
{Type = "it_5_1_2", MergedType = "it_5_1_3", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1.18, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 5}
, 
{Type = "it_5_1_3", MergedType = "it_5_1_4", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 2.35, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 8}
, 
{Type = "it_5_1_4", MergedType = "it_5_1_5", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 4.71, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 14}
, 
{Type = "it_5_1_5", MergedType = "it_5_1_6", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 9.41, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 22}
, 
{Type = "it_5_1_6", MergedType = "it_5_1_7", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 18.82, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 41}
, 
{Type = "it_5_1_7", MergedType = "it_5_1_8", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 37.65, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 76}
, 
{Type = "it_5_1_8", MergedType = "it_5_1_9", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 75.29, BubbleChance = 25, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 143}
, 
{Type = "it_5_1_9", MergedType = "it_5_1_10", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 150.59, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 270}
, 
{Type = "it_5_1_10", MergedType = "it_5_1_11", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 301.18, BubbleChance = 30, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 8}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 510}
, 
{Type = "it_5_1_11", MergedType = "it_5_1_12", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 602.35, BubbleChance = 30, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 15}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 964}
, 
{Type = "it_5_1_12", 
Category = {1}
, BookOrder = 1041, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1204.71, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 28}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 1820}
, 
{Type = "it_5_2_1", MergedType = "it_5_2_2", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 1.11, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 3}
, 
{Type = "it_5_2_2", MergedType = "it_5_2_3", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 2.22, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 5}
, 
{Type = "it_5_2_3", MergedType = "it_5_2_4", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4.44, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 11}
, 
{Type = "it_5_2_4", MergedType = "it_5_2_5", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 8.89, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 22}
, 
{Type = "it_5_2_5", MergedType = "it_5_2_6", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 17.78, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 41}
, 
{Type = "it_5_2_6", MergedType = "it_5_2_7", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 35.56, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 76}
, 
{Type = "it_5_2_7", MergedType = "it_5_2_8", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 71.11, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 192}
, 
{Type = "it_5_2_8", MergedType = "it_5_2_9", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 142.22, BubbleChance = 25, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 362}
, 
{Type = "it_5_2_9", MergedType = "it_5_2_10", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 284.44, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 7}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 683}
, 
{Type = "it_5_2_10", 
Category = {1}
, BookOrder = 1042, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 568.89, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 13}
, 
Generators = {"pd_5_4", "pd_5_5", "pd_5_6", "pd_5_7", "pd_5_8", "pd_5_9", "pd_5_10"}
, Reward = 1288}
, 
{Type = "pd_6_1", MergedType = "pd_6_2", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "pd_6_2", MergedType = "pd_6_3", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "pd_6_3", MergedType = "pd_6_4", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "pd_6_4", MergedType = "pd_6_5", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_6_1_1", Weight = 20}
}
, Cd = 6600, InitialNumber = 8, Frequency = 8, Capacity = 24, SpeedUpPrice = 4}
, 
{Type = "pd_6_5", MergedType = "pd_6_6", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_6_1_1", Weight = 16}
, 
{Code = "it_6_1_2", Weight = 4}
}
, Cd = 6000, InitialNumber = 10, Frequency = 10, Capacity = 30, SpeedUpPrice = 6}
, 
{Type = "pd_6_6", MergedType = "pd_6_7", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_6_1_1", Weight = 15}
, 
{Code = "it_6_1_2", Weight = 4}
, 
{Code = "it_6_1_3", Weight = 1}
}
, Cd = 4200, InitialNumber = 12, Frequency = 12, Capacity = 36, SpeedUpPrice = 8}
, 
{Type = "pd_6_7", MergedType = "pd_6_8", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_6_1_1", Weight = 15}
, 
{Code = "it_6_1_2", Weight = 3}
, 
{Code = "it_6_1_3", Weight = 2}
}
, Cd = 3300, InitialNumber = 14, Frequency = 14, Capacity = 42, SpeedUpPrice = 9}
, 
{Type = "pd_6_8", MergedType = "pd_6_9", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_6_1_1", Weight = 13}
, 
{Code = "it_6_1_2", Weight = 4}
, 
{Code = "it_6_1_3", Weight = 3}
}
, Cd = 2700, InitialNumber = 16, Frequency = 16, Capacity = 48, SpeedUpPrice = 11}
, 
{Type = "pd_6_9", MergedType = "pd_6_10", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_6_1_1", Weight = 11}
, 
{Code = "it_6_1_2", Weight = 5}
, 
{Code = "it_6_1_3", Weight = 4}
}
, Cd = 1800, InitialNumber = 18, Frequency = 18, Capacity = 54, SpeedUpPrice = 13}
, 
{Type = "pd_6_10", 
Category = {2}
, BookOrder = 1050, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_6_1_1", Weight = 9}
, 
{Code = "it_6_1_2", Weight = 6}
, 
{Code = "it_6_1_3", Weight = 5}
}
, Cd = 1500, InitialNumber = 20, Frequency = 20, Capacity = 60, SpeedUpPrice = 15}
, 
{Type = "it_6_1_1", MergedType = "it_6_1_2", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 0.69, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 3}
, 
{Type = "it_6_1_2", MergedType = "it_6_1_3", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1.38, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 5}
, 
{Type = "it_6_1_3", MergedType = "it_6_1_4", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 2.76, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 8}
, 
{Type = "it_6_1_4", MergedType = "it_6_1_5", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 5.52, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 14}
, 
{Type = "it_6_1_5", MergedType = "it_6_1_6", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 11.03, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 27}
, 
{Type = "it_6_1_6", MergedType = "it_6_1_7", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 22.07, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 51}
, 
{Type = "it_6_1_7", MergedType = "it_6_1_8", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 44.14, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 97}
, 
{Type = "it_6_1_8", MergedType = "it_6_1_9", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 88.28, BubbleChance = 25, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 184}
, 
{Type = "it_6_1_9", 
Category = {1}
, BookOrder = 1051, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 176.55, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_6_4", "pd_6_5", "pd_6_6", "pd_6_7", "pd_6_8", "pd_6_9", "pd_6_10"}
, Reward = 346}
, 
{Type = "eq_5_1", MergedType = "eq_5_2", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "eq_5_2", MergedType = "eq_5_3", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "eq_5_3", MergedType = "eq_5_4", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "eq_5_4", MergedType = "eq_5_5", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, 
Recipes = {
{Recipe = "ds_fd_5", Dur = 130}
, 
{Recipe = "ds_fd_6", Dur = 150}
, 
{Recipe = "ds_fd_7", Dur = 640}
, 
{Recipe = "ds_fd_14", Dur = 340}
, 
{Recipe = "ds_fd_15", Dur = 380}
, 
{Recipe = "ds_fd_16", Dur = 380}
, 
{Recipe = "ds_fd_17", Dur = 380}
, 
{Recipe = "ds_fd_18", Dur = 640}
, 
{Recipe = "ds_fd_19", Dur = 840}
, 
{Recipe = "ds_fd_20", Dur = 660}
, 
{Recipe = "ds_e5mt_1", Dur = 640}
, 
{Recipe = "ds_e5mt_2", Dur = 520}
, 
{Recipe = "ds_6e5dst_6", Dur = 1280}
, 
{Recipe = "ds_6e5pasta_30", Dur = 2560}
, 
{Recipe = "ds_6e5flb_4", Dur = 340}
}
}
, 
{Type = "eq_5_5", MergedType = "eq_5_6", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, 
Recipes = {
{Recipe = "ds_fd_5", Dur = 117}
, 
{Recipe = "ds_fd_6", Dur = 135}
, 
{Recipe = "ds_fd_7", Dur = 576}
, 
{Recipe = "ds_fd_14", Dur = 306}
, 
{Recipe = "ds_fd_15", Dur = 342}
, 
{Recipe = "ds_fd_16", Dur = 342}
, 
{Recipe = "ds_fd_17", Dur = 342}
, 
{Recipe = "ds_fd_18", Dur = 576}
, 
{Recipe = "ds_fd_19", Dur = 756}
, 
{Recipe = "ds_fd_20", Dur = 594}
, 
{Recipe = "ds_e5mt_1", Dur = 576}
, 
{Recipe = "ds_e5mt_2", Dur = 468}
, 
{Recipe = "ds_6e5dst_6", Dur = 1152}
, 
{Recipe = "ds_6e5pasta_30", Dur = 2304}
, 
{Recipe = "ds_6e5flb_4", Dur = 306}
}
}
, 
{Type = "eq_5_6", MergedType = "eq_5_7", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, 
Recipes = {
{Recipe = "ds_fd_5", Dur = 104}
, 
{Recipe = "ds_fd_6", Dur = 120}
, 
{Recipe = "ds_fd_7", Dur = 512}
, 
{Recipe = "ds_fd_14", Dur = 272}
, 
{Recipe = "ds_fd_15", Dur = 304}
, 
{Recipe = "ds_fd_16", Dur = 304}
, 
{Recipe = "ds_fd_17", Dur = 304}
, 
{Recipe = "ds_fd_18", Dur = 512}
, 
{Recipe = "ds_fd_19", Dur = 672}
, 
{Recipe = "ds_fd_20", Dur = 528}
, 
{Recipe = "ds_e5mt_1", Dur = 512}
, 
{Recipe = "ds_e5mt_2", Dur = 416}
, 
{Recipe = "ds_6e5dst_6", Dur = 1024}
, 
{Recipe = "ds_6e5pasta_30", Dur = 2048}
, 
{Recipe = "ds_6e5flb_4", Dur = 272}
}
}
, 
{Type = "eq_5_7", MergedType = "eq_5_8", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, 
Recipes = {
{Recipe = "ds_fd_5", Dur = 91}
, 
{Recipe = "ds_fd_6", Dur = 105}
, 
{Recipe = "ds_fd_7", Dur = 448}
, 
{Recipe = "ds_fd_14", Dur = 238}
, 
{Recipe = "ds_fd_15", Dur = 266}
, 
{Recipe = "ds_fd_16", Dur = 266}
, 
{Recipe = "ds_fd_17", Dur = 266}
, 
{Recipe = "ds_fd_18", Dur = 448}
, 
{Recipe = "ds_fd_19", Dur = 588}
, 
{Recipe = "ds_fd_20", Dur = 462}
, 
{Recipe = "ds_e5mt_1", Dur = 448}
, 
{Recipe = "ds_e5mt_2", Dur = 364}
, 
{Recipe = "ds_6e5dst_6", Dur = 896}
, 
{Recipe = "ds_6e5pasta_30", Dur = 1792}
, 
{Recipe = "ds_6e5flb_4", Dur = 238}
}
}
, 
{Type = "eq_5_8", MergedType = "eq_5_9", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, 
Recipes = {
{Recipe = "ds_fd_5", Dur = 78}
, 
{Recipe = "ds_fd_6", Dur = 90}
, 
{Recipe = "ds_fd_7", Dur = 384}
, 
{Recipe = "ds_fd_14", Dur = 204}
, 
{Recipe = "ds_fd_15", Dur = 228}
, 
{Recipe = "ds_fd_16", Dur = 228}
, 
{Recipe = "ds_fd_17", Dur = 228}
, 
{Recipe = "ds_fd_18", Dur = 384}
, 
{Recipe = "ds_fd_19", Dur = 504}
, 
{Recipe = "ds_fd_20", Dur = 396}
, 
{Recipe = "ds_e5mt_1", Dur = 384}
, 
{Recipe = "ds_e5mt_2", Dur = 312}
, 
{Recipe = "ds_6e5dst_6", Dur = 768}
, 
{Recipe = "ds_6e5pasta_30", Dur = 1536}
, 
{Recipe = "ds_6e5flb_4", Dur = 204}
}
}
, 
{Type = "eq_5_9", MergedType = "eq_5_10", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, 
Recipes = {
{Recipe = "ds_fd_5", Dur = 65}
, 
{Recipe = "ds_fd_6", Dur = 75}
, 
{Recipe = "ds_fd_7", Dur = 320}
, 
{Recipe = "ds_fd_14", Dur = 170}
, 
{Recipe = "ds_fd_15", Dur = 190}
, 
{Recipe = "ds_fd_16", Dur = 190}
, 
{Recipe = "ds_fd_17", Dur = 190}
, 
{Recipe = "ds_fd_18", Dur = 320}
, 
{Recipe = "ds_fd_19", Dur = 420}
, 
{Recipe = "ds_fd_20", Dur = 330}
, 
{Recipe = "ds_e5mt_1", Dur = 320}
, 
{Recipe = "ds_e5mt_2", Dur = 260}
, 
{Recipe = "ds_6e5dst_6", Dur = 640}
, 
{Recipe = "ds_6e5pasta_30", Dur = 1280}
, 
{Recipe = "ds_6e5flb_4", Dur = 170}
}
}
, 
{Type = "eq_5_10", 
Category = {4}
, BookOrder = 2005, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, 
Recipes = {
{Recipe = "ds_fd_5", Dur = 52}
, 
{Recipe = "ds_fd_6", Dur = 60}
, 
{Recipe = "ds_fd_7", Dur = 256}
, 
{Recipe = "ds_fd_14", Dur = 136}
, 
{Recipe = "ds_fd_15", Dur = 152}
, 
{Recipe = "ds_fd_16", Dur = 152}
, 
{Recipe = "ds_fd_17", Dur = 152}
, 
{Recipe = "ds_fd_18", Dur = 256}
, 
{Recipe = "ds_fd_19", Dur = 336}
, 
{Recipe = "ds_fd_20", Dur = 264}
, 
{Recipe = "ds_e5mt_1", Dur = 256}
, 
{Recipe = "ds_e5mt_2", Dur = 208}
, 
{Recipe = "ds_6e5dst_6", Dur = 512}
, 
{Recipe = "ds_6e5pasta_30", Dur = 1024}
, 
{Recipe = "ds_6e5flb_4", Dur = 136}
}
}
, 
{Type = "pd_7_1", MergedType = "pd_7_2", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "pd_7_2", MergedType = "pd_7_3", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "pd_7_3", MergedType = "pd_7_4", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "pd_7_4", MergedType = "pd_7_5", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_7_1_1", Weight = 15}
, 
{Code = "it_7_2_1", Weight = 5}
}
, Cd = 6600, InitialNumber = 8, Frequency = 8, Capacity = 24, SpeedUpPrice = 4}
, 
{Type = "pd_7_5", MergedType = "pd_7_6", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_7_1_1", Weight = 12}
, 
{Code = "it_7_1_2", Weight = 3}
, 
{Code = "it_7_2_1", Weight = 5}
}
, Cd = 6000, InitialNumber = 12, Frequency = 12, Capacity = 36, SpeedUpPrice = 8}
, 
{Type = "pd_7_6", MergedType = "pd_7_7", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_7_1_1", Weight = 10}
, 
{Code = "it_7_1_2", Weight = 4}
, 
{Code = "it_7_1_3", Weight = 1}
, 
{Code = "it_7_2_1", Weight = 5}
}
, Cd = 4200, InitialNumber = 14, Frequency = 14, Capacity = 42, SpeedUpPrice = 9}
, 
{Type = "pd_7_7", MergedType = "pd_7_8", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_7_1_1", Weight = 10}
, 
{Code = "it_7_1_2", Weight = 3}
, 
{Code = "it_7_1_3", Weight = 2}
, 
{Code = "it_7_2_1", Weight = 5}
}
, Cd = 3300, InitialNumber = 16, Frequency = 16, Capacity = 48, SpeedUpPrice = 11}
, 
{Type = "pd_7_8", MergedType = "pd_7_9", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_7_1_1", Weight = 8}
, 
{Code = "it_7_1_2", Weight = 4}
, 
{Code = "it_7_1_3", Weight = 3}
, 
{Code = "it_7_2_1", Weight = 5}
}
, Cd = 2700, InitialNumber = 18, Frequency = 18, Capacity = 54, SpeedUpPrice = 13}
, 
{Type = "pd_7_9", MergedType = "pd_7_10", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_7_1_1", Weight = 6}
, 
{Code = "it_7_1_2", Weight = 5}
, 
{Code = "it_7_1_3", Weight = 4}
, 
{Code = "it_7_2_1", Weight = 5}
}
, Cd = 1800, InitialNumber = 20, Frequency = 20, Capacity = 60, SpeedUpPrice = 15}
, 
{Type = "pd_7_10", 
Category = {2}
, BookOrder = 1052, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_7_1_1", Weight = 4}
, 
{Code = "it_7_1_2", Weight = 6}
, 
{Code = "it_7_1_3", Weight = 5}
, 
{Code = "it_7_2_1", Weight = 5}
}
, Cd = 1500, InitialNumber = 22, Frequency = 22, Capacity = 66, SpeedUpPrice = 17}
, 
{Type = "it_7_1_1", MergedType = "it_7_1_2", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 0.42, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 3}
, 
{Type = "it_7_1_2", MergedType = "it_7_1_3", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 0.83, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 5}
, 
{Type = "it_7_1_3", MergedType = "it_7_1_4", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1.67, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 8}
, 
{Type = "it_7_1_4", MergedType = "it_7_1_5", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 3.33, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 11}
, 
{Type = "it_7_1_5", MergedType = "it_7_1_6", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 6.67, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 16}
, 
{Type = "it_7_1_6", MergedType = "it_7_1_7", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 13.33, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 32}
, 
{Type = "it_7_1_7", MergedType = "it_7_1_8", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 26.67, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 59}
, 
{Type = "it_7_1_8", MergedType = "it_7_1_9", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 53.33, BubbleChance = 25, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 113}
, 
{Type = "it_7_1_9", MergedType = "it_7_1_10", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 106.67, BubbleChance = 30, UnlockPrice = 8000, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 216}
, 
{Type = "it_7_1_10", 
Category = {1}
, BookOrder = 1053, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 213.33, UnlockPrice = 12000, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 408}
, 
{Type = "it_7_2_1", MergedType = "it_7_2_2", 
Category = {1}
, BookOrder = 1055, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 2, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 5}
, 
{Type = "it_7_2_2", MergedType = "it_7_2_3", 
Category = {1}
, BookOrder = 1055, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 11}
, 
{Type = "it_7_2_3", MergedType = "it_7_2_4", 
Category = {1}
, BookOrder = 1055, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 8, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 19}
, 
{Type = "it_7_2_4", MergedType = "it_7_2_5", 
Category = {1}
, BookOrder = 1055, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 16, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 38}
, 
{Type = "it_7_2_5", MergedType = "it_7_2_6", 
Category = {1}
, BookOrder = 1055, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 32, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 70}
, 
{Type = "it_7_2_6", MergedType = "it_7_2_7", 
Category = {1}
, BookOrder = 1055, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 64, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 135}
, 
{Type = "it_7_2_7", MergedType = "it_7_2_8", 
Category = {1}
, BookOrder = 1055, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 128, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 257}
, 
{Type = "it_7_2_8", 
Category = {1}
, BookOrder = 1055, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 256, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Generators = {"pd_7_4", "pd_7_5", "pd_7_6", "pd_7_7", "pd_7_8", "pd_7_9", "pd_7_10"}
, Reward = 483}
, 
{Type = "eq_6_1", MergedType = "eq_6_2", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3}
, 
{Type = "eq_6_2", MergedType = "eq_6_3", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135}
, 
{Type = "eq_6_3", MergedType = "eq_6_4", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268}
, 
{Type = "eq_6_4", MergedType = "eq_6_5", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, 
Recipes = {
{Recipe = "ds_e6cockt_7", Dur = 70}
, 
{Recipe = "ds_e6soup_1", Dur = 80}
, 
{Recipe = "ds_e6stewmt_1", Dur = 380}
, 
{Recipe = "ds_e6dst_2", Dur = 380}
, 
{Recipe = "ds_e6sf_16", Dur = 720}
, 
{Recipe = "ds_6e6preingre_3", Dur = 640}
, 
{Recipe = "ds_6e6dst_3", Dur = 720}
, 
{Recipe = "ds_e6sf_18", Dur = 1280}
, 
{Recipe = "ds_6e6rice_3", Dur = 1280}
, 
{Recipe = "ds_6e6dst_5", Dur = 1280}
, 
{Recipe = "ds_6e6rice_4", Dur = 1280}
, 
{Recipe = "ds_6e6semi_1", Dur = 240}
, 
{Recipe = "ds_6e6semi_2", Dur = 480}
, 
{Recipe = "ds_6e6semi_3", Dur = 1280}
}
}
, 
{Type = "eq_6_5", MergedType = "eq_6_6", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, 
Recipes = {
{Recipe = "ds_e6cockt_7", Dur = 63}
, 
{Recipe = "ds_e6soup_1", Dur = 72}
, 
{Recipe = "ds_e6stewmt_1", Dur = 342}
, 
{Recipe = "ds_e6dst_2", Dur = 342}
, 
{Recipe = "ds_e6sf_16", Dur = 648}
, 
{Recipe = "ds_6e6preingre_3", Dur = 576}
, 
{Recipe = "ds_6e6dst_3", Dur = 648}
, 
{Recipe = "ds_e6sf_18", Dur = 1152}
, 
{Recipe = "ds_6e6rice_3", Dur = 1152}
, 
{Recipe = "ds_6e6dst_5", Dur = 1152}
, 
{Recipe = "ds_6e6rice_4", Dur = 1152}
, 
{Recipe = "ds_6e6semi_1", Dur = 216}
, 
{Recipe = "ds_6e6semi_2", Dur = 432}
, 
{Recipe = "ds_6e6semi_3", Dur = 1152}
}
}
, 
{Type = "eq_6_6", MergedType = "eq_6_7", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, 
Recipes = {
{Recipe = "ds_e6cockt_7", Dur = 56}
, 
{Recipe = "ds_e6soup_1", Dur = 64}
, 
{Recipe = "ds_e6stewmt_1", Dur = 304}
, 
{Recipe = "ds_e6dst_2", Dur = 304}
, 
{Recipe = "ds_e6sf_16", Dur = 576}
, 
{Recipe = "ds_6e6preingre_3", Dur = 512}
, 
{Recipe = "ds_6e6dst_3", Dur = 576}
, 
{Recipe = "ds_e6sf_18", Dur = 1024}
, 
{Recipe = "ds_6e6rice_3", Dur = 1024}
, 
{Recipe = "ds_6e6dst_5", Dur = 1024}
, 
{Recipe = "ds_6e6rice_4", Dur = 1024}
, 
{Recipe = "ds_6e6semi_1", Dur = 192}
, 
{Recipe = "ds_6e6semi_2", Dur = 384}
, 
{Recipe = "ds_6e6semi_3", Dur = 1024}
}
}
, 
{Type = "eq_6_7", MergedType = "eq_6_8", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, UnlockPrice = 3000, 
Recipes = {
{Recipe = "ds_e6cockt_7", Dur = 49}
, 
{Recipe = "ds_e6soup_1", Dur = 56}
, 
{Recipe = "ds_e6stewmt_1", Dur = 266}
, 
{Recipe = "ds_e6dst_2", Dur = 266}
, 
{Recipe = "ds_e6sf_16", Dur = 504}
, 
{Recipe = "ds_6e6preingre_3", Dur = 448}
, 
{Recipe = "ds_6e6dst_3", Dur = 504}
, 
{Recipe = "ds_e6sf_18", Dur = 896}
, 
{Recipe = "ds_6e6rice_3", Dur = 896}
, 
{Recipe = "ds_6e6dst_5", Dur = 896}
, 
{Recipe = "ds_6e6rice_4", Dur = 896}
, 
{Recipe = "ds_6e6semi_1", Dur = 168}
, 
{Recipe = "ds_6e6semi_2", Dur = 336}
, 
{Recipe = "ds_6e6semi_3", Dur = 896}
}
}
, 
{Type = "eq_6_8", MergedType = "eq_6_9", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 5000, 
Recipes = {
{Recipe = "ds_e6cockt_7", Dur = 42}
, 
{Recipe = "ds_e6soup_1", Dur = 48}
, 
{Recipe = "ds_e6stewmt_1", Dur = 228}
, 
{Recipe = "ds_e6dst_2", Dur = 228}
, 
{Recipe = "ds_e6sf_16", Dur = 432}
, 
{Recipe = "ds_6e6preingre_3", Dur = 384}
, 
{Recipe = "ds_6e6dst_3", Dur = 432}
, 
{Recipe = "ds_e6sf_18", Dur = 768}
, 
{Recipe = "ds_6e6rice_3", Dur = 768}
, 
{Recipe = "ds_6e6dst_5", Dur = 768}
, 
{Recipe = "ds_6e6rice_4", Dur = 768}
, 
{Recipe = "ds_6e6semi_1", Dur = 144}
, 
{Recipe = "ds_6e6semi_2", Dur = 288}
, 
{Recipe = "ds_6e6semi_3", Dur = 768}
}
}
, 
{Type = "eq_6_9", MergedType = "eq_6_10", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 8000, 
Recipes = {
{Recipe = "ds_e6cockt_7", Dur = 35}
, 
{Recipe = "ds_e6soup_1", Dur = 40}
, 
{Recipe = "ds_e6stewmt_1", Dur = 190}
, 
{Recipe = "ds_e6dst_2", Dur = 190}
, 
{Recipe = "ds_e6sf_16", Dur = 360}
, 
{Recipe = "ds_6e6preingre_3", Dur = 320}
, 
{Recipe = "ds_6e6dst_3", Dur = 360}
, 
{Recipe = "ds_e6sf_18", Dur = 640}
, 
{Recipe = "ds_6e6rice_3", Dur = 640}
, 
{Recipe = "ds_6e6dst_5", Dur = 640}
, 
{Recipe = "ds_6e6rice_4", Dur = 640}
, 
{Recipe = "ds_6e6semi_1", Dur = 120}
, 
{Recipe = "ds_6e6semi_2", Dur = 240}
, 
{Recipe = "ds_6e6semi_3", Dur = 640}
}
}
, 
{Type = "eq_6_10", 
Category = {4}
, BookOrder = 2006, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 12000, 
Recipes = {
{Recipe = "ds_e6cockt_7", Dur = 28}
, 
{Recipe = "ds_e6soup_1", Dur = 32}
, 
{Recipe = "ds_e6stewmt_1", Dur = 152}
, 
{Recipe = "ds_e6dst_2", Dur = 152}
, 
{Recipe = "ds_e6sf_16", Dur = 288}
, 
{Recipe = "ds_6e6preingre_3", Dur = 256}
, 
{Recipe = "ds_6e6dst_3", Dur = 288}
, 
{Recipe = "ds_e6sf_18", Dur = 512}
, 
{Recipe = "ds_6e6rice_3", Dur = 512}
, 
{Recipe = "ds_6e6dst_5", Dur = 512}
, 
{Recipe = "ds_6e6rice_4", Dur = 512}
, 
{Recipe = "ds_6e6semi_1", Dur = 96}
, 
{Recipe = "ds_6e6semi_2", Dur = 192}
, 
{Recipe = "ds_6e6semi_3", Dur = 512}
}
}
, 
{Type = "greenbox_1", 
Category = {3, 6, 7}
, 
GeneratedItems = {
{Code = "item", Weight = 2}
, 
{Code = "ene_2", Weight = 2}
}
, Cd = 1, InitialNumber = 4, Frequency = 1, Capacity = 4, DropsTotal = 4}
, 
{Type = "ene_1", MergedType = "ene_2", 
Category = {1}
, BookOrder = 3002, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, 
CollectRewards = {
{Currency = "energy", Amount = 1}
}
}
, 
{Type = "ene_2", MergedType = "ene_3", 
Category = {1}
, BookOrder = 3002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, 
CollectRewards = {
{Currency = "energy", Amount = 5}
}
}
, 
{Type = "ene_3", MergedType = "ene_4", 
Category = {1}
, BookOrder = 3002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, 
CollectRewards = {
{Currency = "energy", Amount = 15}
}
}
, 
{Type = "ene_4", MergedType = "ene_5", 
Category = {1}
, BookOrder = 3002, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, 
CollectRewards = {
{Currency = "energy", Amount = 40}
}
}
, 
{Type = "ene_5", 
Category = {1}
, BookOrder = 3002, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, 
CollectRewards = {
{Currency = "energy", Amount = 100}
}
}
, 
{Type = "gem_1", MergedType = "gem_2", 
Category = {1}
, BookOrder = 3003, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, 
CollectRewards = {
{Currency = "gem", Amount = 1}
}
}
, 
{Type = "gem_2", MergedType = "gem_3", 
Category = {1}
, BookOrder = 3003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, 
CollectRewards = {
{Currency = "gem", Amount = 3}
}
}
, 
{Type = "gem_3", MergedType = "gem_4", 
Category = {1}
, BookOrder = 3003, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, 
CollectRewards = {
{Currency = "gem", Amount = 7}
}
}
, 
{Type = "gem_4", 
Category = {1}
, BookOrder = 3003, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, 
CollectRewards = {
{Currency = "gem", Amount = 15}
}
}
, 
{Type = "skiptime_1", BoosterType = "SkipTime", Effect = 3600}
, 
{Type = "additem_1", BoosterType = "AddItem", Effect = 8}
, 
{Type = "enebox_1", 
Category = {3, 7}
, 
GeneratedItems = {
{Code = "ene_2", Weight = 2}
, 
{Code = "ene_3", Weight = 1}
, 
{Code = "ene_4", Weight = 1}
}
, Cd = 1, InitialNumber = 4, Frequency = 1, Capacity = 4, DropsTotal = 4}
, 
{Type = "freebox_1", 
Category = {3, 7}
, 
GeneratedItems = {
{Code = "it_1_1_1", Weight = 10}
, 
{Code = "it_1_1_2", Weight = 5}
, 
{Code = "it_2_1_1", Weight = 10}
, 
{Code = "it_2_1_2", Weight = 5}
}
, Cd = 1, InitialNumber = 4, Frequency = 1, Capacity = 4, DropsTotal = 4}
, 
{Type = "newday_1_1", BoosterType = "NewDay", Effect = 3600}
, 
{Type = "newday_2_1", BoosterType = "NewDay", Effect = 7200}
, 
{Type = "pd_a6_1", MergedType = "pd_a6_2", 
Category = {2}
, BookOrder = 1056, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, UnlockPrice = 110, ProtectLevel = 3, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_a6_1_1", Weight = 11}
, 
{Code = "it_a6_2_1", Weight = 9}
}
, Cd = 5400, InitialNumber = 6, Frequency = 6, Capacity = 18, SpeedUpPrice = 3}
, 
{Type = "pd_a6_2", MergedType = "pd_a6_3", 
Category = {2}
, BookOrder = 1056, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 135, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_a6_1_1", Weight = 9}
, 
{Code = "it_a6_1_2", Weight = 2}
, 
{Code = "it_a6_2_1", Weight = 9}
}
, Cd = 4800, InitialNumber = 8, Frequency = 8, Capacity = 24, SpeedUpPrice = 4}
, 
{Type = "pd_a6_3", MergedType = "pd_a6_4", 
Category = {2}
, BookOrder = 1056, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 268, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_a6_1_1", Weight = 8}
, 
{Code = "it_a6_1_2", Weight = 2}
, 
{Code = "it_a6_1_3", Weight = 1}
, 
{Code = "it_a6_2_1", Weight = 9}
}
, Cd = 3600, InitialNumber = 10, Frequency = 10, Capacity = 30, SpeedUpPrice = 6}
, 
{Type = "pd_a6_4", MergedType = "pd_a6_5", 
Category = {2}
, BookOrder = 1056, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, UnlockPrice = 412, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_a6_1_1", Weight = 7}
, 
{Code = "it_a6_1_2", Weight = 3}
, 
{Code = "it_a6_1_3", Weight = 1}
, 
{Code = "it_a6_2_1", Weight = 9}
}
, Cd = 3000, InitialNumber = 12, Frequency = 12, Capacity = 36, SpeedUpPrice = 8}
, 
{Type = "pd_a6_5", MergedType = "pd_a6_6", 
Category = {2}
, BookOrder = 1056, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 824, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_a6_1_1", Weight = 6}
, 
{Code = "it_a6_1_2", Weight = 4}
, 
{Code = "it_a6_1_3", Weight = 1}
, 
{Code = "it_a6_2_1", Weight = 9}
}
, Cd = 2100, InitialNumber = 14, Frequency = 14, Capacity = 42, SpeedUpPrice = 9}
, 
{Type = "pd_a6_6", 
Category = {2}
, BookOrder = 1056, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, UnlockPrice = 1600, UseEnergy = 1, 
GeneratedItems = {
{Code = "it_a6_1_1", Weight = 5}
, 
{Code = "it_a6_1_2", Weight = 5}
, 
{Code = "it_a6_1_3", Weight = 1}
, 
{Code = "it_a6_2_1", Weight = 9}
}
, Cd = 1500, InitialNumber = 16, Frequency = 16, Capacity = 48, SpeedUpPrice = 11}
, 
{Type = "it_a6_1_1", MergedType = "it_a6_1_2", 
Category = {1}
, BookOrder = 1057, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 0.59, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 3}
, 
{Type = "it_a6_1_2", MergedType = "it_a6_1_3", 
Category = {1}
, BookOrder = 1057, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 1.18, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 5}
, 
{Type = "it_a6_1_3", MergedType = "it_a6_1_4", 
Category = {1}
, BookOrder = 1057, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 2.35, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 8}
, 
{Type = "it_a6_1_4", MergedType = "it_a6_1_5", 
Category = {1}
, BookOrder = 1057, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 4.71, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 14}
, 
{Type = "it_a6_1_5", MergedType = "it_a6_1_6", 
Category = {1}
, BookOrder = 1057, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 9.41, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 22}
, 
{Type = "it_a6_1_6", MergedType = "it_a6_1_7", 
Category = {1}
, BookOrder = 1057, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 18.82, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 41}
, 
{Type = "it_a6_1_7", MergedType = "it_a6_1_8", 
Category = {1}
, BookOrder = 1057, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 37.65, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 76}
, 
{Type = "it_a6_1_8", 
Category = {1}
, BookOrder = 1057, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 75.29, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 143}
, 
{Type = "it_a6_2_1", MergedType = "it_a6_2_2", 
Category = {1}
, BookOrder = 1058, 
BookReward = {
{Currency = "skipprop", Amount = 1}
}
, Score = 1.11, UnlockPrice = 110, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 3}
, 
{Type = "it_a6_2_2", MergedType = "it_a6_2_3", 
Category = {1}
, BookOrder = 1058, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 2.22, UnlockPrice = 135, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 5}
, 
{Type = "it_a6_2_3", MergedType = "it_a6_2_4", 
Category = {1}
, BookOrder = 1058, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 4.44, UnlockPrice = 268, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 11}
, 
{Type = "it_a6_2_4", MergedType = "it_a6_2_5", 
Category = {1}
, BookOrder = 1058, 
BookReward = {
{Currency = "skipprop", Amount = 2}
}
, Score = 8.89, BubbleChance = 5, UnlockPrice = 412, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 22}
, 
{Type = "it_a6_2_5", MergedType = "it_a6_2_6", 
Category = {1}
, BookOrder = 1058, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 17.78, BubbleChance = 10, UnlockPrice = 824, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 41}
, 
{Type = "it_a6_2_6", MergedType = "it_a6_2_7", 
Category = {1}
, BookOrder = 1058, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 35.56, BubbleChance = 15, UnlockPrice = 1600, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 76}
, 
{Type = "it_a6_2_7", MergedType = "it_a6_2_8", 
Category = {1}
, BookOrder = 1058, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, Score = 71.11, BubbleChance = 20, UnlockPrice = 3000, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 192}
, 
{Type = "it_a6_2_8", 
Category = {1}
, BookOrder = 1058, 
BookReward = {
{Currency = "energy", Amount = 1}
}
, Score = 142.22, UnlockPrice = 5000, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Generators = {"pd_a6_1", "pd_a6_2", "pd_a6_3", "pd_a6_4", "pd_a6_5", "pd_a6_6"}
, Reward = 362}
, 
{Type = "ds_chopve_1", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_1", Amount = 1}
}
, BookOrder = 4001, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 5, InRandom = 0, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 10}
, 
{Instru = "eq_1_5", Dur = 9}
, 
{Instru = "eq_1_6", Dur = 8}
, 
{Instru = "eq_1_7", Dur = 7}
, 
{Instru = "eq_1_8", Dur = 6}
, 
{Instru = "eq_1_9", Dur = 5}
, 
{Instru = "eq_1_10", Dur = 5}
}
}
, 
{Type = "ds_chopve_2", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_2", Amount = 1}
}
, BookOrder = 4001, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 8, InRandom = 0, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 20}
, 
{Instru = "eq_1_5", Dur = 18}
, 
{Instru = "eq_1_6", Dur = 16}
, 
{Instru = "eq_1_7", Dur = 14}
, 
{Instru = "eq_1_8", Dur = 12}
, 
{Instru = "eq_1_9", Dur = 10}
, 
{Instru = "eq_1_10", Dur = 8}
}
}
, 
{Type = "ds_chopve_3", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_3", Amount = 1}
}
, BookOrder = 4001, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 11, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_chopve_4", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_4", Amount = 1}
}
, BookOrder = 4001, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 2, Reward = 14, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 80}
, 
{Instru = "eq_1_5", Dur = 72}
, 
{Instru = "eq_1_6", Dur = 64}
, 
{Instru = "eq_1_7", Dur = 56}
, 
{Instru = "eq_1_8", Dur = 48}
, 
{Instru = "eq_1_9", Dur = 40}
, 
{Instru = "eq_1_10", Dur = 32}
}
}
, 
{Type = "ds_chopfr_1", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_6", Amount = 1}
}
, BookOrder = 4001, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 32, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 320}
, 
{Instru = "eq_1_5", Dur = 288}
, 
{Instru = "eq_1_6", Dur = 256}
, 
{Instru = "eq_1_7", Dur = 224}
, 
{Instru = "eq_1_8", Dur = 192}
, 
{Instru = "eq_1_9", Dur = 160}
, 
{Instru = "eq_1_10", Dur = 128}
}
}
, 
{Type = "ds_grillve_1", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_7", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 65, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 640}
, 
{Instru = "eq_2_5", Dur = 576}
, 
{Instru = "eq_2_6", Dur = 512}
, 
{Instru = "eq_2_7", Dur = 448}
, 
{Instru = "eq_2_8", Dur = 384}
, 
{Instru = "eq_2_9", Dur = 320}
, 
{Instru = "eq_2_10", Dur = 256}
}
}
, 
{Type = "ds_grillve_2", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_3", Amount = 1}
, 
{Material = "it_1_1_5", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 4, Reward = 30, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 200}
, 
{Instru = "eq_2_5", Dur = 180}
, 
{Instru = "eq_2_6", Dur = 160}
, 
{Instru = "eq_2_7", Dur = 140}
, 
{Instru = "eq_2_8", Dur = 120}
, 
{Instru = "eq_2_9", Dur = 100}
, 
{Instru = "eq_2_10", Dur = 80}
}
}
, 
{Type = "ds_grillve_3", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_5", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 19, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 160}
, 
{Instru = "eq_2_5", Dur = 144}
, 
{Instru = "eq_2_6", Dur = 128}
, 
{Instru = "eq_2_7", Dur = 112}
, 
{Instru = "eq_2_8", Dur = 96}
, 
{Instru = "eq_2_9", Dur = 80}
, 
{Instru = "eq_2_10", Dur = 64}
}
}
, 
{Type = "ds_grillve_4", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_1_1_5", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 3, Reward = 27, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 180}
, 
{Instru = "eq_2_5", Dur = 162}
, 
{Instru = "eq_2_6", Dur = 144}
, 
{Instru = "eq_2_7", Dur = 126}
, 
{Instru = "eq_2_8", Dur = 108}
, 
{Instru = "eq_2_9", Dur = 90}
, 
{Instru = "eq_2_10", Dur = 72}
}
}
, 
{Type = "ds_friedve_1", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_8", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 124, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_friedve_2", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_7", Amount = 1}
, 
{Material = "it_1_1_4", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 78, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 720}
, 
{Instru = "eq_4_5", Dur = 648}
, 
{Instru = "eq_4_6", Dur = 576}
, 
{Instru = "eq_4_7", Dur = 504}
, 
{Instru = "eq_4_8", Dur = 432}
, 
{Instru = "eq_4_9", Dur = 360}
, 
{Instru = "eq_4_10", Dur = 288}
}
}
, 
{Type = "ds_friedve_3", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_3", Amount = 1}
, 
{Material = "it_1_1_5", Amount = 1}
, 
{Material = "it_2_3_1", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 49, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 320}
, 
{Instru = "eq_4_5", Dur = 288}
, 
{Instru = "eq_4_6", Dur = 256}
, 
{Instru = "eq_4_7", Dur = 224}
, 
{Instru = "eq_4_8", Dur = 192}
, 
{Instru = "eq_4_9", Dur = 160}
, 
{Instru = "eq_4_10", Dur = 128}
}
}
, 
{Type = "ds_friedve_4", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_9", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 42, Reward = 238, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 2560}
, 
{Instru = "eq_4_5", Dur = 2304}
, 
{Instru = "eq_4_6", Dur = 2048}
, 
{Instru = "eq_4_7", Dur = 1792}
, 
{Instru = "eq_4_8", Dur = 1536}
, 
{Instru = "eq_4_9", Dur = 1280}
, 
{Instru = "eq_4_10", Dur = 1024}
}
}
, 
{Type = "ds_friedmt_2", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_1", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 5, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 10}
, 
{Instru = "eq_2_5", Dur = 9}
, 
{Instru = "eq_2_6", Dur = 8}
, 
{Instru = "eq_2_7", Dur = 7}
, 
{Instru = "eq_2_8", Dur = 6}
, 
{Instru = "eq_2_9", Dur = 5}
, 
{Instru = "eq_2_10", Dur = 5}
}
}
, 
{Type = "ds_grillmt_1", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_2", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 14, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 20}
, 
{Instru = "eq_1_5", Dur = 18}
, 
{Instru = "eq_1_6", Dur = 16}
, 
{Instru = "eq_1_7", Dur = 14}
, 
{Instru = "eq_1_8", Dur = 12}
, 
{Instru = "eq_1_9", Dur = 10}
, 
{Instru = "eq_1_10", Dur = 8}
}
}
, 
{Type = "ds_grillmt_2", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_4", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 2, Reward = 14, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 80}
, 
{Instru = "eq_2_5", Dur = 72}
, 
{Instru = "eq_2_6", Dur = 64}
, 
{Instru = "eq_2_7", Dur = 56}
, 
{Instru = "eq_2_8", Dur = 48}
, 
{Instru = "eq_2_9", Dur = 40}
, 
{Instru = "eq_2_10", Dur = 32}
}
}
, 
{Type = "ds_grillmt_3", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_3", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 22, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 40}
, 
{Instru = "eq_2_5", Dur = 36}
, 
{Instru = "eq_2_6", Dur = 32}
, 
{Instru = "eq_2_7", Dur = 28}
, 
{Instru = "eq_2_8", Dur = 24}
, 
{Instru = "eq_2_9", Dur = 20}
, 
{Instru = "eq_2_10", Dur = 16}
}
}
, 
{Type = "ds_grillmt_4", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_5", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 3, Reward = 19, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 160}
, 
{Instru = "eq_2_5", Dur = 144}
, 
{Instru = "eq_2_6", Dur = 128}
, 
{Instru = "eq_2_7", Dur = 112}
, 
{Instru = "eq_2_8", Dur = 96}
, 
{Instru = "eq_2_9", Dur = 80}
, 
{Instru = "eq_2_10", Dur = 64}
}
}
, 
{Type = "ds_grillmt_5", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_6", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 32, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 320}
, 
{Instru = "eq_2_5", Dur = 288}
, 
{Instru = "eq_2_6", Dur = 256}
, 
{Instru = "eq_2_7", Dur = 224}
, 
{Instru = "eq_2_8", Dur = 192}
, 
{Instru = "eq_2_9", Dur = 160}
, 
{Instru = "eq_2_10", Dur = 128}
}
}
, 
{Type = "ds_grillmt_6", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_4", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 2, Reward = 41, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 80}
, 
{Instru = "eq_2_5", Dur = 72}
, 
{Instru = "eq_2_6", Dur = 64}
, 
{Instru = "eq_2_7", Dur = 56}
, 
{Instru = "eq_2_8", Dur = 48}
, 
{Instru = "eq_2_9", Dur = 40}
, 
{Instru = "eq_2_10", Dur = 32}
}
}
, 
{Type = "ds_grillmt_7", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_7", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 62, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 640}
, 
{Instru = "eq_2_5", Dur = 576}
, 
{Instru = "eq_2_6", Dur = 512}
, 
{Instru = "eq_2_7", Dur = 448}
, 
{Instru = "eq_2_8", Dur = 384}
, 
{Instru = "eq_2_9", Dur = 320}
, 
{Instru = "eq_2_10", Dur = 256}
}
}
, 
{Type = "ds_grillmt_8", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_8", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 9, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 119, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 1280}
, 
{Instru = "eq_2_5", Dur = 1152}
, 
{Instru = "eq_2_6", Dur = 1024}
, 
{Instru = "eq_2_7", Dur = 896}
, 
{Instru = "eq_2_8", Dur = 768}
, 
{Instru = "eq_2_9", Dur = 640}
, 
{Instru = "eq_2_10", Dur = 512}
}
}
, 
{Type = "ds_grillmt_9", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_5", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 10, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 3, Reward = 78, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 160}
, 
{Instru = "eq_2_5", Dur = 144}
, 
{Instru = "eq_2_6", Dur = 128}
, 
{Instru = "eq_2_7", Dur = 112}
, 
{Instru = "eq_2_8", Dur = 96}
, 
{Instru = "eq_2_9", Dur = 80}
, 
{Instru = "eq_2_10", Dur = 64}
}
}
, 
{Type = "ds_grillmt_10", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_7", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 11, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 273, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 640}
, 
{Instru = "eq_2_5", Dur = 576}
, 
{Instru = "eq_2_6", Dur = 512}
, 
{Instru = "eq_2_7", Dur = 448}
, 
{Instru = "eq_2_8", Dur = 384}
, 
{Instru = "eq_2_9", Dur = 320}
, 
{Instru = "eq_2_10", Dur = 256}
}
}
, 
{Type = "ds_grillmt_11", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_9", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 12, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 224, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 2560}
, 
{Instru = "eq_2_5", Dur = 2304}
, 
{Instru = "eq_2_6", Dur = 2048}
, 
{Instru = "eq_2_7", Dur = 1792}
, 
{Instru = "eq_2_8", Dur = 1536}
, 
{Instru = "eq_2_9", Dur = 1280}
, 
{Instru = "eq_2_10", Dur = 1024}
}
}
, 
{Type = "ds_juice_1", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_4", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 2, Reward = 14, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 80}
, 
{Instru = "eq_3_5", Dur = 72}
, 
{Instru = "eq_3_6", Dur = 64}
, 
{Instru = "eq_3_7", Dur = 56}
, 
{Instru = "eq_3_8", Dur = 48}
, 
{Instru = "eq_3_9", Dur = 40}
, 
{Instru = "eq_3_10", Dur = 32}
}
}
, 
{Type = "ds_juice_2", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_5", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 3, Reward = 19, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 160}
, 
{Instru = "eq_3_5", Dur = 144}
, 
{Instru = "eq_3_6", Dur = 128}
, 
{Instru = "eq_3_7", Dur = 112}
, 
{Instru = "eq_3_8", Dur = 96}
, 
{Instru = "eq_3_9", Dur = 80}
, 
{Instru = "eq_3_10", Dur = 64}
}
}
, 
{Type = "ds_juice_3", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_2", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 8, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 20}
, 
{Instru = "eq_3_5", Dur = 18}
, 
{Instru = "eq_3_6", Dur = 16}
, 
{Instru = "eq_3_7", Dur = 14}
, 
{Instru = "eq_3_8", Dur = 12}
, 
{Instru = "eq_3_9", Dur = 10}
, 
{Instru = "eq_3_10", Dur = 8}
}
}
, 
{Type = "ds_juice_4", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_1", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 5, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 10}
, 
{Instru = "eq_3_5", Dur = 9}
, 
{Instru = "eq_3_6", Dur = 8}
, 
{Instru = "eq_3_7", Dur = 7}
, 
{Instru = "eq_3_8", Dur = 6}
, 
{Instru = "eq_3_9", Dur = 5}
, 
{Instru = "eq_3_10", Dur = 5}
}
}
, 
{Type = "ds_juice_6", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_7", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 62, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 640}
, 
{Instru = "eq_3_5", Dur = 576}
, 
{Instru = "eq_3_6", Dur = 512}
, 
{Instru = "eq_3_7", Dur = 448}
, 
{Instru = "eq_3_8", Dur = 384}
, 
{Instru = "eq_3_9", Dur = 320}
, 
{Instru = "eq_3_10", Dur = 256}
}
}
, 
{Type = "ds_juice_7", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_3", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 11, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 40}
, 
{Instru = "eq_3_5", Dur = 36}
, 
{Instru = "eq_3_6", Dur = 32}
, 
{Instru = "eq_3_7", Dur = 28}
, 
{Instru = "eq_3_8", Dur = 24}
, 
{Instru = "eq_3_9", Dur = 20}
, 
{Instru = "eq_3_10", Dur = 16}
}
}
, 
{Type = "ds_juice_8", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_8", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 119, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 1280}
, 
{Instru = "eq_3_5", Dur = 1152}
, 
{Instru = "eq_3_6", Dur = 1024}
, 
{Instru = "eq_3_7", Dur = 896}
, 
{Instru = "eq_3_8", Dur = 768}
, 
{Instru = "eq_3_9", Dur = 640}
, 
{Instru = "eq_3_10", Dur = 512}
}
}
, 
{Type = "ds_mixdrk_1", 
Category = {5}
, 
Materials = {
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_2_2_3", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 2, Reward = 111, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 120}
, 
{Instru = "eq_1_5", Dur = 108}
, 
{Instru = "eq_1_6", Dur = 96}
, 
{Instru = "eq_1_7", Dur = 84}
, 
{Instru = "eq_1_8", Dur = 72}
, 
{Instru = "eq_1_9", Dur = 60}
, 
{Instru = "eq_1_10", Dur = 48}
}
}
, 
{Type = "ds_mixdrk_2", 
Category = {5}
, 
Materials = {
{Material = "it_2_2_3", Amount = 1}
, 
{Material = "it_2_3_2", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 1, Reward = 38, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 70}
, 
{Instru = "eq_1_5", Dur = 63}
, 
{Instru = "eq_1_6", Dur = 56}
, 
{Instru = "eq_1_7", Dur = 49}
, 
{Instru = "eq_1_8", Dur = 42}
, 
{Instru = "eq_1_9", Dur = 35}
, 
{Instru = "eq_1_10", Dur = 28}
}
}
, 
{Type = "ds_mixdrk_3", 
Category = {5}
, 
Materials = {
{Material = "it_2_2_3", Amount = 1}
, 
{Material = "it_1_2_1_2", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 2, Reward = 105, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 70}
, 
{Instru = "eq_1_5", Dur = 63}
, 
{Instru = "eq_1_6", Dur = 56}
, 
{Instru = "eq_1_7", Dur = 49}
, 
{Instru = "eq_1_8", Dur = 42}
, 
{Instru = "eq_1_9", Dur = 35}
, 
{Instru = "eq_1_10", Dur = 28}
}
}
, 
{Type = "ds_mixdrk_4", 
Category = {5}
, 
Materials = {
{Material = "it_2_2_3", Amount = 1}
, 
{Material = "it_2_3_2", Amount = 1}
, 
{Material = "it_4_2_3", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 3, Reward = 59, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 150}
, 
{Instru = "eq_1_5", Dur = 135}
, 
{Instru = "eq_1_6", Dur = 120}
, 
{Instru = "eq_1_7", Dur = 105}
, 
{Instru = "eq_1_8", Dur = 90}
, 
{Instru = "eq_1_9", Dur = 75}
, 
{Instru = "eq_1_10", Dur = 60}
}
}
, 
{Type = "ds_mixdrk_5", 
Category = {5}
, 
Materials = {
{Material = "it_2_2_3", Amount = 1}
, 
{Material = "it_2_3_2", Amount = 1}
, 
{Material = "it_1_2_1_2", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 2, Reward = 119, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 80}
, 
{Instru = "eq_1_5", Dur = 72}
, 
{Instru = "eq_1_6", Dur = 64}
, 
{Instru = "eq_1_7", Dur = 56}
, 
{Instru = "eq_1_8", Dur = 48}
, 
{Instru = "eq_1_9", Dur = 40}
, 
{Instru = "eq_1_10", Dur = 32}
}
}
, 
{Type = "ds_mixdrk_6", 
Category = {5}
, 
Materials = {
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 1, Reward = 38, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 70}
, 
{Instru = "eq_1_5", Dur = 63}
, 
{Instru = "eq_1_6", Dur = 56}
, 
{Instru = "eq_1_7", Dur = 49}
, 
{Instru = "eq_1_8", Dur = 42}
, 
{Instru = "eq_1_9", Dur = 35}
, 
{Instru = "eq_1_10", Dur = 28}
}
}
, 
{Type = "ds_mixdrk_7", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_4_2_1", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 1, Reward = 22, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 70}
, 
{Instru = "eq_1_5", Dur = 63}
, 
{Instru = "eq_1_6", Dur = 56}
, 
{Instru = "eq_1_7", Dur = 49}
, 
{Instru = "eq_1_8", Dur = 42}
, 
{Instru = "eq_1_9", Dur = 35}
, 
{Instru = "eq_1_10", Dur = 28}
}
}
, 
{Type = "ds_mixdrk_8", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_8", Amount = 1}
, 
{Material = "it_2_3_1", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 9, Reward = 140, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 480}
, 
{Instru = "eq_1_5", Dur = 432}
, 
{Instru = "eq_1_6", Dur = 384}
, 
{Instru = "eq_1_7", Dur = 336}
, 
{Instru = "eq_1_8", Dur = 288}
, 
{Instru = "eq_1_9", Dur = 240}
, 
{Instru = "eq_1_10", Dur = 192}
}
}
, 
{Type = "ds_mixdrk_9", 
Category = {5}
, 
Materials = {
{Material = "it_4_2_7", Amount = 1}
, 
{Material = "it_2_3_2", Amount = 1}
}
, BookOrder = 4005, BookSubOrder = 9, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 22, Reward = 305, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 1280}
, 
{Instru = "eq_1_5", Dur = 1152}
, 
{Instru = "eq_1_6", Dur = 1024}
, 
{Instru = "eq_1_7", Dur = 896}
, 
{Instru = "eq_1_8", Dur = 768}
, 
{Instru = "eq_1_9", Dur = 640}
, 
{Instru = "eq_1_10", Dur = 512}
}
}
, 
{Type = "ds_dst_1", 
Category = {5}
, 
Materials = {
{Material = "it_4_2_5", Amount = 1}
, 
{Material = "it_4_1_1", Amount = 1}
}
, BookOrder = 4006, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 3, Reward = 86, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 160}
, 
{Instru = "eq_1_5", Dur = 144}
, 
{Instru = "eq_1_6", Dur = 128}
, 
{Instru = "eq_1_7", Dur = 112}
, 
{Instru = "eq_1_8", Dur = 96}
, 
{Instru = "eq_1_9", Dur = 80}
, 
{Instru = "eq_1_10", Dur = 64}
}
}
, 
{Type = "ds_friedmt_1", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_1", Amount = 1}
}
, BookOrder = 4010, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 8, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 10}
, 
{Instru = "eq_4_5", Dur = 9}
, 
{Instru = "eq_4_6", Dur = 8}
, 
{Instru = "eq_4_7", Dur = 7}
, 
{Instru = "eq_4_8", Dur = 6}
, 
{Instru = "eq_4_9", Dur = 5}
, 
{Instru = "eq_4_10", Dur = 5}
}
}
, 
{Type = "ds_friedmt_3", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_2", Amount = 1}
}
, BookOrder = 4010, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 8, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 20}
, 
{Instru = "eq_4_5", Dur = 18}
, 
{Instru = "eq_4_6", Dur = 16}
, 
{Instru = "eq_4_7", Dur = 14}
, 
{Instru = "eq_4_8", Dur = 12}
, 
{Instru = "eq_4_9", Dur = 10}
, 
{Instru = "eq_4_10", Dur = 8}
}
}
, 
{Type = "ds_friedmt_4", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_3", Amount = 1}
}
, BookOrder = 4010, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 11, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 40}
, 
{Instru = "eq_4_5", Dur = 36}
, 
{Instru = "eq_4_6", Dur = 32}
, 
{Instru = "eq_4_7", Dur = 28}
, 
{Instru = "eq_4_8", Dur = 24}
, 
{Instru = "eq_4_9", Dur = 20}
, 
{Instru = "eq_4_10", Dur = 16}
}
}
, 
{Type = "ds_friedmt_5", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_6", Amount = 1}
, 
{Material = "it_1_2_3", Amount = 1}
}
, BookOrder = 4010, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 8, Reward = 167, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 480}
, 
{Instru = "eq_4_5", Dur = 432}
, 
{Instru = "eq_4_6", Dur = 384}
, 
{Instru = "eq_4_7", Dur = 336}
, 
{Instru = "eq_4_8", Dur = 288}
, 
{Instru = "eq_4_9", Dur = 240}
, 
{Instru = "eq_4_10", Dur = 192}
}
}
, 
{Type = "ds_friedsf_1", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_2", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 8, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 20}
, 
{Instru = "eq_4_5", Dur = 18}
, 
{Instru = "eq_4_6", Dur = 16}
, 
{Instru = "eq_4_7", Dur = 14}
, 
{Instru = "eq_4_8", Dur = 12}
, 
{Instru = "eq_4_9", Dur = 10}
, 
{Instru = "eq_4_10", Dur = 8}
}
}
, 
{Type = "ds_friedsf_2", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_2", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 8, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 20}
, 
{Instru = "eq_4_5", Dur = 18}
, 
{Instru = "eq_4_6", Dur = 16}
, 
{Instru = "eq_4_7", Dur = 14}
, 
{Instru = "eq_4_8", Dur = 12}
, 
{Instru = "eq_4_9", Dur = 10}
, 
{Instru = "eq_4_10", Dur = 8}
}
}
, 
{Type = "ds_friedsf_3", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_4", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 2, Reward = 24, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 80}
, 
{Instru = "eq_4_5", Dur = 72}
, 
{Instru = "eq_4_6", Dur = 64}
, 
{Instru = "eq_4_7", Dur = 56}
, 
{Instru = "eq_4_8", Dur = 48}
, 
{Instru = "eq_4_9", Dur = 40}
, 
{Instru = "eq_4_10", Dur = 32}
}
}
, 
{Type = "ds_grillsf_1", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_3", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 11, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 40}
, 
{Instru = "eq_2_5", Dur = 36}
, 
{Instru = "eq_2_6", Dur = 32}
, 
{Instru = "eq_2_7", Dur = 28}
, 
{Instru = "eq_2_8", Dur = 24}
, 
{Instru = "eq_2_9", Dur = 20}
, 
{Instru = "eq_2_10", Dur = 16}
}
}
, 
{Type = "ds_grillsf_2", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_3", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 14, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 40}
, 
{Instru = "eq_2_5", Dur = 36}
, 
{Instru = "eq_2_6", Dur = 32}
, 
{Instru = "eq_2_7", Dur = 28}
, 
{Instru = "eq_2_8", Dur = 24}
, 
{Instru = "eq_2_9", Dur = 20}
, 
{Instru = "eq_2_10", Dur = 16}
}
}
, 
{Type = "ds_grillsf_3", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_5", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 8, Reward = 24, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 80}
, 
{Instru = "eq_2_5", Dur = 72}
, 
{Instru = "eq_2_6", Dur = 64}
, 
{Instru = "eq_2_7", Dur = 56}
, 
{Instru = "eq_2_8", Dur = 48}
, 
{Instru = "eq_2_9", Dur = 40}
, 
{Instru = "eq_2_10", Dur = 32}
}
}
, 
{Type = "ds_grillsf_4", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_5", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 3, Reward = 46, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 160}
, 
{Instru = "eq_2_5", Dur = 144}
, 
{Instru = "eq_2_6", Dur = 128}
, 
{Instru = "eq_2_7", Dur = 112}
, 
{Instru = "eq_2_8", Dur = 96}
, 
{Instru = "eq_2_9", Dur = 80}
, 
{Instru = "eq_2_10", Dur = 64}
}
}
, 
{Type = "ds_grillsf_5", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_7", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 84, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 640}
, 
{Instru = "eq_2_5", Dur = 576}
, 
{Instru = "eq_2_6", Dur = 512}
, 
{Instru = "eq_2_7", Dur = 448}
, 
{Instru = "eq_2_8", Dur = 384}
, 
{Instru = "eq_2_9", Dur = 320}
, 
{Instru = "eq_2_10", Dur = 256}
}
}
, 
{Type = "ds_grillsf_6", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_6", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 9, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 46, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 320}
, 
{Instru = "eq_2_5", Dur = 288}
, 
{Instru = "eq_2_6", Dur = 256}
, 
{Instru = "eq_2_7", Dur = 224}
, 
{Instru = "eq_2_8", Dur = 192}
, 
{Instru = "eq_2_9", Dur = 160}
, 
{Instru = "eq_2_10", Dur = 128}
}
}
, 
{Type = "ds_chopfs_1", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_6", Amount = 1}
}
, BookOrder = 4013, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 84, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 320}
, 
{Instru = "eq_1_5", Dur = 288}
, 
{Instru = "eq_1_6", Dur = 256}
, 
{Instru = "eq_1_7", Dur = 224}
, 
{Instru = "eq_1_8", Dur = 192}
, 
{Instru = "eq_1_9", Dur = 160}
, 
{Instru = "eq_1_10", Dur = 128}
}
}
, 
{Type = "ds_fd_1", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_5", Amount = 1}
, 
{Material = "it_3_2_1", Amount = 1}
, 
{Material = "it_2_3_2", Amount = 1}
}
, BookOrder = 4014, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 51, InRandom = 0, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "it_2_3_1_2", Dur = 60}
}
}
, 
{Type = "ds_fd_2", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_5", Amount = 1}
, 
{Material = "it_2_3_1", Amount = 1}
}
, BookOrder = 4014, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 38, InRandom = 0, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "it_2_3_1_2", Dur = 60}
}
}
, 
{Type = "ds_fd_3", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_5", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
}
, BookOrder = 4014, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 54, InRandom = 0, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "it_2_3_1_2", Dur = 60}
}
}
, 
{Type = "ds_fd_4", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_5", Amount = 1}
, 
{Material = "it_2_3_1", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
}
, BookOrder = 4014, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 51, InRandom = 0, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "it_2_3_1_2", Dur = 60}
}
}
, 
{Type = "ds_fd_5", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_1", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 2, Reward = 57, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 130}
, 
{Instru = "eq_5_5", Dur = 117}
, 
{Instru = "eq_5_6", Dur = 104}
, 
{Instru = "eq_5_7", Dur = 91}
, 
{Instru = "eq_5_8", Dur = 78}
, 
{Instru = "eq_5_9", Dur = 65}
, 
{Instru = "eq_5_10", Dur = 52}
}
}
, 
{Type = "ds_fd_6", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_2", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 2, Reward = 41, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 150}
, 
{Instru = "eq_5_5", Dur = 135}
, 
{Instru = "eq_5_6", Dur = 120}
, 
{Instru = "eq_5_7", Dur = 105}
, 
{Instru = "eq_5_8", Dur = 90}
, 
{Instru = "eq_5_9", Dur = 75}
, 
{Instru = "eq_5_10", Dur = 60}
}
}
, 
{Type = "ds_fd_7", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_4", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 2, Reward = 57, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 640}
, 
{Instru = "eq_5_5", Dur = 576}
, 
{Instru = "eq_5_6", Dur = 512}
, 
{Instru = "eq_5_7", Dur = 448}
, 
{Instru = "eq_5_8", Dur = 384}
, 
{Instru = "eq_5_9", Dur = 320}
, 
{Instru = "eq_5_10", Dur = 256}
}
}
, 
{Type = "ds_fd_8", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_5", Amount = 1}
, 
{Material = "ds_friedmt_4", Amount = 1}
, 
{Material = "it_1_1_1", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 89, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 10}
, 
{Instru = "eq_1_5", Dur = 9}
, 
{Instru = "eq_1_6", Dur = 8}
, 
{Instru = "eq_1_7", Dur = 7}
, 
{Instru = "eq_1_8", Dur = 6}
, 
{Instru = "eq_1_9", Dur = 5}
, 
{Instru = "eq_1_10", Dur = 4}
}
}
, 
{Type = "ds_fd_9", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_5", Amount = 1}
, 
{Material = "ds_friedmt_4", Amount = 1}
, 
{Material = "it_1_1_1", Amount = 1}
, 
{Material = "ds_friedmt_3", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 92, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 10}
, 
{Instru = "eq_1_5", Dur = 9}
, 
{Instru = "eq_1_6", Dur = 8}
, 
{Instru = "eq_1_7", Dur = 7}
, 
{Instru = "eq_1_8", Dur = 6}
, 
{Instru = "eq_1_9", Dur = 5}
, 
{Instru = "eq_1_10", Dur = 4}
}
}
, 
{Type = "ds_fd_10", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_5", Amount = 1}
, 
{Material = "ds_friedmt_3", Amount = 1}
, 
{Material = "ds_friedmt_1", Amount = 1}
, 
{Material = "it_2_3_4", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 130, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 50}
, 
{Instru = "eq_1_5", Dur = 45}
, 
{Instru = "eq_1_6", Dur = 40}
, 
{Instru = "eq_1_7", Dur = 35}
, 
{Instru = "eq_1_8", Dur = 30}
, 
{Instru = "eq_1_9", Dur = 25}
, 
{Instru = "eq_1_10", Dur = 20}
}
}
, 
{Type = "ds_fd_11", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_7", Amount = 1}
, 
{Material = "ds_grillsf_7", Amount = 1}
, 
{Material = "it_1_1_1", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 240, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 10}
, 
{Instru = "eq_1_5", Dur = 9}
, 
{Instru = "eq_1_6", Dur = 8}
, 
{Instru = "eq_1_7", Dur = 7}
, 
{Instru = "eq_1_8", Dur = 6}
, 
{Instru = "eq_1_9", Dur = 5}
, 
{Instru = "eq_1_10", Dur = 4}
}
}
, 
{Type = "ds_fd_12", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_6", Amount = 1}
, 
{Material = "ds_friedmt_2", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 54, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 10}
, 
{Instru = "eq_1_5", Dur = 9}
, 
{Instru = "eq_1_6", Dur = 8}
, 
{Instru = "eq_1_7", Dur = 7}
, 
{Instru = "eq_1_8", Dur = 6}
, 
{Instru = "eq_1_9", Dur = 5}
, 
{Instru = "eq_1_10", Dur = 4}
}
}
, 
{Type = "ds_fd_13", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_6", Amount = 1}
, 
{Material = "ds_friedmt_2", Amount = 1}
, 
{Material = "it_2_3_4", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 9, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 100, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_fd_14", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_3", Amount = 1}
, 
{Material = "it_4_1_2", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 10, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 68, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 340}
, 
{Instru = "eq_5_5", Dur = 306}
, 
{Instru = "eq_5_6", Dur = 272}
, 
{Instru = "eq_5_7", Dur = 238}
, 
{Instru = "eq_5_8", Dur = 204}
, 
{Instru = "eq_5_9", Dur = 170}
, 
{Instru = "eq_5_10", Dur = 136}
}
}
, 
{Type = "ds_fd_15", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_3", Amount = 1}
, 
{Material = "it_4_1_3", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 11, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 70, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 380}
, 
{Instru = "eq_5_5", Dur = 342}
, 
{Instru = "eq_5_6", Dur = 304}
, 
{Instru = "eq_5_7", Dur = 266}
, 
{Instru = "eq_5_8", Dur = 228}
, 
{Instru = "eq_5_9", Dur = 190}
, 
{Instru = "eq_5_10", Dur = 152}
}
}
, 
{Type = "ds_fd_16", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_3", Amount = 1}
, 
{Material = "it_3_2_1", Amount = 1}
, 
{Material = "it_3_1_2", Amount = 1}
, 
{Material = "it_1_2_3", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 12, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 97, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 380}
, 
{Instru = "eq_5_5", Dur = 342}
, 
{Instru = "eq_5_6", Dur = 304}
, 
{Instru = "eq_5_7", Dur = 266}
, 
{Instru = "eq_5_8", Dur = 228}
, 
{Instru = "eq_5_9", Dur = 190}
, 
{Instru = "eq_5_10", Dur = 152}
}
}
, 
{Type = "ds_fd_17", 
Category = {5}
, 
Materials = {
{Material = "ds_fd_4", Amount = 1}
, 
{Material = "it_1_2_3", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 13, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 78, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 380}
, 
{Instru = "eq_5_5", Dur = 342}
, 
{Instru = "eq_5_6", Dur = 304}
, 
{Instru = "eq_5_7", Dur = 266}
, 
{Instru = "eq_5_8", Dur = 228}
, 
{Instru = "eq_5_9", Dur = 190}
, 
{Instru = "eq_5_10", Dur = 152}
}
}
, 
{Type = "ds_fd_18", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_7", Amount = 1}
, 
{Material = "it_4_1_1", Amount = 1}
, 
{Material = "it_4_1_2", Amount = 1}
, 
{Material = "it_4_2_1", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 14, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 130, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 640}
, 
{Instru = "eq_5_5", Dur = 576}
, 
{Instru = "eq_5_6", Dur = 512}
, 
{Instru = "eq_5_7", Dur = 448}
, 
{Instru = "eq_5_8", Dur = 384}
, 
{Instru = "eq_5_9", Dur = 320}
, 
{Instru = "eq_5_10", Dur = 256}
}
}
, 
{Type = "ds_fd_19", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_5", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 15, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 7, Reward = 132, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 840}
, 
{Instru = "eq_5_5", Dur = 756}
, 
{Instru = "eq_5_6", Dur = 672}
, 
{Instru = "eq_5_7", Dur = 588}
, 
{Instru = "eq_5_8", Dur = 504}
, 
{Instru = "eq_5_9", Dur = 420}
, 
{Instru = "eq_5_10", Dur = 336}
}
}
, 
{Type = "ds_fd_20", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_5", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
, 
{Material = "it_3_2_1", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_1_2_7", Amount = 1}
}
, BookOrder = 4015, BookSubOrder = 16, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 359, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 660}
, 
{Instru = "eq_5_5", Dur = 594}
, 
{Instru = "eq_5_6", Dur = 528}
, 
{Instru = "eq_5_7", Dur = 462}
, 
{Instru = "eq_5_8", Dur = 396}
, 
{Instru = "eq_5_9", Dur = 330}
, 
{Instru = "eq_5_10", Dur = 264}
}
}
, 
{Type = "ds_fd_21", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_4", Amount = 1}
, 
{Material = "it_1_2_3", Amount = 1}
}
, BookOrder = 4016, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 38, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 340}
, 
{Instru = "eq_4_5", Dur = 306}
, 
{Instru = "eq_4_6", Dur = 272}
, 
{Instru = "eq_4_7", Dur = 238}
, 
{Instru = "eq_4_8", Dur = 204}
, 
{Instru = "eq_4_9", Dur = 170}
, 
{Instru = "eq_4_10", Dur = 136}
}
}
, 
{Type = "ds_fd_23", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_4", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
}
, BookOrder = 4016, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 10, Reward = 41, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 640}
, 
{Instru = "eq_4_5", Dur = 576}
, 
{Instru = "eq_4_6", Dur = 512}
, 
{Instru = "eq_4_7", Dur = 448}
, 
{Instru = "eq_4_8", Dur = 384}
, 
{Instru = "eq_4_9", Dur = 320}
, 
{Instru = "eq_4_10", Dur = 256}
}
}
, 
{Type = "ds_sal_1", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_9", Amount = 1}
, 
{Material = "it_1_1_1", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
}
, BookOrder = 4017, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 251, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 1280}
, 
{Instru = "eq_1_5", Dur = 1152}
, 
{Instru = "eq_1_6", Dur = 1024}
, 
{Instru = "eq_1_7", Dur = 896}
, 
{Instru = "eq_1_8", Dur = 768}
, 
{Instru = "eq_1_9", Dur = 640}
, 
{Instru = "eq_1_10", Dur = 512}
}
}
, 
{Type = "ds_chopfru_1", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_8", Amount = 1}
}
, BookOrder = 4001, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 10, Reward = 119, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 640}
, 
{Instru = "eq_1_5", Dur = 576}
, 
{Instru = "eq_1_6", Dur = 512}
, 
{Instru = "eq_1_7", Dur = 448}
, 
{Instru = "eq_1_8", Dur = 384}
, 
{Instru = "eq_1_9", Dur = 320}
, 
{Instru = "eq_1_10", Dur = 256}
}
}
, 
{Type = "ds_grillsf_7", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_8", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 10, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 157, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 1280}
, 
{Instru = "eq_1_5", Dur = 1152}
, 
{Instru = "eq_1_6", Dur = 1024}
, 
{Instru = "eq_1_7", Dur = 896}
, 
{Instru = "eq_1_8", Dur = 768}
, 
{Instru = "eq_1_9", Dur = 640}
, 
{Instru = "eq_1_10", Dur = 512}
}
}
, 
{Type = "ds_juice_9", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_9", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 224, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 1280}
, 
{Instru = "eq_3_5", Dur = 1152}
, 
{Instru = "eq_3_6", Dur = 1024}
, 
{Instru = "eq_3_7", Dur = 896}
, 
{Instru = "eq_3_8", Dur = 768}
, 
{Instru = "eq_3_9", Dur = 640}
, 
{Instru = "eq_3_10", Dur = 512}
}
}
, 
{Type = "ds_friedsf_4", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_8", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 11, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 421, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_friedve_5", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_10", Amount = 1}
}
, BookOrder = 4002, BookSubOrder = 9, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 448, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_grillmt_12", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_10", Amount = 1}
, 
{Material = "it_4_1_3", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 13, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 432, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 1280}
, 
{Instru = "eq_2_5", Dur = 1152}
, 
{Instru = "eq_2_6", Dur = 1024}
, 
{Instru = "eq_2_7", Dur = 896}
, 
{Instru = "eq_2_8", Dur = 768}
, 
{Instru = "eq_2_9", Dur = 640}
, 
{Instru = "eq_2_10", Dur = 512}
}
}
, 
{Type = "ds_flb_1", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_5", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_1_2_3", Amount = 1}
}
, BookOrder = 4011, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 10, Reward = 65, InRandom = 0, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_flb_2", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_4", Amount = 1}
, 
{Material = "it_1_1_5", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_1_2_3", Amount = 1}
}
, BookOrder = 4011, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 10, Reward = 78, InRandom = 0, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 80}
, 
{Instru = "eq_1_5", Dur = 72}
, 
{Instru = "eq_1_6", Dur = 64}
, 
{Instru = "eq_1_7", Dur = 56}
, 
{Instru = "eq_1_8", Dur = 48}
, 
{Instru = "eq_1_9", Dur = 40}
, 
{Instru = "eq_1_10", Dur = 32}
}
}
, 
{Type = "ds_e3juice_10", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_2", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 9, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 1, Reward = 8, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 10}
, 
{Instru = "eq_3_5", Dur = 9}
, 
{Instru = "eq_3_6", Dur = 8}
, 
{Instru = "eq_3_7", Dur = 7}
, 
{Instru = "eq_3_8", Dur = 6}
, 
{Instru = "eq_3_9", Dur = 5}
, 
{Instru = "eq_3_10", Dur = 4}
}
}
, 
{Type = "ds_e1hotdrk_1", 
Category = {5}
, 
Materials = {
{Material = "it_4_2_5", Amount = 1}
, 
{Material = "it_2_3_2", Amount = 1}
}
, BookOrder = 4020, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 8, Reward = 95, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 340}
, 
{Instru = "eq_1_5", Dur = 306}
, 
{Instru = "eq_1_6", Dur = 272}
, 
{Instru = "eq_1_7", Dur = 238}
, 
{Instru = "eq_1_8", Dur = 204}
, 
{Instru = "eq_1_9", Dur = 170}
, 
{Instru = "eq_1_10", Dur = 136}
}
}
, 
{Type = "ds_e1cockt_1", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_2", Amount = 1}
, 
{Material = "it_7_2_3", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 2, Reward = 30, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 20}
, 
{Instru = "eq_1_5", Dur = 18}
, 
{Instru = "eq_1_6", Dur = 16}
, 
{Instru = "eq_1_7", Dur = 14}
, 
{Instru = "eq_1_8", Dur = 12}
, 
{Instru = "eq_1_9", Dur = 10}
, 
{Instru = "eq_1_10", Dur = 8}
}
}
, 
{Type = "ds_e1cockt_2", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_5", Amount = 1}
, 
{Material = "it_7_2_4", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 4, Reward = 59, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_e1cockt_3", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_7", Amount = 1}
, 
{Material = "ds_juice_1", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 4, Reward = 95, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_e1cockt_4", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_5", Amount = 1}
, 
{Material = "it_2_1_7", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 4, Reward = 78, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_e1cockt_5", 
Category = {5}
, 
Materials = {
{Material = "ds_e3juice_10", Amount = 1}
, 
{Material = "it_1_2_1", Amount = 1}
, 
{Material = "it_7_1_5", Amount = 1}
, 
{Material = "it_5_2_5", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 4, Reward = 84, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_e1cockt_6", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_6", Amount = 1}
, 
{Material = "ds_juice_2", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 4, Reward = 57, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_e6cockt_7", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_1", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 17, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 97, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 70}
, 
{Instru = "eq_6_5", Dur = 63}
, 
{Instru = "eq_6_6", Dur = 56}
, 
{Instru = "eq_6_7", Dur = 49}
, 
{Instru = "eq_6_8", Dur = 42}
, 
{Instru = "eq_6_9", Dur = 35}
, 
{Instru = "eq_6_10", Dur = 28}
}
}
, 
{Type = "ds_e1cockt_8", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_8", Amount = 1}
, 
{Material = "it_7_2_2", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 5, Reward = 138, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 70}
, 
{Instru = "eq_1_5", Dur = 63}
, 
{Instru = "eq_1_6", Dur = 56}
, 
{Instru = "eq_1_7", Dur = 49}
, 
{Instru = "eq_1_8", Dur = 42}
, 
{Instru = "eq_1_9", Dur = 35}
, 
{Instru = "eq_1_10", Dur = 28}
}
}
, 
{Type = "ds_e1cockt_9", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_7", Amount = 1}
, 
{Material = "it_7_2_1", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 4, Reward = 73, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 40}
, 
{Instru = "eq_1_5", Dur = 36}
, 
{Instru = "eq_1_6", Dur = 32}
, 
{Instru = "eq_1_7", Dur = 28}
, 
{Instru = "eq_1_8", Dur = 24}
, 
{Instru = "eq_1_9", Dur = 20}
, 
{Instru = "eq_1_10", Dur = 16}
}
}
, 
{Type = "ds_e4friedmt_6", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_8", Amount = 1}
, 
{Material = "it_7_1_3", Amount = 1}
}
, BookOrder = 4010, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 130, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 70}
, 
{Instru = "eq_4_5", Dur = 63}
, 
{Instru = "eq_4_6", Dur = 56}
, 
{Instru = "eq_4_7", Dur = 49}
, 
{Instru = "eq_4_8", Dur = 42}
, 
{Instru = "eq_4_9", Dur = 35}
, 
{Instru = "eq_4_10", Dur = 28}
}
}
, 
{Type = "ds_e4sf_12", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_3", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 12, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 130, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 70}
, 
{Instru = "eq_4_5", Dur = 63}
, 
{Instru = "eq_4_6", Dur = 56}
, 
{Instru = "eq_4_7", Dur = 49}
, 
{Instru = "eq_4_8", Dur = 42}
, 
{Instru = "eq_4_9", Dur = 35}
, 
{Instru = "eq_4_10", Dur = 28}
}
}
, 
{Type = "ds_e1cockt_10", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_5", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_7_2_5", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 9, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 5, Reward = 111, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 70}
, 
{Instru = "eq_1_5", Dur = 63}
, 
{Instru = "eq_1_6", Dur = 56}
, 
{Instru = "eq_1_7", Dur = 49}
, 
{Instru = "eq_1_8", Dur = 42}
, 
{Instru = "eq_1_9", Dur = 35}
, 
{Instru = "eq_1_10", Dur = 28}
}
}
, 
{Type = "ds_e1cockt_11", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_6", Amount = 1}
, 
{Material = "it_7_2_6", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 10, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 5, Reward = 197, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 80}
, 
{Instru = "eq_1_5", Dur = 72}
, 
{Instru = "eq_1_6", Dur = 64}
, 
{Instru = "eq_1_7", Dur = 56}
, 
{Instru = "eq_1_8", Dur = 48}
, 
{Instru = "eq_1_9", Dur = 40}
, 
{Instru = "eq_1_10", Dur = 32}
}
}
, 
{Type = "ds_e1cockt_12", 
Category = {5}
, 
Materials = {
{Material = "it_4_2_5", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_7_1_5", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 11, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 5, Reward = 186, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 80}
, 
{Instru = "eq_1_5", Dur = 72}
, 
{Instru = "eq_1_6", Dur = 64}
, 
{Instru = "eq_1_7", Dur = 56}
, 
{Instru = "eq_1_8", Dur = 48}
, 
{Instru = "eq_1_9", Dur = 40}
, 
{Instru = "eq_1_10", Dur = 32}
}
}
, 
{Type = "ds_e1cockt_13", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_7", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_2_1_6", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 12, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 5, Reward = 176, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 80}
, 
{Instru = "eq_1_5", Dur = 72}
, 
{Instru = "eq_1_6", Dur = 64}
, 
{Instru = "eq_1_7", Dur = 56}
, 
{Instru = "eq_1_8", Dur = 48}
, 
{Instru = "eq_1_9", Dur = 40}
, 
{Instru = "eq_1_10", Dur = 32}
}
}
, 
{Type = "ds_e1cockt_14", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_7", Amount = 1}
, 
{Material = "it_2_2_5", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_4_2_1", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 13, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 5, Reward = 173, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 80}
, 
{Instru = "eq_1_5", Dur = 72}
, 
{Instru = "eq_1_6", Dur = 64}
, 
{Instru = "eq_1_7", Dur = 56}
, 
{Instru = "eq_1_8", Dur = 48}
, 
{Instru = "eq_1_9", Dur = 40}
, 
{Instru = "eq_1_10", Dur = 32}
}
}
, 
{Type = "ds_e4friedmt_7", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_4", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
}
, BookOrder = 4010, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 6, Reward = 203, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 80}
, 
{Instru = "eq_4_5", Dur = 72}
, 
{Instru = "eq_4_6", Dur = 64}
, 
{Instru = "eq_4_7", Dur = 56}
, 
{Instru = "eq_4_8", Dur = 48}
, 
{Instru = "eq_4_9", Dur = 40}
, 
{Instru = "eq_4_10", Dur = 32}
}
}
, 
{Type = "ds_e1icytre_1", 
Category = {5}
, 
Materials = {
{Material = "it_1_2_1_1", Amount = 1}
, 
{Material = "it_3_2_1", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_4_2_5", Amount = 1}
}
, BookOrder = 4021, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 6, Reward = 221, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 80}
, 
{Instru = "eq_1_5", Dur = 72}
, 
{Instru = "eq_1_6", Dur = 64}
, 
{Instru = "eq_1_7", Dur = 56}
, 
{Instru = "eq_1_8", Dur = 48}
, 
{Instru = "eq_1_9", Dur = 40}
, 
{Instru = "eq_1_10", Dur = 32}
}
}
, 
{Type = "ds_e6soup_1", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_4", Amount = 1}
, 
{Material = "it_5_2_4", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
}
, BookOrder = 4018, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 197, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 80}
, 
{Instru = "eq_6_5", Dur = 72}
, 
{Instru = "eq_6_6", Dur = 64}
, 
{Instru = "eq_6_7", Dur = 56}
, 
{Instru = "eq_6_8", Dur = 48}
, 
{Instru = "eq_6_9", Dur = 40}
, 
{Instru = "eq_6_10", Dur = 32}
}
}
, 
{Type = "ds_e1icytre_2", 
Category = {5}
, 
Materials = {
{Material = "it_2_1_9", Amount = 1}
, 
{Material = "ds_mixdrk_6", Amount = 1}
}
, BookOrder = 4021, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 6, Reward = 197, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 120}
, 
{Instru = "eq_1_5", Dur = 108}
, 
{Instru = "eq_1_6", Dur = 96}
, 
{Instru = "eq_1_7", Dur = 84}
, 
{Instru = "eq_1_8", Dur = 72}
, 
{Instru = "eq_1_9", Dur = 60}
, 
{Instru = "eq_1_10", Dur = 48}
}
}
, 
{Type = "ds_e1cockt_15", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_9", Amount = 1}
, 
{Material = "it_7_2_4", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 14, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 7, Reward = 278, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 120}
, 
{Instru = "eq_1_5", Dur = 108}
, 
{Instru = "eq_1_6", Dur = 96}
, 
{Instru = "eq_1_7", Dur = 84}
, 
{Instru = "eq_1_8", Dur = 72}
, 
{Instru = "eq_1_9", Dur = 60}
, 
{Instru = "eq_1_10", Dur = 48}
}
}
, 
{Type = "ds_e4sf_13", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_7", Amount = 1}
, 
{Material = "it_5_1_8", Amount = 1}
, 
{Material = "it_4_2_1", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 13, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 7, Reward = 230, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 120}
, 
{Instru = "eq_4_5", Dur = 108}
, 
{Instru = "eq_4_6", Dur = 96}
, 
{Instru = "eq_4_7", Dur = 84}
, 
{Instru = "eq_4_8", Dur = 72}
, 
{Instru = "eq_4_9", Dur = 60}
, 
{Instru = "eq_4_10", Dur = 48}
}
}
, 
{Type = "ds_e1cockt_16", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_9", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
, 
{Material = "ds_juice_1", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 15, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 7, Reward = 267, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 120}
, 
{Instru = "eq_1_5", Dur = 108}
, 
{Instru = "eq_1_6", Dur = 96}
, 
{Instru = "eq_1_7", Dur = 84}
, 
{Instru = "eq_1_8", Dur = 72}
, 
{Instru = "eq_1_9", Dur = 60}
, 
{Instru = "eq_1_10", Dur = 48}
}
}
, 
{Type = "ds_e6stewmt_1", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_9", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_7_1_3", Amount = 1}
}
, BookOrder = 4022, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 383, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 380}
, 
{Instru = "eq_6_5", Dur = 342}
, 
{Instru = "eq_6_6", Dur = 304}
, 
{Instru = "eq_6_7", Dur = 266}
, 
{Instru = "eq_6_8", Dur = 228}
, 
{Instru = "eq_6_9", Dur = 190}
, 
{Instru = "eq_6_10", Dur = 152}
}
}
, 
{Type = "ds_e4sf_14", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_9", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
, 
{Material = "it_1_1_4", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 14, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 10, Reward = 402, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 380}
, 
{Instru = "eq_4_5", Dur = 342}
, 
{Instru = "eq_4_6", Dur = 304}
, 
{Instru = "eq_4_7", Dur = 266}
, 
{Instru = "eq_4_8", Dur = 228}
, 
{Instru = "eq_4_9", Dur = 190}
, 
{Instru = "eq_4_10", Dur = 152}
}
}
, 
{Type = "ds_e6dst_2", 
Category = {5}
, 
Materials = {
{Material = "it_1_2_7", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_1_2_1_2", Amount = 1}
, 
{Material = "it_4_1_3", Amount = 1}
}
, BookOrder = 4006, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 10, Reward = 462, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 380}
, 
{Instru = "eq_6_5", Dur = 342}
, 
{Instru = "eq_6_6", Dur = 304}
, 
{Instru = "eq_6_7", Dur = 266}
, 
{Instru = "eq_6_8", Dur = 228}
, 
{Instru = "eq_6_9", Dur = 190}
, 
{Instru = "eq_6_10", Dur = 152}
}
}
, 
{Type = "ds_e5mt_1", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_7", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
}
, BookOrder = 4023, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 13, Reward = 435, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 640}
, 
{Instru = "eq_5_5", Dur = 576}
, 
{Instru = "eq_5_6", Dur = 512}
, 
{Instru = "eq_5_7", Dur = 448}
, 
{Instru = "eq_5_8", Dur = 384}
, 
{Instru = "eq_5_9", Dur = 320}
, 
{Instru = "eq_5_10", Dur = 256}
}
}
, 
{Type = "ds_e1cockt_17", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_10", Amount = 1}
, 
{Material = "ds_juice_1", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 16, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 13, Reward = 464, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 640}
, 
{Instru = "eq_1_5", Dur = 576}
, 
{Instru = "eq_1_6", Dur = 512}
, 
{Instru = "eq_1_7", Dur = 448}
, 
{Instru = "eq_1_8", Dur = 384}
, 
{Instru = "eq_1_9", Dur = 320}
, 
{Instru = "eq_1_10", Dur = 256}
}
}
, 
{Type = "ds_e1cockt_18", 
Category = {5}
, 
Materials = {
{Material = "it_7_2_8", Amount = 1}
, 
{Material = "it_7_1_5", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 17, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 13, Reward = 551, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 640}
, 
{Instru = "eq_1_5", Dur = 576}
, 
{Instru = "eq_1_6", Dur = 512}
, 
{Instru = "eq_1_7", Dur = 448}
, 
{Instru = "eq_1_8", Dur = 384}
, 
{Instru = "eq_1_9", Dur = 320}
, 
{Instru = "eq_1_10", Dur = 256}
}
}
, 
{Type = "ds_e3juice_11", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_10", Amount = 1}
}
, BookOrder = 4004, BookSubOrder = 10, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 13, Reward = 421, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 640}
, 
{Instru = "eq_3_5", Dur = 576}
, 
{Instru = "eq_3_6", Dur = 512}
, 
{Instru = "eq_3_7", Dur = 448}
, 
{Instru = "eq_3_8", Dur = 384}
, 
{Instru = "eq_3_9", Dur = 320}
, 
{Instru = "eq_3_10", Dur = 256}
}
}
, 
{Type = "ds_e1cockt_19", 
Category = {5}
, 
Materials = {
{Material = "ds_e3juice_11", Amount = 1}
, 
{Material = "it_7_1_8", Amount = 1}
, 
{Material = "it_4_1_1", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 18, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 13, Reward = 594, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 7}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 720}
, 
{Instru = "eq_1_5", Dur = 648}
, 
{Instru = "eq_1_6", Dur = 576}
, 
{Instru = "eq_1_7", Dur = 504}
, 
{Instru = "eq_1_8", Dur = 432}
, 
{Instru = "eq_1_9", Dur = 360}
, 
{Instru = "eq_1_10", Dur = 288}
}
}
, 
{Type = "ds_e4sf_15", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_10", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 15, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 14, Reward = 653, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 8}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 720}
, 
{Instru = "eq_4_5", Dur = 648}
, 
{Instru = "eq_4_6", Dur = 576}
, 
{Instru = "eq_4_7", Dur = 504}
, 
{Instru = "eq_4_8", Dur = 432}
, 
{Instru = "eq_4_9", Dur = 360}
, 
{Instru = "eq_4_10", Dur = 288}
}
}
, 
{Type = "ds_e6sf_16", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_9", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 16, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 14, Reward = 853, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 8}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 720}
, 
{Instru = "eq_6_5", Dur = 648}
, 
{Instru = "eq_6_6", Dur = 576}
, 
{Instru = "eq_6_7", Dur = 504}
, 
{Instru = "eq_6_8", Dur = 432}
, 
{Instru = "eq_6_9", Dur = 360}
, 
{Instru = "eq_6_10", Dur = 288}
}
}
, 
{Type = "ds_e1hotdrk_2", 
Category = {5}
, 
Materials = {
{Material = "it_4_2_8", Amount = 1}
, 
{Material = "ds_e1hotdrk_1", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
}
, BookOrder = 4020, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 23, Reward = 756, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 8}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 1280}
, 
{Instru = "eq_1_5", Dur = 1152}
, 
{Instru = "eq_1_6", Dur = 1024}
, 
{Instru = "eq_1_7", Dur = 896}
, 
{Instru = "eq_1_8", Dur = 768}
, 
{Instru = "eq_1_9", Dur = 640}
, 
{Instru = "eq_1_10", Dur = 512}
}
}
, 
{Type = "ds_e1cockt_20", 
Category = {5}
, 
Materials = {
{Material = "it_7_1_10", Amount = 1}
, 
{Material = "ds_e3juice_11", Amount = 1}
}
, BookOrder = 4019, BookSubOrder = 19, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SpecialAnim = "cook_stir", SkipPrice_gem = 45, Reward = 913, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 10}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 2560}
, 
{Instru = "eq_1_5", Dur = 2304}
, 
{Instru = "eq_1_6", Dur = 2048}
, 
{Instru = "eq_1_7", Dur = 1792}
, 
{Instru = "eq_1_8", Dur = 1536}
, 
{Instru = "eq_1_9", Dur = 1280}
, 
{Instru = "eq_1_10", Dur = 1024}
}
}
, 
{Type = "ds_6e1preingre_1", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_3", Amount = 1}
, 
{Material = "it_1_2_3", Amount = 1}
}
, BookOrder = 4025, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 5, Reward = 32, InRandom = 1, 
SellCurrency = {Currency = "delete", Amount = 1}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 50}
, 
{Instru = "eq_1_5", Dur = 45}
, 
{Instru = "eq_1_6", Dur = 40}
, 
{Instru = "eq_1_7", Dur = 35}
, 
{Instru = "eq_1_8", Dur = 30}
, 
{Instru = "eq_1_9", Dur = 25}
, 
{Instru = "eq_1_10", Dur = 20}
}
}
, 
{Type = "ds_6e4pasta_1", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_6", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
, 
{Material = "it_a6_2_1", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 130, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 480}
, 
{Instru = "eq_4_5", Dur = 432}
, 
{Instru = "eq_4_6", Dur = 384}
, 
{Instru = "eq_4_7", Dur = 336}
, 
{Instru = "eq_4_8", Dur = 288}
, 
{Instru = "eq_4_9", Dur = 240}
, 
{Instru = "eq_4_10", Dur = 192}
}
}
, 
{Type = "ds_6e4pasta_2", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_6", Amount = 1}
, 
{Material = "it_3_2_1", Amount = 1}
, 
{Material = "it_3_1_2", Amount = 1}
, 
{Material = "it_a6_2_5", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 108, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 480}
, 
{Instru = "eq_4_5", Dur = 432}
, 
{Instru = "eq_4_6", Dur = 384}
, 
{Instru = "eq_4_7", Dur = 336}
, 
{Instru = "eq_4_8", Dur = 288}
, 
{Instru = "eq_4_9", Dur = 240}
, 
{Instru = "eq_4_10", Dur = 192}
}
}
, 
{Type = "ds_6e4pasta_3", 
Category = {5}
, 
Materials = {
{Material = "ds_6e6semi_1", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 105, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 480}
, 
{Instru = "eq_4_5", Dur = 432}
, 
{Instru = "eq_4_6", Dur = 384}
, 
{Instru = "eq_4_7", Dur = 336}
, 
{Instru = "eq_4_8", Dur = 288}
, 
{Instru = "eq_4_9", Dur = 240}
, 
{Instru = "eq_4_10", Dur = 192}
}
}
, 
{Type = "ds_6e4pasta_4", 
Category = {5}
, 
Materials = {
{Material = "ds_6e6semi_1", Amount = 1}
, 
{Material = "ds_6e3preingre_2", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 16, Reward = 232, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 960}
, 
{Instru = "eq_4_5", Dur = 864}
, 
{Instru = "eq_4_6", Dur = 768}
, 
{Instru = "eq_4_7", Dur = 672}
, 
{Instru = "eq_4_8", Dur = 576}
, 
{Instru = "eq_4_9", Dur = 480}
, 
{Instru = "eq_4_10", Dur = 384}
}
}
, 
{Type = "ds_6e4pasta_5", 
Category = {5}
, 
Materials = {
{Material = "ds_6e6semi_1", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 154, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 640}
, 
{Instru = "eq_4_5", Dur = 576}
, 
{Instru = "eq_4_6", Dur = 512}
, 
{Instru = "eq_4_7", Dur = 448}
, 
{Instru = "eq_4_8", Dur = 384}
, 
{Instru = "eq_4_9", Dur = 320}
, 
{Instru = "eq_4_10", Dur = 256}
}
}
, 
{Type = "ds_6e4pasta_6", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_5", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_a6_2_5", Amount = 1}
, 
{Material = "it_5_2_3", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 108, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 480}
, 
{Instru = "eq_4_5", Dur = 432}
, 
{Instru = "eq_4_6", Dur = 384}
, 
{Instru = "eq_4_7", Dur = 336}
, 
{Instru = "eq_4_8", Dur = 288}
, 
{Instru = "eq_4_9", Dur = 240}
, 
{Instru = "eq_4_10", Dur = 192}
}
}
, 
{Type = "ds_6e3preingre_2", 
Category = {5}
, 
Materials = {
{Material = "it_1_2_6", Amount = 1}
}
, BookOrder = 4025, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 149, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_3_4", Dur = 640}
, 
{Instru = "eq_3_5", Dur = 576}
, 
{Instru = "eq_3_6", Dur = 512}
, 
{Instru = "eq_3_7", Dur = 448}
, 
{Instru = "eq_3_8", Dur = 384}
, 
{Instru = "eq_3_9", Dur = 320}
, 
{Instru = "eq_3_10", Dur = 256}
}
}
, 
{Type = "ds_6e4pasta_7", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_6", Amount = 1}
, 
{Material = "it_5_2_2", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 21, Reward = 278, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_8", 
Category = {5}
, 
Materials = {
{Material = "ds_6e3preingre_2", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_a6_1_2", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 181, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 720}
, 
{Instru = "eq_4_5", Dur = 648}
, 
{Instru = "eq_4_6", Dur = 576}
, 
{Instru = "eq_4_7", Dur = 504}
, 
{Instru = "eq_4_8", Dur = 432}
, 
{Instru = "eq_4_9", Dur = 360}
, 
{Instru = "eq_4_10", Dur = 288}
}
}
, 
{Type = "ds_6e4pasta_9", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_3", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
, 
{Material = "it_1_2_1", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 9, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 105, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 480}
, 
{Instru = "eq_4_5", Dur = 432}
, 
{Instru = "eq_4_6", Dur = 384}
, 
{Instru = "eq_4_7", Dur = 336}
, 
{Instru = "eq_4_8", Dur = 288}
, 
{Instru = "eq_4_9", Dur = 240}
, 
{Instru = "eq_4_10", Dur = 192}
}
}
, 
{Type = "ds_6e4pasta_10", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_5", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 10, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 130, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 480}
, 
{Instru = "eq_4_5", Dur = 432}
, 
{Instru = "eq_4_6", Dur = 384}
, 
{Instru = "eq_4_7", Dur = 336}
, 
{Instru = "eq_4_8", Dur = 288}
, 
{Instru = "eq_4_9", Dur = 240}
, 
{Instru = "eq_4_10", Dur = 192}
}
}
, 
{Type = "ds_6e4pasta_11", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_5", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_a6_2_4", Amount = 1}
, 
{Material = "it_3_1_3", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 11, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 7, Reward = 68, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 240}
, 
{Instru = "eq_4_5", Dur = 216}
, 
{Instru = "eq_4_6", Dur = 192}
, 
{Instru = "eq_4_7", Dur = 168}
, 
{Instru = "eq_4_8", Dur = 144}
, 
{Instru = "eq_4_9", Dur = 120}
, 
{Instru = "eq_4_10", Dur = 96}
}
}
, 
{Type = "ds_6e4pasta_12", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_2", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_7_1_5", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 12, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 122, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 480}
, 
{Instru = "eq_4_5", Dur = 432}
, 
{Instru = "eq_4_6", Dur = 384}
, 
{Instru = "eq_4_7", Dur = 336}
, 
{Instru = "eq_4_8", Dur = 288}
, 
{Instru = "eq_4_9", Dur = 240}
, 
{Instru = "eq_4_10", Dur = 192}
}
}
, 
{Type = "ds_6e4pasta_13", 
Category = {5}
, 
Materials = {
{Material = "ds_6e3preingre_2", Amount = 1}
, 
{Material = "it_3_2_4", Amount = 1}
, 
{Material = "it_a6_1_3", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 13, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 216, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 720}
, 
{Instru = "eq_4_5", Dur = 648}
, 
{Instru = "eq_4_6", Dur = 576}
, 
{Instru = "eq_4_7", Dur = 504}
, 
{Instru = "eq_4_8", Dur = 432}
, 
{Instru = "eq_4_9", Dur = 360}
, 
{Instru = "eq_4_10", Dur = 288}
}
}
, 
{Type = "ds_6e1sala_2", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_a6_2_3", Amount = 1}
}
, BookOrder = 4017, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 170, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 640}
, 
{Instru = "eq_1_5", Dur = 576}
, 
{Instru = "eq_1_6", Dur = 512}
, 
{Instru = "eq_1_7", Dur = 448}
, 
{Instru = "eq_1_8", Dur = 384}
, 
{Instru = "eq_1_9", Dur = 320}
, 
{Instru = "eq_1_10", Dur = 256}
}
}
, 
{Type = "ds_e5mt_2", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_3", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_1_1_3", Amount = 1}
, 
{Material = "it_a6_2_6", Amount = 1}
, 
{Material = "it_6_1_4", Amount = 1}
}
, BookOrder = 4023, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 10, Reward = 130, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 520}
, 
{Instru = "eq_5_5", Dur = 468}
, 
{Instru = "eq_5_6", Dur = 416}
, 
{Instru = "eq_5_7", Dur = 364}
, 
{Instru = "eq_5_8", Dur = 312}
, 
{Instru = "eq_5_9", Dur = 260}
, 
{Instru = "eq_5_10", Dur = 208}
}
}
, 
{Type = "ds_6e4pasta_14", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_4", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_a6_1_6", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 14, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 7, Reward = 70, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 240}
, 
{Instru = "eq_4_5", Dur = 216}
, 
{Instru = "eq_4_6", Dur = 192}
, 
{Instru = "eq_4_7", Dur = 168}
, 
{Instru = "eq_4_8", Dur = 144}
, 
{Instru = "eq_4_9", Dur = 120}
, 
{Instru = "eq_4_10", Dur = 96}
}
}
, 
{Type = "ds_6e4friedmt_8", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_3", Amount = 1}
, 
{Material = "it_1_2_3", Amount = 1}
, 
{Material = "ds_6e6preingre_3", Amount = 1}
}
, BookOrder = 4010, BookSubOrder = 7, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 205, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 720}
, 
{Instru = "eq_4_5", Dur = 648}
, 
{Instru = "eq_4_6", Dur = 576}
, 
{Instru = "eq_4_7", Dur = 504}
, 
{Instru = "eq_4_8", Dur = 432}
, 
{Instru = "eq_4_9", Dur = 360}
, 
{Instru = "eq_4_10", Dur = 288}
}
}
, 
{Type = "ds_6e4pasta_15", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_6", Amount = 1}
, 
{Material = "it_5_2_3", Amount = 1}
, 
{Material = "it_5_2_1", Amount = 1}
, 
{Material = "ds_6e6preingre_3", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 15, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 16, Reward = 251, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 960}
, 
{Instru = "eq_4_5", Dur = 864}
, 
{Instru = "eq_4_6", Dur = 768}
, 
{Instru = "eq_4_7", Dur = 672}
, 
{Instru = "eq_4_8", Dur = 576}
, 
{Instru = "eq_4_9", Dur = 480}
, 
{Instru = "eq_4_10", Dur = 384}
}
}
, 
{Type = "ds_6e6preingre_3", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
}
, BookOrder = 4025, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 11, Reward = 157, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 640}
, 
{Instru = "eq_6_5", Dur = 576}
, 
{Instru = "eq_6_6", Dur = 512}
, 
{Instru = "eq_6_7", Dur = 448}
, 
{Instru = "eq_6_8", Dur = 384}
, 
{Instru = "eq_6_9", Dur = 320}
, 
{Instru = "eq_6_10", Dur = 256}
}
}
, 
{Type = "ds_6e6dst_3", 
Category = {5}
, 
Materials = {
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_1_2_1_2", Amount = 1}
, 
{Material = "it_4_1_1", Amount = 1}
, 
{Material = "it_4_1_2", Amount = 1}
, 
{Material = "it_a6_2_1", Amount = 1}
}
, BookOrder = 4006, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 186, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 720}
, 
{Instru = "eq_6_5", Dur = 648}
, 
{Instru = "eq_6_6", Dur = 576}
, 
{Instru = "eq_6_7", Dur = 504}
, 
{Instru = "eq_6_8", Dur = 432}
, 
{Instru = "eq_6_9", Dur = 360}
, 
{Instru = "eq_6_10", Dur = 288}
}
}
, 
{Type = "ds_6e4pasta_16", 
Category = {5}
, 
Materials = {
{Material = "ds_6e1preingre_1", Amount = 1}
, 
{Material = "it_a6_1_6", Amount = 1}
, 
{Material = "ds_6e6preingre_3", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 16, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 16, Reward = 254, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 960}
, 
{Instru = "eq_4_5", Dur = 864}
, 
{Instru = "eq_4_6", Dur = 768}
, 
{Instru = "eq_4_7", Dur = 672}
, 
{Instru = "eq_4_8", Dur = 576}
, 
{Instru = "eq_4_9", Dur = 480}
, 
{Instru = "eq_4_10", Dur = 384}
}
}
, 
{Type = "ds_6e4pasta_17", 
Category = {5}
, 
Materials = {
{Material = "ds_6e6semi_2", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 17, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 302, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4friedmt_9", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_9", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
, 
{Material = "it_a6_2_1", Amount = 1}
}
, BookOrder = 4010, BookSubOrder = 8, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 321, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_18", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_1", Amount = 1}
, 
{Material = "it_1_1_9", Amount = 1}
, 
{Material = "it_a6_2_1", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 18, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 16, Reward = 248, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 960}
, 
{Instru = "eq_4_5", Dur = 864}
, 
{Instru = "eq_4_6", Dur = 768}
, 
{Instru = "eq_4_7", Dur = 672}
, 
{Instru = "eq_4_8", Dur = 576}
, 
{Instru = "eq_4_9", Dur = 480}
, 
{Instru = "eq_4_10", Dur = 384}
}
}
, 
{Type = "ds_6e2assort_1", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_8", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4027, BookSubOrder = 1, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 335, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 640}
, 
{Instru = "eq_2_5", Dur = 576}
, 
{Instru = "eq_2_6", Dur = 512}
, 
{Instru = "eq_2_7", Dur = 448}
, 
{Instru = "eq_2_8", Dur = 384}
, 
{Instru = "eq_2_9", Dur = 320}
, 
{Instru = "eq_2_10", Dur = 256}
}
}
, 
{Type = "ds_6e4pasta_19", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_5", Amount = 1}
, 
{Material = "it_3_1_2", Amount = 1}
, 
{Material = "ds_6e6preingre_3", Amount = 1}
, 
{Material = "it_1_2_1", Amount = 1}
, 
{Material = "it_a6_2_6", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 19, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 21, Reward = 297, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_20", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_1", Amount = 1}
, 
{Material = "it_5_1_7", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 20, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 308, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_21", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_3", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 21, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 383, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_22", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_5_2_3", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_a6_1_3", Amount = 1}
, 
{Material = "it_a6_2_6", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 22, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 16, Reward = 270, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 960}
, 
{Instru = "eq_4_5", Dur = 864}
, 
{Instru = "eq_4_6", Dur = 768}
, 
{Instru = "eq_4_7", Dur = 672}
, 
{Instru = "eq_4_8", Dur = 576}
, 
{Instru = "eq_4_9", Dur = 480}
, 
{Instru = "eq_4_10", Dur = 384}
}
}
, 
{Type = "ds_e6sf_18", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_4", Amount = 1}
, 
{Material = "it_6_1_6", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4012, BookSubOrder = 18, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 21, Reward = 297, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 1280}
, 
{Instru = "eq_6_5", Dur = 1152}
, 
{Instru = "eq_6_6", Dur = 1024}
, 
{Instru = "eq_6_7", Dur = 896}
, 
{Instru = "eq_6_8", Dur = 768}
, 
{Instru = "eq_6_9", Dur = 640}
, 
{Instru = "eq_6_10", Dur = 512}
}
}
, 
{Type = "ds_6e1icytre_3", 
Category = {5}
, 
Materials = {
{Material = "it_1_2_1_1", Amount = 1}
, 
{Material = "it_3_2_1", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4021, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 351, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 1280}
, 
{Instru = "eq_1_5", Dur = 1152}
, 
{Instru = "eq_1_6", Dur = 1024}
, 
{Instru = "eq_1_7", Dur = 896}
, 
{Instru = "eq_1_8", Dur = 768}
, 
{Instru = "eq_1_9", Dur = 640}
, 
{Instru = "eq_1_10", Dur = 512}
}
}
, 
{Type = "ds_6e1icytre_4", 
Category = {5}
, 
Materials = {
{Material = "it_1_2_1_1", Amount = 1}
, 
{Material = "it_3_2_1", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_4_1_9", Amount = 1}
}
, BookOrder = 4021, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 365, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 1280}
, 
{Instru = "eq_1_5", Dur = 1152}
, 
{Instru = "eq_1_6", Dur = 1024}
, 
{Instru = "eq_1_7", Dur = 896}
, 
{Instru = "eq_1_8", Dur = 768}
, 
{Instru = "eq_1_9", Dur = 640}
, 
{Instru = "eq_1_10", Dur = 512}
}
}
, 
{Type = "ds_6e6rice_3", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_4", Amount = 1}
, 
{Material = "it_5_2_1", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4016, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 394, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 1280}
, 
{Instru = "eq_6_5", Dur = 1152}
, 
{Instru = "eq_6_6", Dur = 1024}
, 
{Instru = "eq_6_7", Dur = 896}
, 
{Instru = "eq_6_8", Dur = 768}
, 
{Instru = "eq_6_9", Dur = 640}
, 
{Instru = "eq_6_10", Dur = 512}
}
}
, 
{Type = "ds_6e1dst_4", 
Category = {5}
, 
Materials = {
{Material = "it_2_1_6", Amount = 1}
, 
{Material = "it_7_1_8", Amount = 1}
, 
{Material = "it_4_2_5", Amount = 1}
, 
{Material = "it_a6_2_4", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
}
, BookOrder = 4006, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 340, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 1280}
, 
{Instru = "eq_1_5", Dur = 1152}
, 
{Instru = "eq_1_6", Dur = 1024}
, 
{Instru = "eq_1_7", Dur = 896}
, 
{Instru = "eq_1_8", Dur = 768}
, 
{Instru = "eq_1_9", Dur = 640}
, 
{Instru = "eq_1_10", Dur = 512}
}
}
, 
{Type = "ds_6e6dst_5", 
Category = {5}
, 
Materials = {
{Material = "it_6_1_5", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4006, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 354, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 1280}
, 
{Instru = "eq_6_5", Dur = 1152}
, 
{Instru = "eq_6_6", Dur = 1024}
, 
{Instru = "eq_6_7", Dur = 896}
, 
{Instru = "eq_6_8", Dur = 768}
, 
{Instru = "eq_6_9", Dur = 640}
, 
{Instru = "eq_6_10", Dur = 512}
}
}
, 
{Type = "ds_6e5dst_6", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_5", Amount = 1}
, 
{Material = "it_4_2_2", Amount = 1}
, 
{Material = "it_6_1_5", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_a6_2_7", Amount = 1}
}
, BookOrder = 4006, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 21, Reward = 297, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 1280}
, 
{Instru = "eq_5_5", Dur = 1152}
, 
{Instru = "eq_5_6", Dur = 1024}
, 
{Instru = "eq_5_7", Dur = 896}
, 
{Instru = "eq_5_8", Dur = 768}
, 
{Instru = "eq_5_9", Dur = 640}
, 
{Instru = "eq_5_10", Dur = 512}
}
}
, 
{Type = "ds_6e1icytre_5", 
Category = {5}
, 
Materials = {
{Material = "it_4_2_5", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_a6_2_6", Amount = 1}
}
, BookOrder = 4021, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 251, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 720}
, 
{Instru = "eq_1_5", Dur = 648}
, 
{Instru = "eq_1_6", Dur = 576}
, 
{Instru = "eq_1_7", Dur = 504}
, 
{Instru = "eq_1_8", Dur = 432}
, 
{Instru = "eq_1_9", Dur = 360}
, 
{Instru = "eq_1_10", Dur = 288}
}
}
, 
{Type = "ds_6e1assort_2", 
Category = {5}
, 
Materials = {
{Material = "ds_grillve_1", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_a6_2_4", Amount = 1}
}
, BookOrder = 4027, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 243, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_1_4", Dur = 640}
, 
{Instru = "eq_1_5", Dur = 576}
, 
{Instru = "eq_1_6", Dur = 512}
, 
{Instru = "eq_1_7", Dur = 448}
, 
{Instru = "eq_1_8", Dur = 384}
, 
{Instru = "eq_1_9", Dur = 320}
, 
{Instru = "eq_1_10", Dur = 256}
}
}
, 
{Type = "ds_6e4stewmt_2", 
Category = {5}
, 
Materials = {
{Material = "it_3_2_4", Amount = 1}
, 
{Material = "it_2_3_5", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
}
, BookOrder = 4022, BookSubOrder = 2, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 16, Reward = 284, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 960}
, 
{Instru = "eq_4_5", Dur = 864}
, 
{Instru = "eq_4_6", Dur = 768}
, 
{Instru = "eq_4_7", Dur = 672}
, 
{Instru = "eq_4_8", Dur = 576}
, 
{Instru = "eq_4_9", Dur = 480}
, 
{Instru = "eq_4_10", Dur = 384}
}
}
, 
{Type = "ds_6e2mt_14", 
Category = {5}
, 
Materials = {
{Material = "it_3_1_8", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
}
, BookOrder = 4003, BookSubOrder = 14, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 21, Reward = 205, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_2_4", Dur = 1280}
, 
{Instru = "eq_2_5", Dur = 1152}
, 
{Instru = "eq_2_6", Dur = 1024}
, 
{Instru = "eq_2_7", Dur = 896}
, 
{Instru = "eq_2_8", Dur = 768}
, 
{Instru = "eq_2_9", Dur = 640}
, 
{Instru = "eq_2_10", Dur = 512}
}
}
, 
{Type = "ds_6e4flb_3", 
Category = {5}
, 
Materials = {
{Material = "it_4_1_3", Amount = 1}
, 
{Material = "it_1_2_7", Amount = 1}
, 
{Material = "it_4_2_4", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
}
, BookOrder = 4011, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 359, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_23", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_8", Amount = 1}
, 
{Material = "it_3_1_6", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_7_1_3", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 23, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 12, Reward = 208, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 720}
, 
{Instru = "eq_4_5", Dur = 648}
, 
{Instru = "eq_4_6", Dur = 576}
, 
{Instru = "eq_4_7", Dur = 504}
, 
{Instru = "eq_4_8", Dur = 432}
, 
{Instru = "eq_4_9", Dur = 360}
, 
{Instru = "eq_4_10", Dur = 288}
}
}
, 
{Type = "ds_6e4pasta_24", 
Category = {5}
, 
Materials = {
{Material = "it_5_1_8", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_4_1_4", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_a6_1_1", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 24, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 348, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_25", 
Category = {5}
, 
Materials = {
{Material = "ds_6e6semi_3", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 25, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 475, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e6rice_4", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_5", Amount = 1}
, 
{Material = "it_6_1_4", Amount = 1}
, 
{Material = "it_7_1_4", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_a6_2_8", Amount = 1}
}
, BookOrder = 4016, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 470, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 1280}
, 
{Instru = "eq_6_5", Dur = 1152}
, 
{Instru = "eq_6_6", Dur = 1024}
, 
{Instru = "eq_6_7", Dur = 896}
, 
{Instru = "eq_6_8", Dur = 768}
, 
{Instru = "eq_6_9", Dur = 640}
, 
{Instru = "eq_6_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_26", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_5", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_a6_2_5", Amount = 1}
, 
{Material = "it_5_2_8", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 26, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 491, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 5}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_27", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_8", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 27, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 329, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_28", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_8", Amount = 1}
, 
{Material = "ds_6e3preingre_2", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 28, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 329, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 1280}
, 
{Instru = "eq_4_5", Dur = 1152}
, 
{Instru = "eq_4_6", Dur = 1024}
, 
{Instru = "eq_4_7", Dur = 896}
, 
{Instru = "eq_4_8", Dur = 768}
, 
{Instru = "eq_4_9", Dur = 640}
, 
{Instru = "eq_4_10", Dur = 512}
}
}
, 
{Type = "ds_6e4pasta_29", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_5", Amount = 1}
, 
{Material = "it_5_2_8", Amount = 1}
, 
{Material = "ds_6e6preingre_3", Amount = 1}
, 
{Material = "it_1_2_1", Amount = 1}
, 
{Material = "it_a6_2_6", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 29, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 44, Reward = 686, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 2560}
, 
{Instru = "eq_4_5", Dur = 2304}
, 
{Instru = "eq_4_6", Dur = 2048}
, 
{Instru = "eq_4_7", Dur = 1792}
, 
{Instru = "eq_4_8", Dur = 1536}
, 
{Instru = "eq_4_9", Dur = 1280}
, 
{Instru = "eq_4_10", Dur = 1024}
}
}
, 
{Type = "ds_6e5pasta_30", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_7", Amount = 1}
, 
{Material = "it_3_1_3", Amount = 1}
, 
{Material = "it_1_1_2", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
, 
{Material = "it_a6_2_8", Amount = 1}
}
, BookOrder = 4024, BookSubOrder = 30, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 44, Reward = 648, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 6}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 2560}
, 
{Instru = "eq_5_5", Dur = 2304}
, 
{Instru = "eq_5_6", Dur = 2048}
, 
{Instru = "eq_5_7", Dur = 1792}
, 
{Instru = "eq_5_8", Dur = 1536}
, 
{Instru = "eq_5_9", Dur = 1280}
, 
{Instru = "eq_5_10", Dur = 1024}
}
}
, 
{Type = "ds_6e5flb_4", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_4", Amount = 1}
, 
{Material = "it_2_3_3", Amount = 1}
, 
{Material = "it_4_2_4", Amount = 1}
}
, BookOrder = 4011, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 8, Reward = 78, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_5_4", Dur = 340}
, 
{Instru = "eq_5_5", Dur = 306}
, 
{Instru = "eq_5_6", Dur = 272}
, 
{Instru = "eq_5_7", Dur = 238}
, 
{Instru = "eq_5_8", Dur = 204}
, 
{Instru = "eq_5_9", Dur = 170}
, 
{Instru = "eq_5_10", Dur = 136}
}
}
, 
{Type = "ds_6e4assort_3", 
Category = {5}
, 
Materials = {
{Material = "it_1_1_3", Amount = 1}
, 
{Material = "it_1_2_5", Amount = 1}
, 
{Material = "it_1_2_6", Amount = 1}
}
, BookOrder = 4027, BookSubOrder = 3, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 16, Reward = 238, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 3}
, 
Instrument = {
{Instru = "eq_4_4", Dur = 960}
, 
{Instru = "eq_4_5", Dur = 864}
, 
{Instru = "eq_4_6", Dur = 768}
, 
{Instru = "eq_4_7", Dur = 672}
, 
{Instru = "eq_4_8", Dur = 576}
, 
{Instru = "eq_4_9", Dur = 480}
, 
{Instru = "eq_4_10", Dur = 384}
}
}
, 
{Type = "ds_6e6semi_1", 
Category = {5}
, 
Materials = {
{Material = "it_5_2_5", Amount = 1}
, 
{Material = "it_a6_1_4", Amount = 1}
}
, BookOrder = 4025, BookSubOrder = 4, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 7, Reward = 62, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 1}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 240}
, 
{Instru = "eq_6_5", Dur = 216}
, 
{Instru = "eq_6_6", Dur = 192}
, 
{Instru = "eq_6_7", Dur = 168}
, 
{Instru = "eq_6_8", Dur = 144}
, 
{Instru = "eq_6_9", Dur = 120}
, 
{Instru = "eq_6_10", Dur = 96}
}
}
, 
{Type = "ds_6e6semi_2", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_4", Amount = 1}
, 
{Material = "it_1_1_5", Amount = 1}
, 
{Material = "it_a6_2_6", Amount = 1}
}
, BookOrder = 4025, BookSubOrder = 5, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 9, Reward = 119, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 2}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 480}
, 
{Instru = "eq_6_5", Dur = 432}
, 
{Instru = "eq_6_6", Dur = 384}
, 
{Instru = "eq_6_7", Dur = 336}
, 
{Instru = "eq_6_8", Dur = 288}
, 
{Instru = "eq_6_9", Dur = 240}
, 
{Instru = "eq_6_10", Dur = 192}
}
}
, 
{Type = "ds_6e6semi_3", 
Category = {5}
, 
Materials = {
{Material = "it_a6_1_4", Amount = 1}
, 
{Material = "it_5_2_8", Amount = 1}
}
, BookOrder = 4025, BookSubOrder = 6, 
BookReward = {
{Currency = "skipprop", Amount = 3}
}
, SkipPrice_gem = 22, Reward = 413, InRandom = 1, 
SellCurrency = {Currency = "energy", Amount = 4}
, 
Instrument = {
{Instru = "eq_6_4", Dur = 1280}
, 
{Instru = "eq_6_5", Dur = 1152}
, 
{Instru = "eq_6_6", Dur = 1024}
, 
{Instru = "eq_6_7", Dur = 896}
, 
{Instru = "eq_6_8", Dur = 768}
, 
{Instru = "eq_6_9", Dur = 640}
, 
{Instru = "eq_6_10", Dur = 512}
}
}
}

