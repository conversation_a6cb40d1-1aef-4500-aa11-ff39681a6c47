"""
店铺订单生成器
基于当前项目配置，实现逐日生成新店铺订单的功能
支持限制条件选择、物品筛选和可视化展示
"""

import os
import re
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter import *
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.font_manager as fm
from collections import defaultdict
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import random
import math

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class ShopItem:
    """店铺物品"""
    code: str
    name: str = ""
    type: str = ""  # pd(生成器), it(物品), eq(器械), ds(菜品)
    level: int = 1
    series: int = 1
    category: List[int] = field(default_factory=list)
    energy_cost: int = 1
    frequency: int = 1
    capacity: int = 1
    generated_items: List[Dict] = field(default_factory=list)
    unlock_price: int = 0
    score: float = 0.0

@dataclass
class DayOrder:
    """单日订单"""
    day: int
    group_id: int
    chapter_id: int
    requirements: List[Dict] = field(default_factory=list)  # 需求物品
    rewards: List[Dict] = field(default_factory=list)  # 奖励物品
    total_energy: float = 0.0
    estimated_time: int = 0  # 预估完成时间(分钟)
    difficulty: str = "普通"  # 难度等级

@dataclass
class ShopOrderPlan:
    """店铺订单计划"""
    shop_name: str
    chapter_id: int
    total_days: int
    daily_orders: List[DayOrder] = field(default_factory=list)
    total_energy: float = 0.0
    total_rewards: Dict[str, int] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)

class ShopOrderGenerator:
    """店铺订单生成器主类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("浮岛物语 - 店铺订单生成器")
        self.root.geometry("1400x900")
        
        # 数据存储
        self.config_path = os.path.join(os.path.dirname(__file__), "Data", "Config")
        self.items_data = {}  # 所有物品数据
        self.shops_data = {}  # 店铺数据
        self.current_plan = None  # 当前生成计划
        self.existing_orders = {}  # 现有订单数据
        
        # 界面变量
        self.selected_shop = StringVar(value="BBQ")
        self.selected_chapter = IntVar(value=1)
        self.generation_days = IntVar(value=7)
        self.energy_mode = StringVar(value="平衡")
        self.difficulty_mode = StringVar(value="普通")
        
        # 限制条件变量
        self.max_energy_per_day = IntVar(value=100)
        self.min_energy_per_day = IntVar(value=20)
        self.allow_generators = BooleanVar(value=True)
        self.allow_items = BooleanVar(value=True)
        self.allow_equipment = BooleanVar(value=True)
        self.allow_dishes = BooleanVar(value=True)
        self.max_item_level = IntVar(value=10)
        self.preferred_series = StringVar(value="全部")
        
        # 创建界面
        self._create_ui()
        
        # 加载配置数据
        self._load_config_data()
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主菜单
        menubar = Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="加载配置", command=self._load_config_data)
        file_menu.add_command(label="保存计划", command=self._save_plan)
        file_menu.add_command(label="加载计划", command=self._load_plan)
        file_menu.add_separator()
        file_menu.add_command(label="导出Excel", command=self._export_excel)
        file_menu.add_command(label="退出", command=self.root.quit)
        menubar.add_cascade(label="文件", menu=file_menu)
        
        # 生成菜单
        generate_menu = Menu(menubar, tearoff=0)
        generate_menu.add_command(label="快速生成", command=self._quick_generate)
        generate_menu.add_command(label="高级生成", command=self._advanced_generate)
        generate_menu.add_command(label="批量生成", command=self._batch_generate)
        menubar.add_cascade(label="生成", menu=generate_menu)
        
        # 分析菜单
        analysis_menu = Menu(menubar, tearoff=0)
        analysis_menu.add_command(label="能量分析", command=self._energy_analysis)
        analysis_menu.add_command(label="物品分析", command=self._item_analysis)
        analysis_menu.add_command(label="难度分析", command=self._difficulty_analysis)
        menubar.add_cascade(label="分析", menu=analysis_menu)
        
        # 创建主框架
        main_paned = ttk.PanedWindow(self.root, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_paned, width=400)
        main_paned.add(control_frame, weight=1)
        
        # 右侧显示面板
        display_frame = ttk.Frame(main_paned)
        main_paned.add(display_frame, weight=2)
        
        # 创建控制面板
        self._create_control_panel(control_frame)
        
        # 创建显示面板
        self._create_display_panel(display_frame)
        
        # 状态栏
        self.status_var = StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=SUNKEN)
        status_bar.pack(side=BOTTOM, fill=X)
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 基础设置
        basic_frame = ttk.LabelFrame(parent, text="基础设置", padding="10")
        basic_frame.pack(fill=X, pady=5)
        
        # 店铺选择
        ttk.Label(basic_frame, text="目标店铺:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        shop_combo = ttk.Combobox(basic_frame, textvariable=self.selected_shop, 
                                 values=["BBQ", "Bakery", "DimSum", "Market", "Morocco", 
                                        "Nice", "Orleans", "Ottoman", "Pasta", "Sausage",
                                        "Seafood", "Sushi", "Tacos", "Tapas", "Thailand", "Wine"],
                                 state="readonly", width=15)
        shop_combo.grid(row=0, column=1, sticky=W, padx=5, pady=2)
        shop_combo.bind('<<ComboboxSelected>>', self._on_shop_changed)
        
        # 章节选择
        ttk.Label(basic_frame, text="目标章节:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        chapter_spin = ttk.Spinbox(basic_frame, from_=1, to=16, textvariable=self.selected_chapter, width=15)
        chapter_spin.grid(row=1, column=1, sticky=W, padx=5, pady=2)
        
        # 生成天数
        ttk.Label(basic_frame, text="生成天数:").grid(row=2, column=0, sticky=W, padx=5, pady=2)
        days_spin = ttk.Spinbox(basic_frame, from_=1, to=30, textvariable=self.generation_days, width=15)
        days_spin.grid(row=2, column=1, sticky=W, padx=5, pady=2)
        
        # 能量模式
        ttk.Label(basic_frame, text="能量模式:").grid(row=3, column=0, sticky=W, padx=5, pady=2)
        energy_combo = ttk.Combobox(basic_frame, textvariable=self.energy_mode,
                                   values=["节能", "平衡", "高效", "极限"], state="readonly", width=15)
        energy_combo.grid(row=3, column=1, sticky=W, padx=5, pady=2)
        
        # 难度模式
        ttk.Label(basic_frame, text="难度模式:").grid(row=4, column=0, sticky=W, padx=5, pady=2)
        difficulty_combo = ttk.Combobox(basic_frame, textvariable=self.difficulty_mode,
                                       values=["简单", "普通", "困难", "专家"], state="readonly", width=15)
        difficulty_combo.grid(row=4, column=1, sticky=W, padx=5, pady=2)
        
        # 限制条件设置
        constraints_frame = ttk.LabelFrame(parent, text="限制条件", padding="10")
        constraints_frame.pack(fill=X, pady=5)
        
        # 能量限制
        ttk.Label(constraints_frame, text="每日能量范围:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        energy_frame = ttk.Frame(constraints_frame)
        energy_frame.grid(row=0, column=1, sticky=W, padx=5, pady=2)
        ttk.Spinbox(energy_frame, from_=1, to=50, textvariable=self.min_energy_per_day, width=8).pack(side=LEFT)
        ttk.Label(energy_frame, text=" - ").pack(side=LEFT)
        ttk.Spinbox(energy_frame, from_=50, to=500, textvariable=self.max_energy_per_day, width=8).pack(side=LEFT)
        
        # 物品类型限制
        ttk.Label(constraints_frame, text="允许物品类型:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        type_frame = ttk.Frame(constraints_frame)
        type_frame.grid(row=1, column=1, sticky=W, padx=5, pady=2)
        ttk.Checkbutton(type_frame, text="生成器", variable=self.allow_generators).pack(side=LEFT)
        ttk.Checkbutton(type_frame, text="物品", variable=self.allow_items).pack(side=LEFT)
        ttk.Checkbutton(type_frame, text="器械", variable=self.allow_equipment).pack(side=LEFT)
        ttk.Checkbutton(type_frame, text="菜品", variable=self.allow_dishes).pack(side=LEFT)
        
        # 等级限制
        ttk.Label(constraints_frame, text="最高物品等级:").grid(row=2, column=0, sticky=W, padx=5, pady=2)
        ttk.Spinbox(constraints_frame, from_=1, to=20, textvariable=self.max_item_level, width=15).grid(
            row=2, column=1, sticky=W, padx=5, pady=2)
        
        # 系列偏好
        ttk.Label(constraints_frame, text="偏好系列:").grid(row=3, column=0, sticky=W, padx=5, pady=2)
        series_combo = ttk.Combobox(constraints_frame, textvariable=self.preferred_series,
                                   values=["全部", "系列1", "系列2", "系列3", "系列4", "系列5"], 
                                   state="readonly", width=15)
        series_combo.grid(row=3, column=1, sticky=W, padx=5, pady=2)
        
        # 操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=X, pady=10)
        
        ttk.Button(button_frame, text="生成订单", command=self._generate_orders, 
                  style="Accent.TButton").pack(fill=X, pady=2)
        ttk.Button(button_frame, text="预览计划", command=self._preview_plan).pack(fill=X, pady=2)
        ttk.Button(button_frame, text="清空结果", command=self._clear_results).pack(fill=X, pady=2)
        ttk.Button(button_frame, text="物品筛选", command=self._open_item_filter).pack(fill=X, pady=2)
    
    def _create_display_panel(self, parent):
        """创建显示面板"""
        # 创建Notebook用于多标签页
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=BOTH, expand=True)
        
        # 订单计划标签页
        self._create_plan_tab()
        
        # 能量分析标签页
        self._create_energy_tab()
        
        # 物品分析标签页
        self._create_items_tab()
        
        # 历史对比标签页
        self._create_history_tab()
    
    def _create_plan_tab(self):
        """创建订单计划标签页"""
        plan_frame = ttk.Frame(self.notebook)
        self.notebook.add(plan_frame, text="订单计划")
        
        # 创建树形视图显示订单
        columns = ("day", "requirements", "rewards", "energy", "time", "difficulty")
        self.plan_tree = ttk.Treeview(plan_frame, columns=columns, show="tree headings", height=15)
        
        # 设置列标题
        self.plan_tree.heading("#0", text="订单")
        self.plan_tree.heading("day", text="天数")
        self.plan_tree.heading("requirements", text="需求物品")
        self.plan_tree.heading("rewards", text="奖励")
        self.plan_tree.heading("energy", text="能量消耗")
        self.plan_tree.heading("time", text="预估时间")
        self.plan_tree.heading("difficulty", text="难度")
        
        # 设置列宽
        self.plan_tree.column("#0", width=100)
        self.plan_tree.column("day", width=60)
        self.plan_tree.column("requirements", width=200)
        self.plan_tree.column("rewards", width=150)
        self.plan_tree.column("energy", width=80)
        self.plan_tree.column("time", width=80)
        self.plan_tree.column("difficulty", width=60)
        
        # 添加滚动条
        plan_scrollbar = ttk.Scrollbar(plan_frame, orient=VERTICAL, command=self.plan_tree.yview)
        self.plan_tree.configure(yscrollcommand=plan_scrollbar.set)
        
        self.plan_tree.pack(side=LEFT, fill=BOTH, expand=True)
        plan_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 绑定双击事件
        self.plan_tree.bind("<Double-1>", self._on_order_double_click)

    def _create_energy_tab(self):
        """创建能量分析标签页"""
        energy_frame = ttk.Frame(self.notebook)
        self.notebook.add(energy_frame, text="能量分析")

        # 创建matplotlib图表
        self.energy_fig, (self.energy_ax1, self.energy_ax2) = plt.subplots(2, 1, figsize=(10, 8))
        self.energy_canvas = FigureCanvasTkAgg(self.energy_fig, energy_frame)
        self.energy_canvas.get_tk_widget().pack(fill=BOTH, expand=True)

        # 初始化图表
        self.energy_ax1.set_title("每日能量消耗趋势")
        self.energy_ax1.set_xlabel("天数")
        self.energy_ax1.set_ylabel("能量消耗")

        self.energy_ax2.set_title("能量消耗分布")
        self.energy_ax2.set_xlabel("能量范围")
        self.energy_ax2.set_ylabel("天数")

    def _create_items_tab(self):
        """创建物品分析标签页"""
        items_frame = ttk.Frame(self.notebook)
        self.notebook.add(items_frame, text="物品分析")

        # 创建分割面板
        items_paned = ttk.PanedWindow(items_frame, orient=HORIZONTAL)
        items_paned.pack(fill=BOTH, expand=True)

        # 左侧物品列表
        items_list_frame = ttk.LabelFrame(items_paned, text="可用物品", padding="5")
        items_paned.add(items_list_frame, weight=1)

        # 物品筛选
        filter_frame = ttk.Frame(items_list_frame)
        filter_frame.pack(fill=X, pady=5)

        ttk.Label(filter_frame, text="筛选:").pack(side=LEFT)
        self.item_filter_var = StringVar()
        filter_entry = ttk.Entry(filter_frame, textvariable=self.item_filter_var)
        filter_entry.pack(side=LEFT, fill=X, expand=True, padx=5)
        filter_entry.bind('<KeyRelease>', self._on_item_filter_changed)

        # 物品列表
        items_columns = ("code", "name", "type", "level", "energy", "frequency")
        self.items_tree = ttk.Treeview(items_list_frame, columns=items_columns, show="headings", height=20)

        self.items_tree.heading("code", text="代码")
        self.items_tree.heading("name", text="名称")
        self.items_tree.heading("type", text="类型")
        self.items_tree.heading("level", text="等级")
        self.items_tree.heading("energy", text="能量")
        self.items_tree.heading("frequency", text="频率")

        self.items_tree.column("code", width=100)
        self.items_tree.column("name", width=120)
        self.items_tree.column("type", width=60)
        self.items_tree.column("level", width=50)
        self.items_tree.column("energy", width=50)
        self.items_tree.column("frequency", width=50)

        items_scrollbar = ttk.Scrollbar(items_list_frame, orient=VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)

        self.items_tree.pack(side=LEFT, fill=BOTH, expand=True)
        items_scrollbar.pack(side=RIGHT, fill=Y)

        # 右侧物品详情
        items_detail_frame = ttk.LabelFrame(items_paned, text="物品详情", padding="5")
        items_paned.add(items_detail_frame, weight=1)

        self.item_detail_text = Text(items_detail_frame, wrap=WORD, font=("Consolas", 10))
        detail_scrollbar = ttk.Scrollbar(items_detail_frame, orient=VERTICAL, command=self.item_detail_text.yview)
        self.item_detail_text.configure(yscrollcommand=detail_scrollbar.set)

        self.item_detail_text.pack(side=LEFT, fill=BOTH, expand=True)
        detail_scrollbar.pack(side=RIGHT, fill=Y)

        # 绑定选择事件
        self.items_tree.bind('<<TreeviewSelect>>', self._on_item_selected)

    def _create_history_tab(self):
        """创建历史对比标签页"""
        history_frame = ttk.Frame(self.notebook)
        self.notebook.add(history_frame, text="历史对比")

        # 创建历史订单对比图表
        self.history_fig, self.history_ax = plt.subplots(figsize=(12, 6))
        self.history_canvas = FigureCanvasTkAgg(self.history_fig, history_frame)
        self.history_canvas.get_tk_widget().pack(fill=BOTH, expand=True)

        self.history_ax.set_title("历史订单对比分析")
        self.history_ax.set_xlabel("章节")
        self.history_ax.set_ylabel("平均能量消耗")

    def _load_config_data(self):
        """加载配置数据"""
        try:
            self.status_var.set("正在加载配置数据...")

            # 加载ItemModelConfig.lua
            self._load_item_model_config()

            # 加载店铺配置
            self._load_shop_configs()

            # 加载现有订单数据
            self._load_existing_orders()

            # 更新物品列表显示
            self._update_items_display()

            self.status_var.set(f"配置加载完成 - 物品:{len(self.items_data)} 店铺:{len(self.shops_data)}")

        except Exception as e:
            self.status_var.set(f"配置加载失败: {e}")
            messagebox.showerror("错误", f"配置加载失败: {e}")

    def _load_item_model_config(self):
        """加载物品模型配置"""
        config_file = os.path.join(self.config_path, "ItemModelConfig.lua")

        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")

        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析Lua配置
        self._parse_item_model_content(content)

    def _parse_item_model_content(self, content):
        """解析物品模型内容"""
        # 移除注释
        content = re.sub(r'--.*?\n', '\n', content)

        # 查找所有配置块
        pattern = r'\{\s*Type\s*=\s*"([^"]+)"([^}]*)\}'
        matches = re.finditer(pattern, content, re.DOTALL)

        for match in matches:
            item_type = match.group(1)
            item_content = match.group(2)

            item = ShopItem(code=item_type)

            # 解析基础属性
            self._parse_item_attributes(item, item_content)

            # 确定物品类型
            if item_type.startswith('pd_'):
                item.type = "生成器"
            elif item_type.startswith('it_'):
                item.type = "物品"
            elif item_type.startswith('eq_'):
                item.type = "器械"
            elif item_type.startswith('ds_'):
                item.type = "菜品"
            else:
                item.type = "其他"

            self.items_data[item_type] = item

    def _parse_item_attributes(self, item, content):
        """解析物品属性"""
        # 解析各种属性
        patterns = {
            'UseEnergy': r'UseEnergy\s*=\s*(\d+)',
            'Frequency': r'Frequency\s*=\s*(\d+)',
            'Capacity': r'Capacity\s*=\s*(\d+)',
            'UnlockPrice': r'UnlockPrice\s*=\s*(\d+)',
            'Score': r'Score\s*=\s*([\d.]+)',
            'Category': r'Category\s*=\s*\{([^}]*)\}',
            'GeneratedItems': r'GeneratedItems\s*=\s*\{([^}]*)\}'
        }

        for attr, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                if attr == 'UseEnergy':
                    item.energy_cost = int(match.group(1))
                elif attr == 'Frequency':
                    item.frequency = int(match.group(1))
                elif attr == 'Capacity':
                    item.capacity = int(match.group(1))
                elif attr == 'UnlockPrice':
                    item.unlock_price = int(match.group(1))
                elif attr == 'Score':
                    item.score = float(match.group(1))
                elif attr == 'Category':
                    categories = re.findall(r'(\d+)', match.group(1))
                    item.category = [int(cat) for cat in categories]
                elif attr == 'GeneratedItems':
                    items_content = match.group(1)
                    item.generated_items = self._parse_generated_items(items_content)

        # 从代码中提取等级和系列信息
        parts = item.code.split('_')
        if len(parts) >= 3:
            try:
                item.series = int(parts[1])
                if len(parts) >= 4:
                    item.level = int(parts[3])
                else:
                    item.level = int(parts[2])
            except ValueError:
                pass

    def _parse_generated_items(self, content):
        """解析产出物品"""
        items = []
        pattern = r'\{\s*Code\s*=\s*"([^"]+)"\s*,\s*Weight\s*=\s*(\d+)\s*\}'
        matches = re.finditer(pattern, content)

        for match in matches:
            items.append({
                'Code': match.group(1),
                'Weight': int(match.group(2))
            })

        return items

    def _load_shop_configs(self):
        """加载店铺配置"""
        mainline_path = os.path.join(self.config_path, "Mainline")

        if not os.path.exists(mainline_path):
            return

        for shop_dir in os.listdir(mainline_path):
            shop_path = os.path.join(mainline_path, shop_dir)
            if os.path.isdir(shop_path):
                task_config_file = os.path.join(shop_path, f"TaskConfig_{shop_dir}.lua")
                if os.path.exists(task_config_file):
                    self.shops_data[shop_dir] = self._load_shop_task_config(task_config_file)

    def _load_shop_task_config(self, config_file):
        """加载店铺任务配置"""
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 简化解析，提取基本信息
        tasks = []
        pattern = r'\{\s*ChapterId\s*=\s*"([^"]+)"\s*,\s*Id\s*=\s*(\d+)\s*,\s*Cost\s*=\s*(\d+)'
        matches = re.finditer(pattern, content)

        for match in matches:
            tasks.append({
                'chapter_id': match.group(1),
                'id': int(match.group(2)),
                'cost': int(match.group(3))
            })

        return tasks

    def _load_existing_orders(self):
        """加载现有订单数据"""
        for chapter in range(1, 17):
            order_file = os.path.join(self.config_path, f"OrderFixedConfig_{chapter}.lua")
            if os.path.exists(order_file):
                self.existing_orders[chapter] = self._load_chapter_orders(order_file)

            group_file = os.path.join(self.config_path, f"OrderGroupConfig_{chapter}.lua")
            if os.path.exists(group_file):
                if chapter not in self.existing_orders:
                    self.existing_orders[chapter] = {}
                self.existing_orders[chapter]['groups'] = self._load_group_orders(group_file)

    def _load_chapter_orders(self, order_file):
        """加载章节订单"""
        with open(order_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 简化解析
        orders = []
        pattern = r'\{\s*Id\s*=\s*"([^"]+)"'
        matches = re.finditer(pattern, content)

        for match in matches:
            orders.append({'id': match.group(1)})

        return {'orders': orders}

    def _load_group_orders(self, group_file):
        """加载组订单"""
        with open(group_file, 'r', encoding='utf-8') as f:
            content = f.read()

        groups = []
        pattern = r'\{\s*GroupId\s*=\s*(\d+)\s*,\s*ChapterId\s*=\s*(\d+)\s*,\s*Day\s*=\s*(\d+)'
        matches = re.finditer(pattern, content)

        for match in matches:
            groups.append({
                'group_id': int(match.group(1)),
                'chapter_id': int(match.group(2)),
                'day': int(match.group(3))
            })

        return groups

    def _generate_orders(self):
        """生成订单"""
        try:
            self.status_var.set("正在生成订单...")

            # 创建订单计划
            plan = ShopOrderPlan(
                shop_name=self.selected_shop.get(),
                chapter_id=self.selected_chapter.get(),
                total_days=self.generation_days.get()
            )

            # 设置约束条件
            plan.constraints = {
                'max_energy_per_day': self.max_energy_per_day.get(),
                'min_energy_per_day': self.min_energy_per_day.get(),
                'allow_generators': self.allow_generators.get(),
                'allow_items': self.allow_items.get(),
                'allow_equipment': self.allow_equipment.get(),
                'allow_dishes': self.allow_dishes.get(),
                'max_item_level': self.max_item_level.get(),
                'preferred_series': self.preferred_series.get(),
                'energy_mode': self.energy_mode.get(),
                'difficulty_mode': self.difficulty_mode.get()
            }

            # 逐日生成订单
            for day in range(1, plan.total_days + 1):
                day_order = self._generate_day_order(day, plan)
                plan.daily_orders.append(day_order)
                plan.total_energy += day_order.total_energy

                # 累计奖励
                for reward in day_order.rewards:
                    currency = reward.get('Currency', '')
                    amount = reward.get('Amount', 0)
                    plan.total_rewards[currency] = plan.total_rewards.get(currency, 0) + amount

            self.current_plan = plan

            # 更新显示
            self._update_plan_display()
            self._update_energy_analysis()

            self.status_var.set(f"订单生成完成 - {plan.total_days}天，总能量:{plan.total_energy:.1f}")

        except Exception as e:
            self.status_var.set(f"订单生成失败: {e}")
            messagebox.showerror("错误", f"订单生成失败: {e}")

    def _generate_day_order(self, day, plan):
        """生成单日订单"""
        day_order = DayOrder(
            day=day,
            group_id=day,
            chapter_id=plan.chapter_id
        )

        # 根据能量模式确定目标能量
        target_energy = self._calculate_target_energy(day, plan)

        # 筛选可用物品
        available_items = self._filter_available_items(plan.constraints)

        # 生成需求物品
        current_energy = 0.0
        requirement_count = random.randint(1, 4)  # 每日1-4个需求

        for _ in range(requirement_count):
            if current_energy >= target_energy:
                break

            # 随机选择物品
            if available_items:
                item_code = random.choice(list(available_items.keys()))
                item = available_items[item_code]

                # 计算数量
                remaining_energy = target_energy - current_energy
                max_count = max(1, int(remaining_energy / item.energy_cost))
                count = random.randint(1, min(max_count, 5))

                requirement = {
                    'Type': item_code,
                    'Count': count,
                    'Energy': item.energy_cost * count
                }

                day_order.requirements.append(requirement)
                current_energy += requirement['Energy']

        day_order.total_energy = current_energy

        # 生成奖励
        day_order.rewards = self._generate_day_rewards(day, current_energy)

        # 计算预估时间和难度
        day_order.estimated_time = self._calculate_estimated_time(day_order)
        day_order.difficulty = self._calculate_difficulty(day_order, plan.constraints)

        return day_order

    def _calculate_target_energy(self, day, plan):
        """计算目标能量"""
        min_energy = plan.constraints['min_energy_per_day']
        max_energy = plan.constraints['max_energy_per_day']
        energy_mode = plan.constraints['energy_mode']

        # 基础能量范围
        base_energy = min_energy + (max_energy - min_energy) * 0.5

        # 根据模式调整
        mode_multipliers = {
            '节能': 0.7,
            '平衡': 1.0,
            '高效': 1.3,
            '极限': 1.6
        }

        multiplier = mode_multipliers.get(energy_mode, 1.0)
        target = base_energy * multiplier

        # 添加随机波动
        variation = random.uniform(0.8, 1.2)
        target *= variation

        # 确保在范围内
        return max(min_energy, min(target, max_energy))

    def _filter_available_items(self, constraints):
        """筛选可用物品"""
        available = {}

        for code, item in self.items_data.items():
            # 检查类型限制
            if item.type == "生成器" and not constraints['allow_generators']:
                continue
            if item.type == "物品" and not constraints['allow_items']:
                continue
            if item.type == "器械" and not constraints['allow_equipment']:
                continue
            if item.type == "菜品" and not constraints['allow_dishes']:
                continue

            # 检查等级限制
            if item.level > constraints['max_item_level']:
                continue

            # 检查系列偏好
            preferred_series = constraints['preferred_series']
            if preferred_series != "全部":
                series_num = int(preferred_series.replace("系列", ""))
                if item.series != series_num:
                    continue

            available[code] = item

        return available

    def _generate_day_rewards(self, day, energy_cost):
        """生成每日奖励"""
        rewards = []

        # 基础经验奖励
        base_exp = int(energy_cost * 0.8)
        rewards.append({'Currency': 'exp', 'Amount': base_exp})

        # 能量奖励
        energy_reward = max(5, int(energy_cost * 0.1))
        rewards.append({'Currency': 'energy', 'Amount': energy_reward})

        # 随机额外奖励
        if random.random() < 0.3:  # 30%概率获得额外奖励
            bonus_items = ['gem', 'pd_1_1', 'pd_2_1', 'eq_1_1']
            bonus_item = random.choice(bonus_items)
            rewards.append({'Currency': bonus_item, 'Amount': 1})

        return rewards

    def _calculate_estimated_time(self, day_order):
        """计算预估完成时间（分钟）"""
        base_time = 0

        for req in day_order.requirements:
            item_code = req['Type']
            count = req['Count']

            if item_code in self.items_data:
                item = self.items_data[item_code]
                # 基于频率和容量计算时间
                time_per_item = max(1, item.frequency * 2)  # 简化计算
                base_time += time_per_item * count

        # 添加基础操作时间
        return max(10, base_time + 5)

    def _calculate_difficulty(self, day_order, constraints):
        """计算难度等级"""
        difficulty_score = 0

        # 基于能量消耗
        energy_ratio = day_order.total_energy / constraints['max_energy_per_day']
        difficulty_score += energy_ratio * 40

        # 基于物品数量
        item_count = len(day_order.requirements)
        difficulty_score += item_count * 10

        # 基于物品等级
        max_level = 0
        for req in day_order.requirements:
            item_code = req['Type']
            if item_code in self.items_data:
                max_level = max(max_level, self.items_data[item_code].level)

        difficulty_score += max_level * 5

        # 转换为难度等级
        if difficulty_score < 30:
            return "简单"
        elif difficulty_score < 60:
            return "普通"
        elif difficulty_score < 90:
            return "困难"
        else:
            return "专家"

    def _update_plan_display(self):
        """更新订单计划显示"""
        # 清空现有数据
        for item in self.plan_tree.get_children():
            self.plan_tree.delete(item)

        if not self.current_plan:
            return

        # 添加计划总览
        plan_node = self.plan_tree.insert("", "end", text=f"{self.current_plan.shop_name}店铺计划",
                                         values=("", f"{self.current_plan.total_days}天",
                                               f"总奖励:{len(self.current_plan.total_rewards)}",
                                               f"{self.current_plan.total_energy:.1f}", "", ""))

        # 添加每日订单
        for day_order in self.current_plan.daily_orders:
            # 格式化需求物品
            req_text = []
            for req in day_order.requirements:
                item_code = req['Type']
                count = req['Count']
                item_name = self.items_data.get(item_code, ShopItem(code=item_code)).name or item_code
                req_text.append(f"{item_name}x{count}")

            # 格式化奖励
            reward_text = []
            for reward in day_order.rewards:
                currency = reward['Currency']
                amount = reward['Amount']
                reward_text.append(f"{currency}x{amount}")

            # 添加到树形视图
            day_node = self.plan_tree.insert(plan_node, "end", text=f"第{day_order.day}天",
                                           values=(day_order.day,
                                                 "; ".join(req_text[:2]) + ("..." if len(req_text) > 2 else ""),
                                                 "; ".join(reward_text[:2]) + ("..." if len(reward_text) > 2 else ""),
                                                 f"{day_order.total_energy:.1f}",
                                                 f"{day_order.estimated_time}分钟",
                                                 day_order.difficulty))

            # 添加详细需求
            for req in day_order.requirements:
                item_code = req['Type']
                count = req['Count']
                energy = req['Energy']
                item_name = self.items_data.get(item_code, ShopItem(code=item_code)).name or item_code

                self.plan_tree.insert(day_node, "end", text=f"  需求: {item_name}",
                                    values=("", f"x{count}", "", f"{energy:.1f}", "", ""))

        # 展开根节点
        self.plan_tree.item(plan_node, open=True)

    def _update_energy_analysis(self):
        """更新能量分析图表"""
        if not self.current_plan:
            return

        # 清空图表
        self.energy_ax1.clear()
        self.energy_ax2.clear()

        # 每日能量趋势
        days = [order.day for order in self.current_plan.daily_orders]
        energies = [order.total_energy for order in self.current_plan.daily_orders]

        self.energy_ax1.plot(days, energies, 'b-o', linewidth=2, markersize=6)
        self.energy_ax1.set_title("每日能量消耗趋势")
        self.energy_ax1.set_xlabel("天数")
        self.energy_ax1.set_ylabel("能量消耗")
        self.energy_ax1.grid(True, alpha=0.3)

        # 添加平均线
        avg_energy = sum(energies) / len(energies)
        self.energy_ax1.axhline(y=avg_energy, color='r', linestyle='--', alpha=0.7, label=f'平均值: {avg_energy:.1f}')
        self.energy_ax1.legend()

        # 能量分布直方图
        self.energy_ax2.hist(energies, bins=min(10, len(energies)), alpha=0.7, color='skyblue', edgecolor='black')
        self.energy_ax2.set_title("能量消耗分布")
        self.energy_ax2.set_xlabel("能量范围")
        self.energy_ax2.set_ylabel("天数")
        self.energy_ax2.grid(True, alpha=0.3)

        # 刷新图表
        self.energy_fig.tight_layout()
        self.energy_canvas.draw()

    def _update_items_display(self):
        """更新物品列表显示"""
        # 清空现有数据
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # 添加物品数据
        for code, item in self.items_data.items():
            self.items_tree.insert("", "end", values=(
                code,
                item.name or code,
                item.type,
                item.level,
                item.energy_cost,
                item.frequency
            ))

    def _on_shop_changed(self, event):
        """店铺选择改变事件"""
        selected_shop = self.selected_shop.get()
        self.status_var.set(f"选择店铺: {selected_shop}")

        # 可以在这里加载特定店铺的配置
        if selected_shop in self.shops_data:
            shop_data = self.shops_data[selected_shop]
            # 更新相关显示

    def _on_order_double_click(self, event):
        """订单双击事件"""
        selection = self.plan_tree.selection()
        if selection:
            item = self.plan_tree.item(selection[0])
            text = item['text']

            if "第" in text and "天" in text:
                # 显示订单详情
                self._show_order_details(selection[0])

    def _on_item_selected(self, event):
        """物品选择事件"""
        selection = self.items_tree.selection()
        if selection:
            item_values = self.items_tree.item(selection[0])['values']
            if item_values:
                item_code = item_values[0]
                self._show_item_details(item_code)

    def _on_item_filter_changed(self, event):
        """物品筛选改变事件"""
        filter_text = self.item_filter_var.get().lower()

        # 清空现有显示
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # 添加筛选后的物品
        for code, item in self.items_data.items():
            if (filter_text in code.lower() or
                filter_text in (item.name or "").lower() or
                filter_text in item.type.lower()):

                self.items_tree.insert("", "end", values=(
                    code,
                    item.name or code,
                    item.type,
                    item.level,
                    item.energy_cost,
                    item.frequency
                ))

    def _show_order_details(self, tree_item):
        """显示订单详情"""
        # 获取订单信息
        item_text = self.plan_tree.item(tree_item)['text']

        # 创建详情窗口
        detail_window = Toplevel(self.root)
        detail_window.title(f"订单详情 - {item_text}")
        detail_window.geometry("600x400")
        detail_window.transient(self.root)
        detail_window.grab_set()

        # 添加详情内容
        detail_text = Text(detail_window, wrap=WORD, font=("Consolas", 10))
        detail_scrollbar = ttk.Scrollbar(detail_window, orient=VERTICAL, command=detail_text.yview)
        detail_text.configure(yscrollcommand=detail_scrollbar.set)

        detail_text.pack(side=LEFT, fill=BOTH, expand=True)
        detail_scrollbar.pack(side=RIGHT, fill=Y)

        # 填充详情信息
        if self.current_plan:
            day_num = int(re.search(r'第(\d+)天', item_text).group(1))
            day_order = self.current_plan.daily_orders[day_num - 1]

            detail_text.insert(END, f"=== {item_text} 详情 ===\n\n")
            detail_text.insert(END, f"总能量消耗: {day_order.total_energy:.1f}\n")
            detail_text.insert(END, f"预估时间: {day_order.estimated_time}分钟\n")
            detail_text.insert(END, f"难度等级: {day_order.difficulty}\n\n")

            detail_text.insert(END, "需求物品:\n")
            for req in day_order.requirements:
                item_code = req['Type']
                count = req['Count']
                energy = req['Energy']
                item = self.items_data.get(item_code, ShopItem(code=item_code))

                detail_text.insert(END, f"  • {item.name or item_code} x{count}\n")
                detail_text.insert(END, f"    代码: {item_code}\n")
                detail_text.insert(END, f"    类型: {item.type}\n")
                detail_text.insert(END, f"    等级: {item.level}\n")
                detail_text.insert(END, f"    能量消耗: {energy:.1f}\n\n")

            detail_text.insert(END, "奖励物品:\n")
            for reward in day_order.rewards:
                currency = reward['Currency']
                amount = reward['Amount']
                detail_text.insert(END, f"  • {currency} x{amount}\n")

        detail_text.config(state=DISABLED)

    def _show_item_details(self, item_code):
        """显示物品详情"""
        if item_code not in self.items_data:
            return

        item = self.items_data[item_code]

        # 清空详情文本
        self.item_detail_text.delete(1.0, END)

        # 填充物品详情
        self.item_detail_text.insert(END, f"=== {item.name or item_code} ===\n\n")
        self.item_detail_text.insert(END, f"代码: {item.code}\n")
        self.item_detail_text.insert(END, f"类型: {item.type}\n")
        self.item_detail_text.insert(END, f"等级: {item.level}\n")
        self.item_detail_text.insert(END, f"系列: {item.series}\n")
        self.item_detail_text.insert(END, f"能量消耗: {item.energy_cost}\n")
        self.item_detail_text.insert(END, f"频率: {item.frequency}\n")
        self.item_detail_text.insert(END, f"容量: {item.capacity}\n")
        self.item_detail_text.insert(END, f"解锁价格: {item.unlock_price}\n")
        self.item_detail_text.insert(END, f"分数: {item.score}\n\n")

        if item.category:
            self.item_detail_text.insert(END, f"分类: {', '.join(map(str, item.category))}\n\n")

        if item.generated_items:
            self.item_detail_text.insert(END, "产出物品:\n")
            for gen_item in item.generated_items:
                code = gen_item.get('Code', '')
                weight = gen_item.get('Weight', 0)
                self.item_detail_text.insert(END, f"  • {code} (权重: {weight})\n")

    def _quick_generate(self):
        """快速生成"""
        # 使用默认设置快速生成
        self._generate_orders()

    def _advanced_generate(self):
        """高级生成"""
        # 打开高级设置窗口
        self._open_advanced_settings()

    def _batch_generate(self):
        """批量生成"""
        # 生成多套方案进行对比
        batch_window = Toplevel(self.root)
        batch_window.title("批量生成对比")
        batch_window.geometry("800x600")
        batch_window.transient(self.root)
        batch_window.grab_set()

        # 生成多套方案
        schemes = []
        for i in range(5):
            # 稍微调整参数生成不同方案
            original_energy_mode = self.energy_mode.get()
            modes = ["节能", "平衡", "高效"]
            self.energy_mode.set(modes[i % len(modes)])

            self._generate_orders()
            if self.current_plan:
                schemes.append({
                    'name': f"方案{i+1}",
                    'plan': self.current_plan,
                    'energy_mode': self.energy_mode.get()
                })

            # 恢复原设置
            self.energy_mode.set(original_energy_mode)

        # 显示对比结果
        self._show_batch_comparison(batch_window, schemes)

    def _show_batch_comparison(self, parent, schemes):
        """显示批量对比结果"""
        # 创建对比表格
        columns = ("scheme", "total_energy", "avg_energy", "total_days", "difficulty_dist")
        comparison_tree = ttk.Treeview(parent, columns=columns, show="headings", height=15)

        comparison_tree.heading("scheme", text="方案")
        comparison_tree.heading("total_energy", text="总能量")
        comparison_tree.heading("avg_energy", text="平均能量")
        comparison_tree.heading("total_days", text="总天数")
        comparison_tree.heading("difficulty_dist", text="难度分布")

        for scheme in schemes:
            plan = scheme['plan']
            avg_energy = plan.total_energy / plan.total_days if plan.total_days > 0 else 0

            # 统计难度分布
            difficulty_count = defaultdict(int)
            for day_order in plan.daily_orders:
                difficulty_count[day_order.difficulty] += 1

            difficulty_str = ", ".join([f"{k}:{v}" for k, v in difficulty_count.items()])

            comparison_tree.insert("", "end", values=(
                f"{scheme['name']} ({scheme['energy_mode']})",
                f"{plan.total_energy:.1f}",
                f"{avg_energy:.1f}",
                plan.total_days,
                difficulty_str
            ))

        comparison_tree.pack(fill=BOTH, expand=True, padx=10, pady=10)

    def _energy_analysis(self):
        """能量分析"""
        if not self.current_plan:
            messagebox.showwarning("警告", "请先生成订单计划")
            return

        # 切换到能量分析标签页
        self.notebook.select(1)

        # 更新分析
        self._update_energy_analysis()

    def _item_analysis(self):
        """物品分析"""
        # 切换到物品分析标签页
        self.notebook.select(2)

        # 更新物品统计
        self._update_item_statistics()

    def _difficulty_analysis(self):
        """难度分析"""
        if not self.current_plan:
            messagebox.showwarning("警告", "请先生成订单计划")
            return

        # 创建难度分析窗口
        analysis_window = Toplevel(self.root)
        analysis_window.title("难度分析")
        analysis_window.geometry("600x400")
        analysis_window.transient(self.root)

        # 统计难度分布
        difficulty_stats = defaultdict(list)
        for day_order in self.current_plan.daily_orders:
            difficulty_stats[day_order.difficulty].append(day_order.total_energy)

        # 显示统计结果
        stats_text = Text(analysis_window, wrap=WORD, font=("Consolas", 10))
        stats_text.pack(fill=BOTH, expand=True, padx=10, pady=10)

        stats_text.insert(END, "=== 难度分析报告 ===\n\n")

        for difficulty, energies in difficulty_stats.items():
            count = len(energies)
            avg_energy = sum(energies) / count
            min_energy = min(energies)
            max_energy = max(energies)

            stats_text.insert(END, f"{difficulty}难度:\n")
            stats_text.insert(END, f"  天数: {count}\n")
            stats_text.insert(END, f"  平均能量: {avg_energy:.1f}\n")
            stats_text.insert(END, f"  能量范围: {min_energy:.1f} - {max_energy:.1f}\n\n")

    def _update_item_statistics(self):
        """更新物品统计"""
        if not self.current_plan:
            return

        # 统计物品使用情况
        item_usage = defaultdict(int)
        type_usage = defaultdict(int)
        level_usage = defaultdict(int)

        for day_order in self.current_plan.daily_orders:
            for req in day_order.requirements:
                item_code = req['Type']
                count = req['Count']

                item_usage[item_code] += count

                if item_code in self.items_data:
                    item = self.items_data[item_code]
                    type_usage[item.type] += count
                    level_usage[item.level] += count

        # 更新显示（这里可以添加图表显示）
        self.item_detail_text.delete(1.0, END)
        self.item_detail_text.insert(END, "=== 物品使用统计 ===\n\n")

        self.item_detail_text.insert(END, "使用最多的物品:\n")
        sorted_items = sorted(item_usage.items(), key=lambda x: x[1], reverse=True)
        for item_code, count in sorted_items[:10]:
            item_name = self.items_data.get(item_code, ShopItem(code=item_code)).name or item_code
            self.item_detail_text.insert(END, f"  {item_name}: {count}次\n")

        self.item_detail_text.insert(END, "\n类型分布:\n")
        for item_type, count in type_usage.items():
            self.item_detail_text.insert(END, f"  {item_type}: {count}次\n")

        self.item_detail_text.insert(END, "\n等级分布:\n")
        for level, count in sorted(level_usage.items()):
            self.item_detail_text.insert(END, f"  等级{level}: {count}次\n")

    def _open_advanced_settings(self):
        """打开高级设置窗口"""
        settings_window = Toplevel(self.root)
        settings_window.title("高级设置")
        settings_window.geometry("500x600")
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 添加高级设置选项
        notebook = ttk.Notebook(settings_window)
        notebook.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # 算法设置
        algo_frame = ttk.Frame(notebook)
        notebook.add(algo_frame, text="算法设置")

        # 奖励设置
        reward_frame = ttk.Frame(notebook)
        notebook.add(reward_frame, text="奖励设置")

        # 约束设置
        constraint_frame = ttk.Frame(notebook)
        notebook.add(constraint_frame, text="约束设置")

        # 这里可以添加更多高级设置选项

    def _open_item_filter(self):
        """打开物品筛选窗口"""
        filter_window = Toplevel(self.root)
        filter_window.title("物品筛选器")
        filter_window.geometry("800x600")
        filter_window.transient(self.root)
        filter_window.grab_set()

        # 创建筛选界面
        filter_frame = ttk.LabelFrame(filter_window, text="筛选条件", padding="10")
        filter_frame.pack(fill=X, padx=10, pady=5)

        # 类型筛选
        ttk.Label(filter_frame, text="物品类型:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        type_vars = {}
        type_frame = ttk.Frame(filter_frame)
        type_frame.grid(row=0, column=1, sticky=W, padx=5, pady=2)

        for item_type in ["生成器", "物品", "器械", "菜品"]:
            var = BooleanVar(value=True)
            type_vars[item_type] = var
            ttk.Checkbutton(type_frame, text=item_type, variable=var).pack(side=LEFT)

        # 等级范围筛选
        ttk.Label(filter_frame, text="等级范围:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        level_frame = ttk.Frame(filter_frame)
        level_frame.grid(row=1, column=1, sticky=W, padx=5, pady=2)

        min_level_var = IntVar(value=1)
        max_level_var = IntVar(value=20)
        ttk.Spinbox(level_frame, from_=1, to=20, textvariable=min_level_var, width=8).pack(side=LEFT)
        ttk.Label(level_frame, text=" - ").pack(side=LEFT)
        ttk.Spinbox(level_frame, from_=1, to=20, textvariable=max_level_var, width=8).pack(side=LEFT)

        # 筛选结果显示
        result_frame = ttk.LabelFrame(filter_window, text="筛选结果", padding="10")
        result_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)

        # 这里可以添加筛选结果的显示

    def _preview_plan(self):
        """预览计划"""
        if not self.current_plan:
            messagebox.showwarning("警告", "请先生成订单计划")
            return

        # 切换到订单计划标签页
        self.notebook.select(0)

    def _clear_results(self):
        """清空结果"""
        self.current_plan = None

        # 清空显示
        for item in self.plan_tree.get_children():
            self.plan_tree.delete(item)

        # 清空图表
        self.energy_ax1.clear()
        self.energy_ax2.clear()
        self.energy_canvas.draw()

        self.item_detail_text.delete(1.0, END)

        self.status_var.set("结果已清空")

    def _save_plan(self):
        """保存计划"""
        if not self.current_plan:
            messagebox.showwarning("警告", "没有可保存的计划")
            return

        filename = filedialog.asksaveasfilename(
            title="保存订单计划",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                # 将计划转换为可序列化的格式
                plan_data = {
                    'shop_name': self.current_plan.shop_name,
                    'chapter_id': self.current_plan.chapter_id,
                    'total_days': self.current_plan.total_days,
                    'total_energy': self.current_plan.total_energy,
                    'total_rewards': self.current_plan.total_rewards,
                    'constraints': self.current_plan.constraints,
                    'daily_orders': []
                }

                for day_order in self.current_plan.daily_orders:
                    plan_data['daily_orders'].append({
                        'day': day_order.day,
                        'group_id': day_order.group_id,
                        'chapter_id': day_order.chapter_id,
                        'requirements': day_order.requirements,
                        'rewards': day_order.rewards,
                        'total_energy': day_order.total_energy,
                        'estimated_time': day_order.estimated_time,
                        'difficulty': day_order.difficulty
                    })

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(plan_data, f, ensure_ascii=False, indent=2)

                self.status_var.set(f"计划已保存到: {filename}")
                messagebox.showinfo("成功", "计划保存成功")

            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

    def _load_plan(self):
        """加载计划"""
        filename = filedialog.askopenfilename(
            title="加载订单计划",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    plan_data = json.load(f)

                # 重建计划对象
                plan = ShopOrderPlan(
                    shop_name=plan_data['shop_name'],
                    chapter_id=plan_data['chapter_id'],
                    total_days=plan_data['total_days']
                )

                plan.total_energy = plan_data['total_energy']
                plan.total_rewards = plan_data['total_rewards']
                plan.constraints = plan_data['constraints']

                for day_data in plan_data['daily_orders']:
                    day_order = DayOrder(
                        day=day_data['day'],
                        group_id=day_data['group_id'],
                        chapter_id=day_data['chapter_id']
                    )
                    day_order.requirements = day_data['requirements']
                    day_order.rewards = day_data['rewards']
                    day_order.total_energy = day_data['total_energy']
                    day_order.estimated_time = day_data['estimated_time']
                    day_order.difficulty = day_data['difficulty']

                    plan.daily_orders.append(day_order)

                self.current_plan = plan

                # 更新显示
                self._update_plan_display()
                self._update_energy_analysis()

                self.status_var.set(f"计划已加载: {filename}")
                messagebox.showinfo("成功", "计划加载成功")

            except Exception as e:
                messagebox.showerror("错误", f"加载失败: {e}")

    def _export_excel(self):
        """导出Excel"""
        if not self.current_plan:
            messagebox.showwarning("警告", "没有可导出的计划")
            return

        messagebox.showinfo("提示", "Excel导出功能需要安装openpyxl库")


def main():
    """主程序入口"""
    root = tk.Tk()
    app = ShopOrderGenerator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
