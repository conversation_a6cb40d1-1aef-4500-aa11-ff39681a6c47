# 合成提示规则详解

## 概述

浮岛物语的棋盘合成提示系统是一个智能化的操作引导系统，通过分析当前棋盘状态、订单需求和玩家等级，自动显示最优的操作建议，帮助玩家高效完成游戏目标。

## 提示系统架构

### 核心组件
- **BoardPrompt**: 提示基类，定义通用接口
- **BoardPromptManager**: 提示管理器，负责选择和调度提示
- **PromptConfig**: 提示配置，定义优先级和关闭等级
- **BoardView**: 棋盘视图，负责显示提示效果

### 提示触发机制
- **定时检测**: 每隔一定时间检查是否需要显示提示
- **事件驱动**: 响应棋盘状态变化事件
- **优先级排序**: 根据配置的优先级选择最重要的提示

## 提示类型详细说明

### 🎯 1. 完成订单提示 (finishOrder)
- **优先级**: 1 (最高)
- **关闭等级**: 5级后关闭
- **触发条件**: 存在可交付的订单
- **显示效果**: 在可交付订单上显示手势动画
- **操作引导**: 引导玩家点击"GO"按钮完成订单

```lua
-- 触发条件检查
function BoardPromptFinishOrder:CanStart(boardView)
  local cells = boardView:GetOrderArea():GetCells()
  for _, cell in pairs(cells) do
    if cell:GetOrderViewState() == OrderState.CanDeliver then
      return true  -- 找到可交付订单
    end
  end
  return false
end
```

### 📝 2. 完成任务提示 (finishTask)
- **优先级**: 2
- **关闭等级**: 5级后关闭
- **触发条件**: 存在可完成的任务
- **显示效果**: 在任务气泡上显示手势动画
- **操作引导**: 引导玩家点击任务气泡领取奖励

### 🔄 3. 合成物品提示 (mergeItems)
- **优先级**: 7
- **关闭等级**: 无限制
- **触发条件**: 棋盘上存在可合成的物品对
- **显示效果**: 两个可合成物品之间显示双向箭头动画
- **操作引导**: 引导玩家拖拽合成物品

#### 合成提示核心逻辑
```lua
function BoardPromptMergeItems:CanStart(boardView)
  local boardModel = boardView:GetModel()
  -- 查找可合成的物品对
  self.m_itemModels = boardModel:FindMergePair(boardView:GetPromptIgnoreItems())
  return self.m_itemModels ~= nil
end
```

#### 合成对查找算法
1. **遍历棋盘物品**: 检查所有棋盘上的物品
2. **类型匹配**: 查找相同类型的物品
3. **合成条件**: 验证物品是否可以合成
4. **排除规则**: 排除被忽略的物品（如正在动画中的物品）

### 📦 4. 点击缓存提示 (tapCacheItems)
- **优先级**: 8
- **关闭等级**: 5级后关闭
- **触发条件**: 棋盘未满 + 缓存队列有物品
- **显示效果**: 在缓存区域显示手势动画
- **操作引导**: 引导玩家点击缓存区取出物品

```lua
function BoardPromptTapCacheItems:CanStart(boardView)
  return not boardView:GetModel():IsBoardFull() and 
         boardView:GetModel():GetCachedItemCount() > 0
end
```

### 🎲 5. 点击生成器提示 (tapSpreadItem)
- **优先级**: 11
- **关闭等级**: 5级后关闭
- **触发条件**: 棋盘未满 + 存在可产出订单需求物品的生成器
- **显示效果**: 在生成器上显示手势动画
- **操作引导**: 引导玩家点击生成器产出物品

#### 生成器提示逻辑
```lua
function BoardPromptTapSpreadItem:_CanStart(boardView)
  local filter = function(itemModel)
    local itemSpread = itemModel:GetComponent(ItemSpread)
    if itemSpread and itemSpread:GetState() == ItemSpreadState.Opened and 
       itemSpread:GetStorageRestNumber() ~= 0 then
      -- 检查生成器产出是否满足订单需求
      local generators = GM.ItemDataModel:GetModelConfig(itemSpread:GetItemModel():GetCode()).GeneratedItems
      for _, gen in pairs(generators) do
        if boardModel:IsUnfilledOrderRequirementsChainOrPdChainItem(gen.Code, filterDirect) then
          return true
        end
      end
    end
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter)
  return #self.m_itemModels ~= 0
end
```

### 🎯 6. 最后订单提示 (lastOrder)
- **优先级**: 6
- **关闭等级**: 5级后关闭
- **触发条件**: 只有一个订单时的智能操作提示
- **显示效果**: 根据操作类型显示不同效果
- **操作引导**: 提供完成最后订单的最优操作路径

#### 最后订单提示的复杂逻辑
1. **缓存物品检查**: 优先检查缓存中是否有需要的物品
2. **厨具状态检查**: 检查是否有已完成的菜品
3. **材料需求分析**: 分析订单需要的材料
4. **合成路径规划**: 规划最优的合成路径
5. **生成器利用**: 检查生成器是否能产出需要的物品

### 🏆 7. 收集物品提示 (collect)
- **优先级**: 未在配置中
- **触发条件**: 存在达到最高等级的可收集物品
- **显示效果**: 在可收集物品上显示手势动画
- **操作引导**: 引导玩家点击收集最高等级物品

```lua
function BoardPromptCollect:CanStart(boardView)
  local filter = function(itemModel)
    if itemModel:GetComponent(ItemCollectable) ~= nil then
      local itemType = itemModel:GetType()
      local chainId = GM.ItemDataModel:GetChainId(itemType)
      local maxLevel = GM.ItemDataModel:GetChainMaxLevel(chainId)
      local curLevel = GM.ItemDataModel:GetChainLevel(itemType)
      return maxLevel ~= nil and curLevel ~= nil and curLevel == maxLevel
    end
    return false
  end
  self.m_itemModels = boardView:GetModel():FilterItems(filter)
  return #self.m_itemModels ~= 0
end
```

## 提示优先级系统

### 优先级配置表
```lua
-- BoardPromptConfig.lua
{
  {Type = "finishOrder", CloseLevel = 5, Priority = 1},      -- 完成订单
  {Type = "finishTask", CloseLevel = 5, Priority = 2},       -- 完成任务
  {Type = "lastOrder", CloseLevel = 5, Priority = 6},        -- 最后订单
  {Type = "mergeItems", Priority = 7},                       -- 合成物品
  {Type = "tapCacheItems", CloseLevel = 5, Priority = 8},    -- 点击缓存
  {Type = "tapSpreadItem", CloseLevel = 5, Priority = 11},   -- 点击生成器
}
```

### 优先级选择逻辑
1. **按优先级排序**: 数字越小优先级越高
2. **等级限制检查**: 检查当前等级是否超过关闭等级
3. **条件验证**: 验证提示的触发条件
4. **首个匹配**: 选择第一个满足条件的提示

```lua
function BaseSceneBoardView:_SelectPrompt(prompts)
  local promptConfig = self.m_model:GetPromptConfig()
  local comparer = function(prompt1, prompt2)
    local priority1 = promptConfig[prompt1:GetType()].Priority
    local priority2 = promptConfig[prompt2:GetType()].Priority
    return priority1 < priority2
  end
  table.sort(prompts, comparer)
  for _, prompt in ipairs(prompts) do
    if prompt:IsOpen(promptConfig) and prompt:CanStart(self) then
      return prompt
    end
  end
  return nil
end
```

## 提示显示控制

### 开关控制
- **全局开关**: PlayerPrefs.OpenHint 控制是否显示提示
- **等级限制**: 超过指定等级自动关闭某些提示
- **特殊情况**: lastOrder提示不受全局开关影响

### 显示条件
- **UI状态**: 所有窗口关闭时才显示提示
- **动画状态**: 物品不在飞行或拖拽状态
- **教程状态**: 没有强制教程进行时
- **时间间隔**: 提示之间有固定的时间间隔

### 视觉效果
- **手势动画**: 点击、拖拽等操作演示
- **高亮效果**: 目标物品的高亮显示
- **箭头指示**: 方向性的操作指引
- **动画循环**: 持续的提示动画直到操作完成

## 智能算法特性

### 物品排序算法
```lua
function BoardPrompt.sortFunc(item1, item2)
  local level1 = GM.ItemDataModel:GetChainLevel(item1:GetCode())
  local level2 = GM.ItemDataModel:GetChainLevel(item2:GetCode())
  return level1 > level2  -- 优先选择等级高的物品
end
```

### 需求匹配算法
- **直接匹配**: 优先匹配直接满足订单的物品
- **链式匹配**: 匹配可以通过合成链达到订单需求的物品
- **生成器匹配**: 匹配生成器可以产出的物品

### 路径优化
- **最短路径**: 选择最少步骤完成订单的操作
- **资源优化**: 优先使用已有资源，减少浪费
- **时间优化**: 考虑生成器CD时间等因素

## 特殊机制

### 蛛网处理
- **包含蛛网**: 优先考虑包含蛛网的合成对
- **排除蛛网**: 在某些情况下排除蛛网物品
- **蛛网合成**: 特殊的蛛网合成逻辑

### 自动运行模式
- **提示简化**: 自动运行时简化某些提示
- **路径规划**: 更智能的操作路径规划
- **资源管理**: 考虑库存等更多因素

### 测试模式
- **测试提示**: 专门用于测试的提示类型
- **调试功能**: 开发阶段的调试提示
- **性能监控**: 提示系统的性能监控

这个提示系统通过复杂的算法和规则，为玩家提供了智能化的操作建议，大大提升了游戏的用户体验和操作效率。
