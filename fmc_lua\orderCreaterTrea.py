import os
import json
import re
from tkinter import *
from tkinter import ttk, messagebox, filedialog
from collections import defaultdict
import math

class OrderEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("订单编辑器 - 浮岛物语")
        self.root.geometry("1400x900")
        
        # 配置路径
        self.config_path = "d:\\MCWork\\GHabout\\FMC\\fmc_lua_1.18.5\\fmc_lua\\Data\\Config"
        self.item_config_path = os.path.join(self.config_path, "ItemModelConfig.lua")
        
        # 数据存储
        self.orders = {}
        self.items = {}  # 只存储it_和ds_开头的项目
        self.generators = {}
        self.loaded_chapters = set()
        
        # 界面变量
        self.chapter_var = StringVar(value="1")
        self.search_var = StringVar()
        self.order_id_var = StringVar()
        self.group_id_var = StringVar()
        self.req_type_var = StringVar()
        self.req_count_var = StringVar(value="1")
        self.reward_type_var = StringVar()
        self.reward_amount_var = StringVar(value="1")
        self.status_var = StringVar(value="就绪")
        self.pd_type_var = StringVar()  # Added initialization
        self.pd_level_var = StringVar() # Added initialization
        
        self.create_widgets()
        self.load_item_config()
        self.load_order_config()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：订单列表
        left_frame = ttk.LabelFrame(main_frame, text="订单列表")
        left_frame.pack(side=LEFT, fill=BOTH, expand=True, padx=(0, 5))
        
        # 章节和搜索控制
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=X, padx=5, pady=5)
        
        ttk.Label(control_frame, text="章节:").pack(side=LEFT)
        chapter_combo = ttk.Combobox(control_frame, textvariable=self.chapter_var, 
                                   values=[str(i) for i in range(1, 17)],
                                   state="readonly", width=5)
        chapter_combo.pack(side=LEFT, padx=(5, 10))
        chapter_combo.bind('<<ComboboxSelected>>', self.on_chapter_changed)
        
        ttk.Label(control_frame, text="搜索:").pack(side=LEFT)
        search_entry = ttk.Entry(control_frame, textvariable=self.search_var, width=15)
        search_entry.pack(side=LEFT, padx=(5, 0))
        search_entry.bind('<KeyRelease>', self.on_search_changed)
        
        # 订单树形视图
        columns = ('ID', '组', '章节', '需求数', '1级等效', '总能量')
        self.order_tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.order_tree.heading(col, text=col)
            if col == 'ID':
                self.order_tree.column(col, width=60)
            elif col in ['组', '章节', '需求数']:
                self.order_tree.column(col, width=50)
            else:
                self.order_tree.column(col, width=80)
        
        # 滚动条
        order_scrollbar = ttk.Scrollbar(left_frame, orient=VERTICAL, command=self.order_tree.yview)
        self.order_tree.configure(yscrollcommand=order_scrollbar.set)
        
        self.order_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=5, pady=5)
        order_scrollbar.pack(side=RIGHT, fill=Y)
        
        self.order_tree.bind('<<TreeviewSelect>>', self.on_order_select)
        
        # 右侧：订单详情
        right_frame = ttk.LabelFrame(main_frame, text="订单详情")
        right_frame.pack(side=RIGHT, fill=BOTH, expand=True, padx=(5, 0))
        
        # 订单基本信息
        info_frame = ttk.LabelFrame(right_frame, text="基本信息")
        info_frame.pack(fill=X, padx=5, pady=5)
        
        ttk.Label(info_frame, text="订单ID:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        ttk.Entry(info_frame, textvariable=self.order_id_var, width=15).grid(row=0, column=1, padx=5, pady=2)
        
        ttk.Label(info_frame, text="组ID:").grid(row=0, column=2, sticky=W, padx=5, pady=2)
        ttk.Entry(info_frame, textvariable=self.group_id_var, width=10).grid(row=0, column=3, padx=5, pady=2)
        
        # 需求物品
        req_frame = ttk.LabelFrame(right_frame, text="需求物品")
        req_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 需求控制
        req_control_frame = ttk.Frame(req_frame)
        req_control_frame.pack(fill=X, padx=5, pady=5)
        
        ttk.Label(req_control_frame, text="物品:").pack(side=LEFT)
        req_combo = ttk.Combobox(req_control_frame, textvariable=self.req_type_var, width=20)
        req_combo.pack(side=LEFT, padx=(5, 10))
        
        ttk.Label(req_control_frame, text="数量:").pack(side=LEFT)
        ttk.Entry(req_control_frame, textvariable=self.req_count_var, width=8).pack(side=LEFT, padx=(5, 10))
        
        ttk.Button(req_control_frame, text="添加", command=self.add_requirement).pack(side=LEFT, padx=5)
        ttk.Button(req_control_frame, text="删除", command=self.delete_requirement).pack(side=LEFT)
        
        # 需求列表
        req_columns = ('物品类型', '数量', '1级等效', '能量')
        self.req_tree = ttk.Treeview(req_frame, columns=req_columns, show='headings', height=8)
        
        for col in req_columns:
            self.req_tree.heading(col, text=col)
            if col == '物品类型':
                self.req_tree.column(col, width=150)
            else:
                self.req_tree.column(col, width=80)
        
        req_tree_scrollbar = ttk.Scrollbar(req_frame, orient=VERTICAL, command=self.req_tree.yview)
        self.req_tree.configure(yscrollcommand=req_tree_scrollbar.set)
        
        self.req_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=5, pady=5)
        req_tree_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 奖励
        reward_frame = ttk.LabelFrame(right_frame, text="奖励")
        reward_frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 奖励控制
        reward_control_frame = ttk.Frame(reward_frame)
        reward_control_frame.pack(fill=X, padx=5, pady=5)
        
        ttk.Label(reward_control_frame, text="类型:").pack(side=LEFT)
        reward_combo = ttk.Combobox(reward_control_frame, textvariable=self.reward_type_var, 
                                  values=["coin", "gem", "energy"], width=15)
        reward_combo.pack(side=LEFT, padx=(5, 10))
        
        ttk.Label(reward_control_frame, text="数量:").pack(side=LEFT)
        ttk.Entry(reward_control_frame, textvariable=self.reward_amount_var, width=8).pack(side=LEFT, padx=(5, 10))
        
        ttk.Button(reward_control_frame, text="添加", command=self.add_reward).pack(side=LEFT, padx=5)
        ttk.Button(reward_control_frame, text="删除", command=self.delete_reward).pack(side=LEFT)
        
        # 奖励列表
        reward_columns = ('类型', '数量')
        self.reward_tree = ttk.Treeview(reward_frame, columns=reward_columns, show='headings', height=6)
        
        for col in reward_columns:
            self.reward_tree.heading(col, text=col)
            self.reward_tree.column(col, width=100)
        
        reward_tree_scrollbar = ttk.Scrollbar(reward_frame, orient=VERTICAL, command=self.reward_tree.yview)
        self.reward_tree.configure(yscrollcommand=reward_tree_scrollbar.set)
        
        self.reward_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=5, pady=5)
        reward_tree_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 操作按钮
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="保存订单", command=self.save_order).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="添加订单", command=self.add_order).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="删除订单", command=self.delete_order).pack(side=LEFT, padx=5)
        
        # 状态栏
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=X, side=BOTTOM)
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=LEFT, padx=10, pady=5)
        
        # 设置组合框的值
        self.req_combo = req_combo
        self.reward_combo = reward_combo
        
    def load_item_config(self):
        """加载物品配置，提取Type和UseEnergy"""
        try:
            with open(self.item_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 正则表达式匹配每个物品条目，并尝试捕获 Type 和 UseEnergy
            # 匹配以 { Type = "item_type" 开头，并可选地包含 UseEnergy = X 的条目
            # item_block_pattern = re.compile(r"{\s*Type\s*=\s*\"(it_[^\"]+|ds_[^\"]+)\"([\s\S]*?)}", re.MULTILINE)
            # use_energy_pattern = re.compile(r"UseEnergy\s*=\s*(\d+)")

            # 更精确地匹配整个物品表块，然后从中提取所需字段
            # 首先找到所有顶级的表定义，通常以 { 开始，以 } 结束，并且包含 Type
            # 这仍然是一个简化版本，复杂的Lua结构可能需要更强大的解析器
            item_definitions = re.findall(r'{\s*Type\s*=\s*"((?:it|ds)_[^"]+)"[^}]*?UseEnergy\s*=\s*(\d+)[^}]*?}|{\s*Type\s*=\s*"((?:it|ds)_[^"]+)"[^}]*?}', content)

            temp_items_for_combobox = set()
            for match in item_definitions:
                item_type = match[0] or match[2] # Type 来自第一个捕获组或第三个
                use_energy_str = match[1] # UseEnergy 来自第二个捕获组
                
                if item_type:
                    self.items[item_type] = {}
                    temp_items_for_combobox.add(item_type)
                    if use_energy_str:
                        self.items[item_type]['UseEnergy'] = int(use_energy_str)
                    else:
                        self.items[item_type]['UseEnergy'] = 0 # 默认没有UseEnergy则为0
            
            # 更新需求和奖励部分的物品类型下拉框
            sorted_items = sorted(list(temp_items_for_combobox))
            self.req_type_combo['values'] = sorted_items
            self.reward_type_combo['values'] = sorted_items # 奖励类型可能也来自这些，或者需要单独的列表
            if sorted_items:
                self.req_type_var.set(sorted_items[0])
                self.reward_type_var.set(sorted_items[0])
            self.status_var.set(f"物品配置已加载 ({len(self.items)} 个条目)")
            print(f"Loaded {len(self.items)} items from ItemModelConfig.lua")
            # print(f"Sample items with UseEnergy: {{k: v for k, v in self.items.items() if 'UseEnergy' in v and v['UseEnergy'] > 0}}[:5]")

        except FileNotFoundError:
            messagebox.showerror("错误", f"物品配置文件未找到: {self.item_config_path}")
            self.status_var.set("错误: ItemModelConfig.lua 未找到")
        except Exception as e:
            messagebox.showerror("错误", f"加载物品配置失败: {str(e)}")
            self.status_var.set("加载物品配置失败")
    
    def _load_single_chapter(self, chapter):
        """加载单个章节的订单"""
        if chapter in self.loaded_chapters:
            return
            
        config_file = os.path.join(self.config_path, f"OrderFixedConfig_{chapter}.lua")
        if not os.path.exists(config_file):
            print(f"配置文件不存在: {config_file}")
            return
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除 return 和最外层的大括号，以便单独处理每个订单表
            content = content.strip()
            if content.startswith("return"):
                content = content[len("return"):].strip()
            if content.startswith("{") and content.endswith("}"):
                content = content[1:-1].strip()
            
            # 匹配每个订单块 (被 {} 包围的部分)
            # 这个正则表达式会尝试匹配最外层的独立 {}块
            # 使用非贪婪匹配 .*? 来避免一个块包含多个块的情况
            # 并确保大括号是正确配对的
            order_blocks_content = []
            brace_level = 0
            current_block = []
            for char in content:
                if char == '{':
                    brace_level += 1
                if brace_level > 0:
                    current_block.append(char)
                if char == '}':
                    brace_level -= 1
                    if brace_level == 0 and current_block:
                        order_blocks_content.append("".join(current_block))
                        current_block = []
            
            chapter_orders = 0
            for i, order_content_str in enumerate(order_blocks_content):
                # 确保提取的 order_content_str 是一个完整的 table 内容，不包含外层的逗号等
                # 再次去除可能存在的前后逗号和空格
                order_content_str = order_content_str.strip().strip(',') 
                print(f"--- Debug: Matched Order Block (Chapter {chapter}, Index {i+1}) ---")
                print(order_content_str)

                parsed_order = self.parse_order_block(order_content_str) # 传递单个订单的字符串
                if parsed_order:
                    # 如果 OrderFixedConfig 中的订单没有显式的索引，我们可以自己生成或依赖Id
                    # parsed_order['Index'] = i + 1 # 或者其他逻辑
                    self.orders[parsed_order['Id']] = parsed_order
                    chapter_orders += 1
                else:
                    print(f"--- Debug: Failed to parse order block (Chapter {chapter}, Index {i+1}) ---")

            if chapter_orders > 0:
                 self.loaded_chapters.add(chapter)
            print(f"章节 {chapter} 加载了 {chapter_orders} 个订单")
            
        except Exception as e:
            print(f"加载章节 {chapter} 订单配置失败: {e}")
            import traceback
            traceback.print_exc()
    
    def parse_order_block(self, order_block):
        try:
            # 提取基本信息
            id_match = re.search(r'Id\s*=\s*"([^"]+)"', order_block)
            chapter_match = re.search(r'ChapterId\s*=\s*(\d+)', order_block)
            group_match = re.search(r'GroupId\s*=\s*(\d+)', order_block)
            
            if not (id_match and chapter_match and group_match):
                print("--- Debug: Failed to match basic info (Id, ChapterId, GroupId) ---") # 新增日志
                print(f"Order Block Content:\n{order_block}") # 新增日志
                return None
            
            order_id = id_match.group(1)
            chapter_id = int(chapter_match.group(1))
            group_id = int(group_match.group(1))
            
            # 提取需求
            requirements = []
            req_pattern = r'Requirement_(\d+)\s*=\s*\{[^}]*Type\s*=\s*"([^"]+)"[^}]*Count\s*=\s*(\d+)[^}]*\}'
            req_matches = re.findall(req_pattern, order_block)
            
            for req_num, req_type, req_count in req_matches:
                if req_type.startswith('it_') or req_type.startswith('ds_'):
                    requirements.append({
                        'Type': req_type,
                        'Count': int(req_count)
                    })
            
            # 提取奖励
            rewards = []
            # 查找Rewards块
            rewards_match = re.search(r'Rewards\s*=\s*\{([^}]+(?:\{[^}]*\}[^}]*)*)\}', order_block, re.DOTALL)
            if rewards_match:
                rewards_content = rewards_match.group(1)
                # 提取每个奖励项
                reward_pattern = r'\{[^}]*Currency\s*=\s*"([^"]+)"[^}]*Amount\s*=\s*(\d+)[^}]*\}'
                reward_matches = re.findall(reward_pattern, rewards_content)
                
                for currency, amount in reward_matches:
                    rewards.append({
                        'Currency': currency,
                        'Amount': int(amount)
                    })
            
            # 提取PreId（如果存在）
            pre_id = None
            pre_match = re.search(r'PreId\s*=\s*"([^"]+)"', order_block)
            if pre_match:
                pre_id = pre_match.group(1)
            
            return {
                'Id': order_id,
                'ChapterId': chapter_id,
                'GroupId': group_id,
                'Requirements': requirements,
                'Rewards': rewards,
                'PreId': pre_id
            }
            
        except Exception as e:
            print(f"解析订单块失败: {e}")
            print(f"Order Block Content during exception:\n{order_block}") # 新增日志
            return None
    
    def load_order_config(self, chapter=None):
        """加载订单配置"""
        if chapter:
            self._load_single_chapter(chapter)
        else:
            # 加载所有章节
            for i in range(1, 17):
                self._load_single_chapter(i)
        
        self.update_order_list()
    
    def update_order_list(self):
        # 清空当前列表
        for item in self.order_tree.get_children():
            self.order_tree.delete(item)
        
        # 获取当前章节
        current_chapter = int(self.chapter_var.get())
        search_text = self.search_var.get().lower()
        
        # 确保章节已加载
        if current_chapter not in self.loaded_chapters:
            self._load_single_chapter(current_chapter)
        
        # 过滤订单
        filtered_orders = [
            order for order in self.orders.values()
            if order['ChapterId'] == current_chapter and
               (not search_text or search_text in order['Id'].lower())
        ]
        
        # 按索引排序
        filtered_orders.sort(key=lambda x: x.get('Index', 0))
        
        # 添加到树形视图
        for order in filtered_orders:
            req_count = len(order.get('Requirements', []))
            level1_equivalent = sum(self.calculate_level1_equivalent(req['Type'], req['Count']) 
                                  for req in order.get('Requirements', []))
            total_energy = sum(self.calculate_energy(req['Type'], req['Count']) 
                             for req in order.get('Requirements', []))
            
            self.order_tree.insert('', 'end', values=(
                order['Id'],
                order['GroupId'],
                order['ChapterId'],
                req_count,
                level1_equivalent,
                f"{total_energy:.1f}"
            ))
    
    def calculate_level1_equivalent(self, item_type, count):
        """计算1级等效数量"""
        if item_type.startswith('it_'):
            # 对于it_类型，根据等级计算
            parts = item_type.split('_')
            if len(parts) >= 4:
                try:
                    level = int(parts[3])
                    return count * (2 ** (level - 1))
                except ValueError:
                    pass
        elif item_type.startswith('ds_'):
            # 对于ds_类型，查找配方中的材料
            if item_type in self.items:
                # 这里可以根据实际的配方计算
                # 暂时返回固定值
                return count * 10
        
        return count
    
    def calculate_energy(self, item_type, count):
        """根据当前PD配置计算能量消耗"""
        if item_type.startswith('it_'):
            # 获取当前PD配置
            pd_type = self.pd_type_var.get()
            pd_level = self.pd_level_var.get()
            if pd_type and pd_level:
                full_pd_type = f"{pd_type}_{pd_level}"
                if full_pd_type in self.items:
                    base_energy = self.items[full_pd_type].get('UseEnergy', 0)
                    if base_energy > 0:
                        # 根据物品等级调整能量消耗
                        parts = item_type.split('_')
                        if len(parts) >= 4:
                            try:
                                level = int(parts[3])
                                level_multiplier = 1 + (level - 1) * 0.5  # 每级增加50%能量消耗
                                return count * base_energy * level_multiplier
                            except ValueError:
                                pass # Fall through to default base_energy if level parsing fails
                        return count * base_energy
        elif item_type.startswith('ds_'):
            # 对于ds_类型，可以根据其配方中的材料和当前PD配置计算能量
            # 这部分需要根据具体的配方逻辑来实现
            # Assuming pd_energy_var is meant to be used here, ensure it's initialized and has a default
            try:
                base_energy = float(self.pd_energy_var.get()) 
                return count * base_energy * 2  # ds_物品默认消耗更多能量
            except (AttributeError, ValueError):
                 # Handle cases where pd_energy_var doesn't exist or its value is not a float
                 # This part might need adjustment based on how pd_energy_var is intended to be used
                 # For now, returning 0 if it's not properly set up.
                 print(f"Warning: pd_energy_var not properly set for ds_ item {item_type}. Defaulting energy to 0.")
                 return 0.0 
        
        # Default return value if no other conditions are met
        return 0.0
        
    def on_chapter_changed(self, event=None):
        """章节改变事件"""
        chapter = int(self.chapter_var.get())
        if chapter not in self.loaded_chapters:
            self._load_single_chapter(chapter)
        self.update_order_list()
    
    def on_search_changed(self, event=None):
        """搜索改变事件"""
        self.update_order_list()
    
    def on_order_select(self, event=None):
        """订单选择事件"""
        selected = self.order_tree.selection()
        if not selected:
            return
        
        order_id = self.order_tree.item(selected[0])['values'][0]
        order = self.orders.get(order_id)
        
        if order:
            # 更新基本信息
            self.order_id_var.set(order['Id'])
            self.group_id_var.set(str(order['GroupId']))
            
            # 更新需求列表
            self.update_requirements_list(order['Requirements'])
            
            # 更新奖励列表
            self.update_rewards_list(order['Rewards'])
    
    def update_requirements_list(self, requirements):
        """更新需求列表"""
        # 清空当前列表
        for item in self.req_tree.get_children():
            self.req_tree.delete(item)
        
        # 添加需求
        for req in requirements:
            item_type = req.get("Type", "")
            count = req.get("Count", 0)
            level1_count = self.calculate_level1_equivalent(item_type, count)
            energy = self.calculate_energy(item_type, count)
            
            self.req_tree.insert("", "end", values=(
                item_type,
                count,
                level1_count,
                f"{energy:.1f}"
            ))
    
    def update_rewards_list(self, rewards):
        """更新奖励列表"""
        # 清空当前列表
        for item in self.reward_tree.get_children():
            self.reward_tree.delete(item)
        
        # 添加奖励
        for rew in rewards:
            self.reward_tree.insert("", "end", values=(
                rew.get("Currency", ""),
                rew.get("Amount", 0)
            ))
    
    def add_requirement(self):
        """添加需求物品"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            item_type = self.req_type_var.get().strip()
            if not item_type:
                messagebox.showwarning("警告", "请选择物品类型")
                return
                
            count = int(self.req_count_var.get())
            if count <= 0:
                raise ValueError("数量必须大于0")
                
            order_id = self.order_tree.item(selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order:
                if "Requirements" not in order:
                    order["Requirements"] = []
                    
                # 检查是否已存在相同类型的物品
                for req in order["Requirements"]:
                    if req["Type"] == item_type:
                        req["Count"] += count
                        break
                else:
                    order["Requirements"].append({
                        "Type": item_type,
                        "Count": count
                    })
                
                self.on_order_select(None)  # 刷新显示
                self.update_order_list()  # 更新订单列表
                self.status_var.set(f"已添加需求: {item_type} x{count}")
                
        except ValueError as ve:
            messagebox.showerror("错误", f"无效的数量: {str(ve)}")
        except Exception as e:
            messagebox.showerror("错误", f"添加需求失败: {str(e)}")
    
    def delete_requirement(self):
        """删除选中的需求物品"""
        selected = self.req_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个需求物品")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order and "Requirements" in order:
                item = self.req_tree.item(selected[0])
                item_type = item['values'][0]
                
                # 删除第一个匹配的物品类型
                for i, req in enumerate(order["Requirements"]):
                    if req["Type"] == item_type:
                        order["Requirements"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.update_order_list()  # 更新订单列表
                self.status_var.set(f"已删除需求: {item_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除需求失败: {str(e)}")
    
    def add_reward(self):
        """添加奖励"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            reward_type = self.reward_type_var.get().strip()
            if not reward_type:
                messagebox.showwarning("警告", "请选择奖励类型")
                return
                
            amount = int(self.reward_amount_var.get())
            if amount <= 0:
                raise ValueError("数量必须大于0")
                
            order_id = self.order_tree.item(selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order:
                if "Rewards" not in order:
                    order["Rewards"] = []
                
                # 检查是否已存在相同类型的奖励
                for reward in order["Rewards"]:
                    if reward["Currency"] == reward_type:
                        reward["Amount"] += amount
                        break
                else:
                    order["Rewards"].append({
                        "Currency": reward_type,
                        "Amount": amount
                    })
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已添加奖励: {reward_type} x{amount}")
                
        except ValueError as ve:
            messagebox.showerror("错误", f"无效的数量: {str(ve)}")
        except Exception as e:
            messagebox.showerror("错误", f"添加奖励失败: {str(e)}")
    
    def delete_reward(self):
        """删除选中的奖励"""
        selected = self.reward_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个奖励")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order and "Rewards" in order:
                item = self.reward_tree.item(selected[0])
                reward_type = item['values'][0]
                
                # 删除第一个匹配的奖励类型
                for i, reward in enumerate(order["Rewards"]):
                    if reward["Currency"] == reward_type:
                        order["Rewards"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已删除奖励: {reward_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除奖励失败: {str(e)}")
    
    def save_order(self):
        """保存订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            order_id = self.order_id_var.get()
            if not order_id:
                messagebox.showwarning("警告", "订单ID不能为空")
                return
                
            try:
                group_id = int(self.group_id_var.get())
            except (ValueError, TclError):
                messagebox.showerror("错误", "组ID必须是数字")
                return
            
            # 查找并更新订单
            if order_id in self.orders:
                order = self.orders[order_id]
                order['GroupId'] = group_id
                # 更新需求和奖励（如果需要从UI获取）
                # ... (代码来更新需求和奖励列表)
                
                # 更新到文件
                # self._write_orders_to_file(order['ChapterId']) # This line is commented out as _write_orders_to_file is not defined
                self.status_var.set(f"订单 {order_id} 已保存")
                self.update_order_list() # 刷新列表以显示更改
            else:
                messagebox.showerror("错误", f"未找到订单ID: {order_id}")

        except Exception as e: # 添加 except 块来处理异常
            messagebox.showerror("错误", f"保存订单失败: {str(e)}")
            self.status_var.set(f"保存订单失败: {str(e)}")
    
    def add_order(self):
        """添加新订单"""
        try:
            # 获取当前最大的订单ID并加1
            if self.orders:
                new_id = str(max([int(o["Id"]) for o in self.orders.values() if o["Id"].isdigit()], default=0) + 1)
            else:
                new_id = "1"
                
            # 获取组ID和章节ID，提供默认值1
            try:
                group_id = int(self.group_id_var.get()) if self.group_id_var.get() else 1
                chapter_id = int(self.chapter_var.get())
            except (ValueError, TclError):
                group_id = 1
                chapter_id = 1
                
            new_order = {
                "Id": new_id,
                "GroupId": group_id,
                "ChapterId": chapter_id,
                "Requirements": [],
                "Rewards": []
            }
            
            self.orders[new_id] = new_order
            self.update_order_list()
            
            # 选中新添加的订单
            for item in self.order_tree.get_children():
                if self.order_tree.item(item, 'values')[0] == new_id:
                    self.order_tree.selection_set(item)
                    self.order_tree.focus(item)
                    self.on_order_select(None)  # 更新详情显示
                    break
                    
            self.status_var.set(f"已添加新订单 {new_id}")
            
        except Exception as e:
            messagebox.showerror("错误", f"添加订单失败: {str(e)}")
            self.status_var.set("添加订单失败")
    
    def delete_order(self):
        """删除订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        order_id = self.order_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("确认", f"确定要删除订单 {order_id} 吗？"):
            if order_id in self.orders:
                del self.orders[order_id]
            self.update_order_list()
            self.status_var.set(f"订单 {order_id} 已删除")

if __name__ == "__main__":
    root = Tk()
    app = OrderEditor(root)
    root.mainloop()
    
    pd_frame = ttk.LabelFrame(details_frame, text="当前PD配置") 
    pd_frame.pack(fill=X, padx=5, pady=5)
    
    pd_type_frame = ttk.Frame(pd_frame)
    pd_type_frame.pack(fill=X, padx=5, pady=2)
    ttk.Label(pd_type_frame, text="PD类型:").pack(side=LEFT)
    self.pd_type_var = StringVar() # <--- self.pd_type_var is created here
    self.pd_type_combo = ttk.Combobox(pd_type_frame, textvariable=self.pd_type_var, state='readonly')
    self.pd_type_combo.pack(side=LEFT, padx=5)
    self.pd_type_combo.bind('<<ComboboxSelected>>', self.on_pd_changed)
    
    # ... (rest of pd_level and pd_energy UI elements) ...

    # Status Bar
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=X, side=BOTTOM)
    ttk.Label(status_frame, textvariable=self.status_var).pack(side=LEFT, padx=10, pady=5)
    # ... (status_frame setup) ...
    # --- ALL UI Elements Initialization END ---
    
    # --- Load Configurations (MUST be AFTER all UI elements are created) ---
    self.load_item_config() 
    self.load_order_config() # This will call update_order_list -> calculate_energy

    # ... rest of the class ...
    
    def on_chapter_changed(self, event=None):
        """章节改变事件"""
        chapter = int(self.chapter_var.get())
        if chapter not in self.loaded_chapters:
            self._load_single_chapter(chapter)
        self.update_order_list()
    
    def on_search_changed(self, event=None):
        """搜索改变事件"""
        self.update_order_list()
    
    def on_order_select(self, event=None):
        """订单选择事件"""
        selected = self.order_tree.selection()
        if not selected:
            return
        
        order_id = self.order_tree.item(selected[0])['values'][0]
        order = self.orders.get(order_id)
        
        if order:
            # 更新基本信息
            self.order_id_var.set(order['Id'])
            self.group_id_var.set(str(order['GroupId']))
            
            # 更新需求列表
            self.update_requirements_list(order['Requirements'])
            
            # 更新奖励列表
            self.update_rewards_list(order['Rewards'])
    
    def update_requirements_list(self, requirements):
        """更新需求列表"""
        # 清空当前列表
        for item in self.req_tree.get_children():
            self.req_tree.delete(item)
        
        # 添加需求
        for req in requirements:
            item_type = req.get("Type", "")
            count = req.get("Count", 0)
            level1_count = self.calculate_level1_equivalent(item_type, count)
            energy = self.calculate_energy(item_type, count)
            
            self.req_tree.insert("", "end", values=(
                item_type,
                count,
                level1_count,
                f"{energy:.1f}"
            ))
    
    def update_rewards_list(self, rewards):
        """更新奖励列表"""
        # 清空当前列表
        for item in self.reward_tree.get_children():
            self.reward_tree.delete(item)
        
        # 添加奖励
        for rew in rewards:
            self.reward_tree.insert("", "end", values=(
                rew.get("Currency", ""),
                rew.get("Amount", 0)
            ))
    
    def add_requirement(self):
        """添加需求物品"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            item_type = self.req_type_var.get().strip()
            if not item_type:
                messagebox.showwarning("警告", "请选择物品类型")
                return
                
            count = int(self.req_count_var.get())
            if count <= 0:
                raise ValueError("数量必须大于0")
                
            order_id = self.order_tree.item(selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order:
                if "Requirements" not in order:
                    order["Requirements"] = []
                    
                # 检查是否已存在相同类型的物品
                for req in order["Requirements"]:
                    if req["Type"] == item_type:
                        req["Count"] += count
                        break
                else:
                    order["Requirements"].append({
                        "Type": item_type,
                        "Count": count
                    })
                
                self.on_order_select(None)  # 刷新显示
                self.update_order_list()  # 更新订单列表
                self.status_var.set(f"已添加需求: {item_type} x{count}")
                
        except ValueError as ve:
            messagebox.showerror("错误", f"无效的数量: {str(ve)}")
        except Exception as e:
            messagebox.showerror("错误", f"添加需求失败: {str(e)}")
    
    def delete_requirement(self):
        """删除选中的需求物品"""
        selected = self.req_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个需求物品")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order and "Requirements" in order:
                item = self.req_tree.item(selected[0])
                item_type = item['values'][0]
                
                # 删除第一个匹配的物品类型
                for i, req in enumerate(order["Requirements"]):
                    if req["Type"] == item_type:
                        order["Requirements"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.update_order_list()  # 更新订单列表
                self.status_var.set(f"已删除需求: {item_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除需求失败: {str(e)}")
    
    def add_reward(self):
        """添加奖励"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            reward_type = self.reward_type_var.get().strip()
            if not reward_type:
                messagebox.showwarning("警告", "请选择奖励类型")
                return
                
            amount = int(self.reward_amount_var.get())
            if amount <= 0:
                raise ValueError("数量必须大于0")
                
            order_id = self.order_tree.item(selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order:
                if "Rewards" not in order:
                    order["Rewards"] = []
                
                # 检查是否已存在相同类型的奖励
                for reward in order["Rewards"]:
                    if reward["Currency"] == reward_type:
                        reward["Amount"] += amount
                        break
                else:
                    order["Rewards"].append({
                        "Currency": reward_type,
                        "Amount": amount
                    })
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已添加奖励: {reward_type} x{amount}")
                
        except ValueError as ve:
            messagebox.showerror("错误", f"无效的数量: {str(ve)}")
        except Exception as e:
            messagebox.showerror("错误", f"添加奖励失败: {str(e)}")
    
    def delete_reward(self):
        """删除选中的奖励"""
        selected = self.reward_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个奖励")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order and "Rewards" in order:
                item = self.reward_tree.item(selected[0])
                reward_type = item['values'][0]
                
                # 删除第一个匹配的奖励类型
                for i, reward in enumerate(order["Rewards"]):
                    if reward["Currency"] == reward_type:
                        order["Rewards"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已删除奖励: {reward_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除奖励失败: {str(e)}")
    
    def save_order(self):
        """保存订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            order_id = self.order_id_var.get()
            if not order_id:
                messagebox.showwarning("警告", "订单ID不能为空")
                return
                
            try:
                group_id = int(self.group_id_var.get())
            except (ValueError, TclError):
                messagebox.showerror("错误", "组ID必须是数字")
                return
            
            # 查找并更新订单
            if order_id in self.orders:
                order = self.orders[order_id]
                order['GroupId'] = group_id
                # 更新需求和奖励（如果需要从UI获取）
                # ... (代码来更新需求和奖励列表)
                
                # 更新到文件
                # self._write_orders_to_file(order['ChapterId']) # This line is commented out as _write_orders_to_file is not defined
                self.status_var.set(f"订单 {order_id} 已保存")
                self.update_order_list() # 刷新列表以显示更改
            else:
                messagebox.showerror("错误", f"未找到订单ID: {order_id}")

        except Exception as e: # 添加 except 块来处理异常
            messagebox.showerror("错误", f"保存订单失败: {str(e)}")
            self.status_var.set(f"保存订单失败: {str(e)}")
    
    def add_order(self):
        """添加新订单"""
        try:
            # 获取当前最大的订单ID并加1
            if self.orders:
                new_id = str(max([int(o["Id"]) for o in self.orders.values() if o["Id"].isdigit()], default=0) + 1)
            else:
                new_id = "1"
                
            # 获取组ID和章节ID，提供默认值1
            try:
                group_id = int(self.group_id_var.get()) if self.group_id_var.get() else 1
                chapter_id = int(self.chapter_var.get())
            except (ValueError, TclError):
                group_id = 1
                chapter_id = 1
                
            new_order = {
                "Id": new_id,
                "GroupId": group_id,
                "ChapterId": chapter_id,
                "Requirements": [],
                "Rewards": []
            }
            
            self.orders[new_id] = new_order
            self.update_order_list()
            
            # 选中新添加的订单
            for item in self.order_tree.get_children():
                if self.order_tree.item(item, 'values')[0] == new_id:
                    self.order_tree.selection_set(item)
                    self.order_tree.focus(item)
                    self.on_order_select(None)  # 更新详情显示
                    break
                    
            self.status_var.set(f"已添加新订单 {new_id}")
            
        except Exception as e:
            messagebox.showerror("错误", f"添加订单失败: {str(e)}")
            self.status_var.set("添加订单失败")
    
    def delete_order(self):
        """删除订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        order_id = self.order_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("确认", f"确定要删除订单 {order_id} 吗？"):
            if order_id in self.orders:
                del self.orders[order_id]
            self.update_order_list()
            self.status_var.set(f"订单 {order_id} 已删除")

if __name__ == "__main__":
    root = Tk()
    app = OrderEditor(root)
    root.mainloop()
    
    pd_frame = ttk.LabelFrame(details_frame, text="当前PD配置") 
    pd_frame.pack(fill=X, padx=5, pady=5)
    
    pd_type_frame = ttk.Frame(pd_frame)
    pd_type_frame.pack(fill=X, padx=5, pady=2)
    ttk.Label(pd_type_frame, text="PD类型:").pack(side=LEFT)
    self.pd_type_var = StringVar() # <--- self.pd_type_var is created here
    self.pd_type_combo = ttk.Combobox(pd_type_frame, textvariable=self.pd_type_var, state='readonly')
    self.pd_type_combo.pack(side=LEFT, padx=5)
    self.pd_type_combo.bind('<<ComboboxSelected>>', self.on_pd_changed)
    
    # ... (rest of pd_level and pd_energy UI elements) ...

    # Status Bar
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=X, side=BOTTOM)
    ttk.Label(status_frame, textvariable=self.status_var).pack(side=LEFT, padx=10, pady=5)
    # ... (status_frame setup) ...
    # --- ALL UI Elements Initialization END ---
    
    # --- Load Configurations (MUST be AFTER all UI elements are created) ---
    self.load_item_config() 
    self.load_order_config() # This will call update_order_list -> calculate_energy

    # ... rest of the class ...
    
    def on_chapter_changed(self, event=None):
        """章节改变事件"""
        chapter = int(self.chapter_var.get())
        if chapter not in self.loaded_chapters:
            self._load_single_chapter(chapter)
        self.update_order_list()
    
    def on_search_changed(self, event=None):
        """搜索改变事件"""
        self.update_order_list()
    
    def on_order_select(self, event=None):
        """订单选择事件"""
        selected = self.order_tree.selection()
        if not selected:
            return
        
        order_id = self.order_tree.item(selected[0])['values'][0]
        order = self.orders.get(order_id)
        
        if order:
            # 更新基本信息
            self.order_id_var.set(order['Id'])
            self.group_id_var.set(str(order['GroupId']))
            
            # 更新需求列表
            self.update_requirements_list(order['Requirements'])
            
            # 更新奖励列表
            self.update_rewards_list(order['Rewards'])
    
    def update_requirements_list(self, requirements):
        """更新需求列表"""
        # 清空当前列表
        for item in self.req_tree.get_children():
            self.req_tree.delete(item)
        
        # 添加需求
        for req in requirements:
            item_type = req.get("Type", "")
            count = req.get("Count", 0)
            level1_count = self.calculate_level1_equivalent(item_type, count)
            energy = self.calculate_energy(item_type, count)
            
            self.req_tree.insert("", "end", values=(
                item_type,
                count,
                level1_count,
                f"{energy:.1f}"
            ))
    
    def update_rewards_list(self, rewards):
        """更新奖励列表"""
        # 清空当前列表
        for item in self.reward_tree.get_children():
            self.reward_tree.delete(item)
        
        # 添加奖励
        for rew in rewards:
            self.reward_tree.insert("", "end", values=(
                rew.get("Currency", ""),
                rew.get("Amount", 0)
            ))
    
    def add_requirement(self):
        """添加需求物品"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            item_type = self.req_type_var.get().strip()
            if not item_type:
                messagebox.showwarning("警告", "请选择物品类型")
                return
                
            count = int(self.req_count_var.get())
            if count <= 0:
                raise ValueError("数量必须大于0")
                
            order_id = self.order_tree.item(selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order:
                if "Requirements" not in order:
                    order["Requirements"] = []
                    
                # 检查是否已存在相同类型的物品
                for req in order["Requirements"]:
                    if req["Type"] == item_type:
                        req["Count"] += count
                        break
                else:
                    order["Requirements"].append({
                        "Type": item_type,
                        "Count": count
                    })
                
                self.on_order_select(None)  # 刷新显示
                self.update_order_list()  # 更新订单列表
                self.status_var.set(f"已添加需求: {item_type} x{count}")
                
        except ValueError as ve:
            messagebox.showerror("错误", f"无效的数量: {str(ve)}")
        except Exception as e:
            messagebox.showerror("错误", f"添加需求失败: {str(e)}")
    
    def delete_requirement(self):
        """删除选中的需求物品"""
        selected = self.req_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个需求物品")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order and "Requirements" in order:
                item = self.req_tree.item(selected[0])
                item_type = item['values'][0]
                
                # 删除第一个匹配的物品类型
                for i, req in enumerate(order["Requirements"]):
                    if req["Type"] == item_type:
                        order["Requirements"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.update_order_list()  # 更新订单列表
                self.status_var.set(f"已删除需求: {item_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除需求失败: {str(e)}")
    
    def add_reward(self):
        """添加奖励"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            reward_type = self.reward_type_var.get().strip()
            if not reward_type:
                messagebox.showwarning("警告", "请选择奖励类型")
                return
                
            amount = int(self.reward_amount_var.get())
            if amount <= 0:
                raise ValueError("数量必须大于0")
                
            order_id = self.order_tree.item(selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order:
                if "Rewards" not in order:
                    order["Rewards"] = []
                
                # 检查是否已存在相同类型的奖励
                for reward in order["Rewards"]:
                    if reward["Currency"] == reward_type:
                        reward["Amount"] += amount
                        break
                else:
                    order["Rewards"].append({
                        "Currency": reward_type,
                        "Amount": amount
                    })
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已添加奖励: {reward_type} x{amount}")
                
        except ValueError as ve:
            messagebox.showerror("错误", f"无效的数量: {str(ve)}")
        except Exception as e:
            messagebox.showerror("错误", f"添加奖励失败: {str(e)}")
    
    def delete_reward(self):
        """删除选中的奖励"""
        selected = self.reward_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个奖励")
            return
            
        order_selected = self.order_tree.selection()
        if not order_selected:
            return
            
        try:
            order_id = self.order_tree.item(order_selected[0])['values'][0]
            order = self.orders.get(order_id)
            
            if order and "Rewards" in order:
                item = self.reward_tree.item(selected[0])
                reward_type = item['values'][0]
                
                # 删除第一个匹配的奖励类型
                for i, reward in enumerate(order["Rewards"]):
                    if reward["Currency"] == reward_type:
                        order["Rewards"].pop(i)
                        break
                
                self.on_order_select(None)  # 刷新显示
                self.status_var.set(f"已删除奖励: {reward_type}")
                
        except Exception as e:
            messagebox.showerror("错误", f"删除奖励失败: {str(e)}")
    
    def save_order(self):
        """保存订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        try:
            order_id = self.order_id_var.get()
            if not order_id:
                messagebox.showwarning("警告", "订单ID不能为空")
                return
                
            try:
                group_id = int(self.group_id_var.get())
            except (ValueError, TclError):
                messagebox.showerror("错误", "组ID必须是数字")
                return
            
            # 查找并更新订单
            if order_id in self.orders:
                order = self.orders[order_id]
                order['GroupId'] = group_id
                # 更新需求和奖励（如果需要从UI获取）
                # ... (代码来更新需求和奖励列表)
                
                # 更新到文件
                # self._write_orders_to_file(order['ChapterId']) # This line is commented out as _write_orders_to_file is not defined
                self.status_var.set(f"订单 {order_id} 已保存")
                self.update_order_list() # 刷新列表以显示更改
            else:
                messagebox.showerror("错误", f"未找到订单ID: {order_id}")

        except Exception as e: # 添加 except 块来处理异常
            messagebox.showerror("错误", f"保存订单失败: {str(e)}")
            self.status_var.set(f"保存订单失败: {str(e)}")
    
    def add_order(self):
        """添加新订单"""
        try:
            # 获取当前最大的订单ID并加1
            if self.orders:
                new_id = str(max([int(o["Id"]) for o in self.orders.values() if o["Id"].isdigit()], default=0) + 1)
            else:
                new_id = "1"
                
            # 获取组ID和章节ID，提供默认值1
            try:
                group_id = int(self.group_id_var.get()) if self.group_id_var.get() else 1
                chapter_id = int(self.chapter_var.get())
            except (ValueError, TclError):
                group_id = 1
                chapter_id = 1
                
            new_order = {
                "Id": new_id,
                "GroupId": group_id,
                "ChapterId": chapter_id,
                "Requirements": [],
                "Rewards": []
            }
            
            self.orders[new_id] = new_order
            self.update_order_list()
            
            # 选中新添加的订单
            for item in self.order_tree.get_children():
                if self.order_tree.item(item, 'values')[0] == new_id:
                    self.order_tree.selection_set(item)
                    self.order_tree.focus(item)
                    self.on_order_select(None)  # 更新详情显示
                    break
                    
            self.status_var.set(f"已添加新订单 {new_id}")
            
        except Exception as e:
            messagebox.showerror("错误", f"添加订单失败: {str(e)}")
            self.status_var.set("添加订单失败")
    
    def delete_order(self):
        """删除订单"""
        selected = self.order_tree.selection()
        if not selected:
            messagebox.showwarning("警告", "请先选择一个订单")
            return
            
        order_id = self.order_tree.item(selected[0])['values'][0]
        if messagebox.askyesno("确认", f"确定要删除订单 {order_id} 吗？"):
            if order_id in self.orders:
                del self.orders[order_id]
            self.update_order_list()
            self.status_var.set(f"订单 {order_id} 已删除")

if __name__ == "__main__":
    root = Tk()
    app = OrderEditor(root)
    root.mainloop()
