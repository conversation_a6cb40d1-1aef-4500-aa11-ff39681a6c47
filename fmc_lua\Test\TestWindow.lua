TestWindow = setmetatable({
  sortingOrder = ESpecialViewSortingOrder.TestWindow,
  canClickWindowMask = true
}, BaseWindow)
TestWindow.__index = TestWindow

function TestWindow:Init()
  self._level = GM.LevelModel:GetCurrentLevel()
  local bReachMax = GM.LevelModel:IfReachMaxLevel()
  local expCur = GM.PropertyDataManager:GetPropertyNum(EPropertyType.Experience)
  local expNeed = GM.LevelModel:GetLevelUpCost()
  self.m_expText.text = "exp:" .. expCur .. "/" .. (bReachMax and "-" or expNeed)
  self.m_levelInputField.text = tostring(self._level)
  self.m_addressableLoad.text = "Add失败-" .. (PlayerPrefs.GetInt(EPlayerPrefKey.TestAddressableLoadFileFail, 0) == 1 and "开" or "关")
  local returnUsrStr = ""
  if not GM.ReturnUserModel:IsOpen() then
    returnUsrStr = "未开启"
  elseif not GM.ReturnUserModel:IsInReturnRewardTime() then
    returnUsrStr = "非回归玩家"
  else
    returnUsrStr = returnUsrStr .. "\n\t回归玩家"
    local endTime = GM.ReturnUserModel:GetExpiredTime()
    returnUsrStr = returnUsrStr .. "\n\t回归持续到：" .. TimeUtil.ToDate(endTime, ETimeFormat.YMDHMS) .. ""
    local rewardFlag = GM.ReturnUserModel:GetReturnRewardFlag()
    local tempStr
    if StringUtil.IsNilOrEmpty(rewardFlag) then
      tempStr = "未发奖"
    elseif rewardFlag == "0" then
      tempStr = "已领奖"
    else
      tempStr = "未领奖"
    end
    returnUsrStr = returnUsrStr .. [[

	]] .. tempStr
  end
  self.m_userIdText.text = "UserId: " .. GM.UserModel:GetUserId() .. [[

group: ]] .. (GM.TestModel.abTestGroupInfo or "空") .. [[

server fileReplace: ]] .. json.encode(GM.ConfigModel:GetServerConfig(ServerConfigKey.FileReplace) or {}) .. [[

local fileReplace: ]] .. json.encode(GM.TestModel:GetTestFileReplaces() or {}) .. [[

return user info: ]] .. returnUsrStr .. [[

dbSize: ]] .. GM.DBTableManager.dataBase:GetFileSize() // 1024 .. "KB" .. [[

httpSaveCount: ]] .. Table.GetMapSize(GM.HttpManager.m_dbTable:GetAllInTable())
  self.m_propertyType = EPropertyType.Energy
  self:_UpdatePropertyInfo()
  self.m_selectServerDropdown.value = tonumber(string.sub(NetworkConfig.GetSelectedServer(), -1, -1)) - 1
  self.m_testServerSchema.text = PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema, "")
  self.m_gameModel = GM.GameModel
  self:UpdatePerSecond()
end

function TestWindow:UpdatePerSecond()
  if self.m_gameModel == nil then
    self.m_gameModel = GM.GameModel
  end
  if self.m_gameModel ~= nil then
    if self.m_gameModel:HasServerTime() then
      local serverTime = self.m_gameModel:GetServerTime()
      self.m_serverTimeText.text = "UTC:" .. TimeUtil.ToDate(serverTime, ETimeFormat.UTCYMDHMS) .. "\n时区:" .. TimeUtil.ToDate(serverTime, ETimeFormat.YMDHMS)
    else
      self.m_serverTimeText.text = "No Server Time"
    end
  end
end

function TestWindow:DeleteDataBase()
  GM.TestModel:DeleteDataBase()
end

function TestWindow:ClearData()
  GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "TIP", "Are you sure you want to clear local data?", "Confirm", "Cancel", function(tbWindow)
    GM.TestModel:ClearData(true)
  end, function(tbWindow)
    tbWindow:Close()
  end, nil, function(window)
    local curOrder = 29999
    window:SetSortingOrder(curOrder)
  end)
end

function TestWindow:RemoveItem()
  if BoardModelHelper.GetActiveModel() == nil then
    GM.UIManager:ShowPrompt("当前不在棋盘场景")
    return
  end
  GM.UIManager:OpenView(UIPrefabConfigName.TestRemoveItemWindow)
end

function TestWindow:ListItem()
  GM.UIManager:OpenView(UIPrefabConfigName.TestItemListWindow)
end

function TestWindow:ClearCacheRoot()
  GM.TestModel:RemoveCachedQueue()
end

function TestWindow:RemoveAll()
  GM.TestModel:RemoveAll()
end

function TestWindow:OpenFavorite()
  if TestFavoriteItemContent.GetInstance() ~= nil then
    GM.UIManager:ShowPrompt("已经打开")
    return
  end
  GM.TestModel:OpenFavoriteContent()
  self:Close()
end

function TestWindow:OpenAllItem()
  if TestAllItemContent.GetInstance() ~= nil then
    GM.UIManager:ShowPrompt("已经打开")
    return
  end
  GM.TestModel:OpenAllItemContent()
  self:Close()
end

function TestWindow:OnPropertyChanged()
  self.m_propertyType = self.m_propertyDropdown.captionText.text
end

function TestWindow:AddProperty()
  local value = tonumber(self:_GetCommonText())
  if self.m_propertyType == "dashtok" then
    for activityType, activityDefinition in pairs(DashActivityDefinition) do
      local model = GM.ActivityManager:GetModel(activityType)
      if model:GetState() ~= ActivityState.Ended and model:GetState() ~= ActivityState.Released then
        model:AddScore(value)
        local cell = MainBoardView.GetInstance():GetOrderArea():GetDashActivityOrder()
        if cell ~= nil then
          cell:UpdateScore()
        end
      end
    end
    return
  end
  if self.m_propertyType == "eventexp" then
    GM.UIManager:ShowPrompt("功能已删除")
    return
  end
  if self.m_propertyType == "spreeexp" then
    local activeModel = GM.ActivityManager:GetStartedSpreeActivity()
    if SpreeActivityDefinition[activeModel:GetType()].GameMode ~= GM.SceneManager:GetGameMode() then
      GM.UIManager:ShowPrompt("不在小副本棋盘场景内")
    else
      RewardApi.AcquireRewards({
        {
          [PROPERTY_TYPE] = EPropertyType.SpreeExp,
          [PROPERTY_COUNT] = value
        }
      }, EPropertySource.Give, EBIType.Test)
    end
    return
  end
  if self.m_propertyType == "passtok" then
    for activityType, activityDefinition in pairs(PassActivityDefinition) do
      local model = GM.ActivityManager:GetModel(activityType)
      if model:GetState() == ActivityState.Started then
        model:AcquireToken(value)
        EventDispatcher.DispatchEvent(activityDefinition.CanTakeRewardNumberChangedEvent)
        break
      end
    end
    return
  end
  if self.m_propertyType == "digtok" then
    for activityType, _ in pairs(DigActivityDefinition) do
      local model = GM.ActivityManager:GetModel(activityType)
      if model:GetState() ~= ActivityState.Ended and model:GetState() ~= ActivityState.Released then
        model:AddScore(value)
      end
    end
  end
  if self.m_propertyType == "porgtok" then
    for activityType, _ in pairs(ProgressActivityDefinition) do
      local model = GM.ActivityManager:GetModel(activityType)
      if model:GetState() == ActivityState.Started and model:CanAddScore() then
        model:_AddScore(value)
        return
      end
    end
  end
  if self.m_propertyType == "blindChestTok" then
    for activityType, activityDefinition in pairs(BlindChestDefinition) do
      local model = GM.ActivityManager:GetModel(activityType)
      if model:GetState() == ActivityState.Started and model:IsActivityOpen() then
        local rewards = {
          {
            [PROPERTY_TYPE] = activityDefinition.ActivityTokenPropertyType,
            [PROPERTY_COUNT] = value
          }
        }
        RewardApi.CryptRewards(rewards)
        RewardApi.AcquireRewards(rewards, EPropertySource.Give, EBIType.Test, nil)
        return
      end
    end
  end
  if value then
    if 0 <= value then
      local reward = {
        [PROPERTY_TYPE] = self.m_propertyType,
        [PROPERTY_COUNT] = value,
        [PROPERTY_CRYPT] = Crypt.CryptCurrency(value)
      }
      GM.PropertyDataManager:AcquireWithCollectAnimation({reward}, EPropertySource.Give, nil, nil, EBIType.Test)
    else
      GM.PropertyDataManager:Consume(self.m_propertyType, math.min(math.abs(value), GM.PropertyDataManager:GetPropertyNum(self.m_propertyType)), EBIType.Test, false, EBIType.Test)
    end
  end
end

function TestWindow:SetProperty()
  if self.m_propertyType == nil or not GM.PropertyDataManager:IsPropertyType(self.m_propertyType) then
    GM.UIManager:ShowPrompt("请选择正确的资产类型")
    return
  end
  local value = tonumber(self:_GetCommonText()) or 0
  local delta = value - GM.PropertyDataManager:GetPropertyNum(self.m_propertyType)
  local numGive = GM.PropertyDataManager:GetPropertyNum(self.m_propertyType, EPropertySource.Give)
  if 0 <= delta + numGive then
    if self.m_propertyType == EPropertyType.Energy and delta < 0 then
      GM.PropertyDataManager:Consume(self.m_propertyType, -delta, EBIType.Test, true)
    else
      local reward = {
        [PROPERTY_TYPE] = self.m_propertyType,
        [PROPERTY_COUNT] = delta,
        [PROPERTY_CRYPT] = Crypt.CryptCurrency(delta)
      }
      GM.PropertyDataManager:AcquireWithSimpleAnimation({reward}, EPropertySource.Give, nil, nil, EBIType.Test)
    end
  else
    local reward1 = {
      [PROPERTY_TYPE] = self.m_propertyType,
      [PROPERTY_COUNT] = 0 - numGive,
      [PROPERTY_CRYPT] = Crypt.CryptCurrency(0 - numGive)
    }
    GM.PropertyDataManager:AcquireWithSimpleAnimation({reward1}, EPropertySource.Give, nil, nil, EBIType.Test)
    local reward2 = {
      [PROPERTY_TYPE] = self.m_propertyType,
      [PROPERTY_COUNT] = delta + numGive,
      [PROPERTY_CRYPT] = Crypt.CryptCurrency(delta + numGive)
    }
    GM.PropertyDataManager:AcquireWithSimpleAnimation({reward2}, EPropertySource.Buy, nil, nil, EBIType.Test)
  end
end

function TestWindow:AddItem()
  local strInput = self:_GetCommonText()
  GM.TestModel:AddItem(strInput)
end

function TestWindow:SetCurTask()
  local chapterName, taskId = self.GetChapterAndId(self:_GetCommonText())
  if taskId then
    self.FinishToTask(chapterName, taskId, function()
      self:RestartGame()
    end)
  else
    GM.UIManager:ShowPrompt("设置失败")
  end
end

function TestWindow.FinishToTask(chapterName, setTaskId, callback)
  GM.TestModel.isSkipping = true
  PlayerPrefs.SetInt(EPlayerPrefKey.TestEnableBackwardProgress, 1)
  EventDispatcher.RemoveTarget(GM.ChapterManager:GetActiveRoomView())
  local forward = true
  local taskModel = GM.TaskManager
  local chapterId = GM.ChapterDataModel:GetChapterIdByName(chapterName)
  if taskModel:IsTaskFinished(chapterId, setTaskId) then
    forward = false
    taskModel:GetMetaData():Clear()
    taskModel:GetFinishedTaskData():Clear()
    taskModel:OnLoadFileConfigFinished()
    taskModel:LateInit()
    GM.ChapterManager:ChangeChapter(GM.ChapterDataModel:GetChapterNameById(1))
    GM.TestModel:ReleaseChapterTemporarily()
  end
  GM.UIManager:ShowPrompt("操作中，请稍候..")
  GM.UIManager:SetEventLock(true)
  DelayExecuteFunc(function()
    GM.UIManager:SetEventLock(false)
    local arrOngoingTaskIds = taskModel:GetOngoingTasks()
    while arrOngoingTaskIds[1] and (arrOngoingTaskIds[1] ~= setTaskId or taskModel:GetOngoingChapterName() ~= chapterName) do
      taskModel:OnTaskFinished(arrOngoingTaskIds[1])
      taskModel:ClaimProgressReward()
      if not arrOngoingTaskIds[1] then
        GM.TaskManager:TryStartNewChapter()
        GM.ChapterManager:ChangeChapter(taskModel:GetOngoingChapterName())
      end
    end
    if not forward and PlayerPrefs.GetInt(EPlayerPrefKey.TestEnableBackwardProgress, 0) == 1 then
      local newUuid = PlatformInterface.GenerateUUID()
      GM.UserModel:SetInstallUuid(newUuid)
    end
    GM.LevelModel:TryLevelUp()
    GM.TestModel.isSkipping = false
    if callback then
      callback()
    end
  end, 0.5)
end

function TestWindow.GetChapterAndId(str)
  local splits = StringUtil.Split(str, "@")
  local chapterName = GM.TaskManager:GetOngoingChapterName()
  if 1 < #splits and tonumber(splits[1]) then
    chapterName = GM.ChapterDataModel:GetChapterNameById(tonumber(splits[1]))
  end
  local taskId = 1 < #splits and splits[2] or splits[1]
  return chapterName, tonumber(taskId), taskId
end

function TestWindow:PlayTimeline()
  if GM.TimelineManager:IsPlayingTimeline() then
    GM.UIManager:ShowPrompt("当前已有时间在播放中！")
    return
  end
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    GM.UIManager:ShowPrompt("当前不在场景！")
    return
  end
  local chapterName, taskId, timelineId = self.GetChapterAndId(self:_GetCommonText())
  if StringUtil.IsNilOrEmpty(timelineId) then
    chapterName = GM.TaskManager:GetOngoingChapterName()
    taskId = GM.TaskManager:GetOngoingTask()
    timelineId = taskId
  end
  GM.TimelineDataModel:_GetMainlineTimelineData(taskId, chapterName, false, function(timelineData)
    if timelineData then
      self:_PlayTimelineWithData(timelineData)
    else
      GM.TimelineDataModel:_GetSpecialTimelineData(timelineId, false, function(specialTimelineData)
        if not specialTimelineData then
          GM.UIManager:ShowPrompt("当前打开的章节找不到时间线: " .. timelineId)
          self:Close()
          return
        end
        self:_PlayTimelineWithData(specialTimelineData)
      end)
    end
  end)
end

function TestWindow:PlayTimelineForEditor(configs)
  local mapTimelineData = TimelineDataModel:_ParseConfigs(configs, GM.ChapterManager.curActiveChapterName)
  local timelineId = next(mapTimelineData)
  self:_PlayTimelineWithData(mapTimelineData[timelineId])
end

function TestWindow:_PlayTimelineWithData(timelineData)
  self:Close()
  if GM.SceneManager:GetGameMode() ~= EGameMode.Main then
    GM.SceneManager:ChangeGameMode(EGameMode.Main)
  end
  timelineData:Init()
  if timelineData.chapterName then
    if timelineData.chapterName == GM.ChapterManager.curActiveChapterName then
      self:_PlayMainlineTimelineWithData(timelineData)
    else
      GM.SceneManager:ChangeChapterWithTransition(timelineData.chapterName, nil, function()
        self:_PlayMainlineTimelineWithData(timelineData)
      end)
    end
  else
    GM.TimelineManager:_StartPlay(timelineData)
    GM.TimelineManager.m_bInTimeline = true
  end
end

function TestWindow:_PlayMainlineTimelineWithData(timelineData)
  GM.TimelineManager.m_bInTimeline = true
  GM.TimelineManager:_StartPlay(timelineData, function()
    local roomView = GM.ChapterManager:GetActiveRoomView()
    roomView:UpdateToLogicChoices(function()
      roomView:_RefreshCharacters()
    end)
  end)
end

function TestWindow:Add10KGem()
  local rewards = {
    {
      [PROPERTY_TYPE] = EPropertyType.Gem,
      [PROPERTY_COUNT] = 10000,
      [PROPERTY_CRYPT] = Crypt.CryptCurrency(10000)
    }
  }
  GM.PropertyDataManager:AcquireWithCollectAnimation(rewards, EPropertySource.Give, nil, nil, EBIType.Test)
end

function TestWindow:Add10KGold()
  local rewards = {
    {
      [PROPERTY_TYPE] = EPropertyType.Gold,
      [PROPERTY_COUNT] = 10000,
      [PROPERTY_CRYPT] = Crypt.CryptCurrency(10000)
    }
  }
  GM.PropertyDataManager:AcquireWithCollectAnimation(rewards, EPropertySource.Give, nil, nil, EBIType.Test)
end

function TestWindow:CompleteRoom()
  local value = tonumber(self:_GetCommonText())
  if value then
    local chapterId = tonumber(value)
    local chapterName = GM.ChapterDataModel:GetChapterNameById(chapterId)
    if not chapterName then
      GM.UIManager:ShowPrompt("不存在此章节数量")
      return
    end
    local arrTaskStarters = GM.TaskDataModel:GetTaskStartersByChapter(chapterName)
    local lastTaskId = arrTaskStarters[#arrTaskStarters]:GetTaskId()
    local taskData = GM.TaskDataModel:GetTaskData(chapterName, lastTaskId)
    if taskData then
      self.FinishToTask(chapterName, lastTaskId, function()
        GM.TaskManager:TryStartNewChapter()
        local arrOngoingTaskIds = GM.TaskManager:GetOngoingTasks()
        Log.Assert(#arrOngoingTaskIds == 1, "逻辑错误")
        GM.TaskManager:OnTaskFinished(lastTaskId)
        GM.TestModel:ForceRemoveCurrentOrders()
        local orderModel = GM.MainBoardModel:GetOrderModel()
        orderModel:OnCompleteRoom(chapterId)
        self:RestartGame()
      end)
    else
      GM.UIManager:ShowPrompt("设置失败")
      return
    end
  end
end

function TestWindow:CompleteAllRooms()
  local chapterId = GM.ChapterDataModel:GetChapterCount()
  local chapterName = GM.ChapterDataModel:GetChapterNameById(chapterId)
  local arrTaskStarters = GM.TaskDataModel:GetTaskStartersByChapter(chapterName)
  local lastTaskId = arrTaskStarters[#arrTaskStarters]:GetTaskId()
  self.FinishToTask(chapterName, lastTaskId, function()
    GM.TaskManager:TryStartNewChapter()
    local arrOngoingTaskIds = GM.TaskManager:GetOngoingTasks()
    Log.Assert(#arrOngoingTaskIds == 1, "逻辑错误")
    GM.TaskManager:OnTaskFinished(lastTaskId)
    GM.TestModel:ForceRemoveCurrentOrders()
    local orderModel = GM.MainBoardModel:GetOrderModel()
    orderModel:OnCompleteRoom(chapterId)
    orderModel:FinishCurrentAllOrders()
    self:RestartGame()
  end)
end

function TestWindow:OpenTutorialInfoWindow()
  GM.UIManager:OpenView(UIPrefabConfigName.TestTutorialInfoWindow)
end

function TestWindow:OpenPlayerprefsWindow()
  GM.UIManager:OpenView(UIPrefabConfigName.TestPlayerprefsWindow)
end

function TestWindow:_GetCommonText()
  return self.m_commonInputField.text
end

function TestWindow:_UpdatePropertyInfo()
  local model = GM.BIManager
  self.m_propertyInfoText.text = "钻石消耗：" .. model:GetInNumber(EBISyncKey.ConsumedFreeGem) .. " + " .. model:GetInNumber(EBISyncKey.ConsumedPaidGem) .. "; 棋盘收集：" .. model:GetInNumber(EPropertyTestKey.GemAcquireFromBoardCollect) .. "\n金币消耗：" .. model:GetInNumber(EPropertyTestKey.ConsumedGold) .. "; 棋盘收集：" .. model:GetInNumber(EPropertyTestKey.GoldAcquireFromBoardCollect) .. "\n体力消耗：" .. model:GetInNumber(EBISyncKey.ConsumedEnergy) .. "\n停留时间：" .. model:GetGameDuration()
end

function TestWindow:OnResetEnergyConsume()
  GM.BIManager:ChangeNumber(EBISyncKey.ConsumedEnergy, -GM.BIManager:GetInNumber(EBISyncKey.ConsumedEnergy))
  self:_UpdatePropertyInfo()
end

function TestWindow:RestartGame()
  GM:RestartGame(nil, EBIProjectType.RestartGameAction.Test)
end

function TestWindow:UploadData()
  GM.SyncModel:UploadData(function(success)
    GM.UIManager:ShowPrompt("上传" .. (success and "成功" or "失败"))
  end)
end

function TestWindow:DownloadData()
  GM.SyncModel:DownloadData()
end

function TestWindow:CheckUpload()
  local shouldUpload = GM.SyncModel:CheckUpload(false, function(success)
    GM.UIManager:ShowPrompt("上传" .. (success and "成功" or "失败"))
  end)
  if shouldUpload then
    GM.UIManager:ShowPrompt("开始上传数据")
  else
    GM.UIManager:ShowPrompt("无需上传数据")
  end
end

function TestWindow:ClearServerData()
  GM.TestModel:ClearServerData(true)
end

function TestWindow:OnServerSelected()
  local selectedServer = self.m_selectServerDropdown.captionText.text
  local selectedSchema = self.m_testServerSchema.text or ""
  if selectedServer ~= NetworkConfig.GetSelectedServer() or selectedSchema ~= PlayerPrefs.GetString(EPlayerPrefKey.TestServerSchema, "") then
    GM.TestModel:ClearData(false)
    NetworkConfig.SetSelectedServer(selectedServer)
    PlayerPrefs.SetString(EPlayerPrefKey.TestServerSchema, selectedSchema)
    PlatformInterface.ExitGame()
  else
    GM.UIManager:ShowPrompt("服务器和 Schema 均无修改。")
  end
end

function TestWindow:OnLocalBackupDataBtnClicked()
  GM.TestModel:InitBackupInfo()
  GM.UIManager:OpenView(UIPrefabConfigName.TestLocalBackupWindow)
end

function TestWindow:OnServerBackupDataBtnClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.TestServerBackupWindow)
end

function TestWindow:OnCodeEditClick()
  GM.UIManager:OpenView(UIPrefabConfigName.TestCodeEditWindow)
end

function TestWindow:OnRemoteCodeClick()
  GM.UIManager:OpenView(UIPrefabConfigName.TestLuaRemoteWindow)
end

function TestWindow:OnDebugAddressClick()
  GM.UIManager:OpenView(UIPrefabConfigName.TestDebugAddressWindow)
end

function TestWindow:OnFinishToOrderGroupClick()
  GM.TestModel.isSkipping = true
  PlayerPrefs.SetInt(EPlayerPrefKey.TestEnableBackwardProgress, 1)
  local chapterName, targetGroupId = self.GetChapterAndId(self:_GetCommonText())
  local chapterId = GM.ChapterDataModel:GetChapterIdByName(chapterName)
  local orderModel = GM.MainBoardModel:GetOrderModel()
  local currentChapterId, currentGroupId = orderModel:GetOrderGroupInfo()
  if currentChapterId == chapterId and currentGroupId == targetGroupId then
    GM.UIManager:ShowPrompt("当前已经在该订单组！")
    return
  elseif chapterId < currentChapterId or chapterId == currentChapterId and targetGroupId < currentGroupId then
    GM.UIManager:ShowPrompt("不支持回退订单组，请清数据重来！")
    return
  end
  GM.UIManager:ShowPrompt("操作中，请稍候..")
  DelayExecuteFuncInView(function()
    local mapOrders = orderModel:GetOrders()
    local index, order = next(mapOrders)
    local taskManager = GM.TaskManager
    while order and (order:GetChapterId() ~= chapterId or order:GetGroupId() ~= targetGroupId) do
      order:SetFinished()
      RewardApi.AcquireRewardsLogic(order:GetRewards(), EPropertySource.Give, EBIType.FinishOrder, EGameMode.Board, CacheItemType.Stack)
      orderModel:FinishOrder(order, {})
      index, order = next(mapOrders)
      if not order then
        if orderModel:CanClaimGroupReward() then
          orderModel:ClaimGroupReward()
          orderModel:TryFillNextGroupOrder()
        end
        index, order = next(mapOrders)
        if not order then
          repeat
            local canFinishTask = taskManager:CanFinishOngoingTask()
            if canFinishTask then
              taskManager:OnTaskFinished(taskManager:GetOngoingTask())
              taskManager:ClaimProgressReward()
              if not taskManager:GetOngoingTask() then
                taskManager:TryStartNewChapter()
                break
              end
            else
              break
            end
          until false
          index, order = next(mapOrders)
          if not orderModel:IsCurChapterCanUnlockGroupFinished() then
            Log.Assert(index ~= nil, "跳订单组金币不足！")
          end
        end
      end
    end
    repeat
      local canFinishTask = taskManager:CanFinishOngoingTask()
      if not canFinishTask then
        break
      end
      taskManager:OnTaskFinished(taskManager:GetOngoingTask())
      taskManager:ClaimProgressReward()
      if taskManager:GetOngoingTask() then
      else
        break
      end
    until false
    GM.LevelModel:TryLevelUp()
    GM.TestModel.isSkipping = false
    GM:RestartGame(nil, EBIProjectType.RestartGameAction.Test)
  end, 0.5, self, true)
end

function TestWindow:OnAutoRunClicked()
  self:Close()
  if GM.UIManager:IsViewExisting(UIPrefabConfigName.TestAutoTimelineView) then
    return
  end
  GM.UIManager:OpenView(UIPrefabConfigName.TestAutoTimelineView)
end

function TestWindow:OnMuteDownloadClicked()
  if PlayerPrefs.GetInt(EPlayerPrefKey.MuteDownload, 0) == 0 then
    GM.UIManager:ShowPrompt("开启静默下载")
    PlayerPrefs.SetInt(EPlayerPrefKey.MuteDownload, 1)
  else
    GM.UIManager:ShowPrompt("关闭静默下载")
    PlayerPrefs.SetInt(EPlayerPrefKey.MuteDownload, 0)
  end
end

function TestWindow:OnClickLanguageSetting()
  GM.UIManager:OpenView(UIPrefabConfigName.TestLanguageSettingWindow)
  self:Close()
end

function TestWindow:OnProgressClicked()
  self:Close()
  if GM.AccountManager:HasAccount() then
    GM.UIManager:OpenView(UIPrefabConfigName.SignOutWindow)
  else
    GM.UIManager:OpenView(UIPrefabConfigName.SignInWindow)
  end
end

function TestWindow:OnProfilingClicked()
  self:Close()
  GM.UIManager:OpenView(UIPrefabConfigName.ProfilingWindow)
end

function TestWindow:InitMatchList()
  local list = {}
  local arrChapterSequence = GM.ChapterDataModel:GetChapterSequence()
  for index, chapterName in ipairs(arrChapterSequence) do
    local mapTaskData = GM.TaskDataModel:GetTaskDatasByChapter(chapterName)
    for taskId, data in pairs(mapTaskData) do
      list[#list + 1] = "[任务]#" .. index .. "@" .. taskId .. "#" .. data:_GetSlotIdsStr()
    end
    local orderGroupConfigs = require("Data.Config.OrderGroupConfig_" .. index)
    for orderGroupIndex = 1, #orderGroupConfigs do
      list[#list + 1] = "[订单组]#" .. index .. "@" .. orderGroupIndex .. "#Day:" .. orderGroupConfigs[orderGroupIndex].Day
    end
  end
  table.sort(list)
  for itemId, _ in pairs(GM.ItemDataModel.m_mapModelConfigs) do
    local name = GM.GameTextModel:GetText("item_" .. itemId .. "_name")
    list[#list + 1] = "[棋子]" .. name .. "#" .. itemId
  end
  self.m_matchList = list
end

function TestWindow:_OnInputPropertyChange()
  if not self.m_matchList then
    self:InitMatchList()
  end
  if not self.m_MatchListLuaTable.gameObject.activeSelf then
    self.m_MatchListLuaTable.gameObject:SetActive(true)
    self.m_MatchListLuaTable:Init(self.m_matchList, function(str)
      local arr = StringUtil.Split(str, "#")
      self.m_commonInputField.text = arr[2]
      self.m_MatchListLuaTable.gameObject:SetActive(false)
      if arr[1] == "[任务]" then
        self:SetCurTask()
      elseif arr[1] == "[订单组]" then
        self:OnFinishToOrderGroupClick()
      end
    end)
  end
  self.m_MatchListLuaTable:UpdateListView(self.m_commonInputField.text)
end

function TestWindow:_OnBgClicked()
  if self.m_MatchListLuaTable.gameObject.activeSelf then
    self.m_MatchListLuaTable.gameObject:SetActive(false)
  end
end

function TestWindow:HideTestInfo()
  GM.UIManager:HideTestInfo()
end

function TestWindow:OnTestAddressableLoadFail()
  local cur = PlayerPrefs.GetInt(EPlayerPrefKey.TestAddressableLoadFileFail, 0)
  if cur == 1 then
    PlayerPrefs.SetInt(EPlayerPrefKey.TestAddressableLoadFileFail, 0)
  else
    PlayerPrefs.SetInt(EPlayerPrefKey.TestAddressableLoadFileFail, 1)
  end
  self.m_addressableLoad.text = "Add失败-" .. (PlayerPrefs.GetInt(EPlayerPrefKey.TestAddressableLoadFileFail, 0) == 1 and "开" or "关")
end

function TestWindow:ExpireToken()
  GM.UIManager:ShowMask()
  local reqCtx = CSNetLibManager:CreateGeneralHttpRequest("https://cola-test-01.global.corp.merge.fun/server/create_expired_token?userid=" .. GM.UserModel:GetUserId(), "GET", 3000, 0)
  reqCtx:SetHeader(NetworkConfig.ContentTypeKey, "application/octet-stream")
  reqCtx:SetCallback(function()
    GM.UIManager:HideMask()
    if reqCtx.Rcode == ResultCode.Succeeded then
      local rawData = reqCtx:GetResponseString()
      rawData = StringUtil.Replace(rawData, "\n", "")
      GM.SsoManager:SetToken(rawData)
      GM.UIManager:ShowPrompt("token即将于1分钟后过期")
    else
      GM.UIManager:ShowPrompt("失败，请重试")
    end
  end)
  reqCtx:Send()
end

function TestWindow:OnShowFileReplaceClicked()
  local testText = "<color=#FF0000>本地 Test FileReplace:</color>\n"
  local testFileReplaces = GM.TestModel:GetTestFileReplaces()
  for file, suffix in pairs(testFileReplaces) do
    testText = testText .. file .. "_" .. suffix .. "\n"
  end
  local text = "\n<color=#FF0000>后台 FileReplace:</color>\n"
  local configs = GM.ConfigModel:GetServerConfig(ServerConfigKey.FileReplace)
  local config
  if configs then
    for i = 1, #configs do
      config = configs[i]
      text = text .. config.file .. config.suffix .. "\n"
    end
  end
  GM.UIManager:OpenView(UIPrefabConfigName.TestTextWindow, "FileReplace 情况", testText .. text)
  self:Close()
end

function TestWindow:OnTestFileReplaceClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.TestFileReplaceWindow)
  self:Close()
end

function TestWindow:OnVibrateLightClicked()
  PlatformInterface.Vibrate(EVibrationType.Light)
end

function TestWindow:OnVibrateMediumClicked()
  PlatformInterface.Vibrate(EVibrationType.Medium)
end

function TestWindow:OnVibrateHeavyClicked()
  PlatformInterface.Vibrate(EVibrationType.Heavy)
end

function TestWindow:SetEnergyGenerationTime()
  GM.EnergyModel:SetGenerationTime(EnergyType.Main, GM.GameModel:GetServerTime() - EnergyModel.TimePerEnergy + 10)
  self:Close()
end

function TestWindow:AddLevel()
  self._level = self._level + 1
  self.m_levelInputField.text = tostring(self._level)
end

function TestWindow:SubLevel()
  local curLevel = GM.LevelModel:GetCurrentLevel()
  self._level = curLevel >= self._level and curLevel or self._level - 1
  self.m_levelInputField.text = tostring(self._level)
end

function TestWindow:ConfirmLevel()
  GM.LevelModel:SyncLevel(tonumber(self.m_levelInputField.text))
  GM.GameModel:Login()
  EventDispatcher.DispatchEvent(EEventType.LevelUp, GM.LevelModel:GetCurrentLevel())
end

function TestWindow:OnShowCenterClicked()
  local go = GameObject()
  go:AddComponent(typeof(RectTransform))
  go.transform:SetParent(GM.UIManager:GetCanvasRoot(), false)
  go.transform.sizeDelta = Vector2(30, 30)
  local canvas = go:AddComponent(typeof(CS.UnityEngine.Canvas))
  canvas.overrideSorting = true
  canvas.sortingOrder = 30000
  local image = go:AddComponent(typeof(Image))
  image.color = CSColor.red
  local uiLayer = LayerMask.NameToLayer("UI")
  go.transform:SetLayerRecursively(uiLayer)
end

function TestWindow:OpenServerConfigInfoWindow()
  GM.UIManager:OpenView(UIPrefabConfigName.TestServerConfigInfoWindow)
end

function TestWindow:UnlockDiscoveries()
  GM.TestModel.isSkipping = true
  local itemDataModel = GM.ItemDataModel
  for itemType, _ in pairs(itemDataModel.m_mapModelConfigs) do
    itemDataModel:SetUnlocked(itemType)
  end
  GM.TestModel.isSkipping = false
end

function TestWindow:ClaimDiscoveries()
  GM.TestModel.isSkipping = true
  local itemDataModel = GM.ItemDataModel
  for itemType, _ in pairs(itemDataModel.m_mapModelConfigs) do
    if itemDataModel:GetUnlockState(itemType) == EItemUnlockState.Unlocked then
      itemDataModel:ClaimUnlockedRewards(itemType)
    end
  end
  GM.TestModel.isSkipping = false
end

function TestWindow:FillInventory()
  GM.TestModel.isSkipping = true
  local boardModel = GM.MainBoardModel
  local capacity = boardModel:GetStoreSlotCount()
  local inStoreCount = boardModel:GetStoredItemCount()
  local needCount = capacity - inStoreCount
  local arrBoardItems = boardModel:FilterItems(function(item)
    return boardModel:CanItemMove(item)
  end, needCount, false)
  for i = inStoreCount + 1, capacity do
    if arrBoardItems[1] then
      boardModel:StoreItem(arrBoardItems[1])
      table.remove(arrBoardItems, 1)
    else
      local newItem = boardModel:GenerateItem(nil, "it_1_1_1")
      boardModel.m_itemStoreModel:AddItem(newItem)
    end
  end
  EventDispatcher.DispatchEvent(EEventType.ItemStored)
  GM.TestModel.isSkipping = false
end

function TestWindow:OnTreasureDigClicked()
  GM.UIManager:OpenView(UIPrefabConfigName.TestDigActivityToolWindow)
end

function TestWindow:AddItemBySeries()
  local items = GM.TestModel:GetItemTypesInMap()
  local key = self:_GetCommonText()
  items = items[key]
  if Table.IsEmpty(items) then
    GM.UIManager:ShowPrompt("错误的棋子序列")
    return
  end
  local boardModel = BoardModelHelper.GetActiveModel()
  if boardModel == nil then
    GM.UIManager:ShowPrompt("当前不在棋盘场景")
  end
  local cachedItems = {}
  local position
  for _, type in ipairs(items) do
    GM.ItemDataModel:SetUnlocked(type)
    if Table.IsEmpty(cachedItems) then
      position = boardModel:FindEmptyPositionInValidOrder()
    end
    if position == nil then
      cachedItems[#cachedItems + 1] = type
    else
      GM.TestModel:AddItemOnBoard(boardModel, position, type)
    end
  end
  for i = #cachedItems, 1, -1 do
    boardModel:CacheItems({
      cachedItems[i]
    }, CacheItemType.Stack)
  end
  if not Table.IsEmpty(cachedItems) then
    GM.UIManager:ShowPrompt("棋盘已满，添加到缓存队列")
  else
    GM.UIManager:ShowPrompt("添加成功")
  end
end

function TestWindow:ReleaseChapterTemporarily()
  GM.TestModel:ReleaseChapterTemporarily()
  self:Close()
end

function TestWindow:OnTestOrderGroupClick()
  self:Close()
  GM.UIManager:OpenView(UIPrefabConfigName.TestOrderGroupWindow)
end

function TestWindow:OnDataBalanceClick()
  GM.MiscModel:Set(EMiscKey.DataBalanceDiff, "")
  GM.MiscModel:Set(EMiscKey.DataBalanceVersion, "")
  GM.UIManager:ShowTestPrompt("重启再次触发投放兼容")
end

function TestWindow:OpenOrderEnergyDiff()
  self:Close()
  GM.UIManager:OpenView(UIPrefabConfigName.TestOrderEnergyDiffWindow)
end

function TestWindow:CheckItemLeak()
  GM.MainBoardModel:CheckItemLeak()
end

function TestWindow:ShowPropertySource()
  local mapBasicPropertyTypes = {
    [EPropertyType.Gem] = true,
    [EPropertyType.Gold] = true,
    [EPropertyType.Energy] = true,
    [EPropertyType.SkipProp] = true
  }
  local info = ""
  local propertyDataManager = GM.PropertyDataManager
  local buyNum, giveNum
  for ePropertyName, ePropertyType in pairs(EPropertyType) do
    buyNum = propertyDataManager:GetPropertyNum(ePropertyType, EPropertySource.Buy)
    giveNum = propertyDataManager:GetPropertyNum(ePropertyType, EPropertySource.Give)
    if mapBasicPropertyTypes[ePropertyType] then
      info = info .. "<color=#FF0000>"
    end
    info = info .. "【" .. ePropertyName .. "】 付费" .. buyNum .. "  赠送" .. giveNum .. "\n"
    if mapBasicPropertyTypes[ePropertyType] then
      info = info .. "</color>"
    end
  end
  GM.UIManager:OpenView(UIPrefabConfigName.TestTextWindow, "Property Source Info", info)
  self:Close()
end

function TestWindow:TestShowAllPossibleChef()
  GM.ChapterManager:GetActiveRoomView():TestShowAllPossibleChef()
end

function TestWindow:UnityRestoreRewards()
  local str = self:_GetCommonText()
  if GM.InAppPurchaseModel:_RestoreRewards(str) then
    GM.UIManager:ShowTestPrompt("测试补单成功")
  end
end
