TextAssetConfigName = {
  BoardModelConfig = "BoardModelConfig",
  BoardModelConfigLocker = "BoardModelConfigLocker",
  BoardPromptConfig = "BoardPromptConfig",
  BubbleConfig = "BubbleConfig",
  ChapterConfig = "ChapterConfig",
  CleanOrderConfig = "CleanOrderConfig",
  DigItemConfig = "DigItemConfig",
  DigLevelsConfig = "DigLevelsConfig",
  ExtraBoardActivityBoardConfig = "ExtraBoardActivityBoardConfig",
  ExtraBoardCobwebConfig = "ExtraBoardCobwebConfig",
  ExtraBoardItemModelConfig = "ExtraBoardItemModelConfig",
  ExtraBoardItemModelConfig_cobweb = "ExtraBoardItemModelConfig_cobweb",
  ExtraBoardItemModelConfig_cwenergy = "ExtraBoardItemModelConfig_cwenergy",
  ExtraBoardItemModelConfig_energy = "ExtraBoardItemModelConfig_energy",
  FunctionEnableConfig = "FunctionEnableConfig",
  InventorySlotConfig = "InventorySlotConfig",
  ItemModelConfig = "ItemModelConfig",
  ItemPropertyConfig = "ItemPropertyConfig",
  ItemViewConfig = "ItemViewConfig",
  LevelConfig = "LevelConfig",
  MainBoardBgConfig = "MainBoardBgConfig",
  NotificationTextConfig = "NotificationTextConfig",
  OrderAvatarConfig = "OrderAvatarConfig",
  OrderFixedConfig_1 = "OrderFixedConfig_1",
  OrderFixedConfig_2 = "OrderFixedConfig_2",
  OrderFixedConfig_3 = "OrderFixedConfig_3",
  OrderFixedConfig_4 = "OrderFixedConfig_4",
  OrderFixedConfig_5 = "OrderFixedConfig_5",
  OrderFixedConfig_6 = "OrderFixedConfig_6",
  OrderFixedConfig_7 = "OrderFixedConfig_7",
  OrderFixedConfig_8 = "OrderFixedConfig_8",
  OrderFixedConfig_9 = "OrderFixedConfig_9",
  OrderFixedConfig_10 = "OrderFixedConfig_10",
  OrderFixedConfig_11 = "OrderFixedConfig_11",
  OrderFixedConfig_12 = "OrderFixedConfig_12",
  OrderFixedConfig_13 = "OrderFixedConfig_13",
  OrderFixedConfig_14 = "OrderFixedConfig_14",
  OrderFixedConfig_15 = "OrderFixedConfig_15",
  OrderFixedConfig_16 = "OrderFixedConfig_16",
  OrderGroupConfig_1 = "OrderGroupConfig_1",
  OrderGroupConfig_2 = "OrderGroupConfig_2",
  OrderGroupConfig_3 = "OrderGroupConfig_3",
  OrderGroupConfig_4 = "OrderGroupConfig_4",
  OrderGroupConfig_5 = "OrderGroupConfig_5",
  OrderGroupConfig_6 = "OrderGroupConfig_6",
  OrderGroupConfig_7 = "OrderGroupConfig_7",
  OrderGroupConfig_8 = "OrderGroupConfig_8",
  OrderGroupConfig_9 = "OrderGroupConfig_9",
  OrderGroupConfig_10 = "OrderGroupConfig_10",
  OrderGroupConfig_11 = "OrderGroupConfig_11",
  OrderGroupConfig_12 = "OrderGroupConfig_12",
  OrderGroupConfig_13 = "OrderGroupConfig_13",
  OrderGroupConfig_14 = "OrderGroupConfig_14",
  OrderGroupConfig_15 = "OrderGroupConfig_15",
  OrderGroupConfig_16 = "OrderGroupConfig_16",
  OrderHigherGroupConfig = "OrderHigherGroupConfig",
  PDItemSPConfig = "PDItemSPConfig",
  question = "question",
  sensitiveWord = "sensitiveWord",
  ShopDailyDealsConfig = "ShopDailyDealsConfig",
  ShopDailyDealsConfig_additem = "ShopDailyDealsConfig_additem",
  ShopDailyDealsConfig_leveldown = "ShopDailyDealsConfig_leveldown",
  ShopFlashSaleConfig = "ShopFlashSaleConfig",
  ShopIAPConfig = "ShopIAPConfig",
  ShopIAPConfig_tag = "ShopIAPConfig_tag",
  ShopOrderConfig = "ShopOrderConfig",
  SystemConfig = "SystemConfig",
  TaskConfig_Bakery = "TaskConfig_Bakery",
  TaskConfig_BBQ = "TaskConfig_BBQ",
  TaskConfig_DimSum = "TaskConfig_DimSum",
  TaskConfig_Market = "TaskConfig_Market",
  TaskConfig_Morocco = "TaskConfig_Morocco",
  TaskConfig_Nice = "TaskConfig_Nice",
  TaskConfig_Orleans = "TaskConfig_Orleans",
  TaskConfig_Ottoman = "TaskConfig_Ottoman",
  TaskConfig_Pasta = "TaskConfig_Pasta",
  TaskConfig_Sausage = "TaskConfig_Sausage",
  TaskConfig_Seafood = "TaskConfig_Seafood",
  TaskConfig_Sushi = "TaskConfig_Sushi",
  TaskConfig_Tacos = "TaskConfig_Tacos",
  TaskConfig_Tapas = "TaskConfig_Tapas",
  TaskConfig_Thailand = "TaskConfig_Thailand",
  TaskConfig_Wine = "TaskConfig_Wine",
  TaskGroupConfig = "TaskGroupConfig"
}
setmetatable(TextAssetConfigName, {
  __index = function(_, key)
    Log.Error("TextAssetConfigName try to index a nil key: " .. tostring(key))
    return nil
  end
})

function TextAssetConfigName.HasConfig(name)
  if name == nil then
    return false
  end
  return rawget(TextAssetConfigName, name) ~= nil
end
