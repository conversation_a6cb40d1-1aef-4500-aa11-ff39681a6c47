import os
from openpyxl import load_workbook
from flask import Flask, render_template_string, request, send_file
from pathlib import Path
import pandas as pd
import tempfile

app = Flask(__name__)

# HTML模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Excel对比工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .diff-only-in-1 { background-color: #ffcdd2; }
        .diff-only-in-2 { background-color: #c8e6c9; }
        .diff-both { background-color: #fff9c4; }
        .sticky-header th { 
            position: sticky;
            top: 0;
            background: white;
            z-index: 1;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <h2 class="mt-3 mb-4">Excel对比工具</h2>
    
    <form method="post" enctype="multipart/form-data" class="mb-4">
        <div class="row g-3">
            <div class="col-md-5">
                <label class="form-label">第一个Excel文件</label>
                <input type="file" name="file1" class="form-control" accept=".xlsx" required>
            </div>
            <div class="col-md-5">
                <label class="form-label">第二个Excel文件</label>
                <input type="file" name="file2" class="form-control" accept=".xlsx" required>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">对比</button>
            </div>
        </div>
    </form>

    {% if diff_data is not none %}
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead class="sticky-header">
                <tr>
                    {% for header in headers %}
                    <th>{{ header }}</th>
                    {% endfor %}
                    <th>差异类型</th>
                </tr>
            </thead>
            <tbody>
                {% for row in diff_data %}
                <tr class="{{ row.diff_class }}">
                    {% for cell in row.data %}
                    <td>{{ cell }}</td>
                    {% endfor %}
                    <td>{{ row.diff_type }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

def compare_excel_data(file1, file2):
    """比较两个Excel文件的数据"""
    # 读取Excel文件
    wb1 = load_workbook(file1)
    wb2 = load_workbook(file2)
    
    # 获取第一个sheet
    ws1 = wb1.active
    ws2 = wb2.active
    
    # 获取表头
    headers = [cell.value for cell in ws1[1]]
    
    # 创建字典来存储数据
    data1 = {row[0].value: [cell.value for cell in row] for row in ws1.iter_rows(min_row=2)}
    data2 = {row[0].value: [cell.value for cell in row] for row in ws2.iter_rows(min_row=2)}
    
    # 获取所有唯一ID
    all_ids = sorted(set(list(data1.keys()) + list(data2.keys())))
    
    # 存储对比结果
    diff_data = []
    
    for id_value in all_ids:
        if id_value in data1 and id_value in data2:
            # 两个文件都有这个ID
            if data1[id_value] != data2[id_value]:
                # 数据不同
                diff_data.append({
                    'data': data1[id_value],
                    'diff_type': 'File1中的数据',
                    'diff_class': 'diff-both'
                })
                diff_data.append({
                    'data': data2[id_value],
                    'diff_type': 'File2中的数据',
                    'diff_class': 'diff-both'
                })
        elif id_value in data1:
            # 只在file1中有
            diff_data.append({
                'data': data1[id_value],
                'diff_type': '仅在File1中',
                'diff_class': 'diff-only-in-1'
            })
        else:
            # 只在file2中有
            diff_data.append({
                'data': data2[id_value],
                'diff_type': '仅在File2中',
                'diff_class': 'diff-only-in-2'
            })
    
    return headers, diff_data

@app.route('/', methods=['GET', 'POST'])
def index():
    diff_data = None
    headers = None
    
    if request.method == 'POST':
        if 'file1' not in request.files or 'file2' not in request.files:
            return '请选择两个Excel文件'
        
        file1 = request.files['file1']
        file2 = request.files['file2']
        
        if file1.filename == '' or file2.filename == '':
            return '请选择两个Excel文件'
        
        # 创建临时文件来保存上传的Excel
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp1, \
             tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp2:
            file1.save(temp1.name)
            file2.save(temp2.name)
            headers, diff_data = compare_excel_data(temp1.name, temp2.name)
            
            # 删除临时文件
            os.unlink(temp1.name)
            os.unlink(temp2.name)
    
    return render_template_string(HTML_TEMPLATE, diff_data=diff_data, headers=headers)

if __name__ == '__main__':
    app.run(debug=True, port=5000) 