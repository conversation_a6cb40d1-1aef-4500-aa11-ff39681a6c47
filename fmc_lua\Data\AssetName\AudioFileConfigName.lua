AudioFileConfigName = {
  bgmBoard = "bgmBoard",
  bgmMakeover = "bgmMakeover",
  cook_stir_audio = "cook_stir_audio",
  it_2_3_2_transform = "it_2_3_2_transform",
  SfxAreaDone = "SfxAreaDone",
  sfxBatFly = "sfxBatFly",
  SfxBlender = "SfxBlender",
  sfxBlindChestOpen = "sfxBlindChestOpen",
  SfxBoardFull = "SfxBoardFull",
  SfxBowl = "SfxBowl",
  SfxBpCoinFly = "SfxBpCoinFly",
  SfxBpUnlock = "SfxBpUnlock",
  sfxBreakEggFail = "sfxBreakEggFail",
  sfxBreakEggFin = "sfxBreakEggFin",
  sfxBreakEggSuc = "sfxBreakEggSuc",
  SfxBuild = "SfxBuild",
  SfxButtonClick = "SfxButtonClick",
  SfxButtonClickin = "SfxButtonClickin",
  SfxButtonClickout = "SfxButtonClickout",
  SfxCdSkip = "SfxCdSkip",
  sfxChoice = "sfxChoice",
  SfxChooseAddItem = "SfxChooseAddItem",
  SfxChooseDuplicate = "SfxChooseDuplicate",
  SfxChooseEnergyFree = "SfxChooseEnergyFree",
  sfxChooseItem = "sfxChooseItem",
  SfxChooseSplit = "SfxChooseSplit",
  sfxClap = "sfxClap",
  SfxCloseWindow = "SfxCloseWindow",
  SfxCoinRaceCoinGrow = "SfxCoinRaceCoinGrow",
  SfxCoinRaceDropCoin = "SfxCoinRaceDropCoin",
  SfxColdingClick = "SfxColdingClick",
  SfxCuttingBoard = "SfxCuttingBoard",
  sfxDialoguePopout = "sfxDialoguePopout",
  SfxDishDone = "SfxDishDone",
  SfxDishOut = "SfxDishOut",
  sfxDive = "sfxDive",
  sfxDragItem = "sfxDragItem",
  sfxDragItemEnd = "sfxDragItemEnd",
  sfxDragItemSwitch = "sfxDragItemSwitch",
  SfxDragSpecialItem = "SfxDragSpecialItem",
  sfxEnergyBoostOff = "sfxEnergyBoostOff",
  sfxEnergyBoostOn = "sfxEnergyBoostOn",
  SfxEnergyCrush = "SfxEnergyCrush",
  SfxExpCollect = "SfxExpCollect",
  SfxFirework = "SfxFirework",
  sfxFloor = "sfxFloor",
  SfxFoodSpeed = "SfxFoodSpeed",
  SfxFreeGift = "SfxFreeGift",
  sfxFurniture = "sfxFurniture",
  SfxGemCrush = "SfxGemCrush",
  SfxGeneral = "SfxGeneral",
  SfxGoldOrderShow = "SfxGoldOrderShow",
  SfxGrill = "SfxGrill",
  SfxInventoryOff = "SfxInventoryOff",
  SfxInventoryOn = "SfxInventoryOn",
  sfxItemUnlock = "sfxItemUnlock",
  SfxLevelUp = "SfxLevelUp",
  sfxMergeArea = "sfxMergeArea",
  sfxMergeBox = "sfxMergeBox",
  SfxMergeBoxMulti01 = "SfxMergeBoxMulti01",
  SfxMergeBubbleBreak = "SfxMergeBubbleBreak",
  SfxMergeBubbleSpawn = "SfxMergeBubbleSpawn",
  SfxMergeCd = "SfxMergeCd",
  SfxMergeCollectCoins = "SfxMergeCollectCoins",
  SfxMergeCollectDiamond = "SfxMergeCollectDiamond",
  SfxMergeCollectEnergy = "SfxMergeCollectEnergy",
  SfxMergeCollectExperience = "SfxMergeCollectExperience",
  SfxMergelv2 = "SfxMergelv2",
  SfxMergelv3 = "SfxMergelv3",
  SfxMergelv4 = "SfxMergelv4",
  SfxMergelv5 = "SfxMergelv5",
  SfxMergelv6 = "SfxMergelv6",
  SfxMergelv7 = "SfxMergelv7",
  SfxMergelv8 = "SfxMergelv8",
  SfxMergelv9 = "SfxMergelv9",
  SfxMergeSpawnManual = "SfxMergeSpawnManual",
  SfxMergeSpawnManualEnergyBoost = "SfxMergeSpawnManualEnergyBoost",
  SfxNewDay = "SfxNewDay",
  SfxNewItem = "SfxNewItem",
  SfxNewOrderGroup = "SfxNewOrderGroup",
  sfxNewTask = "sfxNewTask",
  SfxOcean = "SfxOcean",
  SfxOpenBox = "SfxOpenBox",
  sfxOrderComplete = "sfxOrderComplete",
  sfxOrderServe = "sfxOrderServe",
  SfxOrderShow = "SfxOrderShow",
  SfxOrderShow2 = "SfxOrderShow2",
  SfxOven = "SfxOven",
  SfxPan = "SfxPan",
  sfxPinataBreak = "sfxPinataBreak",
  sfxPinataHit = "sfxPinataHit",
  SfxPlaceFood = "SfxPlaceFood",
  SfxPlaceItem = "SfxPlaceItem",
  SfxPot = "SfxPot",
  SfxProgress = "SfxProgress",
  sfxRewardCollect = "sfxRewardCollect",
  SfxRewardCrush = "SfxRewardCrush",
  SfxRoomSwitch = "SfxRoomSwitch",
  SfxScenesSwitch = "SfxScenesSwitch",
  SfxScenesSwitchin = "SfxScenesSwitchin",
  SfxScenesSwitchout = "SfxScenesSwitchout",
  SfxSellItem = "SfxSellItem",
  SfxShopBuy = "SfxShopBuy",
  SfxSilverOrderGift = "SfxSilverOrderGift",
  SfxSpeedCard = "SfxSpeedCard",
  sfxSpendCoins = "sfxSpendCoins",
  SfxStoreOpen = "SfxStoreOpen",
  SfxTapCard = "SfxTapCard",
  SfxTaskChest = "SfxTaskChest",
  SfxTaskReady = "SfxTaskReady",
  SfxTaskStart = "SfxTaskStart",
  sfxTreasureFound = "sfxTreasureFound",
  SfxUnlockGift = "SfxUnlockGift",
  sfxWallpaper = "sfxWallpaper"
}
setmetatable(AudioFileConfigName, {
  __index = function(_, key)
    Log.Error("AudioFileConfigName try to index a nil key: " .. tostring(key))
    return nil
  end
})

function AudioFileConfigName.HasConfig(name)
  if name == nil then
    return false
  end
  return rawget(AudioFileConfigName, name) ~= nil
end
