"""
简单测试脚本
"""

print("开始测试...")

try:
    print("1. 测试导入data_models...")
    from data_models import OrderData, GeneratorData, ItemData
    print("   data_models导入成功")
    
    print("2. 测试导入config_loader...")
    from config_loader import ConfigLoader
    print("   config_loader导入成功")
    
    print("3. 测试导入energy_calculator...")
    from energy_calculator import EnergyCalculator
    print("   energy_calculator导入成功")
    
    print("4. 测试导入order_generator...")
    from order_generator import OrderGenerator
    print("   order_generator导入成功")
    
    print("5. 测试创建配置加载器...")
    import os
    config_path = os.path.join(os.path.dirname(__file__), "Data", "Config")
    config_loader = ConfigLoader(config_path)
    print(f"   配置路径: {config_path}")
    
    print("所有模块导入成功！")
    
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()
