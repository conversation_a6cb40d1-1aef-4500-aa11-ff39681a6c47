OrderStateHelper = {}

function OrderStateHelper.TryFillOrderRequirement(boardModel, requirement, needCount)
  if GM.ItemDataModel:IsDisposableInstrument(requirement) then
    local items = boardModel:FilterItemsByType(requirement, function(item)
      return item:IsEmptyInstrument()
    end, needCount, true)
    local existCount = #items
    if needCount <= existCount then
      return true
    else
      return false, existCount
    end
  else
    return true
  end
end
