AdModel = {}
AdModel.__index = AdModel
EAdType = {
  All = "all",
  Energy = "energy",
  Bubble = "bubble",
  SpeedUp = "speedUp",
  ShopBox = "shopBox",
  ShopRefresh = "shopRefresh"
}
AD_MAX_LOAD_TIME = 8

function AdModel:Init()
  self.m_dbTable = GM.DBTableManager:GetTable(EDBTableConfigs.Ad)
end

function AdModel:OnSyncDataFinished()
  local adInfos = self.m_dbTable:GetValues()
  self.m_mapAdType2ShowedInfo = {}
  for k, v in pairs(adInfos) do
    self.m_mapAdType2ShowedInfo[k] = v
  end
end

function AdModel:GetConfig(eAdType)
  return self.m_mapAdType2Config[eAdType]
end

function AdModel:LoadServerConfig()
  self.m_mapAdType2Config = {}
  local arrConfig = GM.ConfigModel:GetServerConfig(ServerConfigKey.Ad)
  if arrConfig then
    local config
    for _, v in pairs(arrConfig) do
      config = AdConfig.Create(v)
      self.m_mapAdType2Config[config.eType] = config
    end
  end
end

function AdModel:GetData()
  return self.m_dbTable
end

function AdModel:FromSyncData(dataArr)
  self.m_dbTable:FromArr(dataArr)
end

function AdModel:_IsAdReady()
  if DeviceInfo.IsUnityEditor() then
    return false
  end
  local bIsReady = false
  local funcIsReady = function()
    bIsReady = GM.SDKHelper:GetAdManager():IsRVReady()
  end
  SafeCall(funcIsReady)
  return bIsReady
end

function AdModel:GetEnergyNum(eAdType, param)
  local result = self:CanShowAd(eAdType, param)
  return IsNumber(result) and result or 0
end

function AdModel:CanShowAd(eAdType, param)
  if not self:_IsAdReady() then
    return false
  end
  local tbAllControl = self.m_mapAdType2Config[EAdType.All] or {}
  local tbTargetControl = self.m_mapAdType2Config[eAdType] or {}
  if not tbAllControl.bEnable or not tbTargetControl.bEnable then
    return false
  end
  local uCurLevel = GM.LevelModel:GetCurrentLevel()
  if tbAllControl.endLevel and uCurLevel >= tbAllControl.endLevel then
    return false
  end
  if tbTargetControl.endLevel and uCurLevel >= tbTargetControl.endLevel then
    return false
  end
  local showInfo = self.m_mapAdType2ShowedInfo[eAdType]
  local serverTime = GM.GameModel:GetServerTime()
  local date = serverTime // Sec2Day
  local uTotalShowedCount = 0
  for type, v in pairs(self.m_mapAdType2ShowedInfo) do
    if v.lastShowTime // Sec2Day ~= date then
      v.count = 0
      self:_SaveData(type)
    end
    uTotalShowedCount = uTotalShowedCount + v.count
  end
  if tbAllControl.maxNum and uTotalShowedCount >= tbAllControl.maxNum then
    return false
  end
  if showInfo then
    if tbTargetControl.maxNum and showInfo.count >= tbTargetControl.maxNum then
      return false
    end
    if tbAllControl.interval and serverTime - showInfo.lastShowTime <= tbAllControl.interval then
      return false
    end
    if tbTargetControl.interval and serverTime - showInfo.lastShowTime <= tbTargetControl.interval then
      return false
    end
  end
  if eAdType == EAdType.Bubble or eAdType == EAdType.SpeedUp then
    local config = tbTargetControl.params[1]
    if not (config and config.maxPrice) or param > config.maxPrice then
      return false
    end
  elseif eAdType == EAdType.Energy then
    local config = tbTargetControl.params[1]
    if config and config.energy and 0 < config.energy then
      return config.energy
    else
      return false
    end
  end
  return true
end

function AdModel:ShowAd(eAdType, ext)
  PlayerPrefs.DeleteKey(EPlayerPrefKey.AdShowingTime)
  if self:_IsAdReady() then
    self:ShowAdWhenIsReady(eAdType)
  else
    GM.UIManager:ShowMask()
    self.isTryingToLoadAd = true
    self.waitToLoadTime = AD_MAX_LOAD_TIME
    self.waitToShowAdType = eAdType
  end
  self.m_showAdType = eAdType
  self.m_ext = ext
end

function AdModel:ShowAdWhenIsReady(eAdType)
  GM.BIManager:LogAd(eAdType, EBIType.AdStart)
  local bShow = false
  local funcShowAd = function()
    bShow = GM.SDKHelper:GetAdManager():ShowRV()
  end
  SafeCall(funcShowAd)
  local funcGetLoadedNetwork = function()
    local loadedNetwork = GM.SDKHelper:GetAdManager():GetLoadedNetwork()
    GM.BIManager:LogAd(eAdType, EBIType.AdLoadedNetwork, loadedNetwork or "")
  end
  SafeCall(funcGetLoadedNetwork)
  if bShow then
    self.m_eShowingAdType = eAdType
    GM.UIManager:ShowMask()
    GM:OnEnteringThirdPartyApp(false)
    self.m_bHasCorrectEnd = false
    DelayExecuteFunc(function()
      if not self.m_bHasCorrectEnd then
        GM.UIManager:HideMask()
        GM:OnBackFromThirdPartyApp(false)
      end
    end, AD_MAX_LOAD_TIME)
    Log.Info("AdModel ShowAd true ", LogTag.Ad)
    CSFirebaseManager:CrashlyticsLog("AdModel ShowAd true " .. eAdType)
    local saveStr = tostring(GM.GameModel:GetServerTime()) .. "#" .. eAdType
    PlayerPrefs.SetString(EPlayerPrefKey.AdShowingTime, saveStr)
  else
    self:_OnShowAdFailed(eAdType)
  end
end

function AdModel:_OnShowAdFailed(eAdType)
  GM.BIManager:LogAd(eAdType, EBIType.AdFailed)
  Log.Info("AdModel ShowAd false ", LogTag.Ad)
  CSFirebaseManager:CrashlyticsLog("AdModel ShowAd false " .. eAdType)
  self:_PopShowAdFailedWindow()
  EventDispatcher.DispatchEvent(EEventType.OnAdEnd, {bSuccess = false, eAdType = eAdType})
end

function AdModel:UpdatePerSecond()
  self:_CheckIsReady()
end

function AdModel:_CheckIsReady()
  if not self.isTryingToLoadAd then
    return
  end
  if self.waitToLoadTime > 0 then
    if self:_IsAdReady() then
      self.isTryingToLoadAd = nil
      self.waitToLoadTime = nil
      GM.UIManager:HideMask()
      self:ShowAdWhenIsReady(self.waitToShowAdType)
    else
      self.waitToLoadTime = self.waitToLoadTime - 1
    end
  else
    self.isTryingToLoadAd = nil
    self.waitToLoadTime = nil
    GM.UIManager:HideMask()
    self:_OnShowAdFailed(self.waitToShowAdType)
  end
end

function AdModel:OnAdEnd(msg)
  local successThreshhold = 2
  local eRewardStatus = msg.Reward or 0
  local log = "AdModel OnAdEnd " .. eRewardStatus
  Log.Info(log, LogTag.Ad)
  CSFirebaseManager:CrashlyticsLog(log)
  if GM.UIManager then
    GM.UIManager:HideMask()
  end
  GM:OnBackFromThirdPartyApp(false)
  self.m_bHasCorrectEnd = true
  if not self.m_eShowingAdType then
    return
  end
  if successThreshhold < eRewardStatus and self.m_eShowingAdType then
    self:_IncreaseAdShowedCount(self.m_eShowingAdType)
  end
  if successThreshhold < eRewardStatus then
    GM.BIManager:LogAd(self.m_showAdType, EBIType.AdSuccess, self.m_ext)
    PlayerPrefs.DeleteKey(EPlayerPrefKey.AdShowingTime)
  else
    self:_PopShowAdFailedWindow()
    GM.BIManager:LogAd(self.m_showAdType, EBIType.AdFailed, msg.Message or "")
  end
  EventDispatcher.DispatchEvent(EEventType.OnAdEnd, {
    bSuccess = successThreshhold < eRewardStatus,
    eAdType = self.m_eShowingAdType
  })
  self.m_eShowingAdType = nil
end

function AdModel:OnAdEarned(adEarnedEventArgs)
  local day = PlayerPrefs.GetInt(EPlayerPrefKey.AdRevuneDailyDay)
  local targetDay = GM.GameModel:GetServerTime() // Sec2Day
  if day ~= targetDay then
    PlayerPrefs.SetInt(EPlayerPrefKey.AdRevuneDailyDay, targetDay)
    PlayerPrefs.SetFloat(EPlayerPrefKey.AdRevuneTimesSum, 0.0)
    PlayerPrefs.SetFloat(EPlayerPrefKey.AdRevuneDailySum, 0.0)
  end
  local previousSum = PlayerPrefs.GetFloat(EPlayerPrefKey.AdRevuneDailySum)
  local totalSum = previousSum + adEarnedEventArgs.revenue
  PlayerPrefs.SetFloat(EPlayerPrefKey.AdRevuneDailySum, totalSum)
  local adEarnTable = {
    currency = adEarnedEventArgs.currency,
    revenue = adEarnedEventArgs.revenue,
    network = adEarnedEventArgs.network,
    country = adEarnedEventArgs.country
  }
  GM.BIManager:LogAd(self.m_showAdType, EBIType.AdEarned, json.encode(adEarnTable))
end

function AdModel:_PopShowAdFailedWindow()
  if not GM.UIManager then
    return
  end
  local canCompensate, showAdType = self:CheckCanCompensateReward()
  GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("show_ad_failed_window_title"), GM.GameTextModel:GetText("show_ad_failed_window_desc"), GM.GameTextModel:GetText("show_ad_failed_window_btn"), function()
    if canCompensate then
      self:CompensateAdReward(EBIType.AdFailedCompensate, showAdType)
    end
  end, nil, nil)
end

function AdModel:_IncreaseAdShowedCount(eAdType)
  if not self.m_mapAdType2ShowedInfo[eAdType] then
    self.m_mapAdType2ShowedInfo[eAdType] = {
      count = 1,
      lastShowTime = GM.GameModel:GetServerTime()
    }
  else
    local info = self.m_mapAdType2ShowedInfo[eAdType]
    info.count = info.count + 1
    info.lastShowTime = GM.GameModel:GetServerTime()
  end
  self:_SaveData(eAdType)
end

function AdModel:_SaveData(eAdType)
  self.m_dbTable:BatchSet({
    [eAdType] = {
      count = self.m_mapAdType2ShowedInfo[eAdType].count,
      lastShowTime = self.m_mapAdType2ShowedInfo[eAdType].lastShowTime
    }
  })
end

function AdModel:CheckCanCompensateReward()
  local showAdTimeStr = PlayerPrefs.GetString(EPlayerPrefKey.AdShowingTime, "")
  if showAdTimeStr == "" then
    return false
  end
  local splitStrList = StringUtil.Split(showAdTimeStr, "#")
  if #splitStrList ~= 2 then
    return false
  end
  local showAdTime = tonumber(splitStrList[1])
  local showAdType = splitStrList[2]
  if GM.GameModel:GetServerTime() - showAdTime < 30 then
    PlayerPrefs.DeleteKey(EPlayerPrefKey.AdShowingTime)
    return false
  end
  return true, showAdType
end

function AdModel:CompensateAdReward(eBIType, showAdType)
  GM.UIManager:OpenView(UIPrefabConfigName.GeneralMsgWindow, GM.GameTextModel:GetText("ad_compensate_window_title"), GM.GameTextModel:GetText("ad_compensate_window_desc"), GM.GameTextModel:GetText("progress_conflict_confirm_btn"), function()
    local tbReward = {
      {
        [PROPERTY_TYPE] = EPropertyType.Energy,
        [PROPERTY_COUNT] = 10
      }
    }
    RewardApi.AcquireRewards(tbReward, EPropertySource.Give, eBIType, nil, EGameMode.Board, CacheItemType.Stack)
    PlayerPrefs.DeleteKey(EPlayerPrefKey.AdShowingTime)
    local bValid = false
    for _, v in pairs(EAdType) do
      if v == showAdType then
        bValid = true
        break
      end
    end
    if bValid then
      self:_IncreaseAdShowedCount(showAdType)
    end
  end)
end

function AdModel:TryCompensateAfterRestartGame()
  local canCompensate, showAdType = self:CheckCanCompensateReward()
  if not canCompensate then
    return
  end
  self:CompensateAdReward(EBIType.AdCompensate, showAdType)
end
