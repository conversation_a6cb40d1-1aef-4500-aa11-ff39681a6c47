local DBKey = {
  Round = "round",
  PlayerDatas = "playerDatas",
  MyScore = "myScore",
  EntryTime = "entryTime",
  CompeleteTime = "compeleteTime",
  ReceiveRewardState = "receiveRewardState",
  RoundRanks = "roundRanks",
  LastScore = "lastScore",
  LastRank = "lastRank",
  Settled = "settled"
}
local MyUserId = 1
RaceEventType = {
  StateChanged = 1,
  OpenMainWindow = 2,
  ScoreChanged = 3
}
local DBColumnValue = "value"
local ArrRomanNumbers = {
  "I",
  "II",
  "III",
  "IV",
  "V",
  "VI",
  "VII",
  "VIII",
  "IX",
  "X",
  "XI",
  "XII"
}
BaseRaceActivityModel = setmetatable({
  FixedPlayerNumber = 4,
  EntryGapTime = 3600,
  EntryOpName = ""
}, BaseActivityModel)
BaseRaceActivityModel.__index = BaseRaceActivityModel

function BaseRaceActivityModel:GetActivityDefinitionByType(activityType)
  Log.Error("GetActivityDefinition() 是抽象接口")
end

function BaseRaceActivityModel:GetCurrentRoundReward()
  Log.Error("GetCurrentRoundReward() 是抽象接口")
end

function BaseRaceActivityModel:GetResourceLabels()
  return self.m_activityDefinition.ResourceLabels
end

function BaseRaceActivityModel:Init(activityType, virtualDBTable)
  self.m_activityDefinition = self:GetActivityDefinitionByType(activityType)
  BaseActivityModel.Init(self, activityType, virtualDBTable)
  self:_LoadPlayerData()
end

function BaseRaceActivityModel:GetActivityDefinition()
  return self.m_activityDefinition
end

function BaseRaceActivityModel:_LoadPlayerData()
  self.m_arrPlayerData = {}
  local players = self.m_dbTable:GetValue(DBKey.PlayerDatas, DBColumnValue)
  if players ~= nil then
    local arr = json.decode(players)
    local targetScore = self:GetTargetScore()
    for index, value in ipairs(arr) do
      local lastScore = self:GetLastScore(value.id)
      local lastRank = self:GetLastRank(value.id)
      self.m_arrPlayerData[#self.m_arrPlayerData + 1] = RacePlayerData.Create(self, value, targetScore, lastScore, lastRank)
    end
    self:_AddMySelf()
  end
end

function BaseRaceActivityModel:_DropData()
  BaseActivityModel._DropData(self)
  self.m_bSettled = false
  self.m_mapAllRoundRanks = nil
end

function BaseRaceActivityModel:_CalculateState()
  if self.m_config == nil then
    return ActivityState.Released, -1
  end
  local serverTime = GM.GameModel:GetServerTime()
  if serverTime < self.m_config.sTime then
    return ActivityState.Preparing, self.m_config.sTime
  elseif serverTime < self.m_config.eTime then
    if self:_IsSettled() then
      return ActivityState.Ended, self.m_config.rTime
    end
    if self:HasFinishedAllRounds() then
      return ActivityState.Ended, self.m_config.rTime
    end
    if not self:IsInRace() and self:IsDeadline() and GM.UIManager.allWindowClosed and not GM.UIManager.isMaskVisible then
      return ActivityState.Ended, self.m_config.rTime
    end
    return ActivityState.Started, self.m_config.eTime
  elseif self.m_config.rTime and serverTime < self.m_config.rTime then
    return ActivityState.Ended, self.m_config.rTime
  else
    return ActivityState.Released, -1
  end
end

function BaseRaceActivityModel:_OnStateChanged()
  EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent)
  self.event:Call(RaceEventType.StateChanged)
  if self:GetState() == ActivityState.Ended and not self:_IsSettled() then
    self:_DoSettle()
  end
end

function BaseRaceActivityModel:_LoadOtherServerConfig(config)
  local userId = GM.UserModel:GetUserId()
  self.m_config.sTime = self.m_config.sTime + userId % 30
end

function BaseRaceActivityModel:CheckConfigValid()
  if not GameConfig.IsTestMode() then
    return
  end
  for _, roundConfig in ipairs(self.m_config.roundConfigs) do
    local total = 0
    local mapGroupType = {}
    if not roundConfig.playerNum then
      Log.Error("playerNum 为空！")
    else
      for _, playerNumConfig in ipairs(roundConfig.playerNum) do
        total = total + playerNumConfig.group_num
        mapGroupType[playerNumConfig.group_type] = true
      end
      if total ~= self.FixedPlayerNumber then
        Log.Error("playerNum 假人数量不符要求！")
      end
    end
    if roundConfig.playerNumWeight then
      for _, weightConfig in ipairs(roundConfig.playerNumWeight) do
        if not mapGroupType[weightConfig.group_type] then
          Log.Error("playerNumWeight 含有不存在的 groupType！")
        end
      end
    end
  end
end

function BaseRaceActivityModel:TryCompetitionEntry(successCallback, failCallback)
  local round = self:GetCurrentRound()
  local startTime = NetTimeStamp.Create(EBIType.NetworkCheckAction.StartRaceEntry)
  GM.BIManager:LogNet(EBIType.NetworkCheckAction.StartRaceEntry)
  local callback = function(bSuccess, tbResp, reqCtx)
    local timeinterval = startTime:EndAndGetDur()
    if bSuccess and tbResp.rcode == 0 then
      Log.Assert(#tbResp.robots == self.FixedPlayerNumber, "player data number error!")
      if #tbResp.robots ~= self.FixedPlayerNumber then
        return
      end
      self:_SetEntryTime(GM.GameModel:GetServerTime())
      self.m_dbTable:Set(DBKey.Round, DBColumnValue, round + 1)
      self.m_dbTable:Remove(DBKey.MyScore)
      self.m_dbTable:Remove(DBKey.ReceiveRewardState)
      self:_RemoveLastScore()
      self:_RemoveLastRank()
      self:_CreateRobotPlayers(tbResp.robots)
      self:_SaveOppoPlayer()
      self:_AddMySelf()
      GM.SyncModel:CheckUpload(false)
      if successCallback ~= nil then
        successCallback()
      else
        GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_type, true, true)
      end
      EventDispatcher.DispatchEvent(self.m_activityDefinition.StateChangedEvent, {isEnterCompetition = true})
      self:LogActivity(self.m_activityDefinition.SignupBIType, self:GetCurrentRound())
      self:LogActivity(EBIType.ActivityRankUp, 0)
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.RaceEntrySuccess, nil, reqCtx, timeinterval)
    else
      self:OnNetError(function()
        self:TryCompetitionEntry()
      end)
      GM.BIManager:LogNet(EBIType.NetworkCheckAction.RaceEntryFailed, tbResp, reqCtx, timeinterval)
      if failCallback ~= nil then
        failCallback()
      end
    end
  end
  local arrNumConfig = self.m_config.roundConfigs[round + 1].playerNum
  local arrWeightConfig = self.m_config.roundConfigs[round + 1].playerNumWeight or {}
  local mapGroupWeight = {}
  for _, weightConfig in ipairs(arrWeightConfig) do
    local groupType = weightConfig.group_type
    if not mapGroupWeight[groupType] then
      mapGroupWeight[groupType] = {}
    end
    mapGroupWeight[groupType][#mapGroupWeight[groupType] + 1] = weightConfig
  end
  local arrPlayerNum = {}
  local totalNum = 0
  for _, numConfig in ipairs(arrNumConfig) do
    local groupNum = numConfig.group_num
    totalNum = totalNum + groupNum
    local arrGroupWeight = mapGroupWeight[numConfig.group_type]
    if not arrGroupWeight then
      arrPlayerNum[#arrPlayerNum + 1] = numConfig
    else
      local arrWeightConfig = Table.ListWeightSelectN(arrGroupWeight, "weight", groupNum) or {}
      for _, weightConfig in ipairs(arrWeightConfig) do
        arrPlayerNum[#arrPlayerNum + 1] = {
          group_num = 1,
          group_type = weightConfig.group_type .. "_" .. weightConfig.child_type
        }
      end
    end
  end
  ApiMessage.RaceActivityEntry(self.EntryOpName, round + 1, totalNum, arrPlayerNum, callback)
end

function BaseRaceActivityModel:_CreateRobotPlayers(robots)
  local targetScore = self:GetTargetScore()
  local playerData
  self.m_arrPlayerData = {}
  for index, value in ipairs(robots) do
    playerData = RacePlayerData.Create(self, value, targetScore, 0)
    self.m_arrPlayerData[#self.m_arrPlayerData + 1] = playerData
  end
end

function BaseRaceActivityModel:HasNetwork()
  return Application.internetReachability ~= NetworkReachability.NotReachable
end

function BaseRaceActivityModel:TryOpenMainWindow()
  if not self:HasNetwork() then
    self:OnNetError(function()
      self:TryOpenMainWindow()
    end)
    return
  end
  GM.UIManager:OpenView(self.m_activityDefinition.MainWindowPrefabName, self.m_type)
end

function BaseRaceActivityModel:IsActivityOpen()
  if self:GetState() ~= ActivityState.Preparing and self:GetState() ~= ActivityState.Started or self:HasFinishedAllRounds() then
    return false
  end
  if not self:_IsEndRound() and self:_HasReceivedReward() and self:IsDeadline() then
    return false
  end
  return true
end

function BaseRaceActivityModel:IsInRace()
  local round = self:GetCurrentRound()
  if 0 < round and round <= #self.m_config.roundConfigs and not self:_HasReceivedReward() then
    return true
  end
  return false
end

function BaseRaceActivityModel:IsDeadline()
  local surplusTime = self.m_config.eTime - GM.GameModel:GetServerTime()
  return surplusTime < self.EntryGapTime
end

function BaseRaceActivityModel:HasFinishedAllRounds()
  return self:_IsEndRound() and self:_HasReceivedReward()
end

function BaseRaceActivityModel:UpdatePlayerRank()
  if Table.IsEmpty(self.m_arrPlayerData) then
    return
  end
  local entryTime = self:_GetEntryTime()
  local gapTime = GM.GameModel:GetServerTime() - entryTime
  local isAllZero = true
  for index, value in ipairs(self.m_arrPlayerData) do
    value:UpdateContent(gapTime)
  end
  local targetScore = self:GetTargetScore()
  table.sort(self.m_arrPlayerData, function(a, b)
    return self:_PlayerRankSortFunc(a, b)
  end)
  for index, value in ipairs(self.m_arrPlayerData) do
    value:SetRank(index)
  end
end

function BaseRaceActivityModel:_PlayerRankSortFunc(a, b)
  if a.m_curScore ~= b.m_curScore then
    return a.m_curScore > b.m_curScore
  end
  local bEnd = a.m_curScore == a.targetScore
  local isAPlayer = a:IsMySelf()
  local isBPlayer = b:IsMySelf()
  if bEnd then
    if a.m_curTime ~= b.m_curTime then
      return a.m_curTime < b.m_curTime
    end
    if isAPlayer and not isBPlayer then
      return true
    elseif not isAPlayer and isBPlayer then
      return false
    end
    return a:GetUserId() > b:GetUserId()
  end
  if isAPlayer and not isBPlayer then
    return true
  elseif not isAPlayer and isBPlayer then
    return false
  end
  if a.m_curTime ~= b.m_curTime then
    return a.m_curTime < b.m_curTime
  end
  return a:GetUserId() > b:GetUserId()
end

function BaseRaceActivityModel:UpdateLastScore()
  if Table.IsEmpty(self.m_arrPlayerData) then
    return
  end
  for index, value in ipairs(self.m_arrPlayerData) do
    value:UpdateLastScore()
  end
  self:_SaveLastScore()
end

function BaseRaceActivityModel:UpdateLastRank()
  if Table.IsEmpty(self.m_arrPlayerData) then
    return
  end
  for index, value in ipairs(self.m_arrPlayerData) do
    value:UpdateLastRank()
  end
  self:_SaveLastRank()
end

function BaseRaceActivityModel:GetMyRank(needToUpdate)
  if Table.IsEmpty(self.m_arrPlayerData) then
    return 0
  end
  if needToUpdate then
    self:UpdatePlayerRank()
  end
  for i = 1, #self.m_arrPlayerData do
    if self.m_arrPlayerData[i]:IsMySelf() then
      return self.m_arrPlayerData[i]:GetRank()
    end
  end
end

function BaseRaceActivityModel:GetMyLastRank()
  if Table.IsEmpty(self.m_arrPlayerData) then
    return 0
  end
  for i = 1, #self.m_arrPlayerData do
    if self.m_arrPlayerData[i]:IsMySelf() then
      return self.m_arrPlayerData[i]:GetLastRank()
    end
  end
end

function BaseRaceActivityModel:CanClaimReward()
  if self:_HasReceivedReward() then
    return false
  end
  local targetScore = self:GetTargetScore()
  if targetScore == 0 then
    return false
  end
  if targetScore > self:GetMyScore() then
    return false
  end
  return true
end

function BaseRaceActivityModel:TryClaimReward()
  if not self:CanClaimReward() then
    return false
  end
  local rank = self:GetMyRank()
  local rewards = self:GetCurrentRoundReward()[rank]
  self:_SetReceiveReward()
  self:UpdateScoreProgress()
  if not Table.IsEmpty(rewards) then
    RewardApi.CryptRewards(rewards)
    RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Give, self.m_activityDefinition.RewardBIType, EGameMode.Board, CacheItemType.Stack)
  end
  self:LogActivity(EBIType.ActivityRankUp, self:GetCurrentRound())
  return true, rewards
end

function BaseRaceActivityModel:GetAllPlayerData()
  return self.m_arrPlayerData
end

function BaseRaceActivityModel:GetRoundCount()
  return #self.m_config.roundConfigs
end

function BaseRaceActivityModel:GetTargetScore()
  local curRound = self:GetCurrentRound()
  if curRound == 0 then
    return 0
  end
  return self.m_config.roundConfigs[curRound].target
end

function BaseRaceActivityModel:_IsEndRound()
  local round = self:GetCurrentRound()
  return round == #self.m_config.roundConfigs
end

function BaseRaceActivityModel:GetMyScore()
  return self.m_dbTable:GetValue(DBKey.MyScore, "score") or 0
end

function BaseRaceActivityModel:GetMyTime()
  return self.m_dbTable:GetValue(DBKey.MyScore, "time") or 0
end

function BaseRaceActivityModel:_GetAllRoundRanks()
  if not self.m_mapAllRoundRanks then
    local ranks = self.m_dbTable:GetValue(DBKey.RoundRanks, DBColumnValue)
    if not ranks then
      self.m_mapAllRoundRanks = {}
    else
      self.m_mapAllRoundRanks = Table.DeepCopy(json.decode(ranks), true)
    end
  end
  return self.m_mapAllRoundRanks
end

function BaseRaceActivityModel:GetRoundRanks(round)
  return self:_GetAllRoundRanks()[tostring(round)]
end

function BaseRaceActivityModel:_SaveRoundRank(round, rank)
  self.m_mapAllRoundRanks = self:_GetAllRoundRanks()
  self.m_mapAllRoundRanks[tostring(round)] = rank
  self.m_dbTable:Set(DBKey.RoundRanks, DBColumnValue, json.encode(self.m_mapAllRoundRanks))
end

function BaseRaceActivityModel:CanAddScore()
  if self:GetState() ~= ActivityState.Started then
    return false
  end
  local myScore = self:GetMyScore()
  local targetScore = self:GetTargetScore()
  if myScore >= targetScore then
    return false
  end
  return true
end

function BaseRaceActivityModel:GetScoreRatio()
  return 1
end

function BaseRaceActivityModel:_AddMyScore(score, fromStr)
  if not self:CanAddScore() then
    return
  end
  if GM.SceneManager:GetGameMode() ~= EGameMode.Board then
    return false
  end
  local myScore = self:GetMyScore()
  local targetScore = self:GetTargetScore()
  myScore = math.min(targetScore, myScore + score)
  local serTime = GM.GameModel:GetServerTime()
  local entryTime = self:_GetEntryTime()
  local data = {
    [DBKey.MyScore] = {
      time = serTime - entryTime,
      score = myScore
    }
  }
  self.m_dbTable:BatchSet(data)
  if targetScore <= myScore then
    self:_SaveRoundRank(self:GetCurrentRound(), self:GetMyRank(true))
    local biInfo = {}
    local tbPlayer
    for i = 1, #self.m_arrPlayerData do
      tbPlayer = {
        id = self.m_arrPlayerData[i]:GetUserId(),
        score = self.m_arrPlayerData[i]:GetCurScore(serTime - entryTime),
        rank = self.m_arrPlayerData[i]:GetRank(),
        round = self:GetCurrentRound(),
        rankTime = serTime,
        startTime = entryTime,
        isMySelf = self.m_arrPlayerData[i]:IsMySelf(),
        tag = self.m_arrPlayerData[i].tag
      }
      biInfo[#biInfo + 1] = tbPlayer
    end
    GM.BIManager:LogAction(self.m_activityDefinition.RankBIType, biInfo)
    EventDispatcher.DispatchEvent(EEventType.OrderStatusChanged)
    EventDispatcher.DispatchEvent(EEventType.ActivityRaceCompleted)
  end
  self:UpdateScoreProgress(true)
  local tb = {
    score = score,
    round = self:GetCurrentRound(),
    type = fromStr,
    starttime = entryTime
  }
  self:LogActivity(EBIType.ActivityAddScore, GM.BIManager:TableToString(tb))
end

function BaseRaceActivityModel:GetCurrentRound()
  return self.m_dbTable:GetValue(DBKey.Round, DBColumnValue) or 0
end

function BaseRaceActivityModel:GetCurrentRoundInRoman()
  local round = self:GetCurrentRound()
  if 12 < round or round == 0 then
    return round
  end
  return ArrRomanNumbers[round]
end

function BaseRaceActivityModel:GetNextRoundInRoman()
  local round = self:GetCurrentRound() + 1
  if 12 < round or round == 0 then
    return round
  end
  return ArrRomanNumbers[round]
end

function BaseRaceActivityModel:_SetEntryTime(time)
  self.m_dbTable:Set(DBKey.EntryTime, DBColumnValue, time)
end

function BaseRaceActivityModel:_GetEntryTime()
  return self.m_dbTable:GetValue(DBKey.EntryTime, DBColumnValue) or 0
end

function BaseRaceActivityModel:_HasReceivedReward()
  local state = self.m_dbTable:GetValue(DBKey.ReceiveRewardState, DBColumnValue) or 0
  return state ~= 0
end

function BaseRaceActivityModel:_SetReceiveReward()
  self.m_dbTable:Set(DBKey.ReceiveRewardState, DBColumnValue, 1)
end

function BaseRaceActivityModel:_SaveOppoPlayer()
  local arr = {}
  for index, value in ipairs(self.m_arrPlayerData) do
    if value:GetUserId() ~= GM.UserModel:GetUserId() then
      arr[#arr + 1] = value:GetData()
    end
  end
  self.m_dbTable:Set(DBKey.PlayerDatas, DBColumnValue, json.encode(arr))
end

function BaseRaceActivityModel:_DoSettle()
  Log.Assert(not self:_IsSettled(), "should only settle once.")
  self.m_dbTable:Set(DBKey.Settled, "value", true)
  self.m_bSettled = true
end

function BaseRaceActivityModel:_IsSettled()
  if self.m_bSettled == nil then
    self.m_bSettled = self.m_dbTable:GetValue(DBKey.Settled, "value") or false
  end
  return self.m_bSettled
end

function BaseRaceActivityModel:IsMySelf(userId)
  return userId == MyUserId
end

function BaseRaceActivityModel:_AddMySelf()
  self.m_arrPlayerData = self.m_arrPlayerData or {}
  local data = self:_CreateMySelfData()
  self.m_arrPlayerData[#self.m_arrPlayerData + 1] = data
end

function BaseRaceActivityModel:_CreateMySelfData()
  local data = RacePlayerData.Create(self, {
    id = MyUserId,
    name = GM.GameTextModel:GetText("coin_race_main_you"),
    icon = GM.UserProfileModel:GetIcon(),
    type = 0
  }, self:GetTargetScore(), self:GetLastScore(MyUserId), self:GetLastRank(MyUserId))
  return data
end

function BaseRaceActivityModel:GetGapTime()
  local entryTime = self:_GetEntryTime()
  return GM.GameModel:GetServerTime() - entryTime
end

function BaseRaceActivityModel:_SaveLastScore()
  if Table.IsEmpty(self.m_arrPlayerData) then
    self:_RemoveLastScore()
    return
  end
  local saveTb = {}
  saveTb.round = self:GetCurrentRound()
  for idx, value in ipairs(self.m_arrPlayerData) do
    saveTb[tostring(value:GetUserId())] = value.lastScore or 0
  end
  self.m_dbTable:Set(DBKey.LastScore, DBColumnValue, json.encode(saveTb))
end

function BaseRaceActivityModel:GetLastScore(userId)
  local str = self.m_dbTable:GetValue(DBKey.LastScore, DBColumnValue)
  if StringUtil.IsNilOrEmpty(str) then
    return 0
  end
  local lastScoreMap = json.decode(str)
  if lastScoreMap.round ~= self:GetCurrentRound() then
    self:_SaveLastScore()
    return 0
  end
  return lastScoreMap[tostring(userId)] or 0
end

function BaseRaceActivityModel:_RemoveLastScore()
  self.m_dbTable:Remove(DBKey.LastScore, DBColumnValue)
end

function BaseRaceActivityModel:GetLastRank(userId)
  local str = self.m_dbTable:GetValue(DBKey.LastRank, DBColumnValue)
  if StringUtil.IsNilOrEmpty(str) then
    return
  end
  local lastScoreMap = json.decode(str)
  if lastScoreMap.round ~= self:GetCurrentRound() then
    self:_SaveLastRank()
    return
  end
  return lastScoreMap[tostring(userId)]
end

function BaseRaceActivityModel:_SaveLastRank()
  if Table.IsEmpty(self.m_arrPlayerData) then
    self:_RemoveLastRank()
    return
  end
  local saveTb = {}
  saveTb.round = self:GetCurrentRound()
  for idx, value in ipairs(self.m_arrPlayerData) do
    saveTb[tostring(value:GetUserId())] = value:GetLastRank()
  end
  self.m_dbTable:Set(DBKey.LastRank, DBColumnValue, json.encode(saveTb))
end

function BaseRaceActivityModel:_RemoveLastRank()
  self.m_dbTable:Remove(DBKey.LastRank, DBColumnValue)
end

function BaseRaceActivityModel:UpdateScoreProgress(bChanged)
  self.event:Call(RaceEventType.ScoreChanged, bChanged)
end

function BaseRaceActivityModel:OpenMainWindowEventCall()
  self.event:Call(RaceEventType.OpenMainWindow)
end

function BaseRaceActivityModel:OnNetError(retry)
  GM.UIManager:OpenView(UIPrefabConfigName.TwoButtonWindow, "bad_network_window_title", "network_error_desc", "common_button_cancel", "bad_network_window_retry", nil, function(window)
    window:Close()
    if retry then
      retry()
    end
  end)
end

function BaseRaceActivityModel:CanShowExclaimation()
  if self:CanClaimReward() then
    return true
  end
  if self:IsActivityOpen() and self:GetState() == ActivityState.Started and not self:IsInRace() then
    return true
  end
  return false
end
