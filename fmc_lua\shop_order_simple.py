"""
店铺订单生成器 - 简化版
不依赖matplotlib，专注核心功能
"""

import os
import re
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter import *
from collections import defaultdict
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
import random
import math

@dataclass
class ShopItem:
    """店铺物品"""
    code: str
    name: str = ""
    type: str = ""  # pd(生成器), it(物品), eq(器械), ds(菜品)
    level: int = 1
    series: int = 1
    category: List[int] = field(default_factory=list)
    energy_cost: int = 1
    frequency: int = 1
    capacity: int = 1
    generated_items: List[Dict] = field(default_factory=list)
    unlock_price: int = 0
    score: float = 0.0

@dataclass
class DayOrder:
    """单日订单"""
    day: int
    group_id: int
    chapter_id: int
    requirements: List[Dict] = field(default_factory=list)  # 需求物品
    rewards: List[Dict] = field(default_factory=list)  # 奖励物品
    total_energy: float = 0.0
    estimated_time: int = 0  # 预估完成时间(分钟)
    difficulty: str = "普通"  # 难度等级

@dataclass
class ShopOrderPlan:
    """店铺订单计划"""
    shop_name: str
    chapter_id: int
    total_days: int
    daily_orders: List[DayOrder] = field(default_factory=list)
    total_energy: float = 0.0
    total_rewards: Dict[str, int] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)

class SimpleShopOrderGenerator:
    """简化版店铺订单生成器"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("浮岛物语 - 店铺订单生成器 (简化版)")
        self.root.geometry("1200x800")
        
        # 数据存储
        self.config_path = os.path.join(os.path.dirname(__file__), "Data", "Config")
        self.items_data = {}  # 所有物品数据
        self.current_plan = None  # 当前生成计划
        
        # 界面变量
        self.selected_shop = StringVar(value="BBQ")
        self.selected_chapter = IntVar(value=1)
        self.generation_days = IntVar(value=7)
        self.energy_mode = StringVar(value="平衡")
        self.difficulty_mode = StringVar(value="普通")
        
        # 限制条件变量
        self.max_energy_per_day = IntVar(value=100)
        self.min_energy_per_day = IntVar(value=20)
        self.allow_generators = BooleanVar(value=True)
        self.allow_items = BooleanVar(value=True)
        self.allow_equipment = BooleanVar(value=True)
        self.allow_dishes = BooleanVar(value=True)
        self.max_item_level = IntVar(value=10)
        
        # 创建界面
        self._create_ui()
        
        # 加载配置数据
        self._load_config_data()
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主菜单
        menubar = Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="加载配置", command=self._load_config_data)
        file_menu.add_command(label="保存计划", command=self._save_plan)
        file_menu.add_command(label="加载计划", command=self._load_plan)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        menubar.add_cascade(label="文件", menu=file_menu)
        
        # 生成菜单
        generate_menu = Menu(menubar, tearoff=0)
        generate_menu.add_command(label="生成订单", command=self._generate_orders)
        generate_menu.add_command(label="清空结果", command=self._clear_results)
        menubar.add_cascade(label="生成", menu=generate_menu)
        
        # 创建主框架
        main_paned = ttk.PanedWindow(self.root, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_paned, width=350)
        main_paned.add(control_frame, weight=1)
        
        # 右侧显示面板
        display_frame = ttk.Frame(main_paned)
        main_paned.add(display_frame, weight=2)
        
        # 创建控制面板
        self._create_control_panel(control_frame)
        
        # 创建显示面板
        self._create_display_panel(display_frame)
        
        # 状态栏
        self.status_var = StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=SUNKEN)
        status_bar.pack(side=BOTTOM, fill=X)
    
    def _create_control_panel(self, parent):
        """创建控制面板"""
        # 基础设置
        basic_frame = ttk.LabelFrame(parent, text="基础设置", padding="10")
        basic_frame.pack(fill=X, pady=5)
        
        # 店铺选择
        ttk.Label(basic_frame, text="目标店铺:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        shop_combo = ttk.Combobox(basic_frame, textvariable=self.selected_shop, 
                                 values=["BBQ", "Bakery", "DimSum", "Market", "Morocco", 
                                        "Nice", "Orleans", "Ottoman", "Pasta", "Sausage",
                                        "Seafood", "Sushi", "Tacos", "Tapas", "Thailand", "Wine"],
                                 state="readonly", width=15)
        shop_combo.grid(row=0, column=1, sticky=W, padx=5, pady=2)
        
        # 章节选择
        ttk.Label(basic_frame, text="目标章节:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        chapter_spin = ttk.Spinbox(basic_frame, from_=1, to=16, textvariable=self.selected_chapter, width=15)
        chapter_spin.grid(row=1, column=1, sticky=W, padx=5, pady=2)
        
        # 生成天数
        ttk.Label(basic_frame, text="生成天数:").grid(row=2, column=0, sticky=W, padx=5, pady=2)
        days_spin = ttk.Spinbox(basic_frame, from_=1, to=30, textvariable=self.generation_days, width=15)
        days_spin.grid(row=2, column=1, sticky=W, padx=5, pady=2)
        
        # 能量模式
        ttk.Label(basic_frame, text="能量模式:").grid(row=3, column=0, sticky=W, padx=5, pady=2)
        energy_combo = ttk.Combobox(basic_frame, textvariable=self.energy_mode,
                                   values=["节能", "平衡", "高效", "极限"], state="readonly", width=15)
        energy_combo.grid(row=3, column=1, sticky=W, padx=5, pady=2)
        
        # 限制条件设置
        constraints_frame = ttk.LabelFrame(parent, text="限制条件", padding="10")
        constraints_frame.pack(fill=X, pady=5)
        
        # 能量限制
        ttk.Label(constraints_frame, text="每日能量范围:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        energy_frame = ttk.Frame(constraints_frame)
        energy_frame.grid(row=0, column=1, sticky=W, padx=5, pady=2)
        ttk.Spinbox(energy_frame, from_=1, to=50, textvariable=self.min_energy_per_day, width=8).pack(side=LEFT)
        ttk.Label(energy_frame, text=" - ").pack(side=LEFT)
        ttk.Spinbox(energy_frame, from_=50, to=500, textvariable=self.max_energy_per_day, width=8).pack(side=LEFT)
        
        # 物品类型限制
        ttk.Label(constraints_frame, text="允许物品类型:").grid(row=1, column=0, sticky=W, padx=5, pady=2)
        type_frame = ttk.Frame(constraints_frame)
        type_frame.grid(row=1, column=1, sticky=W, padx=5, pady=2)
        ttk.Checkbutton(type_frame, text="生成器", variable=self.allow_generators).pack(side=LEFT)
        ttk.Checkbutton(type_frame, text="物品", variable=self.allow_items).pack(side=LEFT)
        ttk.Checkbutton(type_frame, text="器械", variable=self.allow_equipment).pack(side=LEFT)
        ttk.Checkbutton(type_frame, text="菜品", variable=self.allow_dishes).pack(side=LEFT)
        
        # 等级限制
        ttk.Label(constraints_frame, text="最高物品等级:").grid(row=2, column=0, sticky=W, padx=5, pady=2)
        ttk.Spinbox(constraints_frame, from_=1, to=20, textvariable=self.max_item_level, width=15).grid(
            row=2, column=1, sticky=W, padx=5, pady=2)
        
        # 操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=X, pady=10)
        
        ttk.Button(button_frame, text="生成订单", command=self._generate_orders).pack(fill=X, pady=2)
        ttk.Button(button_frame, text="清空结果", command=self._clear_results).pack(fill=X, pady=2)
        ttk.Button(button_frame, text="物品筛选", command=self._show_item_filter).pack(fill=X, pady=2)
        
        # 统计信息
        stats_frame = ttk.LabelFrame(parent, text="统计信息", padding="10")
        stats_frame.pack(fill=X, pady=5)
        
        self.stats_text = Text(stats_frame, height=8, wrap=WORD, font=("Consolas", 9))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=LEFT, fill=BOTH, expand=True)
        stats_scrollbar.pack(side=RIGHT, fill=Y)
    
    def _create_display_panel(self, parent):
        """创建显示面板"""
        # 创建Notebook用于多标签页
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=BOTH, expand=True)
        
        # 订单计划标签页
        self._create_plan_tab()
        
        # 物品列表标签页
        self._create_items_tab()
        
        # 详细信息标签页
        self._create_details_tab()
    
    def _create_plan_tab(self):
        """创建订单计划标签页"""
        plan_frame = ttk.Frame(self.notebook)
        self.notebook.add(plan_frame, text="订单计划")
        
        # 创建树形视图显示订单
        columns = ("day", "requirements", "rewards", "energy", "time", "difficulty")
        self.plan_tree = ttk.Treeview(plan_frame, columns=columns, show="tree headings", height=20)
        
        # 设置列标题
        self.plan_tree.heading("#0", text="订单")
        self.plan_tree.heading("day", text="天数")
        self.plan_tree.heading("requirements", text="需求物品")
        self.plan_tree.heading("rewards", text="奖励")
        self.plan_tree.heading("energy", text="能量消耗")
        self.plan_tree.heading("time", text="预估时间")
        self.plan_tree.heading("difficulty", text="难度")
        
        # 设置列宽
        self.plan_tree.column("#0", width=120)
        self.plan_tree.column("day", width=60)
        self.plan_tree.column("requirements", width=250)
        self.plan_tree.column("rewards", width=180)
        self.plan_tree.column("energy", width=80)
        self.plan_tree.column("time", width=80)
        self.plan_tree.column("difficulty", width=60)
        
        # 添加滚动条
        plan_scrollbar = ttk.Scrollbar(plan_frame, orient=VERTICAL, command=self.plan_tree.yview)
        self.plan_tree.configure(yscrollcommand=plan_scrollbar.set)
        
        self.plan_tree.pack(side=LEFT, fill=BOTH, expand=True)
        plan_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 绑定双击事件
        self.plan_tree.bind("<Double-1>", self._on_order_double_click)

    def _create_items_tab(self):
        """创建物品列表标签页"""
        items_frame = ttk.Frame(self.notebook)
        self.notebook.add(items_frame, text="物品列表")

        # 物品筛选
        filter_frame = ttk.Frame(items_frame)
        filter_frame.pack(fill=X, pady=5, padx=5)

        ttk.Label(filter_frame, text="筛选:").pack(side=LEFT)
        self.item_filter_var = StringVar()
        filter_entry = ttk.Entry(filter_frame, textvariable=self.item_filter_var)
        filter_entry.pack(side=LEFT, fill=X, expand=True, padx=5)
        filter_entry.bind('<KeyRelease>', self._on_item_filter_changed)

        # 物品列表
        items_columns = ("code", "name", "type", "level", "energy", "frequency")
        self.items_tree = ttk.Treeview(items_frame, columns=items_columns, show="headings", height=25)

        self.items_tree.heading("code", text="代码")
        self.items_tree.heading("name", text="名称")
        self.items_tree.heading("type", text="类型")
        self.items_tree.heading("level", text="等级")
        self.items_tree.heading("energy", text="能量")
        self.items_tree.heading("frequency", text="频率")

        self.items_tree.column("code", width=120)
        self.items_tree.column("name", width=150)
        self.items_tree.column("type", width=80)
        self.items_tree.column("level", width=60)
        self.items_tree.column("energy", width=60)
        self.items_tree.column("frequency", width=60)

        items_scrollbar = ttk.Scrollbar(items_frame, orient=VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_scrollbar.set)

        self.items_tree.pack(side=LEFT, fill=BOTH, expand=True, padx=5)
        items_scrollbar.pack(side=RIGHT, fill=Y)

        # 绑定选择事件
        self.items_tree.bind('<<TreeviewSelect>>', self._on_item_selected)

    def _create_details_tab(self):
        """创建详细信息标签页"""
        details_frame = ttk.Frame(self.notebook)
        self.notebook.add(details_frame, text="详细信息")

        self.details_text = Text(details_frame, wrap=WORD, font=("Consolas", 10))
        details_scrollbar = ttk.Scrollbar(details_frame, orient=VERTICAL, command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scrollbar.set)

        self.details_text.pack(side=LEFT, fill=BOTH, expand=True)
        details_scrollbar.pack(side=RIGHT, fill=Y)

    def _load_config_data(self):
        """加载配置数据"""
        try:
            self.status_var.set("正在加载配置数据...")

            # 加载ItemModelConfig.lua
            self._load_item_model_config()

            # 更新物品列表显示
            self._update_items_display()

            # 更新统计信息
            self._update_statistics()

            self.status_var.set(f"配置加载完成 - 物品:{len(self.items_data)}")

        except Exception as e:
            self.status_var.set(f"配置加载失败: {e}")
            messagebox.showerror("错误", f"配置加载失败: {e}")

    def _load_item_model_config(self):
        """加载物品模型配置"""
        config_file = os.path.join(self.config_path, "ItemModelConfig.lua")

        if not os.path.exists(config_file):
            raise FileNotFoundError(f"配置文件不存在: {config_file}")

        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 解析Lua配置
        self._parse_item_model_content(content)

    def _parse_item_model_content(self, content):
        """解析物品模型内容"""
        # 移除注释
        content = re.sub(r'--.*?\n', '\n', content)

        # 查找所有配置块
        pattern = r'\{\s*Type\s*=\s*"([^"]+)"([^}]*)\}'
        matches = re.finditer(pattern, content, re.DOTALL)

        for match in matches:
            item_type = match.group(1)
            item_content = match.group(2)

            item = ShopItem(code=item_type)

            # 解析基础属性
            self._parse_item_attributes(item, item_content)

            # 确定物品类型
            if item_type.startswith('pd_'):
                item.type = "生成器"
            elif item_type.startswith('it_'):
                item.type = "物品"
            elif item_type.startswith('eq_'):
                item.type = "器械"
            elif item_type.startswith('ds_'):
                item.type = "菜品"
            else:
                item.type = "其他"

            self.items_data[item_type] = item

    def _parse_item_attributes(self, item, content):
        """解析物品属性"""
        # 解析各种属性
        patterns = {
            'UseEnergy': r'UseEnergy\s*=\s*(\d+)',
            'Frequency': r'Frequency\s*=\s*(\d+)',
            'Capacity': r'Capacity\s*=\s*(\d+)',
            'UnlockPrice': r'UnlockPrice\s*=\s*(\d+)',
            'Score': r'Score\s*=\s*([\d.]+)',
            'Category': r'Category\s*=\s*\{([^}]*)\}',
            'GeneratedItems': r'GeneratedItems\s*=\s*\{([^}]*)\}'
        }

        for attr, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                if attr == 'UseEnergy':
                    item.energy_cost = int(match.group(1))
                elif attr == 'Frequency':
                    item.frequency = int(match.group(1))
                elif attr == 'Capacity':
                    item.capacity = int(match.group(1))
                elif attr == 'UnlockPrice':
                    item.unlock_price = int(match.group(1))
                elif attr == 'Score':
                    item.score = float(match.group(1))
                elif attr == 'Category':
                    categories = re.findall(r'(\d+)', match.group(1))
                    item.category = [int(cat) for cat in categories]
                elif attr == 'GeneratedItems':
                    items_content = match.group(1)
                    item.generated_items = self._parse_generated_items(items_content)

        # 从代码中提取等级和系列信息
        parts = item.code.split('_')
        if len(parts) >= 3:
            try:
                item.series = int(parts[1])
                if len(parts) >= 4:
                    item.level = int(parts[3])
                else:
                    item.level = int(parts[2])
            except ValueError:
                pass

    def _parse_generated_items(self, content):
        """解析产出物品"""
        items = []
        pattern = r'\{\s*Code\s*=\s*"([^"]+)"\s*,\s*Weight\s*=\s*(\d+)\s*\}'
        matches = re.finditer(pattern, content)

        for match in matches:
            items.append({
                'Code': match.group(1),
                'Weight': int(match.group(2))
            })

        return items

    def _generate_orders(self):
        """生成订单"""
        try:
            self.status_var.set("正在生成订单...")

            # 创建订单计划
            plan = ShopOrderPlan(
                shop_name=self.selected_shop.get(),
                chapter_id=self.selected_chapter.get(),
                total_days=self.generation_days.get()
            )

            # 设置约束条件
            plan.constraints = {
                'max_energy_per_day': self.max_energy_per_day.get(),
                'min_energy_per_day': self.min_energy_per_day.get(),
                'allow_generators': self.allow_generators.get(),
                'allow_items': self.allow_items.get(),
                'allow_equipment': self.allow_equipment.get(),
                'allow_dishes': self.allow_dishes.get(),
                'max_item_level': self.max_item_level.get(),
                'energy_mode': self.energy_mode.get(),
                'difficulty_mode': self.difficulty_mode.get()
            }

            # 逐日生成订单
            for day in range(1, plan.total_days + 1):
                day_order = self._generate_day_order(day, plan)
                plan.daily_orders.append(day_order)
                plan.total_energy += day_order.total_energy

                # 累计奖励
                for reward in day_order.rewards:
                    currency = reward.get('Currency', '')
                    amount = reward.get('Amount', 0)
                    plan.total_rewards[currency] = plan.total_rewards.get(currency, 0) + amount

            self.current_plan = plan

            # 更新显示
            self._update_plan_display()
            self._update_statistics()

            self.status_var.set(f"订单生成完成 - {plan.total_days}天，总能量:{plan.total_energy:.1f}")

        except Exception as e:
            self.status_var.set(f"订单生成失败: {e}")
            messagebox.showerror("错误", f"订单生成失败: {e}")

    def _generate_day_order(self, day, plan):
        """生成单日订单"""
        day_order = DayOrder(
            day=day,
            group_id=day,
            chapter_id=plan.chapter_id
        )

        # 根据能量模式确定目标能量
        target_energy = self._calculate_target_energy(day, plan)

        # 筛选可用物品
        available_items = self._filter_available_items(plan.constraints)

        # 生成需求物品
        current_energy = 0.0
        requirement_count = random.randint(1, 4)  # 每日1-4个需求

        for _ in range(requirement_count):
            if current_energy >= target_energy:
                break

            # 随机选择物品
            if available_items:
                item_code = random.choice(list(available_items.keys()))
                item = available_items[item_code]

                # 计算数量
                remaining_energy = target_energy - current_energy
                max_count = max(1, int(remaining_energy / item.energy_cost))
                count = random.randint(1, min(max_count, 5))

                requirement = {
                    'Type': item_code,
                    'Count': count,
                    'Energy': item.energy_cost * count
                }

                day_order.requirements.append(requirement)
                current_energy += requirement['Energy']

        day_order.total_energy = current_energy

        # 生成奖励
        day_order.rewards = self._generate_day_rewards(day, current_energy)

        # 计算预估时间和难度
        day_order.estimated_time = self._calculate_estimated_time(day_order)
        day_order.difficulty = self._calculate_difficulty(day_order, plan.constraints)

        return day_order

    def _calculate_target_energy(self, day, plan):
        """计算目标能量"""
        min_energy = plan.constraints['min_energy_per_day']
        max_energy = plan.constraints['max_energy_per_day']
        energy_mode = plan.constraints['energy_mode']

        # 基础能量范围
        base_energy = min_energy + (max_energy - min_energy) * 0.5

        # 根据模式调整
        mode_multipliers = {
            '节能': 0.7,
            '平衡': 1.0,
            '高效': 1.3,
            '极限': 1.6
        }

        multiplier = mode_multipliers.get(energy_mode, 1.0)
        target = base_energy * multiplier

        # 添加随机波动
        variation = random.uniform(0.8, 1.2)
        target *= variation

        # 确保在范围内
        return max(min_energy, min(target, max_energy))

    def _filter_available_items(self, constraints):
        """筛选可用物品"""
        available = {}

        for code, item in self.items_data.items():
            # 检查类型限制
            if item.type == "生成器" and not constraints['allow_generators']:
                continue
            if item.type == "物品" and not constraints['allow_items']:
                continue
            if item.type == "器械" and not constraints['allow_equipment']:
                continue
            if item.type == "菜品" and not constraints['allow_dishes']:
                continue

            # 检查等级限制
            if item.level > constraints['max_item_level']:
                continue

            available[code] = item

        return available

    def _generate_day_rewards(self, day, energy_cost):
        """生成每日奖励"""
        rewards = []

        # 基础经验奖励
        base_exp = int(energy_cost * 0.8)
        rewards.append({'Currency': 'exp', 'Amount': base_exp})

        # 能量奖励
        energy_reward = max(5, int(energy_cost * 0.1))
        rewards.append({'Currency': 'energy', 'Amount': energy_reward})

        # 随机额外奖励
        if random.random() < 0.3:  # 30%概率获得额外奖励
            bonus_items = ['gem', 'pd_1_1', 'pd_2_1', 'eq_1_1']
            bonus_item = random.choice(bonus_items)
            rewards.append({'Currency': bonus_item, 'Amount': 1})

        return rewards

    def _calculate_estimated_time(self, day_order):
        """计算预估完成时间（分钟）"""
        base_time = 0

        for req in day_order.requirements:
            item_code = req['Type']
            count = req['Count']

            if item_code in self.items_data:
                item = self.items_data[item_code]
                # 基于频率和容量计算时间
                time_per_item = max(1, item.frequency * 2)  # 简化计算
                base_time += time_per_item * count

        # 添加基础操作时间
        return max(10, base_time + 5)

    def _calculate_difficulty(self, day_order, constraints):
        """计算难度等级"""
        difficulty_score = 0

        # 基于能量消耗
        energy_ratio = day_order.total_energy / constraints['max_energy_per_day']
        difficulty_score += energy_ratio * 40

        # 基于物品数量
        item_count = len(day_order.requirements)
        difficulty_score += item_count * 10

        # 基于物品等级
        max_level = 0
        for req in day_order.requirements:
            item_code = req['Type']
            if item_code in self.items_data:
                max_level = max(max_level, self.items_data[item_code].level)

        difficulty_score += max_level * 5

        # 转换为难度等级
        if difficulty_score < 30:
            return "简单"
        elif difficulty_score < 60:
            return "普通"
        elif difficulty_score < 90:
            return "困难"
        else:
            return "专家"

    def _update_plan_display(self):
        """更新订单计划显示"""
        # 清空现有数据
        for item in self.plan_tree.get_children():
            self.plan_tree.delete(item)

        if not self.current_plan:
            return

        # 添加计划总览
        plan_node = self.plan_tree.insert("", "end", text=f"{self.current_plan.shop_name}店铺计划",
                                         values=("", f"{self.current_plan.total_days}天",
                                               f"总奖励:{len(self.current_plan.total_rewards)}",
                                               f"{self.current_plan.total_energy:.1f}", "", ""))

        # 添加每日订单
        for day_order in self.current_plan.daily_orders:
            # 格式化需求物品
            req_text = []
            for req in day_order.requirements:
                item_code = req['Type']
                count = req['Count']
                item_name = self.items_data.get(item_code, ShopItem(code=item_code)).name or item_code
                req_text.append(f"{item_name}x{count}")

            # 格式化奖励
            reward_text = []
            for reward in day_order.rewards:
                currency = reward['Currency']
                amount = reward['Amount']
                reward_text.append(f"{currency}x{amount}")

            # 添加到树形视图
            day_node = self.plan_tree.insert(plan_node, "end", text=f"第{day_order.day}天",
                                           values=(day_order.day,
                                                 "; ".join(req_text[:2]) + ("..." if len(req_text) > 2 else ""),
                                                 "; ".join(reward_text[:2]) + ("..." if len(reward_text) > 2 else ""),
                                                 f"{day_order.total_energy:.1f}",
                                                 f"{day_order.estimated_time}分钟",
                                                 day_order.difficulty))

            # 添加详细需求
            for req in day_order.requirements:
                item_code = req['Type']
                count = req['Count']
                energy = req['Energy']
                item_name = self.items_data.get(item_code, ShopItem(code=item_code)).name or item_code

                self.plan_tree.insert(day_node, "end", text=f"  需求: {item_name}",
                                    values=("", f"x{count}", "", f"{energy:.1f}", "", ""))

        # 展开根节点
        self.plan_tree.item(plan_node, open=True)

    def _update_items_display(self):
        """更新物品列表显示"""
        # 清空现有数据
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # 添加物品数据
        for code, item in self.items_data.items():
            self.items_tree.insert("", "end", values=(
                code,
                item.name or code,
                item.type,
                item.level,
                item.energy_cost,
                item.frequency
            ))

    def _update_statistics(self):
        """更新统计信息"""
        self.stats_text.delete(1.0, END)

        if not self.items_data:
            self.stats_text.insert(END, "暂无数据")
            return

        # 物品类型统计
        type_count = defaultdict(int)
        level_count = defaultdict(int)
        series_count = defaultdict(int)

        for item in self.items_data.values():
            type_count[item.type] += 1
            level_count[item.level] += 1
            series_count[item.series] += 1

        self.stats_text.insert(END, "=== 物品统计 ===\n")
        self.stats_text.insert(END, f"总物品数: {len(self.items_data)}\n\n")

        self.stats_text.insert(END, "类型分布:\n")
        for item_type, count in type_count.items():
            self.stats_text.insert(END, f"  {item_type}: {count}个\n")

        self.stats_text.insert(END, "\n等级分布:\n")
        for level in sorted(level_count.keys()):
            count = level_count[level]
            self.stats_text.insert(END, f"  等级{level}: {count}个\n")

        self.stats_text.insert(END, "\n系列分布:\n")
        for series in sorted(series_count.keys()):
            count = series_count[series]
            self.stats_text.insert(END, f"  系列{series}: {count}个\n")

        # 如果有当前计划，显示计划统计
        if self.current_plan:
            self.stats_text.insert(END, "\n=== 当前计划统计 ===\n")
            self.stats_text.insert(END, f"店铺: {self.current_plan.shop_name}\n")
            self.stats_text.insert(END, f"章节: {self.current_plan.chapter_id}\n")
            self.stats_text.insert(END, f"总天数: {self.current_plan.total_days}\n")
            self.stats_text.insert(END, f"总能量: {self.current_plan.total_energy:.1f}\n")
            self.stats_text.insert(END, f"平均每日能量: {self.current_plan.total_energy/self.current_plan.total_days:.1f}\n")

            # 难度分布
            difficulty_count = defaultdict(int)
            for day_order in self.current_plan.daily_orders:
                difficulty_count[day_order.difficulty] += 1

            self.stats_text.insert(END, "\n难度分布:\n")
            for difficulty, count in difficulty_count.items():
                self.stats_text.insert(END, f"  {difficulty}: {count}天\n")

    def _on_order_double_click(self, event):
        """订单双击事件"""
        selection = self.plan_tree.selection()
        if selection:
            item = self.plan_tree.item(selection[0])
            text = item['text']

            if "第" in text and "天" in text:
                # 显示订单详情
                self._show_order_details(selection[0])

    def _on_item_selected(self, event):
        """物品选择事件"""
        selection = self.items_tree.selection()
        if selection:
            item_values = self.items_tree.item(selection[0])['values']
            if item_values:
                item_code = item_values[0]
                self._show_item_details(item_code)

    def _on_item_filter_changed(self, event):
        """物品筛选改变事件"""
        filter_text = self.item_filter_var.get().lower()

        # 清空现有显示
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # 添加筛选后的物品
        for code, item in self.items_data.items():
            if (filter_text in code.lower() or
                filter_text in (item.name or "").lower() or
                filter_text in item.type.lower()):

                self.items_tree.insert("", "end", values=(
                    code,
                    item.name or code,
                    item.type,
                    item.level,
                    item.energy_cost,
                    item.frequency
                ))

    def _show_order_details(self, tree_item):
        """显示订单详情"""
        # 获取订单信息
        item_text = self.plan_tree.item(tree_item)['text']

        if self.current_plan and "第" in item_text and "天" in item_text:
            day_num = int(re.search(r'第(\d+)天', item_text).group(1))
            day_order = self.current_plan.daily_orders[day_num - 1]

            # 切换到详细信息标签页并显示
            self.notebook.select(2)
            self.details_text.delete(1.0, END)

            self.details_text.insert(END, f"=== {item_text} 详情 ===\n\n")
            self.details_text.insert(END, f"总能量消耗: {day_order.total_energy:.1f}\n")
            self.details_text.insert(END, f"预估时间: {day_order.estimated_time}分钟\n")
            self.details_text.insert(END, f"难度等级: {day_order.difficulty}\n\n")

            self.details_text.insert(END, "需求物品:\n")
            for req in day_order.requirements:
                item_code = req['Type']
                count = req['Count']
                energy = req['Energy']
                item = self.items_data.get(item_code, ShopItem(code=item_code))

                self.details_text.insert(END, f"  • {item.name or item_code} x{count}\n")
                self.details_text.insert(END, f"    代码: {item_code}\n")
                self.details_text.insert(END, f"    类型: {item.type}\n")
                self.details_text.insert(END, f"    等级: {item.level}\n")
                self.details_text.insert(END, f"    能量消耗: {energy:.1f}\n\n")

            self.details_text.insert(END, "奖励物品:\n")
            for reward in day_order.rewards:
                currency = reward['Currency']
                amount = reward['Amount']
                self.details_text.insert(END, f"  • {currency} x{amount}\n")

    def _show_item_details(self, item_code):
        """显示物品详情"""
        if item_code not in self.items_data:
            return

        item = self.items_data[item_code]

        # 切换到详细信息标签页并显示
        self.notebook.select(2)
        self.details_text.delete(1.0, END)

        # 填充物品详情
        self.details_text.insert(END, f"=== {item.name or item_code} ===\n\n")
        self.details_text.insert(END, f"代码: {item.code}\n")
        self.details_text.insert(END, f"类型: {item.type}\n")
        self.details_text.insert(END, f"等级: {item.level}\n")
        self.details_text.insert(END, f"系列: {item.series}\n")
        self.details_text.insert(END, f"能量消耗: {item.energy_cost}\n")
        self.details_text.insert(END, f"频率: {item.frequency}\n")
        self.details_text.insert(END, f"容量: {item.capacity}\n")
        self.details_text.insert(END, f"解锁价格: {item.unlock_price}\n")
        self.details_text.insert(END, f"分数: {item.score}\n\n")

        if item.category:
            self.details_text.insert(END, f"分类: {', '.join(map(str, item.category))}\n\n")

        if item.generated_items:
            self.details_text.insert(END, "产出物品:\n")
            for gen_item in item.generated_items:
                code = gen_item.get('Code', '')
                weight = gen_item.get('Weight', 0)
                self.details_text.insert(END, f"  • {code} (权重: {weight})\n")

    def _show_item_filter(self):
        """显示物品筛选器"""
        # 切换到物品列表标签页
        self.notebook.select(1)
        messagebox.showinfo("提示", "请在物品列表标签页的筛选框中输入关键词进行筛选")

    def _clear_results(self):
        """清空结果"""
        self.current_plan = None

        # 清空显示
        for item in self.plan_tree.get_children():
            self.plan_tree.delete(item)

        self.details_text.delete(1.0, END)
        self._update_statistics()

        self.status_var.set("结果已清空")

    def _save_plan(self):
        """保存计划"""
        if not self.current_plan:
            messagebox.showwarning("警告", "没有可保存的计划")
            return

        filename = filedialog.asksaveasfilename(
            title="保存订单计划",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                # 将计划转换为可序列化的格式
                plan_data = {
                    'shop_name': self.current_plan.shop_name,
                    'chapter_id': self.current_plan.chapter_id,
                    'total_days': self.current_plan.total_days,
                    'total_energy': self.current_plan.total_energy,
                    'total_rewards': self.current_plan.total_rewards,
                    'constraints': self.current_plan.constraints,
                    'daily_orders': []
                }

                for day_order in self.current_plan.daily_orders:
                    plan_data['daily_orders'].append({
                        'day': day_order.day,
                        'group_id': day_order.group_id,
                        'chapter_id': day_order.chapter_id,
                        'requirements': day_order.requirements,
                        'rewards': day_order.rewards,
                        'total_energy': day_order.total_energy,
                        'estimated_time': day_order.estimated_time,
                        'difficulty': day_order.difficulty
                    })

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(plan_data, f, ensure_ascii=False, indent=2)

                self.status_var.set(f"计划已保存到: {filename}")
                messagebox.showinfo("成功", "计划保存成功")

            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

    def _load_plan(self):
        """加载计划"""
        filename = filedialog.askopenfilename(
            title="加载订单计划",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    plan_data = json.load(f)

                # 重建计划对象
                plan = ShopOrderPlan(
                    shop_name=plan_data['shop_name'],
                    chapter_id=plan_data['chapter_id'],
                    total_days=plan_data['total_days']
                )

                plan.total_energy = plan_data['total_energy']
                plan.total_rewards = plan_data['total_rewards']
                plan.constraints = plan_data['constraints']

                for day_data in plan_data['daily_orders']:
                    day_order = DayOrder(
                        day=day_data['day'],
                        group_id=day_data['group_id'],
                        chapter_id=day_data['chapter_id']
                    )
                    day_order.requirements = day_data['requirements']
                    day_order.rewards = day_data['rewards']
                    day_order.total_energy = day_data['total_energy']
                    day_order.estimated_time = day_data['estimated_time']
                    day_order.difficulty = day_data['difficulty']

                    plan.daily_orders.append(day_order)

                self.current_plan = plan

                # 更新显示
                self._update_plan_display()
                self._update_statistics()

                self.status_var.set(f"计划已加载: {filename}")
                messagebox.showinfo("成功", "计划加载成功")

            except Exception as e:
                messagebox.showerror("错误", f"加载失败: {e}")


def main():
    """主程序入口"""
    root = tk.Tk()
    app = SimpleShopOrderGenerator(root)
    root.mainloop()


if __name__ == "__main__":
    main()
