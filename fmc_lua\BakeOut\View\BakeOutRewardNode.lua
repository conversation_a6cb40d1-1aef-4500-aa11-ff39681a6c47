BakeOutRewardNode = {}
BakeOutRewardNode.__index = BakeOutRewardNode

function BakeOutRewardNode:UpdateContent(rewards, rank, window)
  local active = self.gameObject.activeSelf
  self.m_rewards = rewards
  self.m_window = window
  if rewards == nil or #rewards == 0 then
    if active then
      self.gameObject:SetActive(false)
    end
    return
  end
  if not active then
    self.gameObject:SetActive(true)
  end
  local bShowBox = 1 < #rewards or rank <= 3
  self.m_boxGo:SetActive(bShowBox)
  self.m_rewardImg.gameObject:SetActive(not bShowBox)
  self.m_rewardText.gameObject:SetActive(not bShowBox)
  if 1 <= rank and rank <= 3 then
    self.m_boxImg.sprite = self["m_boxSprite" .. rank]
  end
  if not bShowBox then
    local imageName
    local rewardData = rewards[1]
    if Table.Contain(EPropertyType, rewardData[PROPERTY_TYPE]) then
      imageName = EPropertySprite[rewardData[PROPERTY_TYPE]]
      self.m_rewardImg.transform:SetLocalScaleXY(1.2)
    else
      imageName = GM.ItemDataModel:GetSpriteName(rewardData[PROPERTY_TYPE])
      self.m_rewardImg.transform:SetLocalScaleXY(0.7)
    end
    SpriteUtil.SetImage(self.m_rewardImg, imageName, true)
    self.m_rewardText.text = 1 < rewardData[PROPERTY_COUNT] and "x" .. rewardData[PROPERTY_COUNT] or ""
  end
end

function BakeOutRewardNode:OnBoxClick()
  self.m_window:ShowRewardTip(self.m_rewards, self.m_boxGo.transform, -4, 42.5)
end
