BundleChainModel = setmetatable({
  DataGroupClass = BundleChainServerConfigDataGroup
}, BundleActivityBaseModel)
BundleChainModel.__index = BundleChainModel
local DBKey = {
  HasPaidThisRound = "hptr",
  CurIndex = "curIndex",
  ChainPurchaseInfo = "cpi"
}

function BundleChainModel:CanShowEntry(dataGroup)
  return not self:HasPurchaseFinished(dataGroup)
end

function BundleChainModel:HasPurchaseFinished(dataGroup)
  return dataGroup:GetConfigDataByIndex(self:GetCurChainStep(dataGroup)) == nil
end

function BundleChainModel:_CanTriggerBundleWindow(dataGroup, eTriggerType)
  return BundleActivityBaseModel._CanTriggerBundleWindow(self, dataGroup, eTriggerType) and not self:HasPurchaseFinished(dataGroup)
end

function BundleChainModel:_OnPurchaseFinished(dataGroup, bundleId)
  if self:HasPurchaseFinished(dataGroup) then
    self:OnBundleStateChanged(dataGroup)
  end
end

function BundleChainModel:_OnBundleTriggered(dataGroup)
  self:_SetStep(dataGroup, 1)
  self:_SetPaidThisRound(dataGroup, false)
end

function BundleChainModel:OnBundleStateChanged(dataGroup, bTriggered)
  if self:GetBundleState(dataGroup) == EBundleState.Opening and bTriggered then
    self:_OnBundleTriggered(dataGroup)
  elseif self:GetBundleState(dataGroup) == EBundleState.Close then
    self:TryRecoverFreeRewards(dataGroup)
  end
  BundleActivityBaseModel.OnBundleStateChanged(self, dataGroup, bTriggered)
end

function BundleChainModel:OnSceneViewLoaded()
  self.m_bSceneViewLoaded = true
  for _, dataGroup in ipairs(self.m_dataGroups) do
    self:TryRecoverFreeRewards(dataGroup)
  end
end

function BundleChainModel:TryRecoverFreeRewards(dataGroup, bImmediately)
  if not self.m_bSceneViewLoaded then
    return false
  end
  if self:GetBundleState(dataGroup) == EBundleState.Close and self:_HasPaidThisRound(dataGroup) then
    local rewards, step = self:_GetRecordPurchaseRewards(dataGroup, false)
    if not Table.IsEmpty(rewards) then
      self:_AcquireRecoverRewards(rewards, dataGroup:GetBundleUIType(), bImmediately)
      self:_SetStep(dataGroup, step)
      return true
    end
  end
  return false
end

function BundleChainModel:BuyBundle(dataGroup, callback)
  local bundleData = self:GetCurStepBundleConfigData(dataGroup)
  GM.BIManager:LogAction(EBIType.BundleBuy, dataGroup:GetGroupId() .. "_" .. self:GetCurChainStep(dataGroup))
  self:_BuyBundle(dataGroup, bundleData, callback)
end

function BundleChainModel:_BuyBundle(dataGroup, bundleData, callback)
  if bundleData:IsFree() then
    local eFree = self:_HasPaidThisRound(dataGroup) and EPropertySource.Buy or EPropertySource.Give
    RewardApi.AcquireRewardsLogic(bundleData:GetGoods(), eFree, EBIType.BundleFree, nil, CacheItemType.Stack)
    self:_Set2NextStep(dataGroup)
    self:_OnPurchaseFinished(dataGroup, bundleData:GetBundleId())
    if callback ~= nil then
      callback(bundleData:GetGoods())
    end
  else
    local payId = bundleData:GetPurchaseId()
    local rewards, step = self:_GetRecordPurchaseRewards(dataGroup, true)
    local freeRewards = self:_GetRecordPurchaseRewards(dataGroup, false, self:GetCurChainStep(dataGroup) + 1)
    local purchaseInfo = {
      purchaseId = payId,
      rewards = rewards,
      uiCode = dataGroup:GetBundleUIType(),
      step = step
    }
    self:_RecordPurchaseInfo(dataGroup:GetGroupId(), purchaseInfo)
    GM.BundleManager:Buy(self.m_bundleType, bundleData, function(rewards)
      local uiDefinition = BundleUIType[dataGroup:GetBundleUIType()]
      if self:_IsValidDataGroup(dataGroup) and (self:GetBundleState(dataGroup) == EBundleState.Opening or dataGroup:IsDisposable() and GM.UIManager:GetOpenedViewByName(uiDefinition.window) ~= nil) then
        self:_Set2NextStep(dataGroup)
        self:_SetPaidThisRound(dataGroup, true)
        self:_ReleasePurchaseInfo(dataGroup:GetGroupId())
        if callback ~= nil then
          callback(rewards)
        end
      else
        if not Table.IsEmpty(freeRewards) then
          RewardApi.AcquireRewardsLogic(freeRewards, EPropertySource.Buy, EBIType.ChainRestoreAfterFinish, nil, CacheItemType.Stack)
          if not Table.IsEmpty(purchaseInfo.rewards) then
            local validRewards = RewardApi.FilterRewards(purchaseInfo.rewards)
            if not Table.IsEmpty(validRewards) then
              local uiDefinition = BundleUIType[purchaseInfo.uiCode] or {}
              BundlePopupHelper.AddWindowToPopupChain(UIPrefabConfigName.BundleRewardWindow, validRewards, uiDefinition.titleText, true)
            end
          end
        end
        if self:_IsValidDataGroup(dataGroup) then
          if self:GetCurChainStep(dataGroup) < purchaseInfo.step then
            self:_SetStep(dataGroup, step)
          end
          self:_ReleasePurchaseInfo(dataGroup:GetGroupId())
        end
      end
    end, true)
  end
end

function BundleChainModel:GetCurStepBundleConfigData(dataGroup)
  local step = self:GetCurChainStep(dataGroup)
  return dataGroup:GetConfigDataByIndex(step)
end

function BundleChainModel:_RecordPurchaseInfo(groupId, info)
  self:_SetBundleDBData(groupId, DBKey.ChainPurchaseInfo, StringUtil.Replace(json.encode(info), ",", "@"))
end

function BundleChainModel:_GetRecordPurchaseInfo(groupId)
  local strInfo = self:_GetBundleDBData(groupId, DBKey.ChainPurchaseInfo)
  if not StringUtil.IsNilOrEmpty(strInfo) then
    return json.decode(StringUtil.Replace(strInfo, "@", ","))
  end
  return nil
end

function BundleChainModel:_ReleasePurchaseInfo(groupId)
  self:_RemoveBundleDBData(groupId, DBKey.ChainPurchaseInfo)
end

function BundleChainModel:_GetRecordPurchaseRewards(dataGroup, bIAPBefore, step)
  local step = step or self:_GetCurChainStep(dataGroup)
  if step == nil then
    return
  end
  local rewards = {}
  local bIAP = false
  local circleLength = #(dataGroup:GetCircleBundleIds() or {})
  local circleStep = 0
  while true do
    local data = dataGroup:GetConfigDataByIndex(step)
    if data == nil or 0 < circleLength and circleLength <= circleStep then
      break
    end
    if data:IsFree() then
      if step > #(dataGroup:GetBundleIds() or {}) then
        circleStep = circleStep + 1
      end
      Table.ListAppend(rewards, data:GetGoods())
    elseif not bIAP and bIAPBefore then
      bIAP = true
      Table.ListAppend(rewards, data:GetGoods())
    else
      break
    end
    step = step + 1
  end
  return rewards, step
end

function BundleChainModel:_IsValidDataGroup(dataGroup)
  for _, validDataGroup in ipairs(self.m_dataGroups or {}) do
    if validDataGroup:GetGroupId() == dataGroup:GetGroupId() then
      return true
    end
  end
  return false
end

function BundleChainModel:CanOpenRecoverWindowImmediately(uiCode)
  local uiDefinition = BundleUIType[uiCode]
  if uiDefinition ~= nil and not Table.IsEmpty(uiDefinition.entryInfo) then
    for _, info in ipairs(uiDefinition.entryInfo) do
      if info.scene == EBundleEntryScene.Activity and info.window ~= nil and GM.UIManager:GetOpenedViewByName(info.window) ~= nil then
        return true
      end
    end
  end
  return false
end

function BundleChainModel:_AcquireRecoverRewards(rewards, uiCode, bImmediately)
  if bImmediately == nil then
    bImmediately = self:CanOpenRecoverWindowImmediately(uiCode)
  end
  if not Table.IsEmpty(rewards) then
    RewardApi.AcquireRewardsLogic(rewards, EPropertySource.Buy, EBIType.ChainRestoreAfterFinish, nil, CacheItemType.Stack)
    local validRewards = RewardApi.FilterRewards(rewards)
    if not Table.IsEmpty(validRewards) then
      local uiDefinition = BundleUIType[uiCode] or {}
      if bImmediately then
        GM.UIManager:OpenView(UIPrefabConfigName.BundleRewardWindow, validRewards, uiDefinition.titleText, true)
      else
        BundlePopupHelper.AddWindowToPopupChain(UIPrefabConfigName.BundleRewardWindow, validRewards, uiDefinition.titleText, true)
      end
    end
  end
end

function BundleChainModel:OnRestoreIAPRewards(eIAPType)
  local purchaseInfo, curStepData
  for _, dataGroup in ipairs(self.m_dataGroups) do
    purchaseInfo = self:_GetRecordPurchaseInfo(dataGroup:GetGroupId())
    if purchaseInfo ~= nil and purchaseInfo.purchaseId == eIAPType and not Table.IsEmpty(purchaseInfo.rewards) then
      self:_ReleasePurchaseInfo(dataGroup:GetGroupId())
      curStepData = self:GetCurStepBundleConfigData(dataGroup)
      local rewards
      if self:GetBundleState(dataGroup) == EBundleState.Opening and curStepData:GetPurchaseId() == purchaseInfo.purchaseId then
        self:_SetPaidThisRound(dataGroup, true)
        self:_Set2NextStep(dataGroup)
        rewards = curStepData:GetGoods()
      else
        rewards = purchaseInfo.rewards
      end
      if not Table.IsEmpty(rewards) and not GM.BundleManager:CheckNeedGiveGems(rewards) then
        self:_AcquireRecoverRewards(rewards, purchaseInfo.uiCode)
        self:OnPurchaseFinished(dataGroup:GetGroupId(), curStepData:GetBundleId())
        Log.Info("链式礼包补单，优先取存储的购买数据进行补发")
        return true, dataGroup
      end
      return false
    end
  end
  local curBundleData, curDataGroup, backupBundleData, backupDataGroup
  for _, dataGroup in ipairs(self.m_dataGroups or {}) do
    curBundleData = self:GetCurStepBundleConfigData(dataGroup)
    if curBundleData and curBundleData:GetPurchaseId() == eIAPType then
      curDataGroup = dataGroup
      break
    end
    curBundleData = nil
    if backupBundleData == nil then
      backupBundleData = dataGroup:GetConfigDataByPurchaseId(eIAPType)
      if backupBundleData ~= nil then
        backupDataGroup = dataGroup
      end
    end
  end
  if curDataGroup ~= nil then
    local recoverRewards
    if self:GetBundleState(curDataGroup) == EBundleState.Opening then
      self:_SetPaidThisRound(curDataGroup, true)
      self:_Set2NextStep(curDataGroup)
      recoverRewards = curBundleData:GetGoods()
    else
      local rewards, step = self:_GetRecordPurchaseRewards(curDataGroup, true)
      recoverRewards = rewards
      self:_SetStep(curDataGroup, step)
    end
    if not GM.BundleManager:CheckNeedGiveGems(recoverRewards) then
      self:_AcquireRecoverRewards(recoverRewards, curDataGroup:GetBundleUIType())
      Log.Info("链式礼包补单，第二选择: 当前step与购买项正好相等的情况")
      return true, curDataGroup
    end
    return false
  end
  if backupBundleData ~= nil and backupDataGroup ~= nil then
    local rewards = self:_GetRecordPurchaseRewards(backupDataGroup, true, backupBundleData:GetStep())
    if not GM.BundleManager:CheckNeedGiveGems(rewards) then
      self:_AcquireRecoverRewards(rewards, backupDataGroup:GetBundleUIType())
      Log.Info("链式礼包补单，最终兜底就是任选一个支付项尝试补发")
      return true, backupDataGroup
    end
  end
  return false
end

function BundleChainModel:_HasPaidThisRound(dataGroup)
  return self:_GetBundleDBData(dataGroup:GetGroupId(), DBKey.HasPaidThisRound) == "1"
end

function BundleChainModel:_SetPaidThisRound(dataGroup, paid)
  local groupId = dataGroup:GetGroupId()
  if not paid then
    self:_RemoveBundleDBData(groupId, DBKey.HasPaidThisRound)
  else
    self:_SetBundleDBData(groupId, DBKey.HasPaidThisRound, "1")
  end
end

function BundleChainModel:_Set2NextStep(dataGroup)
  if dataGroup == nil then
    return false
  end
  local curStep = self:_GetCurChainStep(dataGroup) or 1
  self:_SetStep(dataGroup, curStep + 1)
  EventDispatcher.DispatchEvent(EEventType.BundleChainActivityContentUpdateEvent)
  return true
end

function BundleChainModel:_SetStep(dataGroup, step)
  self:_SetBundleDBData(dataGroup:GetGroupId(), DBKey.CurIndex, tostring(step))
end

function BundleChainModel:GetCurChainStep(dataGroup)
  return self:_GetCurChainStep(dataGroup) or 1
end

function BundleChainModel:_GetCurChainStep(dataGroup)
  return tonumber(self:_GetBundleDBData(dataGroup:GetGroupId(), DBKey.CurIndex))
end
