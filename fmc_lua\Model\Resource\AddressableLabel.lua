AddressableLabel = {
  Default = "Default",
  BP1 = "BP1",
  BP2 = "BP2",
  BP3 = "BP3",
  BP4 = "BP4",
  BP5 = "BP5",
  BP6 = "BP6",
  BP7 = "BP7",
  BPCommon = "BPCommon",
  BakeOut = "BakeOut",
  BlindChest1 = "BlindChest1",
  BlindChestCommon = "BlindChestCommon",
  BoaerThailand = "BoaerThailand",
  BoardMorocco = "BoardMorocco",
  BoardNice = "BoardNice",
  BoardOrleans = "BoardOrleans",
  ChapterBBQ = "ChapterBBQ",
  ChapterBakery = "ChapterBakery",
  ChapterDimSum = "ChapterDimSum",
  ChapterMarket = "ChapterMarket",
  ChapterMorocco = "ChapterMorocco",
  ChapterNice = "ChapterNice",
  ChapterOrleans = "ChapterOrleans",
  ChapterOttoman = "ChapterOttoman",
  ChapterPasta = "ChapterPasta",
  ChapterSausage = "ChapterSausage",
  ChapterSeafood = "ChapterSeafood",
  ChapterSushi = "ChapterSushi",
  ChapterTacos = "ChapterTacos",
  ChapterTapas = "ChapterTapas",
  ChapterThailand = "ChapterThailand",
  ChapterWine = "ChapterWine",
  CoinRace = "CoinRace",
  DigActivityCommon = "DigActivityCommon",
  DigItems1 = "DigItems1",
  ExtraBoard1 = "ExtraBoard1",
  ExtraBoard2 = "ExtraBoard2",
  ExtraBoard3 = "ExtraBoard3",
  ExtraBoard4 = "ExtraBoard4",
  ExtraBoard5 = "ExtraBoard5",
  ExtraBoardCommon = "ExtraBoardCommon",
  Lua = "Lua",
  PkRace = "PkRace",
  PkRaceCommon = "PkRaceCommon",
  ProgressActivity1 = "ProgressActivity1",
  ProgressActivityCommon = "ProgressActivityCommon",
  SurpriseChest = "SurpriseChest",
  SurpriseChestCommon = "SurpriseChestCommon",
  TreasureDig = "TreasureDig"
}
if GameConfig.IsTestMode() then
  setmetatable(AddressableLabel, {
    __index = function(_, key)
      Debug.LogError("AddressableLabel try to index a nil key: " .. tostring(key) .. "\n" .. debug.traceback())
      return key
    end
  })
end

function AddressableLabel.HasLabel(name)
  if name == nil then
    return false
  end
  return rawget(AddressableLabel, name) ~= nil
end
