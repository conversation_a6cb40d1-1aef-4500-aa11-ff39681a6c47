ItemModelHelper = {}

function ItemModelHelper.GetSkipInfo(itemModel, inBoard)
  if itemModel == nil then
    return false, nil
  end
  if inBoard == false then
    return false, nil
  end
  local itemTransform = itemModel:GetComponent(ItemTransform)
  if itemTransform ~= nil and itemTransform:GetDuration() ~= nil then
    return true, itemTransform:GetSpeedUpCost()
  end
  local itemSpread = itemModel:GetComponent(ItemSpread)
  if itemSpread ~= nil and itemSpread:ShowCountDown() then
    return true, itemSpread:GetSpeedUpCost()
  end
  local itemCook = itemModel:GetComponent(ItemCook)
  if itemCook ~= nil and itemCook:GetState() == EItemCookState.Cooking then
    local cost, isSkipProp = itemCook:GetSpeedUpCost()
    return true, cost, isSkipProp
  end
  return false, nil
end

function ItemModelHelper.MergeCost(item1, item2)
  return {
    costEnergy = (item1.costEnergy or 0) + (item2.costEnergy or 0),
    costEnergyCurDay = (item1.costEnergyCurDay or 0) + (item2.costEnergyCurDay or 0),
    shopGemCost = (item1.shopGemCost or 0) + (item2.shopGemCost or 0),
    bubbleGemCost = (item1.bubbleGemCost or 0) + (item2.bubbleGemCost or 0),
    cookGemCost = (item1.cookGemCost or 0) + (item2.cookGemCost or 0),
    cookSkipPropCost = (item1.cookSkipPropCost or 0) + (item2.cookSkipPropCost or 0)
  }
end

function ItemModelHelper.SplitCost(item, splitCount)
  return {
    costEnergy = (item.costEnergy or 0) / splitCount,
    costEnergyCurDay = (item.costEnergyCurDay or 0) / splitCount,
    shopGemCost = (item.shopGemCost or 0) / splitCount,
    bubbleGemCost = (item.bubbleGemCost or 0) / splitCount,
    cookGemCost = (item.cookGemCost or 0) / splitCount,
    cookSkipPropCost = (item.cookSkipPropCost or 0) / splitCount
  }
end

function ItemModelHelper.GetCost(item)
  return {
    costEnergy = item.costEnergy,
    costEnergyCurDay = item.costEnergyCurDay,
    shopGemCost = item.shopGemCost,
    bubbleGemCost = item.bubbleGemCost,
    cookGemCost = item.cookGemCost,
    cookSkipPropCost = item.cookSkipPropCost
  }
end

local costFormat = "id:%s,e:%s,ed:%s,sg:%s,bg:%s,cg:%s,cs:%s"

function ItemModelHelper.FormatCost2String(item)
  return string.format(costFormat, item:GetCode(), item.costEnergy or 0, item.costEnergyCurDay or 0, item.shopGemCost or 0, item.bubbleGemCost or 0, item.cookGemCost or 0, item.cookSkipPropCost or 0)
end
