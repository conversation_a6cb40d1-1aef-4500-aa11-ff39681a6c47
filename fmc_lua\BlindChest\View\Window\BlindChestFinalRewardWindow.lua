BlindChestFinalRewardWindow = setmetatable({}, BoxRewardWindow)
BlindChestFinalRewardWindow.__index = BlindChestFinalRewardWindow

function BlindChestFinalRewardWindow:Init(...)
  BoxRewardWindow.Init(self, ...)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.AutoPopup
  })
end

function BlindChestFinalRewardWindow:SetSprite(imgName)
  SpriteUtil.SetImage(self.m_boxImg, imgName, true)
end

BlindChestTopRewardWindow = setmetatable({}, RewardWindow)
BlindChestTopRewardWindow.__index = BlindChestTopRewardWindow

function BlindChestTopRewardWindow:Init(...)
  RewardWindow.Init(self, ...)
  self:LogWindowAction(EBIType.UIActionType.Open, {
    EBIReferType.AutoPopup
  })
end
