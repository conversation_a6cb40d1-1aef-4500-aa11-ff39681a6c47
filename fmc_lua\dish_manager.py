"""
菜品库管理系统主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from tkinter import *
import json
import os
from datetime import datetime
from dish_database import DishDatabase, Dish, ProcessingStep

class DishManager:
    """菜品库管理器主界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("菜品库管理系统")
        self.root.geometry("1400x900")
        
        # 初始化数据库
        self.db = DishDatabase()
        
        # 当前用户ID（可以从登录系统获取）
        self.current_user = "admin"
        
        # 创建界面
        self._create_ui()
        
        # 加载数据
        self._load_data()
    
    def _create_ui(self):
        """创建用户界面"""
        # 创建主菜单
        menubar = Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = Menu(menubar, tearoff=0)
        file_menu.add_command(label="导入MC配置", command=self._import_mc_config)
        file_menu.add_command(label="导入FL配置", command=self._import_fl_config)
        file_menu.add_separator()
        file_menu.add_command(label="导出Excel", command=self._export_excel)
        file_menu.add_command(label="退出", command=self.root.quit)
        menubar.add_cascade(label="文件", menu=file_menu)
        
        # 管理菜单
        manage_menu = Menu(menubar, tearoff=0)
        manage_menu.add_command(label="管理食材", command=self._manage_ingredients)
        manage_menu.add_command(label="管理器械", command=self._manage_equipment)
        manage_menu.add_command(label="管理国家", command=self._manage_countries)
        manage_menu.add_command(label="管理餐厅主题", command=self._manage_themes)
        menubar.add_cascade(label="管理", menu=manage_menu)
        
        # 创建主框架
        main_paned = ttk.PanedWindow(self.root, orient=HORIZONTAL)
        main_paned.pack(fill=BOTH, expand=True, padx=5, pady=5)
        
        # 左侧搜索面板
        search_frame = ttk.Frame(main_paned, width=400)
        main_paned.add(search_frame, weight=1)
        
        # 右侧显示面板
        display_frame = ttk.Frame(main_paned)
        main_paned.add(display_frame, weight=2)
        
        # 创建搜索面板
        self._create_search_panel(search_frame)
        
        # 创建显示面板
        self._create_display_panel(display_frame)
        
        # 状态栏
        self.status_var = StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=SUNKEN)
        status_bar.pack(side=BOTTOM, fill=X)
    
    def _create_search_panel(self, parent):
        """创建搜索面板"""
        # 搜索条件
        search_frame = ttk.LabelFrame(parent, text="搜索条件", padding="10")
        search_frame.pack(fill=X, pady=5)
        
        # 菜品名称关键词
        ttk.Label(search_frame, text="菜品名称:").grid(row=0, column=0, sticky=W, padx=5, pady=2)
        self.name_keyword_var = StringVar()
        ttk.Entry(search_frame, textvariable=self.name_keyword_var, width=25).grid(
            row=0, column=1, sticky=W, padx=5, pady=2)
        
        # 食材选择（最多5个）
        ttk.Label(search_frame, text="食材(最多5个):").grid(row=1, column=0, sticky=NW, padx=5, pady=2)
        self.ingredients_frame = ttk.Frame(search_frame)
        self.ingredients_frame.grid(row=1, column=1, sticky=W, padx=5, pady=2)
        self.selected_ingredients = []
        self._create_ingredient_selector()
        
        # 器械选择（最多5个）
        ttk.Label(search_frame, text="器械(最多5个):").grid(row=2, column=0, sticky=NW, padx=5, pady=2)
        self.equipment_frame = ttk.Frame(search_frame)
        self.equipment_frame.grid(row=2, column=1, sticky=W, padx=5, pady=2)
        self.selected_equipment = []
        self._create_equipment_selector()
        
        # 国家选择
        ttk.Label(search_frame, text="国家:").grid(row=3, column=0, sticky=W, padx=5, pady=2)
        self.country_var = StringVar()
        self.country_combo = ttk.Combobox(search_frame, textvariable=self.country_var, 
                                         state="readonly", width=22)
        self.country_combo.grid(row=3, column=1, sticky=W, padx=5, pady=2)
        
        # 餐厅主题选择
        ttk.Label(search_frame, text="餐厅主题:").grid(row=4, column=0, sticky=W, padx=5, pady=2)
        self.theme_var = StringVar()
        self.theme_combo = ttk.Combobox(search_frame, textvariable=self.theme_var,
                                       state="readonly", width=22)
        self.theme_combo.grid(row=4, column=1, sticky=W, padx=5, pady=2)
        
        # 来源选择
        ttk.Label(search_frame, text="来源:").grid(row=5, column=0, sticky=W, padx=5, pady=2)
        self.source_var = StringVar()
        source_combo = ttk.Combobox(search_frame, textvariable=self.source_var,
                                   values=["", "MC", "FL", "其他"], state="readonly", width=22)
        source_combo.grid(row=5, column=1, sticky=W, padx=5, pady=2)
        
        # 搜索按钮
        button_frame = ttk.Frame(search_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="搜索", command=self._search_dishes).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="清空", command=self._clear_search).pack(side=LEFT, padx=5)
        ttk.Button(button_frame, text="新建菜品", command=self._create_new_dish).pack(side=LEFT, padx=5)
        
        # 搜索结果统计
        stats_frame = ttk.LabelFrame(parent, text="搜索统计", padding="10")
        stats_frame.pack(fill=X, pady=5)
        
        self.stats_text = Text(stats_frame, height=6, wrap=WORD, font=("Consolas", 9))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=LEFT, fill=BOTH, expand=True)
        stats_scrollbar.pack(side=RIGHT, fill=Y)
    
    def _create_ingredient_selector(self):
        """创建食材选择器"""
        # 食材输入框和按钮
        input_frame = ttk.Frame(self.ingredients_frame)
        input_frame.pack(fill=X)
        
        self.ingredient_var = StringVar()
        ingredient_combo = ttk.Combobox(input_frame, textvariable=self.ingredient_var, width=15)
        ingredient_combo.pack(side=LEFT, padx=2)
        
        ttk.Button(input_frame, text="添加", 
                  command=lambda: self._add_ingredient()).pack(side=LEFT, padx=2)
        
        # 已选食材显示
        self.ingredients_listbox = Listbox(self.ingredients_frame, height=4, width=25)
        self.ingredients_listbox.pack(fill=X, pady=2)
        self.ingredients_listbox.bind('<Double-Button-1>', self._remove_ingredient)
        
        # 更新食材下拉列表
        ingredients = self.db.get_ingredients()
        ingredient_combo['values'] = ingredients
    
    def _create_equipment_selector(self):
        """创建器械选择器"""
        # 器械输入框和按钮
        input_frame = ttk.Frame(self.equipment_frame)
        input_frame.pack(fill=X)
        
        self.equipment_var = StringVar()
        equipment_combo = ttk.Combobox(input_frame, textvariable=self.equipment_var, width=15)
        equipment_combo.pack(side=LEFT, padx=2)
        
        ttk.Button(input_frame, text="添加",
                  command=lambda: self._add_equipment()).pack(side=LEFT, padx=2)
        
        # 已选器械显示
        self.equipment_listbox = Listbox(self.equipment_frame, height=4, width=25)
        self.equipment_listbox.pack(fill=X, pady=2)
        self.equipment_listbox.bind('<Double-Button-1>', self._remove_equipment)
        
        # 更新器械下拉列表
        equipment = self.db.get_equipment()
        equipment_combo['values'] = equipment
    
    def _create_display_panel(self, parent):
        """创建显示面板"""
        # 创建Notebook用于多标签页
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=BOTH, expand=True)
        
        # 搜索结果标签页
        self._create_results_tab()
        
        # 菜品详情标签页
        self._create_details_tab()
        
        # 编辑菜品标签页
        self._create_edit_tab()
    
    def _create_results_tab(self):
        """创建搜索结果标签页"""
        results_frame = ttk.Frame(self.notebook)
        self.notebook.add(results_frame, text="搜索结果")
        
        # 工具栏
        toolbar = ttk.Frame(results_frame)
        toolbar.pack(fill=X, pady=5)
        
        ttk.Button(toolbar, text="查看详情", command=self._view_details).pack(side=LEFT, padx=5)
        ttk.Button(toolbar, text="编辑菜品", command=self._edit_dish).pack(side=LEFT, padx=5)
        ttk.Button(toolbar, text="删除菜品", command=self._delete_dish).pack(side=LEFT, padx=5)
        ttk.Button(toolbar, text="导出选中", command=self._export_selected).pack(side=LEFT, padx=5)
        
        # 搜索结果列表
        columns = ("dish_id", "name", "source", "theme", "country", "steps", "updated_time")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=20)
        
        self.results_tree.heading("dish_id", text="菜品ID")
        self.results_tree.heading("name", text="菜品名称")
        self.results_tree.heading("source", text="来源")
        self.results_tree.heading("theme", text="餐厅主题")
        self.results_tree.heading("country", text="国家")
        self.results_tree.heading("steps", text="加工步骤数")
        self.results_tree.heading("updated_time", text="更新时间")
        
        self.results_tree.column("dish_id", width=100)
        self.results_tree.column("name", width=200)
        self.results_tree.column("source", width=60)
        self.results_tree.column("theme", width=100)
        self.results_tree.column("country", width=80)
        self.results_tree.column("steps", width=80)
        self.results_tree.column("updated_time", width=150)
        
        # 添加滚动条
        results_scrollbar = ttk.Scrollbar(results_frame, orient=VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_tree.pack(side=LEFT, fill=BOTH, expand=True)
        results_scrollbar.pack(side=RIGHT, fill=Y)
        
        # 绑定双击事件
        self.results_tree.bind("<Double-1>", lambda e: self._view_details())
    
    def _create_details_tab(self):
        """创建菜品详情标签页"""
        details_frame = ttk.Frame(self.notebook)
        self.notebook.add(details_frame, text="菜品详情")
        
        self.details_text = Text(details_frame, wrap=WORD, font=("Consolas", 10))
        details_scrollbar = ttk.Scrollbar(details_frame, orient=VERTICAL, command=self.details_text.yview)
        self.details_text.configure(yscrollcommand=details_scrollbar.set)
        
        self.details_text.pack(side=LEFT, fill=BOTH, expand=True)
        details_scrollbar.pack(side=RIGHT, fill=Y)
    
    def _create_edit_tab(self):
        """创建编辑菜品标签页"""
        edit_frame = ttk.Frame(self.notebook)
        self.notebook.add(edit_frame, text="编辑菜品")
        
        # 这里将在后续添加编辑界面
        ttk.Label(edit_frame, text="菜品编辑界面将在此处实现", 
                 font=("Arial", 12)).pack(expand=True)
    
    def _load_data(self):
        """加载数据"""
        # 加载国家和餐厅主题到下拉列表
        countries = self.db.get_countries()
        country_values = [""] + [f"{k} - {v}" for k, v in countries.items()]
        self.country_combo['values'] = country_values
        
        themes = self.db.get_restaurant_themes()
        theme_values = [""] + [f"{k} - {v}" for k, v in themes.items()]
        self.theme_combo['values'] = theme_values
        
        # 初始搜索显示所有菜品
        self._search_dishes()
    
    def _add_ingredient(self):
        """添加食材"""
        ingredient = self.ingredient_var.get().strip()
        if ingredient and ingredient not in self.selected_ingredients and len(self.selected_ingredients) < 5:
            self.selected_ingredients.append(ingredient)
            self.ingredients_listbox.insert(END, ingredient)
            self.ingredient_var.set("")
        elif len(self.selected_ingredients) >= 5:
            messagebox.showwarning("警告", "最多只能选择5个食材")
    
    def _remove_ingredient(self, event):
        """移除食材"""
        selection = self.ingredients_listbox.curselection()
        if selection:
            index = selection[0]
            ingredient = self.ingredients_listbox.get(index)
            self.ingredients_listbox.delete(index)
            self.selected_ingredients.remove(ingredient)
    
    def _add_equipment(self):
        """添加器械"""
        equipment = self.equipment_var.get().strip()
        if equipment and equipment not in self.selected_equipment and len(self.selected_equipment) < 5:
            self.selected_equipment.append(equipment)
            self.equipment_listbox.insert(END, equipment)
            self.equipment_var.set("")
        elif len(self.selected_equipment) >= 5:
            messagebox.showwarning("警告", "最多只能选择5个器械")
    
    def _remove_equipment(self, event):
        """移除器械"""
        selection = self.equipment_listbox.curselection()
        if selection:
            index = selection[0]
            equipment = self.equipment_listbox.get(index)
            self.equipment_listbox.delete(index)
            self.selected_equipment.remove(equipment)

    def _search_dishes(self):
        """搜索菜品"""
        try:
            # 获取搜索条件
            name_keyword = self.name_keyword_var.get().strip() or None
            ingredients = self.selected_ingredients if self.selected_ingredients else None
            equipment = self.selected_equipment if self.selected_equipment else None

            # 解析国家和主题
            country = None
            if self.country_var.get():
                country = self.country_var.get().split(" - ")[0]

            theme = None
            if self.theme_var.get():
                theme = self.theme_var.get().split(" - ")[0]

            source = self.source_var.get() or None

            # 执行搜索
            dishes = self.db.search_dishes(
                ingredients=ingredients,
                equipment=equipment,
                country=country,
                theme=theme,
                source=source,
                name_keyword=name_keyword
            )

            # 更新结果显示
            self._update_results_display(dishes)

            # 更新统计信息
            self._update_search_stats(dishes)

            self.status_var.set(f"搜索完成，找到 {len(dishes)} 个菜品")

        except Exception as e:
            messagebox.showerror("错误", f"搜索失败: {e}")
            self.status_var.set("搜索失败")

    def _update_results_display(self, dishes):
        """更新结果显示"""
        # 清空现有结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # 添加搜索结果
        for dish in dishes:
            self.results_tree.insert("", "end", values=(
                dish.dish_id,
                dish.name,
                dish.source,
                dish.restaurant_theme_name,
                dish.country_name,
                len(dish.processing_steps),
                dish.updated_time[:19] if dish.updated_time else ""
            ))

    def _update_search_stats(self, dishes):
        """更新搜索统计"""
        self.stats_text.delete(1.0, END)

        if not dishes:
            self.stats_text.insert(END, "未找到符合条件的菜品")
            return

        # 统计信息
        total_count = len(dishes)
        source_stats = {}
        theme_stats = {}
        country_stats = {}

        for dish in dishes:
            # 来源统计
            source_stats[dish.source] = source_stats.get(dish.source, 0) + 1

            # 主题统计
            theme_stats[dish.restaurant_theme_name] = theme_stats.get(dish.restaurant_theme_name, 0) + 1

            # 国家统计
            country_stats[dish.country_name] = country_stats.get(dish.country_name, 0) + 1

        self.stats_text.insert(END, f"=== 搜索统计 ===\n")
        self.stats_text.insert(END, f"总菜品数: {total_count}\n\n")

        self.stats_text.insert(END, "来源分布:\n")
        for source, count in source_stats.items():
            self.stats_text.insert(END, f"  {source}: {count}个\n")

        self.stats_text.insert(END, "\n主题分布:\n")
        for theme, count in theme_stats.items():
            if theme:
                self.stats_text.insert(END, f"  {theme}: {count}个\n")

        self.stats_text.insert(END, "\n国家分布:\n")
        for country, count in country_stats.items():
            if country:
                self.stats_text.insert(END, f"  {country}: {count}个\n")

    def _clear_search(self):
        """清空搜索条件"""
        self.name_keyword_var.set("")
        self.country_var.set("")
        self.theme_var.set("")
        self.source_var.set("")

        # 清空食材和器械选择
        self.selected_ingredients.clear()
        self.selected_equipment.clear()
        self.ingredients_listbox.delete(0, END)
        self.equipment_listbox.delete(0, END)

        # 重新搜索显示所有菜品
        self._search_dishes()

    def _view_details(self):
        """查看菜品详情"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个菜品")
            return

        # 获取选中的菜品ID
        item = self.results_tree.item(selection[0])
        dish_id = item['values'][0]

        # 获取菜品详情
        dish = self.db.get_dish_by_id(dish_id)
        if not dish:
            messagebox.showerror("错误", "菜品不存在")
            return

        # 切换到详情标签页
        self.notebook.select(1)

        # 显示详情
        self._display_dish_details(dish)

    def _display_dish_details(self, dish):
        """显示菜品详情"""
        self.details_text.delete(1.0, END)

        self.details_text.insert(END, f"=== {dish.name} ===\n\n")
        self.details_text.insert(END, f"菜品ID: {dish.dish_id}\n")
        self.details_text.insert(END, f"来源: {dish.source}\n")
        self.details_text.insert(END, f"原项目ID: {dish.source_id}\n")
        self.details_text.insert(END, f"餐厅主题: {dish.restaurant_theme_name} ({dish.restaurant_theme_id})\n")
        self.details_text.insert(END, f"国家: {dish.country_name} ({dish.country_id})\n")
        self.details_text.insert(END, f"创建时间: {dish.created_time}\n")
        self.details_text.insert(END, f"更新时间: {dish.updated_time}\n\n")

        if dish.processing_steps:
            self.details_text.insert(END, "=== 加工步骤 ===\n")
            for step in dish.processing_steps:
                self.details_text.insert(END, f"\n第{step.step_number}轮加工:\n")

                if step.ingredients:
                    self.details_text.insert(END, f"  食材: {', '.join(step.ingredients)}\n")

                if step.equipment:
                    self.details_text.insert(END, f"  器械: {step.equipment}\n")

                if step.processing_time > 0:
                    self.details_text.insert(END, f"  时间: {step.processing_time}分钟\n")

                if step.result_name:
                    self.details_text.insert(END, f"  产出: {step.result_name}\n")

        # 显示编辑记录
        records = self.db.get_edit_records(dish.dish_id)
        if records:
            self.details_text.insert(END, "\n=== 编辑记录 ===\n")
            for record in records:
                self.details_text.insert(END, f"\n{record.edit_time[:19]}\n")
                self.details_text.insert(END, f"  操作人: {record.operator_id}\n")
                self.details_text.insert(END, f"  操作: {record.edit_content}\n")

    def _edit_dish(self):
        """编辑菜品"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个菜品")
            return

        # 获取选中的菜品ID
        item = self.results_tree.item(selection[0])
        dish_id = item['values'][0]

        # 打开编辑窗口
        self._open_edit_window(dish_id)

    def _create_new_dish(self):
        """创建新菜品"""
        self._open_edit_window()

    def _open_edit_window(self, dish_id=None):
        """打开编辑窗口"""
        edit_window = DishEditWindow(self.root, self.db, dish_id, self.current_user)
        edit_window.window.transient(self.root)
        edit_window.window.grab_set()

        # 等待窗口关闭后刷新结果
        self.root.wait_window(edit_window.window)
        self._search_dishes()

    def _delete_dish(self):
        """删除菜品"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个菜品")
            return

        # 确认删除
        if not messagebox.askyesno("确认", "确定要删除选中的菜品吗？"):
            return

        # TODO: 实现删除功能
        messagebox.showinfo("提示", "删除功能待实现")

    def _export_selected(self):
        """导出选中菜品"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要导出的菜品")
            return

        # TODO: 实现导出功能
        messagebox.showinfo("提示", "导出功能待实现")

    def _import_mc_config(self):
        """导入MC配置"""
        config_file = filedialog.askopenfilename(
            title="选择MC配置文件",
            filetypes=[("Lua文件", "*.lua"), ("所有文件", "*.*")]
        )

        if config_file:
            try:
                from config_importer import ConfigImporter
                importer = ConfigImporter(self.db)
                count = importer.import_from_item_model_config(config_file, "MC")
                messagebox.showinfo("成功", f"成功导入 {count} 个菜品")
                self._search_dishes()  # 刷新显示
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {e}")

    def _import_fl_config(self):
        """导入FL配置"""
        config_file = filedialog.askopenfilename(
            title="选择FL配置文件",
            filetypes=[("Lua文件", "*.lua"), ("所有文件", "*.*")]
        )

        if config_file:
            try:
                from config_importer import ConfigImporter
                importer = ConfigImporter(self.db)
                count = importer.import_from_item_model_config(config_file, "FL")
                messagebox.showinfo("成功", f"成功导入 {count} 个菜品")
                self._search_dishes()  # 刷新显示
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {e}")

    def _export_excel(self):
        """导出Excel"""
        # 获取当前搜索结果
        selection = self.results_tree.get_children()
        if not selection:
            messagebox.showwarning("警告", "没有可导出的菜品")
            return

        # 选择保存路径
        output_file = filedialog.asksaveasfilename(
            title="导出Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )

        if output_file:
            try:
                from config_importer import ConfigImporter
                importer = ConfigImporter(self.db)

                # 获取当前显示的菜品
                dish_ids = []
                for item_id in selection:
                    item = self.results_tree.item(item_id)
                    dish_ids.append(item['values'][0])

                # 获取菜品对象
                dishes = []
                for dish_id in dish_ids:
                    dish = self.db.get_dish_by_id(dish_id)
                    if dish:
                        dishes.append(dish)

                count = importer.export_to_excel(output_file, dishes)
                messagebox.showinfo("成功", f"成功导出 {count} 条记录到Excel文件")

            except ImportError:
                messagebox.showerror("错误", "需要安装pandas和openpyxl库:\npip install pandas openpyxl")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    def _manage_ingredients(self):
        """管理食材"""
        # TODO: 实现食材管理
        messagebox.showinfo("提示", "食材管理功能待实现")

    def _manage_equipment(self):
        """管理器械"""
        # TODO: 实现器械管理
        messagebox.showinfo("提示", "器械管理功能待实现")

    def _manage_countries(self):
        """管理国家"""
        # TODO: 实现国家管理
        messagebox.showinfo("提示", "国家管理功能待实现")

    def _manage_themes(self):
        """管理餐厅主题"""
        # TODO: 实现餐厅主题管理
        messagebox.showinfo("提示", "餐厅主题管理功能待实现")


# 导入编辑窗口类
from dish_edit_window import DishEditWindow


def main():
    """主程序入口"""
    root = tk.Tk()
    app = DishManager(root)
    root.mainloop()


if __name__ == "__main__":
    main()
