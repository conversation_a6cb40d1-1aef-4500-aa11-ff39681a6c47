需求背景														
	我们希望能够有这么一个订单编辑器													
	这个编辑器可以读取一些基础的配置信息，如：生成器信息、合成树信息、器械制作信息、菜品信息等数据													
		我们可以通过调整配置信息，更改一些基础设置，如合成树线路、菜品制作方式等												
	策划在输入了一些订单指标参数及若干指定菜品后，编辑器能够按照一定的规则随机计算，输出若干套不同的、符合标准的订单配置方案													
		在输出这些订单方案的同时，还能够展示这些方案的一些关键性指标供策划同学参考												
	策划主要是使用这个订单编辑器来提升订单的配置效率													
														
														
操作流程及期望结果														
	0. 准备阶段：程序读取若干基础信息，做好计算准备工作													
		a. 读取生成器配置信息（LevelInitiativeProduce），后续操作可以通过导入的玩家此时拥有的生成器等级、数量，计算得到生成器产出效率												
			a1. 生成器产出效率有多种计算方式：平均效率、动态效率等，具体计算方法在后面有详细描述											
		b. 读取合成树配置信息（LevelGoodsBase），程序可以明确每个合成物品的生成器来源及等级												
		c. 读取器械制作菜品信息（LevelGoodsCooking），程序可以明确每个可制作菜品所用器械、所需要原料												
		d. 读取器械配置信息（LevelInstrument），后续操作同学可以通过导入的玩家此时拥有的器械等级、数量，计算得到器械情况												
			d1. 当菜品制作过程中需要器械时，使用最大等级的器械来计算加工时间											
		e. 读取菜品列表信息（LevelRecipe），能够计算出每个菜品的物品需求、转化为对应合成线一级合成物品数量等信息												
		f. 额外新增一个配置表格（新增表格），菜品表，配置每个菜品的虚拟等级、最短间隔波数、基础系列（支持小数）、剩余物品（衍生物产出的额外物品）以及额外体力数												
														
	1. 首先配置波订单基础指标及一些选项设置（将指标及选项设置通过配置表的方式，让程序进行读取，具体见“输入内容”页签）													
		0. 选填项，前置波次信息，即在当前菜系下，之前若干波的订单配置												
		a. 必填项，指定每波订单中的特色菜品，数量范围在4-7个（如果配置8个，报错，且舍弃第8个菜品）												
		b. 必填项，波订单体力消耗，波动范围比例												
		c. 必填项，玩家当前拥有的生成器等级及数量												
		d. 必填项，配置生成器的轮数范围												
			d1. 每个生成器单独配置一个轮数范围，一轮为生成器完全cd后达到其“产出次数上限”时的状态											
		e. 必填项，玩家当前拥有的器械等级及数量（和生成器相关配置放到一个表中）												
		f. 选填项，器械加工次数、总时间、单次最大时间												
			f1. 后续可能会对每个生成器单独配置											
		g. 选填项，波订单结束时剩余折合1级物品数量范围、实际物品数量范围												
		h. 必填项，波内7个订单体力消耗权重；例：10,10,15,15,15,20,30												
		i. 必填项，当前体力消耗模式，2bet、4bet、8bet												
		j. 必填项，生成器效率的计算方式（平均效率、动态效率、top3平均效率）												
		k. 必填项，每类生成器的总点击次数范围（20250610）												
		l. 必填项，每类生成器必须使用的物品最高等级（且不超过该等级）（20250610）												
		m. 必填项，单波内体力消耗最高的两个订单是否采取拥有生成器的最高效率计算并且不增加生成器轮数消耗（20250610）												
														
	2. 根据上一步设置的基础指标，程序计算得到波订单的随机订单结果													
		需要满足以下条件：												
			波订单生成7个随机订单，每个订单中包含1-2个物品											
			特色菜品全部包含在随机生成的订单中，每个订单中最多有1个特色菜品											
			波订单的每种生成器轮数需要满足配置范围（LevelInitiativeProduce.capacity算一轮）											
			波订单的器械参数需要满足配置范围											
			波订单的最终剩余折合1级物品数量、实际物品数量需要满足配置范围											
		波订单最终生成的随机订单，按照难度从低到高的顺序，计算确定该波订单的最优完成顺序，并记录每个订单的体力消耗（计算方法在第4步中有基础逻辑介绍）												
		输出内容：												
			输出20套符合要求的配置，每套配置展示上述指标条件的具体数值（后续会添加其它指标的展示需求，具体见“输出内容”页签）											
		后续可以进行的操作：												
			我们可以在选择了某个随机配置的基础上，进行下一波指标的输入，得出后续的随机配置结果											
														
	3. 随机计算过程（策划端方案）													
		a. 逻辑计算原则：特色菜品为先，包含所配置物品最高等级的菜品第二，普通菜品进行填充；大体力订单为先，小体力订单为后；												
		b. 细节步骤												
			b1. 首先根据设置的“订单体力消耗权重比例”和“波订单体力消耗”，计算得到每个订单的体力期望消耗											
			b2. 之后根据特色菜品，匹配对应的订单位置											
				1） 选择体力需求最高的特色菜品，为其匹配符合条件的订单位置										
					1.1）特色菜品的体力需求，占对应订单体力需求的60%-80%范围									
					1.2）如果特色菜品体力需求比最大的订单还大，则匹配最大订单，且该订单中会只设置1个物品，即特色菜品									
				2）根据其它限制条件，随机生成订单中剩余的物品										
					2.1）首先从体力需求来匹配，该订单剩余的体力，基础上下浮动10%（剩余体力的10%，左闭右闭）									
					2.2）其次基于各个生成器的使用，需要满足生成器轮数限制，不能超过配置的轮数上限									
					2.3）再次基于器械的使用，需要满足配置的器械使用次数及时间限制									
					2.4）在满足上述条件的所有物品中，随机选取1个									
					2.5）如果没有对应条件的物品，则逐步扩大体力波动幅度，每次扩大10%，最大可至上下浮动30%									
				3）每次计算体力需求时，按照所有当前已确定物品的整体来进行计算，不计算单个物品										
					3.1）每次确定一个物品后，需要计算此时的剩余物品信息（各个系列的1级物品数量以及衍生物1级物品数量）									
					3.2）如果剩余物品信息此时超过了配置数值，则将目前剩余物品中对应虚拟等级最高的物品作为下一个非特色物品需求（需要加一张表，配置虚拟等级）									
				4）在某个订单的需求物品确定后，更新波订单剩余所需的体力消耗数量（配置波订单体力消耗 - 已确定订单体力需求），并以此来决定剩余其它各个订单的体力期望消耗										
			b3. 循环上一步操作，直至所有特色菜品匹配到订单中											
			b4. 之后分配最高等级物品											
				1）判断是否有特色菜品是否符合系列必须使用的物品最高等级要求，如符合，则跳过该系列的最高等级要求。进入下一个系列的判断。										
				2）当有系列的最高等级要求不被特色菜品满足时。选择包含该物品的菜品，并为其匹配符合条件的订单位置										
					2.1）筛选包含该物品的菜品，筛选出体力小于剩余的最大订单的物品。									
					2.2）随机选择一个作为菜品，该菜品的体力需求，占对应订单体力需求的60%-80%范围									
					2.3）如果没有任何包含该物品的菜品符合需求。则匹配最大订单，且该订单中会只设置1个物品，从该菜品的一级加工物品中随机一个。									
				3）根据其它限制条件，随机生成订单中剩余的物品										
					3.1）首先从体力需求来匹配，该订单剩余的体力，基础上下浮动10%（剩余体力的10%，左闭右闭）									
					3.2）其次基于各个生成器的使用，需要满足生成器轮数限制，不能超过配置的轮数上限									
					3.3）再次基于器械的使用，需要满足配置的器械使用次数及时间限制									
					3.4）在满足上述条件的所有物品中，随机选取1个									
					3.5）如果没有对应条件的物品，则逐步扩大体力波动幅度，每次扩大10%，最大可至上下浮动30%									
				4）每次计算体力需求时，按照所有当前已确定物品的整体来进行计算，不计算单个物品										
					4.1）每次确定一个物品后，需要计算此时的剩余物品信息（各个系列的1级物品数量以及衍生物1级物品数量）									
					4.2）如果剩余物品信息此时超过了配置数值，则将目前剩余物品中对应虚拟等级最高的物品作为下一个非特色物品需求（需要加一张表，配置虚拟等级）									
				5）在某个订单的需求物品确定后，更新波订单剩余所需的体力消耗数量（配置波订单体力消耗 - 已确定订单体力需求），并以此来决定剩余其它各个订单的体力期望消耗										
			b5. 之后再确定剩余订单											
				1）选择体力需求最高的订单，每个订单划分为2个物品										
				2）每个订单中第一个物品体力需求范围占对应订单体力需求的60%-80%，以此为基础确定第一个物品的物品随机范围										
				3）再根据其它限制条件（b2.1-b2.5，b3.1-b3.5的限制条件），随机确定订单中的第一个物品										
				4）在确定了订单中第一个物品后，根据该订单的剩余体力需求、以及其它限制条件（b2.1-b2.5,b3.1-b3.5的限制条件），随机生成订单中的第二个物品										
				5）如果某个订单随机不到符合需求的物品，则扩大体力消耗范围，上下限分别提高10%、20%、30%										
				6）每次计算体力需求时，按照所有当前已确定物品的整体来进行计算，不计算单个物品										
					6.1）每次确定一个物品后，需要计算此时的剩余物品信息（各个系列的1级物品数量以及衍生物1级物品数量）									
					6.2）如果此时超过了配置数值，则将目前对应虚拟等级的物品作为下一个非特色物品需求									
			b6. 在确定了所有订单后，需要最后再计算一次体力消耗、生成器轮数、器械参数、剩余物品、占用棋盘格子等指标											
				1）重复输出20套不同的随机方案，供策划进行选择										
			b7. 补充限制条件（持续添加）											
				1）相同的菜品在同一波订单中不会重复出现										
					1.1）每个菜品都会配置一个最短间隔波数，支持灵活的根据菜品来配置间隔波数（和虚拟等级使用同一张新增表）									
				2）需要根据玩家此时拥有的生成器，随机可以生成的菜品（不能随机玩家此时没有对应生成器的菜品）										
				3）每个国家的特色菜品只会出现在对应国家的餐厅范围内（配置有效，程序不用考虑）										
				4）每个国家的特色菜品只有在之前已经手动配置出现过后，才会在随机范围内出现										
														
	4. 每波7个订单全部确定后，需要计算确定该波订单的最优完成顺序													
		a. 单独计算每个订单的体力消耗，并记录剩余物品												
			a1. 在7个订单中，选择体力消耗最少的订单围坐第1个订单，记录其体力消耗											
		b. 以第1个订单为基础，在其剩余物品条件下，计算其它订单作为第2个订单时，每个订单的体力消耗，并记录剩余物品												
			b1. 选择体力消耗最少的订单作为第2个订单，记录其体力消耗											
		c. 以此类推，决定从第1个订单到最后一个订单的顺序及每个订单的体力消耗												
														
	5. 每个指标的具体计算方法													
		a. 体力消耗（参考右侧流程图进行计算）												
			a1. 将订单中每个菜品拆分成各个生成器可以直接产出的物品，衍生物也要向前追溯到基础的生成器上											
			a2. 将每个物品根据其物品等级，计算得到折合1级物品数量，根据系列求和											
			a3. 根据每个系列的折合1级物品数量总和、“生成器效率”（后面有不同效率的计算方法说明）以及“bet倍率”，计算每个生成器对应系列的需求点击次数；											
				1）需求点击次数= 折合1级物品数量总和/生成器效率/bet倍率										
				2）每个生成器会对应多个系列，有多个需求点击次数，取最大值										
			a4. 将每个生成器的需求点击次数最大值求和，乘以bet倍率，得到该波订单的体力消耗											
			a5. 被动生成器产出的物品，体力消耗均为0											
														
		b. 生成器效率计算方法（补充根据现有“LevelInitiativeProduce”计算得到生成器效率的公式算法）												
			b1. 平均效率计算方法：（在计算开始时即可以计算得到的一个固定效率值）											
				1）根据用户当前拥有的生成器等级及数量，加权平均得到每个生成器类别对应系列的效率（每点击1次该生成器产出对应系列1级合成物的数量）										
				例：	如右侧表格所示，玩家此时拥有8级饮品生成器1个，6级饮品生成器1个，5级饮品生成器1个									
					根据右侧饮品生成器各个级别的基础效率表，计算得到此时的平均效率									
					系列A平均效率 =(1.05*10+1.1*12+1.35*16)/(10+12+16)=1.192							12	1.192	 10.07 
					系列B平均效率 =(0.15*10+0.15*12+0.15*16)/(10+12+16)=0.15									
					系列C平均效率 =(0.15*10+0.15*12+0.15*16)/(10+12+16)=0.15									
					选择体力消耗最高的两个订单采取最高效率计算时，这两个订单用配置的当前效率最高的生成器作为效率计算依据（20250611）									
			b2. 动态效率计算方法：（随着订单进度的推进，动态的取此时正在使用等级的生成器对应的效率值）											
				1）根据用户当前拥有的生成器等级及数量，按照等级从高到低依次使用，取此时正在使用等级的生成器对应的效率数值										
				2）每个生成器最多点击“产出次数上限”（配置表“LevelInitiativeProduce.capacity”）次										
				3）高级生成器点击达到“产出次数上限”后，继续使用次一级生成器										
				4）最低级生成器点击达到“产出次数上限”后，回到最高等级生成器，此时该生成器轮数+1										
				5）这里的点击次数在a3步骤中已经考虑到了“bet倍率”的影响										
														
		c. 生成器所需轮数												
			c1. 平均效率计算方法：											
				1）生成器所需轮数 = 每个生成器需求点击次数最大值（a3步骤的结果数值）/产出次数上限（配置表“LevelInitiativeProduce.capacity”）之和										
				选择体力消耗最高的两个订单采取最高效率计算时，这两个订单的消耗不参与轮数的计算（20250611）										
			c2. 动态效率计算方法：											
				1）生成器所需轮数，在计算体力消耗过程中根据动态效率计算方法统计得到										
														
		d. 剩余物品数量及棋盘格子占用数量（输出内容）												
			d1. 折合1级剩余物品数量											
				1）根据计算“体力消耗”时的a3步骤，得到每个生成器的点击次数最大值，减去每个系列各自的需求点击次数，得到每个系列点击次数的差值										
				2）根据生成器效率以及bet倍率，相乘得到每个系列各自剩余的折合1级物品数量										
					2.1）按照各个系列，分别展示各自的折合1级剩余物品数量									
			d2. 实际剩余物品数量											
				1）根据每个系列各自剩余的折合1级物品数量，将其拆分为2^n数值求和，得到其物品数量										
					1.1）按照各个系列，分别展示各自的实际剩余物品数量									
				2）衍生物通过新增表格中的额外物品内容进行计算										
			d3. 棋盘格子占用											
				1）按照订单提交顺序，统计每个订单在完成时的棋盘格子占用数										
					1.1）每个订单在可以提交时，统计包含可以提交物品在内的所有棋盘格子占用数量									
					1.2）棋盘格子占用有以下几个分类：生成器占用、器械占用、订单物品占用、其它剩余物品占用									
					1.3）其中生成器占用、器械占用在初始输入的信息中就能够获得									
					1.4）订单物品占用是指该订单中有几个需要提交的物品									
					1.5）其它剩余物品占用是指此时剩余物品中计算得到的实际物品数量									
					1.6）衍生物使用新增表格中的额外物品内容进行计算									
														
	6. 其它补充内容													
		a. 数值精度												
			体力消耗可以使用小数											
			折合1级物品可以使用小数											
			新增表格中配置的“基础系列”也支持小数（1/4水）											
				基础系列物品的小数部分可以进行累加求和，不能往上再进行合成										
		b. 根据“LevelInitiativeProduce”配置表计算得到生成器效率的公式算法												
			b1. 读取“LevelInitiativeProduce.production”数据并对其进行解析											
				1）该数据格式如下所示，这是“饮料生成器9”的配置数据										
					100020|5;100022|5;100023|4;100040|3;100021|3									
				2）这个数据代表了每点击20次生成器，会产出5个100020，5个100022，4个100023，3个100040，3个100021										
			b2. 根据上一步读取出来的数据，计算其总点击次数、每个系列的产出折合1级物品数量											
				1）还以上面“饮料生成器9”的配置数据为例，其总点击次数为20										
				2）通过配置表“LevelGoodsBase”查出其可以产出的各个物品的对应系列及折合1级物品数量										
					2.1）100020是1003系列的1级物品，产出5个；100022是1003系列的2级物品，产出5个；100023是1003系列的3级物品，产出4个；									
						通过计算得到其20次点击中可以获得31个1003系列的折合1级物品								
						其在1003系列的生成器效率 = 31/20 = 1.55								
					2.2）100040是1005系列的1级物品，产出3个									
						通过计算得到其20次点击中可以获得3个1005系列的折合1级物品								
						其在1005系列的生成器效率 = 3/20 = 0.15								
					2.3）100021是1002系列的1级物品，产出3个									
						通过计算得到其20次点击中可以获得3个1002系列的折合1级物品								
						其在1002系列的生成器效率 = 3/20 = 0.15								
		c. 菜品制作过程中额外消耗体力的计算方式												
			c1. 先使用配置的方式											
			c2. 后续考虑提供计算方法，方便程序进行计算，不再需要进行手动配置											
