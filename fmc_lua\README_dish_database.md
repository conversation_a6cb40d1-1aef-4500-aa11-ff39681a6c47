# 菜品库管理系统

## 概述

基于您的需求开发的完整菜品库管理系统，支持菜品录入、查询、编辑和导入导出功能。系统采用SQLite数据库存储，提供直观的图形界面操作。

## 核心功能

### 1. 固定录入信息管理
- **食材库**: 预置常用食材，支持分类管理
- **器械库**: 预置常用器械，支持分类管理  
- **国家库**: 预置国家代码和名称
- **餐厅主题库**: 预置餐厅主题ID和名称
- **菜品来源**: MC、FL、其他网站等来源管理

### 2. 菜品录入功能

#### 手工录入格式
- **菜品库ID**: 唯一标识符
- **菜品名字**: 支持多步骤菜品的每个阶段命名
- **多轮加工**: 支持N轮加工流程
  - 每轮食材A\B\C\D\E
  - 每轮所需器械和时间
  - 每轮加工产出名称
- **菜品来源**: MC\FL\其他
- **菜品ID**: 在原项目中的ID
- **餐厅主题**: ID和名称
- **国家**: ID和名称

#### 外部导入
- **MC配置导入**: 读取MC项目的ItemModelConfig.lua
- **FL配置导入**: 读取FL项目的ItemModelConfig.lua
- **JSON导入**: 支持标准JSON格式导入
- **批量导入**: 支持批量处理多个配置文件

### 3. 菜品查询功能

#### 多条件交叉查询
- **食材筛选**: 最多5个复选，至少选一个
- **器械筛选**: 最多5个复选，可不选
- **国家筛选**: 单选，可不选
- **餐厅主题筛选**: 单选，可不选
- **来源筛选**: 单选，可不选
- **名称关键词**: 支持模糊搜索

#### 查询结果展示
- **列表展示**: 菜品ID、名称、来源、主题、国家等
- **详细信息**: 点击查看完整的菜品信息
- **统计分析**: 实时显示搜索结果统计
- **排序筛选**: 支持多种排序方式

### 4. 菜品编辑功能
- **在线编辑**: 直接编辑查询出的菜品
- **版本控制**: 保存编辑历史记录
- **操作追溯**: 记录操作人ID、编辑内容、编辑时间
- **数据验证**: 确保数据完整性和一致性

### 5. 导出功能
- **Excel导出**: 支持详细信息的Excel表格导出
- **JSON导出**: 支持标准JSON格式导出
- **格式化展示**: 每个菜品信息展开顺序平铺

## 文件结构

```
fmc_lua/
├── dish_manager.py              # 主程序界面
├── dish_database.py             # 数据库管理模块
├── dish_edit_window.py          # 菜品编辑窗口
├── config_importer.py           # 配置导入模块
├── test_dish_database.py        # 测试脚本
├── README_dish_database.md      # 说明文档
└── dish_database.db             # SQLite数据库文件（运行时生成）
```

## 使用方法

### 启动程序
```bash
python dish_manager.py
```

### 基本操作流程

#### 1. 菜品搜索
1. **设置搜索条件**：
   - 在左侧面板输入菜品名称关键词
   - 选择食材（最多5个）
   - 选择器械（最多5个）
   - 选择国家和餐厅主题
   - 选择来源

2. **执行搜索**：
   - 点击"搜索"按钮
   - 查看右侧搜索结果列表
   - 查看左下角的搜索统计信息

#### 2. 菜品查看
1. **查看列表**：在搜索结果中浏览菜品列表
2. **查看详情**：双击菜品或点击"查看详情"按钮
3. **详细信息**：在"菜品详情"标签页查看完整信息

#### 3. 菜品编辑
1. **选择菜品**：在搜索结果中选择要编辑的菜品
2. **打开编辑**：点击"编辑菜品"按钮
3. **编辑信息**：
   - 修改基本信息（名称、来源等）
   - 添加/删除/编辑加工步骤
   - 设置食材、器械、时间等
4. **保存更改**：点击"保存"按钮

#### 4. 新建菜品
1. **创建新菜品**：点击"新建菜品"按钮
2. **填写信息**：
   - 输入菜品ID和名称
   - 选择来源、餐厅主题、国家
   - 添加加工步骤
3. **保存菜品**：完成编辑后保存

#### 5. 配置导入
1. **选择导入类型**：
   - 文件 → 导入MC配置
   - 文件 → 导入FL配置
2. **选择文件**：选择对应的.lua配置文件
3. **确认导入**：系统自动解析并导入菜品

#### 6. 数据导出
1. **选择菜品**：在搜索结果中选择要导出的菜品
2. **导出Excel**：文件 → 导出Excel
3. **选择路径**：选择保存位置和文件名

### 高级功能

#### 加工步骤编辑
1. **添加步骤**：在编辑窗口点击"添加步骤"
2. **编辑步骤**：双击步骤进行编辑
3. **步骤管理**：
   - 上移/下移调整顺序
   - 删除不需要的步骤
   - 设置食材、器械、时间、产出

#### 编辑记录查看
1. **查看历史**：在菜品详情中查看编辑记录
2. **操作追溯**：查看每次编辑的操作人和时间
3. **内容对比**：查看具体的编辑内容

## 数据库结构

### 主要表结构
- **dishes**: 菜品主表
- **ingredients**: 食材表
- **equipment**: 器械表
- **countries**: 国家表
- **restaurant_themes**: 餐厅主题表
- **edit_records**: 编辑记录表

### 数据关系
- 菜品与餐厅主题：多对一关系
- 菜品与国家：多对一关系
- 菜品与加工步骤：一对多关系（JSON存储）
- 菜品与编辑记录：一对多关系

## 配置导入说明

### ItemModelConfig.lua解析
- **菜品识别**：自动识别以"ds_"开头的菜品代码
- **食材提取**：从GeneratedItems中提取食材信息
- **属性解析**：解析UseEnergy、Frequency等属性
- **主题推断**：根据代码自动推断餐厅主题
- **国家推断**：根据代码自动推断所属国家

### JSON格式支持
```json
{
  "id": "dish_001",
  "name": "菜品名称",
  "source": "来源",
  "theme_id": "主题ID",
  "country_id": "国家ID",
  "steps": [
    {
      "step_number": 1,
      "ingredients": ["食材1", "食材2"],
      "equipment": "器械",
      "time": 30,
      "result": "产出名称"
    }
  ]
}
```

## 扩展功能

### 已实现
- 完整的CRUD操作
- 多条件搜索和筛选
- 配置文件导入
- Excel/JSON导出
- 编辑历史追溯
- 数据完整性验证

### 可扩展
- 图片上传和管理
- 营养成分分析
- 成本计算功能
- 菜品评分系统
- 用户权限管理
- 数据同步功能

## 技术特性

### 数据库
- **SQLite**: 轻量级、无需配置
- **事务支持**: 确保数据一致性
- **索引优化**: 提高查询性能
- **备份恢复**: 支持数据备份

### 界面
- **Tkinter**: 跨平台GUI框架
- **响应式布局**: 适应不同屏幕尺寸
- **多标签页**: 清晰的功能分区
- **实时更新**: 动态刷新显示内容

### 数据处理
- **JSON序列化**: 复杂数据结构存储
- **正则表达式**: 配置文件解析
- **数据验证**: 输入数据校验
- **错误处理**: 完善的异常处理

## 注意事项

1. **数据备份**: 定期备份dish_database.db文件
2. **权限管理**: 多人使用时注意操作权限
3. **数据一致性**: 避免同时编辑同一菜品
4. **文件编码**: 配置文件需要UTF-8编码

## 故障排除

### 常见问题
1. **数据库锁定**: 重启程序解决
2. **导入失败**: 检查配置文件格式
3. **搜索无结果**: 检查搜索条件设置
4. **编辑保存失败**: 检查必填字段

### 性能优化
1. **定期清理**: 删除无用的编辑记录
2. **索引维护**: 重建数据库索引
3. **数据压缩**: 定期压缩数据库文件

这个菜品库管理系统完全满足您的需求，提供了完整的菜品管理解决方案，支持多种数据来源的导入和多种格式的导出，是一个功能强大、易于使用的菜品管理工具。
