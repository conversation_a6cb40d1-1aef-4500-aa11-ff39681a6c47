# Link模式中寻找剩余体力消耗最小订单的算法分析

## 概述

在FlambeTime的Link模式中，系统需要自动选择剩余体力消耗最小的订单作为下一个目标。本文档详细分析这个选择算法的实现逻辑。

## 🎯 核心算法入口

### 主要调用流程
```lua
-- FlambeTimeModel.lua 第381-391行
function FlambeTimeModel:_GetNextTargetOrder(orders)
  local energyDiff, minEnergyDiff, nextTargetOrder
  for id, order in pairs(orders) do
    energyDiff = GM.MainBoardModel:GetOrderFillEnergyDiff(order)  -- 计算每个订单的体力差额
    if minEnergyDiff == nil or minEnergyDiff > energyDiff then   -- 找最小值
      minEnergyDiff = energyDiff
      nextTargetOrder = order
    end
  end
  return nextTargetOrder  -- 返回体力消耗最小的订单
end
```

### 触发时机
- **Link模式启动**: 完成第一个订单后触发
- **Link模式继续**: 每完成一个订单后自动选择下一个
- **条件检查**: 剩余订单数量 > 1 时继续Link

## ⚡ 体力差额计算详解

### 🔍 GetOrderFillEnergyDiff 方法
```lua
-- BaseSceneBoardModel.lua 第281-296行
function BaseSceneBoardModel:GetOrderFillEnergyDiff(order)
  local log = "ID: " .. order:GetId() .. "\n"
  local unfilledRequirements, codeCountMap = self:GetUnfilledOrderRequirements(false, order)
  local energyDiff = 0
  local oneEnergyDiff, oneLog
  
  -- 遍历订单的未满足需求
  for requireItem, requireCount in pairs(unfilledRequirements) do
    if not GM.ItemDataModel:IsDishes(requireItem) then  -- 只计算非菜品的体力消耗
      log = log .. "\n\t棋子 " .. requireItem .. " 缺少 " .. requireCount .. "个\n"
      oneEnergyDiff, oneLog = self:_GetEnergyDiff(requireItem, requireCount, codeCountMap, "")
      energyDiff = energyDiff + oneEnergyDiff
      log = log .. oneLog
    end
  end
  
  log = log .. "\n总体力差额为  " .. energyDiff .. "\n"
  return energyDiff, log
end
```

### 🧮 _GetEnergyDiff 递归计算
```lua
-- BaseSceneBoardModel.lua 第298-316行
function BaseSceneBoardModel:_GetEnergyDiff(requireItem, requireCount, codeCountMap, log)
  -- 步骤1: 计算折合1级物品的缺少数量
  local remainRequireToLevel1Count = ItemUtility.GetRemainRequireToLevel1CountMinusBelowLevelItems(
    requireItem, requireCount, codeCountMap
  )
  
  log = log .. "\t刨除已有低等级棋子后，折合缺少一级棋子 " .. remainRequireToLevel1Count .. "个\n"
  
  if remainRequireToLevel1Count <= 0 then
    return 0, log  -- 已有足够物品，无需额外体力
  end
  
  -- 步骤2: 获取物品链信息
  local requireChain = GM.ItemDataModel:GetChainId(requireItem)
  local level1Item = ItemUtility.GetItemType(requireChain, 1)
  local transfromFrom = GM.ItemDataModel:GetTransformFrom(level1Item)
  
  -- 步骤3: 计算体力消耗
  if transfromFrom == nil then
    -- 基础生成器物品：直接计算体力
    local oneEnergy = GM.ItemDataModel:GetItemCurProduceEnergy(level1Item)
    local result = oneEnergy * remainRequireToLevel1Count
    log = log .. "\t一个一级棋子体力消耗为 " .. oneEnergy .. "，" .. remainRequireToLevel1Count .. "个为" .. result .. "\n"
    return result, log
  else
    -- 合成物品：递归计算原材料体力
    log = log .. "\t向上追溯 " .. transfromFrom .. " " .. remainRequireToLevel1Count .. "个\n"
    return self:_GetEnergyDiff(transfromFrom, remainRequireToLevel1Count, codeCountMap, log)
  end
end
```

## 📊 体力计算的关键步骤

### 1️⃣ 未满足需求分析
```lua
-- 获取订单未满足的需求
local unfilledRequirements, codeCountMap = self:GetUnfilledOrderRequirements(false, order)

-- unfilledRequirements 结构示例:
{
  ["it_1_3"] = 2,    -- 需要2个3级蔬菜
  ["it_2_2"] = 1,    -- 需要1个2级水果
  ["ds_chopve_1"] = 1 -- 需要1个切菜
}

-- codeCountMap 结构示例:
{
  ["it_1_1"] = 5,    -- 棋盘+库存有5个1级蔬菜
  ["it_1_2"] = 2,    -- 棋盘+库存有2个2级蔬菜
  ["it_2_1"] = 3     -- 棋盘+库存有3个1级水果
}
```

### 2️⃣ 折合计算逻辑
```lua
-- ItemUtility.GetRemainRequireToLevel1CountMinusBelowLevelItems 的作用:
-- 需要: it_1_3 × 2个 (3级蔬菜2个)
-- 已有: it_1_1 × 5个, it_1_2 × 2个
-- 计算: 
--   - 2个it_1_2可以合成1个it_1_3，还需要1个it_1_3
--   - 1个it_1_3需要4个it_1_1 (2^(3-1) = 4)
--   - 已有5个it_1_1，足够合成1个it_1_3
--   - 结果: remainRequireToLevel1Count = 0
```

### 3️⃣ 体力消耗计算
```lua
-- 基础生成器物品的体力计算
local oneEnergy = GM.ItemDataModel:GetItemCurProduceEnergy(level1Item)
-- oneEnergy 通常为生成器的基础体力消耗，如:
-- - 蔬菜生成器: 1体力/个
-- - 水果生成器: 1体力/个  
-- - 肉类生成器: 2体力/个

-- 总体力 = 单个体力 × 需要数量
local totalEnergy = oneEnergy * remainRequireToLevel1Count
```

## 🔄 递归追溯机制

### 合成物品的体力计算
```lua
-- 示例: 计算ds_chopve_1 (切菜)的体力消耗
-- 1. ds_chopve_1 的 transfromFrom = "it_1_1" (1级蔬菜)
-- 2. 递归调用 _GetEnergyDiff("it_1_1", count, codeCountMap, log)
-- 3. it_1_1 的 transfromFrom = nil (基础生成器物品)
-- 4. 返回 1体力 × count
```

### 多层合成的处理
```lua
-- 示例: 高级菜品的体力计算
-- ds_grillve_2 (高级烤肉) 
--   ↓ transfromFrom
-- ds_grillve_1 (基础烤肉)
--   ↓ transfromFrom  
-- it_3_1 (1级肉类)
--   ↓ 基础生成器
-- 2体力/个
```

## 🎯 订单选择策略

### 选择算法特点
1. **贪心算法**: 每次选择当前体力消耗最小的订单
2. **实时计算**: 考虑当前棋盘和库存状态
3. **精确评估**: 递归计算到基础生成器级别
4. **只计算非菜品**: 菜品体力消耗通过材料间接计算

### 优化考虑
```lua
-- 体力差额的影响因素:
-- 1. 订单需求物品的等级 (高等级需要更多基础物品)
-- 2. 当前库存状况 (已有物品可以减少体力需求)
-- 3. 物品合成链长度 (更长的链条可能需要更多体力)
-- 4. 生成器基础体力消耗 (不同系列的基础体力不同)
```

## 📈 实际应用示例

### 示例场景
```lua
-- 当前有3个订单:
-- 订单A: 需要 it_1_3 × 2, ds_chopve_1 × 1
-- 订单B: 需要 it_2_2 × 3, ds_juice_1 × 1  
-- 订单C: 需要 it_3_1 × 5, ds_grillve_1 × 1

-- 当前库存:
-- it_1_1 × 10, it_1_2 × 3
-- it_2_1 × 8, it_2_2 × 1
-- it_3_1 × 2

-- 计算结果:
-- 订单A: 体力差额 = 2体力 (需要补充材料)
-- 订单B: 体力差额 = 5体力 (需要更多水果)
-- 订单C: 体力差额 = 8体力 (肉类体力消耗高)

-- 选择: 订单A (体力差额最小)
```

### 日志输出示例
```
ID: order_001

	棋子 it_1_3 缺少 2个
	刨除已有低等级棋子后，折合缺少一级棋子 2个
	一个一级棋子体力消耗为 1，2个为2

总体力差额为 2
```

## 🎮 Link模式的战略意义

### 效率最大化
- **最小体力路径**: 优先完成容易的订单，节省体力
- **资源优化**: 充分利用现有库存和棋盘物品
- **连锁效应**: 完成订单获得的奖励可以帮助后续订单

### 游戏平衡
- **难度递增**: 随着简单订单完成，剩余订单难度增加
- **策略选择**: 玩家可以通过库存管理影响订单选择
- **时间压力**: Link模式的时间限制增加紧迫感

这个算法通过精确的体力计算和贪心选择策略，实现了Link模式下的智能订单排序，既保证了游戏的挑战性，又提供了合理的难度曲线。
