import pandas as pd
import re
import os

def parse_lua_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 首先分割每个任务块
    tasks = re.findall(r'{\s*ChapterId\s*=\s*"[^"]+"\s*,(?:[^{}]|{(?:[^{}]|{[^{}]*})*})*}', content)
    
    data = []
    for task_content in tasks:
        try:
            # 提取基本信息
            chapter_id = re.search(r'ChapterId\s*=\s*"([^"]+)"', task_content).group(1)
            task_id = int(re.search(r'Id\s*=\s*(\d+)', task_content).group(1))
            cost = int(re.search(r'Cost\s*=\s*(\d+)', task_content).group(1))
            
            # 提取Rewards信息
            rewards = re.findall(r'Currency\s*=\s*"([^"]+)",\s*Amount\s*=\s*(\d+)', task_content)
            exp_amount = 0
            energy_amount = 0
            for currency, amount in rewards:
                amount = int(amount)
                if currency == "exp":
                    exp_amount = amount
                elif currency == "energy":
                    energy_amount = amount
            
            # 准备基础数据
            row_data = {
                'ChapterId': chapter_id,
                'Id': task_id,
                'Cost': cost,
                'Rewards-Currency-exp': 'exp',
                'Rewards-Currency-expAmount': exp_amount,
                'Rewards-Currency-energy': 'energy',
                'Rewards-Currency-energyAmount': energy_amount,
                'SlotState1-Slot': '',
                'SlotState1-State': '',
                'SlotState2-Slot': '',
                'SlotState2-State': '',
                'SlotState3-Slot': '',
                'SlotState3-State': ''
            }
            
            # 提取SlotState信息
            slot_states = re.findall(r'Slot\s*=\s*"([^"]+)",\s*State\s*=\s*(\d+)', task_content)
            for i, (slot, state) in enumerate(slot_states):
                if i < 3:  # 处理前三个SlotState
                    row_data[f'SlotState{i+1}-Slot'] = slot
                    row_data[f'SlotState{i+1}-State'] = int(state)
            
            data.append(row_data)
        except Exception as e:
            print(f"处理任务时出错: {str(e)}")
            continue
    
    if not data:
        print(f"警告: 文件 {file_path} 没有解析到任何数据")
        return None
    
    return pd.DataFrame(data)

def find_lua_files(base_path):
    lua_files = []
    for root, dirs, files in os.walk(base_path):
        for file in files:
            if file.startswith('TaskConfig_') and file.endswith('.lua'):
                full_path = os.path.join(root, file)
                lua_files.append(full_path)
    return lua_files

def main():
    # 基础路径
    base_path = 'd:/MCWork/GHabout/FMC/fmc_lua_1.18.5/fmc_lua/Data/Config/Mainline'
    
    # 创建Excel写入器
    output_file = '建筑任务.xlsx'
    writer = pd.ExcelWriter(output_file, engine='openpyxl')
    
    # 获取所有lua文件
    lua_files = find_lua_files(base_path)
    
    # 处理每个文件
    for lua_file in lua_files:
        try:
            # 从文件名获取sheet名
            sheet_name = os.path.basename(lua_file).replace('TaskConfig_', '').replace('.lua', '')
            
            # 解析数据
            df = parse_lua_file(lua_file)
            if df is not None and not df.empty:
                # 按Id排序
                df = df.sort_values('Id')
                
                # 写入到对应的sheet
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                print(f'已处理: {sheet_name}')
            
        except Exception as e:
            print(f'处理文件 {lua_file} 时出错: {str(e)}')
    
    # 保存Excel文件
    writer.close()
    print(f'\n所有数据已成功导出到 {output_file}')

if __name__ == '__main__':
    main() 